<resources>
    <!-- Question -->
    <string name="deck_layout_question_bottom_sheet_title">Вопрос</string>
    <string name="deck_layout_question_bottom_sheet_subtitle">Напиши вопрос, на который будешь делать расклад, или оставь поле пустым</string>
    <string name="deck_layout_question_bottom_sheet_hint">О чем хочешь спросить карты?</string>
    <string name="deck_layout_question_bottom_sheet_next_button">Далее</string>

    <!-- Exit Dialog -->
    <string name="deck_layout_exit_dialog_cancel_button">Отмена</string>
    <string name="deck_layout_exit_dialog_exit_button">Выйти</string>
    <string name="deck_layout_exit_dialog_title">Точно выйти</string>
    <string name="deck_layout_exit_dialog_message">Данные этого расклада будут потеряны</string>

    <string name="deck_layout_flat_tab">Обычный расклад</string>
    <string name="deck_layout_ar_tab">AR</string>
    <string name="deck_layout_finish">Завершить</string>
    <string name="deck_layout_next_card">Еще карту</string>
    <string name="deck_layout_last_card">Последнюю карту</string>

    <!-- Predefined Layout -->
    <string name="predefined_layout_header">Темы раскладов</string>
    <string name="predefined_layout_question_number">Вопрос %1$d из %2$d</string>
    <string name="predefined_layout_next_question">Следующий вопрос</string>
    <string name="predefined_layout_tab_all">Все</string>

    <!-- Random card -->
    <string name="random_card_create_record">Добавить запись</string>
    <string name="random_card_close_popup">Закрыть</string>
    <string name="random_card_close_dialog_title">Точно закрыть?</string>
    <string name="random_card_close_dialog_message">Данные этого расклада будут потеряны</string>
    <string name="random_card_close_dialog_cancel_button">ОТМЕНА</string>
    <string name="random_card_close_dialog_skip_button">ЗАКРЫТЬ</string>

    <!-- AR -->
    <string name="deck_layout_ar_loading_message">Твои карты совсем скоро загрузятся во всей своей красе!</string>
    <string name="deck_layout_ar_layout_message">Направь камеру на плоскую поверхность, чтобы рамка стала белой, а затем нажми на экран</string>
    <string name="random_card_go_to_record">Смотреть запись</string>
    <string name="random_card_deck_title">Карта дня</string>
    <string name="random_card_deck_title_card_counter">%1$d из %2$d</string>
    <string name="deck_layout_camera_permission_denied_header">Включить AR</string>
</resources>
