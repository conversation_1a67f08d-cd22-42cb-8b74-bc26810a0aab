<resources>
    <!-- Main Auth -->
    <string name="main_auth_sign_up_button_text">Зарегистрироваться</string>
    <string name="main_auth_sign_in_button_text">Войти</string>
    <string name="main_auth_sign_in_by_sso_text">Или войти с помощью</string>

    <string name="auth_user_type_email">Почта</string>
    <string name="auth_user_type_sso">SSO</string>
    <string name="auth_user_type_sso_vk">Авторизация через "ВКонтакте"</string>
    <string name="auth_user_type_sso_ok">Авторизация через "Одноклассники"</string>
    <string name="auth_user_type_sso_yandex">Авторизация через "Яндекс"</string>

    <!-- Sign Up -->
    <string name="sign_up_toolbar_text">Регистрация</string>
    <string name="sign_up_sent_email_title">Подтверди email</string>
    <string name="sign_up_sent_email_text">На почту %s отправлено письмо для подтверждения адреса эл почты. Для завершения регистрации выполни инструкции из письма. После перехода по ссылке этот экран автоматически свернется</string>

    <!-- Sign In -->
    <string name="sign_in_toolbar_text">Вход</string>

    <!-- Reset Password -->
    <string name="reset_password_toolbar_text">Восстановление пароля</string>
    <string name="reset_password_title">Почта</string>
    <string name="reset_password_text">Мы отправим тебе ссылку для изменения пароля</string>
    <string name="reset_password_sent_email_title">Открой почту</string>
    <string name="reset_password_sent_email_text">На почту %s отправлено письмо для изменения пароля.</string>
    <string name="reset_password_dismiss_dialog_title">Точно закрыть?</string>
    <string name="reset_password_dismiss_dialog_text">При отказе от верификации ты попадешь на экран входа</string>
    <string name="reset_password_dismiss_dialog_confirm">ЗАКРЫТЬ</string>
    <string name="reset_password_dismiss_dialog_cancel">ОТМЕНА</string>

    <!-- Password Enter -->
    <string name="password_enter_error">Пароль должен содержать не менее 8 символов</string>
    <string name="password_enter_wrong_password_error">Неверный пароль</string>
    <string name="password_enter_title">Пароль</string>
    <string name="password_enter_forget_password">Не помнишь пароль?</string>
    <string name="password_enter_signin">Войти</string>

    <!-- Password Create -->
    <string name="password_create_title">Введи пароль</string>
    <string name="password_create_hint">Минимальная длина пароля — 8 символов</string>
    <string name="password_create_error">Пароль должен содержать не менее 8 символов</string>
    <string name="password_create_placeholder">Пароль</string>
    <string name="password_create_confirm_title">Повтори пароль</string>
    <string name="password_create_confirm_error">Пароли не совпадают</string>
    <string name="password_create_signup">Зарегистрироваться</string>

    <!-- Email Enter -->
    <string name="email_enter_error">Почта может содержать только латиницу, цифры и символы @._-</string>
    <string name="email_enter_placeholder"><EMAIL></string>
    <string name="email_enter_signin">Войти</string>
    <string name="email_enter_signup">Зарегистрироваться</string>
    <string name="email_enter_next_button">Отправить</string>
    <string name="email_enter_terms">Я согласен с [условиями использования сервиса] и [обработки персональных данных]</string>
    <string name="email_enter_has_account_error">Такой пользователь еще не зарегистрирован</string>
    <string name="email_enter_no_has_account_error">Такой пользователь уже зарегистрирован</string>
    <string name="password_confirm_button_confirm">Далее</string>

</resources>
