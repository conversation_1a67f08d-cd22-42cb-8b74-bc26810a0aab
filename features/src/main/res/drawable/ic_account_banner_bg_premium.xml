<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="343dp"
    android:height="140dp"
    android:viewportWidth="343"
    android:viewportHeight="140">
  <group>
    <clip-path
        android:pathData="M0,0h343v140h-343z"/>
    <path
        android:pathData="M0,0h343v140h-343z"
        android:fillColor="#2E3251"
        android:fillAlpha="0.55"/>
    <path
        android:pathData="M125.04,89.68m-23.81,90.19a93.27,93.27 59.79,1 1,47.62 -180.37a93.27,93.27 59.79,1 1,-47.62 180.37"
        android:strokeAlpha="0.8"
        android:fillAlpha="0.8">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="192.48"
            android:startY="107.39"
            android:endX="50.54"
            android:endY="58.25"
            android:type="linear">
          <item android:offset="0" android:color="#006749BB"/>
          <item android:offset="0.52" android:color="#386749BB"/>
          <item android:offset="1" android:color="#AA6749BB"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M204.72,43.29m59.97,-71.44a93.27,93.27 85.01,1 1,-119.93 142.89a93.27,93.27 85.01,1 1,119.93 -142.89"
        android:strokeAlpha="0.8"
        android:fillAlpha="0.8">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="151.26"
            android:startY="-1.46"
            android:endX="258.73"
            android:endY="103.47"
            android:type="linear">
          <item android:offset="0" android:color="#006749BB"/>
          <item android:offset="0.52" android:color="#386749BB"/>
          <item android:offset="1" android:color="#AA6749BB"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M161.43,62.96m59.97,-71.44a93.27,93.27 85.01,1 1,-119.93 142.89a93.27,93.27 85.01,1 1,119.93 -142.89"
        android:strokeAlpha="0.8"
        android:fillAlpha="0.8">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="107.96"
            android:startY="18.21"
            android:endX="215.43"
            android:endY="123.14"
            android:type="linear">
          <item android:offset="0" android:color="#006749BB"/>
          <item android:offset="0.52" android:color="#386749BB"/>
          <item android:offset="1" android:color="#AA6749BB"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M271.12,61.12m-89.81,-89.81a127.01,127.01 0,1 1,179.62 179.62a127.01,127.01 90,1 1,-179.62 -179.62"
        android:strokeAlpha="0.8"
        android:fillAlpha="0.8">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="204.08"
            android:startY="128.34"
            android:endX="359.15"
            android:endY="-5.01"
            android:type="linear">
          <item android:offset="0" android:color="#006749BB"/>
          <item android:offset="0.52" android:color="#386749BB"/>
          <item android:offset="1" android:color="#876749BB"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M76.43,60.26m48.48,92.75a104.65,104.65 107.4,1 1,-96.96 -185.49a104.65,104.65 107.4,1 1,96.96 185.49"
        android:strokeAlpha="0.8"
        android:fillAlpha="0.8">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="145.71"
            android:startY="23.93"
            android:endX="-9.08"
            android:endY="90.55"
            android:type="linear">
          <item android:offset="0" android:color="#006749BB"/>
          <item android:offset="0.52" android:color="#386749BB"/>
          <item android:offset="1" android:color="#876749BB"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
