<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="343dp"
    android:height="140dp"
    android:viewportWidth="343"
    android:viewportHeight="140">
  <group>
    <clip-path
        android:pathData="M0,0h343v140h-343z"/>
    <path
        android:pathData="M0,0h343v140h-343z"
        android:fillColor="#E3563B"/>
    <path
        android:strokeWidth="1"
        android:pathData="M166.71,137.92C129.49,156.57 92.74,167.32 63.43,169.62C48.77,170.77 35.99,169.81 25.96,166.7C15.92,163.58 8.66,158.33 4.95,150.91C1.23,143.49 1.36,134.54 4.87,124.63C8.38,114.72 15.26,103.91 24.95,92.86C44.34,70.75 74.94,47.74 112.16,29.08C149.38,10.43 186.12,-0.32 215.44,-2.62C230.1,-3.77 242.87,-2.81 252.91,0.3C262.95,3.42 270.2,8.67 273.92,16.09C277.64,23.51 277.51,32.46 274,42.37C270.49,52.28 263.61,63.09 253.92,74.14C234.53,96.25 203.93,119.26 166.71,137.92Z"
        android:strokeAlpha="0.15"
        android:fillColor="#00000000">
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="240.61"
            android:startY="-5.47"
            android:endX="51.57"
            android:endY="169.48"
            android:type="linear">
          <item android:offset="0" android:color="#7FFFFFFF"/>
          <item android:offset="1" android:color="#FFFFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:strokeWidth="1"
        android:pathData="M215.67,124.92C178.45,143.57 141.71,154.32 112.39,156.62C97.73,157.77 84.96,156.81 74.92,153.7C64.88,150.58 57.63,145.33 53.91,137.91C50.19,130.49 50.32,121.54 53.83,111.63C57.34,101.72 64.22,90.91 73.91,79.86C93.3,57.75 123.9,34.74 161.12,16.08C198.34,-2.57 235.09,-13.32 264.4,-15.62C279.06,-16.77 291.83,-15.81 301.87,-12.7C311.91,-9.58 319.17,-4.33 322.88,3.09C326.6,10.51 326.47,19.46 322.96,29.37C319.45,39.28 312.57,50.09 302.88,61.14C283.49,83.25 252.89,106.26 215.67,124.92Z"
        android:strokeAlpha="0.15"
        android:fillColor="#00000000">
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="289.57"
            android:startY="-18.47"
            android:endX="100.53"
            android:endY="156.48"
            android:type="linear">
          <item android:offset="0" android:color="#7FFFFFFF"/>
          <item android:offset="1" android:color="#FFFFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:strokeWidth="1"
        android:pathData="M24.49,85.26L24.97,85.38L24.49,85.26L23.35,89.71L19.19,90.92L19.33,91.4L19.19,90.92C18.61,91.09 18.61,91.91 19.19,92.08L23.35,93.29L24.49,97.74C24.64,98.35 25.49,98.35 25.65,97.74L26.78,93.29L30.94,92.08C31.52,91.91 31.52,91.09 30.94,90.92L26.78,89.71L25.65,85.26C25.49,84.65 24.64,84.65 24.49,85.26ZM23.57,93.36C23.57,93.36 23.57,93.36 23.57,93.36L23.57,93.36ZM26.56,93.36L26.56,93.36L26.56,93.36Z"
        android:fillColor="#ffffff"
        android:fillAlpha="0.9"
        android:strokeColor="#E3563B"/>
    <path
        android:strokeWidth="1"
        android:pathData="M53.28,104.26C53.13,103.65 52.27,103.65 52.12,104.26M53.28,104.26L52.12,104.26M53.28,104.26L52.8,104.38L52.12,104.26M53.28,104.26L54.98,110.92L61.21,112.75C61.79,112.91 61.79,113.73 61.21,113.9L54.98,115.72L53.28,122.38C53.13,122.99 52.27,122.99 52.12,122.38L50.42,115.72L44.19,113.9C43.61,113.73 43.61,112.91 44.19,112.75L50.42,110.92L52.12,104.26M53.09,104.5C53.09,104.5 53.09,104.5 53.09,104.5L53.09,104.5L52.81,104.43L53.09,104.5ZM52.31,104.5L52.31,104.5C52.31,104.5 52.31,104.5 52.31,104.5L52.59,104.43L52.31,104.5Z"
        android:fillColor="#ffffff"
        android:strokeColor="#E3563B"/>
    <path
        android:strokeWidth="1"
        android:pathData="M324.58,18.22C324.42,17.63 323.58,17.63 323.42,18.22L322.09,23.09L317.22,24.42C316.63,24.58 316.63,25.42 317.22,25.58L322.09,26.91L323.42,31.78C323.58,32.37 324.42,32.37 324.58,31.78L325.91,26.91L330.78,25.58C331.37,25.42 331.37,24.58 330.78,24.42L325.91,23.09L324.58,18.22Z"
        android:fillColor="#ffffff"
        android:fillAlpha="0.9"
        android:strokeColor="#E3563B"/>
    <path
        android:strokeWidth="1"
        android:pathData="M258.47,109.47L258.86,109.58C258.86,109.58 258.86,109.58 258.86,109.58L258.86,109.58L258.47,109.47ZM258.47,109.47L258.58,109.86C258.58,109.86 258.58,109.86 258.58,109.86L258.58,109.86L258.47,109.47ZM242.49,106.11C242.49,106.11 242.49,106.11 242.49,106.11L242.49,106.11ZM255.11,119.51C255.11,119.51 255.11,119.51 255.11,119.51L255.11,119.51ZM268.52,106.89C268.51,106.89 268.51,106.89 268.51,106.89L268.52,106.89ZM256.08,93.22C255.92,92.63 255.08,92.63 254.92,93.22L252.2,103.2L242.22,105.92C241.63,106.08 241.63,106.92 242.22,107.08L252.2,109.8L254.92,119.78C255.08,120.37 255.92,120.37 256.08,119.78L258.8,109.8L268.78,107.08C269.37,106.92 269.37,106.08 268.78,105.92L258.8,103.2L256.08,93.22Z"
        android:fillColor="#ffffff"
        android:strokeColor="#E3563B"/>
  </group>
</vector>
