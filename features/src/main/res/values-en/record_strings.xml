<?xml version='1.0' encoding='utf-8'?>
<resources>
    <string name="journal_title">Journal</string>
    <string name="journal_tab_layouts">Prompts</string>
    <string name="journal_tab_favourite">Selected</string>
    <string name="journal_tab">Tests</string>
    <string name="record_list_add_button">Add entry</string>
    <string name="record_list_missing_question_text">Space for your question</string>
    <string name="record_list_all_empty_title">Here is the storage for your prompts</string>
    <string name="record_list_all_empty_message">Lay out prompts, add entries, and come back to understand yourself and your wishes better</string>
    <string name="record_list_favourite_empty_title">Most important entries</string>
    <string name="record_list_favourite_empty_message">Press tab on the entry screen, and it will appear here</string>
    <string name="record_list_archived_empty_title">Empty for now</string>
    <string name="record_list_archived_empty_message">You can move irrelevant entries to Archive by swiping left in Journal. Also any entry can be restored</string>
    <string name="record_list_popup_restored_from_archive">Deleted from Archive</string>
    <string name="record_list_popup_moved_to_archive">Added to Archive</string>
    <string name="record_list_popup_restored_from_favourite">Deleted from Selected</string>
    <string name="record_list_popup_moved_to_favourite">Added to Selected</string>
    <string name="record_list_layout_title">A lot of fun stuff here!</string>
    <string name="record_list_layout_message">Lay out prompts, grade mood and energy, come back here to get to know yourself better</string>
    <string name="record_list_course_title">Course «%s»</string>
    <string name="add_record_title">Entry</string>
    <string name="add_record_question_hint">Write about your concerns</string>
    <string name="add_record_question_label">Question</string>
    <string name="add_record_new_comment_hint">Write down your thoughts about the prompt</string>
    <string name="add_record_new_card_hint">Write down your thoughts about the card</string>
    <string name="add_record_comment_label">Comment</string>
    <string name="add_record_comment_hint">You can add a comment</string>
    <string name="add_record_authorize_button_label">Sign up to save entries</string>
    <string name="add_record_authorize_button">Signup</string>
    <string name="add_record_skip_button">Skip</string>
    <string name="add_record_complete_button">Done</string>
    <string name="add_record_auth_dialog_title">Log in</string>
    <string name="add_record_auth_dialog_message">Log in to use this feature</string>
    <string name="add_record_auth_dialog_cancel_button">Cancel</string>
    <string name="add_record_auth_dialog_auth_button">Sign in</string>
    <string name="add_record_skip_dialog_title">Sure about skipping?</string>
    <string name="add_record_skip_dialog_message">Your comments will not be saved without signing up</string>
    <string name="add_record_skip_dialog_cancel_button">Cancel</string>
    <string name="add_record_skip_dialog_skip_button">Skip</string>
    <string name="add_record_feeling_mood_title">Grade mood from 1 to 5</string>
    <string name="add_record_feeling_energy_title">Grade energy from 1 to 5</string>
    <string name="add_record_premium_action_button">Premium</string>
    <string name="common_record_complete_button">Save</string>
    <string name="common_record_comment_label">Comments on the layout (%s)</string>
    <string name="archive_title">Archive</string>
    <string name="archive_record_details_title">Archived entry</string>
    <string name="archive_record_complete_button">Restore</string>
    <string name="calendar_header">Calendar</string>
    <string name="subscription_bottom_sheet_header">Available with subscription</string>
    <string name="subscription_button_trial">Trial subscription (7 days)</string>
    <string name="subscription_button_payment">Pay</string>
    <string name="subscription_button_more">Details</string>
    <string name="subscription_per_month">%s/month</string>
    <string name="create_record_top_bar_title">New entry</string>
    <string name="create_record_add_card">Add an image</string>
    <string name="camera_permission_dialog_title">App requests camera access</string>
    <string name="camera_permission_dialog_description">App requests camera access for working with AR-prompts and adding your own decks</string>
    <string name="camera_permission_dialog_confirm_button">Allow</string>
    <string name="camera_permission_dialog_dismiss_button">Don\'t allow</string>
    <string name="camera_permission_title">Add card image</string>
    <string name="camera_permission_description">You didn\'t allow camera access. You can change access in phone Settings</string>
    <string name="camera_permission_button_title">Open Settings</string>
    <string name="camera_frame_hint">Place card inside frame</string>
    <string name="image_tracking_frame_hint">Place card inside frame to see magic</string>
    <string name="camera_photo_button_title">Take photo</string>
    <string name="image_tracking_button_title">Continue</string>
    <string name="confirm_photo_accept_photo">Saved</string>
    <string name="confirm_photo_retake_photo">Retake photo</string>
    <string name="record_card_list_card_comment_placeholder">Add a comment</string>
    <string name="record_card_list_card_hint_label">LU prompt</string>
    <string name="record_card_list_daily_card_hint_label">Question for the Card of the Day from LU</string>
    <string name="common_record_comment_label_empty">Commentary on the layout</string>
    <string name="journal_tests_empty_title">Here will be the completed tests</string>
    <string name="journal_tests_empty_text">Take tests in the lessons, and they will appear here</string>
    <string name="journal_tests_test_results">Test results</string>
</resources>
