<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Main -->
    <string name="account_main_header">Account</string>
    <string name="account_main_premium_title">Try Premium</string>
    <string name="account_main_premium_text">Unlimited prompts and a lot more</string>
    <string name="account_main_premium_button_text">Details</string>
    <string name="account_main_additional_header">Additional info</string>
    <string name="account_main_additional_subscription">Subscription</string>
    <string name="account_main_additional_buy_decks">Purchase decks</string>
    <string name="account_main_additional_promocodes">Promo codes</string>
    <string name="account_main_additional_tutorial">Training</string>
    <string name="account_main_additional_favourite">Favorite cards</string>
    <string name="account_main_settings_header">Settings</string>
    <string name="account_main_settings_language">Language</string>
    <string name="account_main_settings_notifications">Notifications</string>
    <string name="account_main_about_app_header">About the app</string>
    <string name="account_main_about_app_help">"Help and feedback "</string>
    <string name="account_main_about_app_rate">Rate the app</string>
    <string name="account_main_about_app_text">About the app</string>
    <string name="account_main_button_add_your_deck">Add your deck</string>
    <string name="account_main_name_without_user">Sign up/Sign in</string>
    <string name="account_main_name_unspecified">User info</string>
    <string name="account_main_name_caption">Sync on all devices</string>
    <string name="account_main_permission_notification_title">Allow notifications</string>
    <string name="account_main_permission_notification_message">Go to Settings and allow Notifications.</string>
    <string name="account_main_permission_notification_button_cancel">Cancel</string>
    <string name="account_main_language_russian">Russian</string>
    <string name="account_main_language_english">English</string>
    <string name="account_main_language_spanish">Spanish</string>
    <string name="account_main_language_ukrainian">Ukrainian</string>
    <string name="account_main_language_kazakh">Kazakh</string>
    <string name="account_main_language_french">French</string>
    <string name="account_main_language_arab">Arabic</string>
    <string name="account_main_permission_notification_button_proceed">Open Settings</string>

    <!-- Subscription -->
    <string name="account_subscription_image_title">Subscription</string>
    <string name="account_subscription_image_text_1">Cosmos inside you.</string>
    <string name="account_subscription_image_text_2">Start soul-searching with LU</string>
    <string name="account_subscription_success_header_trial">Trial version activated!</string>
    <string name="account_subscription_success_header_payment">Successful payment!</string>
    <string name="account_subscription_success_header_payment_changed">Plan change successful!</string>
    <string name="account_subscription_cancel_header">Cancel subscriptions</string>
    <string name="account_subscription_success_text_trial">After a week the subscription will be extended automatically</string>
    <string name="account_subscription_success_text_payment">After the paid period expires, the subscription will be extended automatically</string>
    <string name="account_subscription_success_text_payment_changed">You are on a new plan in the next billing cycle</string>
    <string name="account_subscription_cancel_text">Premium subscription is available till %1$s. Then app will switch to the free version.</string>
    <string name="account_subscription_success_button_text">To the Main page</string>
    <string name="account_subscription_cancel_button_text">Confirm</string>
    <string name="account_subscription_options_block_header">Subscription details</string>
    <string name="account_subscription_option_layout_title">More than one prompt a day</string>
    <string name="account_subscription_option_layout_text">Pull a card from a deck or use prompts with questions</string>
    <string name="account_subscription_option_analytics_title">Mood and energy tracker</string>
    <string name="account_subscription_option_analytics_text">Trace your mental health with the help of convenient tracker in Analytics</string>
    <string name="account_subscription_option_photo_title">Adding cards as images</string>
    <string name="account_subscription_option_photo_text">Add any physical card to the app, by taking its picture, and describe your feelings about it in Journal.</string>
    <string name="account_subscription_option_comments_title">Comments</string>
    <string name="account_subscription_option_comments_text">Add comments about completed prompts any time</string>
    <string name="account_subscription_option_favourite_title">Selected</string>
    <string name="account_subscription_option_favourite_text">We know that you will want to add certain entries and cards to Selected. You can do it by tapping the tab</string>
    <string name="account_subscription_option_archive_title">Archive</string>
    <string name="account_subscription_option_archive_text">Move irrelevant entries to Archive</string>
    <string name="account_subscription_plans_header">Plans</string>
    <string name="account_subscription_plan_title_trial">Once the trial period is over, subscription will be extended automatically. You can cancel it anytime.</string>
    <string name="account_subscription_plan_title_payment">You can change or cancel your subscription by going to your account settings on the Google Play after purchase.</string>
    <string name="account_subscription_plan_title_payment_changed">Your subscription expires on %1$s. You can select a different plan in advance, and you will be charged at the end of the paid period.</string>
    <string name="account_subscription_plan_seven_days_off">A free week</string>
    <string name="account_subscription_plan_caption_economy">Savings %1$d%%</string>
    <string name="account_subscription_plan_cancel">Cancel subscription</string>
    <string name="account_subscription_button_text_trial">Try for free</string>
    <string name="account_subscription_button_text_payment">Pay</string>
    <string name="account_subscription_button_text_tariffs">Plans</string>
    <string name="subscription_bottom_sheet_trial_text">Use extra opportunities for more efficient work:</string>
    <string name="subscription_options_layouts">more than one prompt a day</string>
    <string name="subscription_options_analytics">analytics</string>
    <string name="subscription_options_favourite">adding cards and entries to favorite</string>
    <string name="subscription_options_archive">archiving entries</string>

    <!-- Lessons -->
    <string name="account_subscription_success_message">Successful payment!</string>
    <string name="account_lessons_title">Training</string>
    <string name="account_lessons_tutorial_title">App manual</string>
    <string name="account_lessons_tutorial_caption">Quick training and onboarding</string>
    <string name="account_lesson_details_title">Lesson</string>

    <!-- Lessons -->
    <string name="tutorial_welcome">"Hi! I am here for your convenience and will assist with your first MAK experience "</string>

    <!-- About the app -->
    <string name="about_the_app_title">About the app</string>
    <string name="about_the_app_terms_of_use">Terms of use</string>
    <string name="about_the_app_privacy_policy">Privacy policy</string>
    <string name="about_the_app_about_creators_of_application">About the app creators</string>
    <string name="about_the_app_version">Version</string>
    <string name="about_the_app_about_us_leading_text">Hi!</string>
    <string name="about_the_app_about_us_main_text">The LU Metaphorical Cards app serves as a visualization and association tool for your subconscious. It can assist you in finding solutions to complex problems, overcoming crises, and uncovering hidden thoughts, feelings, and beliefs preventing one from achieving goals. LU Metaphorical Cards are also useful for developing creative thinking, improving communications, and strengthening relationships.\n\nThe LU Metaphorical Cards app is a unique medium, where the right questions allow users to better understand themselves and their inner world. You can ask your own questions or use topic-based prompts.\n\nThe LU Metaphorical Cards are specifically created to trigger associations. While looking at an image, users connect it with their own feelings and background which allows them to identify underlying problems and to find ways to solve them. Below are some important recommendations to follow while using the cards: Don’t focus on the visual features of the image. Instead, focus on your inner sensations that might have to do with your feelings, thoughts, memories, and emotions.\n\nDon’t analyze the metaphorical image in detail.\nAllow yourself to feel any insights and associations emerging in connection with the image. Don’t analyze them; simply observe.\n\nAfter completing the prompt, figure out what resonated with you most, and write down your comments and associations in the diary. You can trace changes of mood and emotional state, which will allow you to better understand yourself and your inner processes.\n\nThe LU Metaphorical Cards is a versatile tool. Try them out, and you will see that they can change your life!</string>

    <!-- Shop -->
    <string name="account_shop_title">Purchase decks</string>
    <string name="account_shop_deck_has_ar">AR</string>
    <plurals name="account_shop_cards_in_deck">
        <item quantity="one">%1$d card</item>
        <item quantity="other">%1$d cards</item>
    </plurals>
    <string name="account_shop_deck_is_available">Purchased</string>
    <string name="account_shop_buy_text">Go to the website to purchase a paper deck</string>
    <string name="account_shop_button_to_website">To the website</string>

    <!-- Profile -->
    <string name="account_profile_title">Profile</string>
    <string name="account_profile_name">Name</string>
    <string name="account_profile_gender">Sex</string>
    <string name="account_profile_birth_year">Birth year</string>
    <string name="account_profile_email">Email</string>
    <string name="account_profile_change_password">Change password</string>
    <string name="account_profile_button_logout">Log out</string>
    <string name="account_profile_button_delete_account">Delete</string>
    <string name="account_profile_subscription_until">Active till  %s</string>
    <string name="user_gender_male">Male</string>
    <string name="user_gender_female">Female</string>
    <string name="account_profile_dialog_logout_title">Do you really want to log out?</string>
    <string name="account_profile_dialog_logout_text">All data is saved, but you will need to relogin</string>
    <string name="account_profile_dialog_logout_button_dismiss">Cancel</string>
    <string name="account_profile_dialog_logout_button_proceed">Log out</string>
    <string name="account_profile_dialog_delete_account_title">Do you really want to delete?</string>
    <string name="account_profile_dialog_delete_account_text">If you delete your account, all the data will be erased</string>
    <string name="account_profile_dialog_delete_account_button_dismiss">Cancel</string>
    <string name="account_profile_dialog_delete_account_button_proceed">Delete</string>
    <string name="account_profile_dialog_non_editable_field_title">Can\'t edit this field</string>
    <string name="account_profile_dialog_non_editable_field_text">Outside service was used to login</string>
    <string name="account_profile_edit_title">Edit</string>
    <string name="account_profile_edit_name_title">Name</string>
    <string name="account_profile_edit_name_footer">Character limit (2–30)</string>
    <string name="account_profile_edit_birth_year_title">Birth year</string>
    <string name="account_profile_edit_gender_title">Sex</string>
    <string name="account_profile_edit_password_title">Change password</string>
    <string name="account_profile_edit_password_header">Enter current password</string>
    <string name="account_profile_edit_password_new_header">Enter new password</string>
    <string name="account_profile_edit_password_confirm_header">Re-enter password</string>
    <string name="account_profile_edit_password_error_wrong_password">Wrong password</string>
    <string name="account_profile_edit_password_error_user_collision">User is already registered</string>
    <string name="account_profile_edit_password_error_email_collision">New email address must differ from old one</string>
    <string name="account_profile_edit_password_placeholder">Password</string>
    <string name="account_profile_edit_password_success_message">Changes saved</string>
    <string name="account_profile_delete_confirm_password_toolbar_text">Delete account</string>
    <string name="account_profile_delete_confirm_password_textfield_header">Enter password</string>

    <!-- Profile email -->
    <string name="account_profile_edit_email_verify_password_title">Enter current password</string>
    <string name="account_profile_edit_email_verify_password_header">Enter password to change email address</string>
    <string name="account_profile_edit_email_verify_password_placeholder">password</string>
    <string name="account_profile_edit_email_verify_password_error">Wrong password</string>
    <string name="account_profile_edit_email_new_email_title">New email</string>
    <string name="account_profile_edit_email_verify_new_email_title">"Confirm email "</string>
    <string name="account_profile_edit_email_verify_new_email_text">Email sent to %s to confirm your email address. To complete signup, follow email instructions. Once you click the link, this tab will be minimized</string>

    <!-- Feedback -->
    <string name="feedback_title">Help and feedback</string>
    <string name="feedback_question_subject">Pick a question topic</string>
    <string name="feedback_email">Contact email</string>
    <string name="feedback_email_placeholder"><EMAIL></string>
    <string name="feedback_email_error">Email can only contain Latin characters, numbers and symbols @._-</string>
    <string name="feedback_question">What is your question?</string>
    <string name="feedback_question_placeholder">Message box</string>
    <string name="feedback_button_title">Send</string>
    <string name="feedback_message_success">Message sent</string>

    <!-- Promocodes -->
    <string name="promocode_success">Premium activated</string>
    <string name="promocode_invalid">Invalid promo code</string>
    <string name="promocode_already_has_subscription">Cannot use promo code with active subscription</string>
    <string name="promocode_title">Promo code</string>
    <string name="promocode_input_title">Enter promo code</string>
    <string name="promocode_input_placeholder">Promo code</string>
    <string name="promocode_button">Activate</string>

    <string-array name="feedback_question_topics">
        <item name="feedback_question_topic_registration">Registration</item>
        <item name="feedback_question_topic_payment">Payment</item>
        <item name="feedback_question_topic_subscription">Subscription</item>
        <item name="feedback_question_topic_journal">Journal</item>
        <item name="feedback_question_topic_decks">Decks</item>
        <item name="feedback_question_topic_ar">AR</item>
        <item name="feedback_question_topic_other">Other</item>
    </string-array>
    <string name="account_profile_subscription_not_active">Not Active</string>
    <string name="account_profile_subscription">Subscription</string>
    <string name="account_profile_subscription_user_id">User ID</string>
    <string name="account_profile_subscription_user_id_copied">Copied to clipboard</string>
    <string name="subscription_look_tariffs">Subscription Plans</string>
    <string name="account_profile_edit_name_placeholder">Your name</string>
    <string name="about_the_app_about_creators">About the app creators</string>
    <string name="account_banner_star_deck_header">Game</string>
    <string name="account_banner_star_deck_item">Collect unique deck</string>

</resources>
