<?xml version='1.0' encoding='utf-8'?>
<resources>
    <string name="main_auth_sign_up_button_text">Sign up</string>
    <string name="main_auth_sign_in_button_text">Sign in</string>
    <string name="main_auth_sign_in_by_sso_text">Or sign in with</string>
    <string name="auth_user_type_email">Email</string>
    <string name="auth_user_type_sso">SSO</string>
    <string name="auth_user_type_sso_vk">Log in with "VKontakte"</string>
    <string name="auth_user_type_sso_ok">Log in with "Odnoklassniki"</string>
    <string name="auth_user_type_sso_yandex">Log in with "Yandex"</string>
    <string name="sign_up_toolbar_text">Sign up</string>
    <string name="sign_up_sent_email_title">Confirm email</string>
    <string name="sign_up_sent_email_text">Email sent to %s to confirm your email address. To complete signup, follow email instructions.                                                       Once you click the link, this tab will be minimized</string>
    <string name="sign_in_toolbar_text">Sign in</string>
    <string name="reset_password_toolbar_text">Reset password</string>
    <string name="reset_password_title">Email</string>
    <string name="reset_password_text">We will send you a link to change your password</string>
    <string name="reset_password_sent_email_title">Open your email</string>
    <string name="reset_password_sent_email_text">Message has been sent to %s to change your password</string>
    <string name="reset_password_dismiss_dialog_title">Sure about closing?</string>
    <string name="reset_password_dismiss_dialog_text">Refusal to verify will take you to the login page</string>
    <string name="reset_password_dismiss_dialog_confirm">Close</string>
    <string name="reset_password_dismiss_dialog_cancel">Cancel</string>
    <string name="password_enter_wrong_password_error">Wrong password</string>
    <string name="password_enter_title">Password</string>
    <string name="password_enter_forget_password">Forgot password?</string>
    <string name="password_enter_signin">Sign in</string>
    <string name="password_create_title">Enter password</string>
    <string name="password_enter_error">Password must contain at least 8 characters</string>
    <string name="password_create_error">Password must contain at least 8 characters</string>
    <string name="password_create_hint">Min password length is 8 characters</string>
    <string name="password_create_placeholder">Password</string>
    <string name="password_create_confirm_title">Re-enter password</string>
    <string name="password_create_confirm_error">Passwords don\'t match</string>
    <string name="password_create_signup">Sign up</string>
    <string name="email_enter_error">Email can only contain Latin characters, numbers and symbols @._-</string>
    <string name="email_enter_placeholder"><EMAIL></string>
    <string name="email_enter_signin">Sign in</string>
    <string name="email_enter_signup">Sign up</string>
    <string name="email_enter_next_button">Send</string>
    <string name="email_enter_terms">I agree to the [terms and conditions of service use] and [personal data processing]</string>
    <string name="email_enter_has_account_error">User not found</string>
    <string name="email_enter_no_has_account_error">User is already registered</string>
    <string name="password_confirm_button_confirm">Confirm</string>
</resources>