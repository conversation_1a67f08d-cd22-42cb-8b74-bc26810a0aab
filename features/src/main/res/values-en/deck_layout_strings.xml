<?xml version='1.0' encoding='utf-8'?>
<resources>
    <string name="deck_layout_question_bottom_sheet_title">Question</string>
    <string name="deck_layout_question_bottom_sheet_subtitle">Enter a question for your prompt or leave the space empty</string>
    <string name="deck_layout_question_bottom_sheet_hint">What do you want to ask the cards about?</string>
    <string name="deck_layout_question_bottom_sheet_next_button">Next</string>
    <string name="deck_layout_exit_dialog_cancel_button">Cancel</string>
    <string name="deck_layout_exit_dialog_exit_button">Exit</string>
    <string name="deck_layout_exit_dialog_title">Sure about exiting?</string>
    <string name="deck_layout_exit_dialog_message">This prompt data will be lost</string>
    <string name="deck_layout_flat_tab">Regular</string>
    <string name="deck_layout_ar_tab">AR</string>
    <string name="deck_layout_finish">Finish</string>
    <string name="deck_layout_next_card">Another card</string>
    <string name="deck_layout_last_card">Last card</string>
    <string name="predefined_layout_header">Prompt topics</string>
    <string name="predefined_layout_question_number">Question %1$d of %2$d</string>
    <string name="predefined_layout_next_question">Next question</string>
    <string name="predefined_layout_tab_all">All</string>
    <string name="random_card_close_popup">Close</string>
    <string name="random_card_close_dialog_title">Sure about exiting?</string>
    <string name="random_card_close_dialog_message">This prompt data will be lost</string>
    <string name="random_card_close_dialog_cancel_button">Cancel</string>
    <string name="random_card_close_dialog_skip_button">Exit</string>
    <string name="deck_layout_ar_loading_message">Your cards will soon get uploaded in all their glory!</string>
    <string name="deck_layout_ar_layout_message">Point camera at flat service to get white frame, then tap the screen</string>
    <string name="random_card_create_record">Add an entry</string>
    <string name="random_card_go_to_record">See an entry</string>
    <string name="random_card_deck_title">Card of the day</string>
    <string name="random_card_deck_title_card_counter">%1$d of %2$d</string>
    <string name="deck_layout_camera_permission_denied_header">Activate AR</string>
</resources>
