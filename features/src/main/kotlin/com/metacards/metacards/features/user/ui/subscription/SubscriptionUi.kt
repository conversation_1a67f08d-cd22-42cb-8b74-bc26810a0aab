package com.metacards.metacards.features.user.ui.subscription

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.dialog.default_component.DefaultDialog
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.isScrolledToEnd
import com.metacards.metacards.core.utils.toLocalDate
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.SuccessUi
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.payments.ui.PaymentComponent
import com.metacards.metacards.features.user.ui.subscription.widget.PlansBlock
import com.metacards.metacards.features.user.ui.subscription.widget.SubscriptionHeader
import com.metacards.metacards.features.user.ui.subscription.widget.SubscriptionOptionsBlock
import dev.icerock.moko.resources.StringResource
import dev.icerock.moko.resources.desc.ResourceFormattedStringDesc
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.datetime.Clock

@Composable
fun SubscriptionUi(
    component: SubscriptionComponent,
    modifier: Modifier = Modifier,
) {
    val coroutineScope = rememberCoroutineScope()
    val userSubscriptionState by component.baseSubscriptionComponent.userSubscriptionState.collectAsState()
    val subscriptionScreenState by component.subscriptionScreenState.collectAsState()
    val isTrialAvailable by component.baseSubscriptionComponent.isTrialAvailable.collectAsState()

    val lazyColumnState = rememberLazyListState()
    val isScrolledToEnd by remember { derivedStateOf { lazyColumnState.isScrolledToEnd() } }

    if (subscriptionScreenState == SubscriptionScreenState.START) {
        BoxWithFade(modifier, listOfColors = CustomTheme.colors.gradient.backgroundList) {
            Column {
                TopNavigationBar(
                    title = R.string.account_main_additional_subscription.strResDesc(),
                    leadingIcon = {
                        BackNavigationItem()
                    }
                )

                LazyColumn(
                    state = lazyColumnState,
                    verticalArrangement = Arrangement.spacedBy(24.dp),
                    contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    item { SubscriptionHeader() }

                    item { SubscriptionOptionsBlock(Modifier.padding(bottom = 8.dp)) }

                    item { PlansBlock(component, Modifier.padding(bottom = 100.dp)) }
                }
            }

            SubscriptionButton(
                Modifier
                    .fillMaxWidth()
                    .background(
                        brush = Brush.verticalGradient(
                            listOf(Color.Transparent, Color.Black)
                        )
                    )
                    .navigationBarsPadding()
                    .padding(start = 16.dp, end = 16.dp, bottom = 4.dp)
                    .align(Alignment.BottomCenter),
                isScrolledToEnd,
                getButtonText(isScrolledToEnd, userSubscriptionState, isTrialAvailable),
                component,
                coroutineScope,
                lazyColumnState
            )
        }
    } else {
        SuccessUi(
            header = getHeader(subscriptionScreenState, userSubscriptionState, isTrialAvailable),
            mainText = getText(subscriptionScreenState, userSubscriptionState, isTrialAvailable),
            buttonText = getSuccessButtonText(subscriptionScreenState),
            onCompleteButtonClick = component::onGoToMainClick,
            onBackClick = component::onSuccessBackClick
        )
    }

    PaymentComponent(component = component.paymentComponent, modifier = Modifier.fillMaxSize())

    DefaultDialog(dialogControl = component.dialogControl)
}

@Composable
private fun SubscriptionButton(
    modifier: Modifier,
    isScrolledToEnd: Boolean,
    text: String,
    component: SubscriptionComponent,
    coroutineScope: CoroutineScope,
    lazyColumnState: LazyListState,
) {
    MetaAccentButton(
        text = text,
        onClick = {
            if (isScrolledToEnd) {
                component.onCompleteClick()
            } else {
                coroutineScope.launch {
                    lazyColumnState.animateScrollToItem(
                        lazyColumnState.layoutInfo.totalItemsCount - 1
                    )
                }
            }
        },
        modifier = modifier
    )
}

@Composable
private fun getSuccessButtonText(subscriptionScreenState: SubscriptionScreenState) =
    if (subscriptionScreenState == SubscriptionScreenState.CANCEL) {
        R.string.account_subscription_cancel_button_text.strResDesc()
    } else {
        R.string.account_subscription_success_button_text.strResDesc()
    }

@Composable
private fun getButtonText(
    isScrolledToEnd: Boolean,
    userSubscriptionState: User.SubscriptionState?,
    isTrialAvailable: Boolean,
) = if (isScrolledToEnd) {
    if (userSubscriptionState is User.SubscriptionState.None && isTrialAvailable) {
        R.string.account_subscription_button_text_trial.strResDesc().localizedByLocal()
    } else {
        R.string.account_subscription_button_text_payment.strResDesc().localizedByLocal()
    }
} else {
    R.string.account_subscription_button_text_tariffs.strResDesc().localizedByLocal()
}

@Composable
private fun getHeader(
    subscriptionScreenState: SubscriptionScreenState,
    userSubscriptionState: User.SubscriptionState?,
    isTrialAvailable: Boolean,
) = if (subscriptionScreenState == SubscriptionScreenState.CANCEL) {
    R.string.account_subscription_cancel_header.strResDesc()
} else {
    when (userSubscriptionState) {
        is User.SubscriptionState.None, null -> {
            if (isTrialAvailable) {
                R.string.account_subscription_success_header_trial.strResDesc()
            } else {
                R.string.account_subscription_success_header_payment.strResDesc()
            }
        }

        is User.SubscriptionState.Ongoing -> {
            R.string.account_subscription_success_header_payment_changed.strResDesc()
        }
    }
}

@Composable
private fun getText(
    subscriptionScreenState: SubscriptionScreenState,
    userSubscriptionState: User.SubscriptionState?,
    isTrialAvailable: Boolean,
) = if (subscriptionScreenState == SubscriptionScreenState.CANCEL) {
    val resource = StringResource(R.string.account_subscription_cancel_text)
    val instant = (userSubscriptionState as? User.SubscriptionState.Ongoing)?.expirationDate
        ?: Clock.System.now()
    val date = instant.toLocalDate()
    val args = listOf("${date.dayOfMonth}.${date.monthNumber}.${date.year}")
    ResourceFormattedStringDesc(resource, args)
} else {
    when (userSubscriptionState) {
        is User.SubscriptionState.None, null -> {
            if (isTrialAvailable) {
                R.string.account_subscription_success_text_trial.strResDesc()
            } else {
                R.string.account_subscription_success_text_payment.strResDesc()
            }
        }

        is User.SubscriptionState.Ongoing -> {
            R.string.account_subscription_success_text_payment_changed.strResDesc()
        }
    }
}

@Preview
@Composable
fun SubscriptionUiPreview() {
    AppTheme {
        SubscriptionUi(component = FakeSubscriptionComponent())
    }
}
