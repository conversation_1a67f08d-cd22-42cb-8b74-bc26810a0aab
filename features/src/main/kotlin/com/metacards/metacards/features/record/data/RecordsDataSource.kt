package com.metacards.metacards.features.record.data

import android.net.Uri
import com.google.firebase.Timestamp
import com.google.firebase.firestore.Query
import com.google.firebase.firestore.toObject
import com.metacards.metacards.core.functions.CloudFunctionsService
import com.metacards.metacards.core.functions.FunctionResult
import com.metacards.metacards.core.network.firestore.FirestoreService
import com.metacards.metacards.core.network.firestore.FirestoreStorage
import com.metacards.metacards.core.network.firestore.getFlowWithLoadable
import com.metacards.metacards.core.user.domain.UserId
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.mapLoadable
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.record.data.dto.NewRecordDto
import com.metacards.metacards.features.record.data.dto.RecordDto
import com.metacards.metacards.features.record.domain.RecordFilter
import com.metacards.metacards.features.record.domain.RecordId
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.tasks.await
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import java.util.UUID

class RecordsDataSource(
    private val firestoreService: FirestoreService,
    private val firestoreStorage: FirestoreStorage,
    private val functionsService: CloudFunctionsService
) {
    fun getRecordById(userId: UserId, recordId: RecordId): Flow<LoadableState<RecordDto>> {
        val flow = firestoreService.db.collection(getRecordsCollectionPath(userId))
            .document(recordId.value)
            .getFlowWithLoadable(firestoreService)

        return flow.mapLoadable { it?.toObject<RecordDto>()?.copy(id = it.id) }
    }

    suspend fun getNewestRecords(
        userId: UserId,
        filter: RecordFilter,
        limit: Int
    ): List<RecordDto> {
        return firestoreService.db.collection(getRecordsCollectionPath(userId))
            .applyFilter(filter)
            .orderBy(CREATION_TIME_FIELD, Query.Direction.DESCENDING)
            .limit(limit.toLong())
            .load()
    }

    suspend fun getRecordsOlderThan(
        userId: UserId,
        timestamp: Timestamp,
        filter: RecordFilter,
        limit: Int,
        inclusive: Boolean
    ): List<RecordDto> {
        return firestoreService.db.collection(getRecordsCollectionPath(userId))
            .applyFilter(filter)
            .orderBy(CREATION_TIME_FIELD, Query.Direction.DESCENDING)
            .let {
                if (inclusive) {
                    it.startAt(timestamp)
                } else {
                    it.startAfter(timestamp)
                }
            }
            .limit(limit.toLong())
            .load()
    }

    suspend fun getRecordsNewerThan(
        userId: UserId,
        timestamp: Timestamp,
        filter: RecordFilter,
        limit: Int
    ): List<RecordDto> {
        return firestoreService.db.collection(getRecordsCollectionPath(userId))
            .applyFilter(filter)
            .orderBy(CREATION_TIME_FIELD, Query.Direction.DESCENDING)
            .endBefore(timestamp)
            .limitToLast(limit.toLong())
            .load()
    }

    suspend fun getRecordsBetweenTimes(
        userId: UserId,
        startTime: Timestamp,
        endTime: Timestamp,
        filter: RecordFilter
    ): List<RecordDto> {
        return firestoreService.db.collection(getRecordsCollectionPath(userId))
            .applyFilter(filter)
            .orderBy(CREATION_TIME_FIELD, Query.Direction.ASCENDING)
            .startAfter(startTime)
            .endBefore(endTime)
            .load()
    }

    fun getRecordsByCardId(
        userId: UserId,
        cardId: CardId
    ): Flow<LoadableState<List<RecordDto>>> {
        return firestoreService.db.collection(getRecordsCollectionPath(userId))
            .filterByCardId(cardId)
            .orderBy(CREATION_TIME_FIELD, Query.Direction.DESCENDING)
            .loadAsFlow()
    }

    suspend fun createRecord(newRecordDto: NewRecordDto): FunctionResult<String> {
        val json = Json { encodeDefaults = true }
        val encoded = json.encodeToString(newRecordDto)
        return functionsService.call(CREATE_RECORD_FUNCTION_NAME, encoded)
    }

    suspend fun updateRecord(
        userId: UserId,
        record: RecordDto
    ) {
        firestoreService.db.collection(getRecordsCollectionPath(userId))
            .document(record.id)
            .set(record)
            .await()
    }

    suspend fun getCardPhotoUri(
        userId: UserId,
        byte: ByteArray,
    ): Uri? {
        var uri: Uri? = null
        val fileName = UUID.randomUUID().toString().plus(CARD_PHOTO_EXTENSION)
        val fileRef = firestoreStorage.storage.reference
            .child(USERS_CARDS_FIELDs)
            .child(userId.value)
            .child(fileName)

        fileRef
            .putBytes(byte)
            .await()

        fileRef
            .downloadUrl
            .addOnSuccessListener { uri = it }
            .await()

        return uri
    }

    suspend fun getDailyCardRecordId(userId: UserId): String? {
        val user = firestoreService.db.collection(USERS_COLLECTION_PATH)
            .document(userId.value)
            .get()
            .await()

        val userTimezone = user.get(USER_TIMEZONE_FIELD)

        return firestoreService.db.collection(getRecordsCollectionPath(userId))
            .filterByWhetherIsDaily(userTimezone as Long)
            .load()
            .firstOrNull()?.id
    }

    private fun Query.applyFilter(filter: RecordFilter): Query {
        return if (filter.onlyFavourite) {
            whereEqualTo(IS_FAVOURITE_FIELD, true)
        } else {
            this
        }.whereEqualTo(IS_ARCHIVED_FIELD, filter.archived)
    }

    private fun Query.filterByCardId(cardId: CardId): Query {
        return whereArrayContains(TOTAL_CARDS_FIELD, cardId.value)
    }

    private fun Query.filterByWhetherIsDaily(userTimezone: Long): Query {
        return whereEqualTo(DAILY_CARD_RECORD_FIELD, true)
            .whereGreaterThan(CREATION_TIME_FIELD, getUserMidnightTimestamp(userTimezone))
    }

    private suspend fun Query.load(): List<RecordDto> {
        return get().await().documents.mapNotNull { document ->
            document.toObject<RecordDto>().also { it?.id = document.id }
        }
    }

    private fun Query.loadAsFlow(): Flow<LoadableState<List<RecordDto>>> {
        val flow = getFlowWithLoadable(firestoreService)
        return flow
            .mapLoadable { documents ->
                documents?.mapNotNull { document ->
                    document.toObject<RecordDto>().also { it.id = document.id }
                }
            }
    }

    private fun getUserMidnightTimestamp(userTimezone: Long): Timestamp {
        // Преобразование смещения временной зоны пользователя в ZoneId
        val userZoneId = ZoneId.ofOffset("GMT", ZoneOffset.ofTotalSeconds((userTimezone * 60).toInt()))

        // Получение текущей даты в часовом поясе пользователя
        val userNow = ZonedDateTime.now(userZoneId)

        // Обнуление времени до полуночи
        val userMidnight = userNow.truncatedTo(ChronoUnit.DAYS)

        // Преобразование ZonedDateTime в Instant
        val instant = userMidnight.toInstant()

        // Создание объекта Timestamp из Instant
        return Timestamp(instant.epochSecond, 0)
    }

    companion object {
        private const val USERS_COLLECTION_PATH = "users"
        private const val RECORDS_COLLECTION_PATH = "records"

        private const val IS_FAVOURITE_FIELD = "isFavourite"
        private const val IS_ARCHIVED_FIELD = "isArchived"
        private const val CREATION_TIME_FIELD = "creationDate"
        private const val COMMENTS_FIELD = "comments"
        private const val QUESTIONS_FIELD = "question"
        private const val TOTAL_CARDS_FIELD = "totalCards"
        private const val TOTAL_CARDS_FIELDs = "id"
        private const val USERS_CARDS_FIELDs = "usersCards"
        private const val DAILY_CARD_RECORD_FIELD = "isDailyCardLayout"
        private const val USER_TIMEZONE_FIELD = "timezone"

        private const val CREATE_RECORD_FUNCTION_NAME = "createRecord"

        private const val CARD_PHOTO_EXTENSION = ".jpg"

        fun getRecordsCollectionPath(userId: UserId): String {
            return USERS_COLLECTION_PATH + "/" + userId.value + "/" + RECORDS_COLLECTION_PATH
        }
    }
}