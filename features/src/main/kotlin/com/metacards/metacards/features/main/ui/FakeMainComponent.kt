package com.metacards.metacards.features.main.ui

import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.core.preferences.models.TutorialState
import com.metacards.metacards.core.utils.createFakeChildStackStateFlow
import com.metacards.metacards.core.widget.navigation_bar.NavigationItem
import com.metacards.metacards.core.widget.navigation_bar.NavigationPage
import com.metacards.metacards.features.home.ui.FakeHomeComponent
import com.metacards.metacards.features.tutorial.data.TutorialStep
import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import com.metacards.metacards.features.user_analytics.ui.UserAnalyticsComponent
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.datetime.Instant

class FakeMainComponent : MainComponent {

    override val childStack: StateFlow<ChildStack<*, MainComponent.Child>> =
        createFakeChildStackStateFlow(MainComponent.Child.Home(FakeHomeComponent()))

    override val navigationItems: MutableStateFlow<List<NavigationItem>> =
        MutableStateFlow(
            NavigationItem.ALL.map { item ->
                if (item.page == NavigationPage.Home) item.copy(isSelected = true) else item
            }
        )
    override val dailyCardState = MutableStateFlow(null)
    override val isShakerPopupVisible = MutableStateFlow(false)

    override val tutorialState: StateFlow<TutorialState> = MutableStateFlow(TutorialState.NONE)
    override val tutorialMessage: StateFlow<TutorialMessage?> = MutableStateFlow(null)
    override val tutorialStep: StateFlow<TutorialStep> = MutableStateFlow(TutorialStep.DECKS)

    override fun onNavigationPageChange(navigationPage: NavigationPage, analyticsInitialTab: UserAnalyticsComponent.Tab) = Unit
    override fun onShakeDetected() = Unit
    override fun showJournalRecordsForDate(time: Instant) = Unit
    override fun onShakerPopupDismiss() = Unit
}
