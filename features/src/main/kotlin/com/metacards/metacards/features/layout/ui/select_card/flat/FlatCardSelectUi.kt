package com.metacards.metacards.features.layout.ui.select_card.flat

import android.annotation.SuppressLint
import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.ui.MetaCardDefaults
import com.metacards.metacards.features.layout.domain.CardWithCover
import com.metacards.metacards.features.layout.ui.select_card.CardSelectComponent
import com.metacards.metacards.features.layout.ui.select_card.FakeCardSelectComponent
import com.metacards.metacards.features.layout.ui.select_card.LocalInteractionEnabled
import com.metacards.metacards.features.layout.ui.select_card.widget.calculateDelay
import com.metacards.metacards.features.layout.ui.select_card.widget.calculateOffset
import com.metacards.metacards.features.layout.ui.select_card.widget.offsetAnimation

@Composable
fun FlatSelectUi(
    cards: List<CardWithCover>,
    component: CardSelectComponent,
    modifier: Modifier = Modifier
) {
    val shouldAnimateLayout = LocalInteractionEnabled.current

    val state = rememberLazyGridState()
    var isFirstComposition = remember { true }
    val firstVisibleItemIndex by remember { derivedStateOf { state.firstVisibleItemIndex } }
    val layoutInfo by remember { derivedStateOf { state.layoutInfo } }
    val questionText by component.questionText.collectAsState()
    val questionTextLocalized = questionText.localizedByLocal().ifEmpty { null }

    BoxWithConstraints(modifier = modifier) {
        val animationDuration = 200
        val columns = 3
        val cardAspectRatio = MetaCardDefaults.aspectRatio
        val cardPadding = 16.dp
        val cardWidth = (maxWidth - cardPadding * columns) / columns
        val cardHeight = cardWidth * cardAspectRatio

        LazyVerticalGrid(
            modifier = Modifier.padding(horizontal = cardPadding),
            columns = GridCells.Fixed(columns),
            horizontalArrangement = Arrangement.spacedBy(cardPadding),
            state = state,
            userScrollEnabled = LocalInteractionEnabled.current,
            contentPadding = WindowInsets.navigationBars.asPaddingValues()
        ) {
            itemsIndexed(cards) { index, card ->
                val count = layoutInfo.visibleItemsInfo.count()
                val delay = calculateDelay(index, columns)
                val fromOffset = calculateOffset(index, columns, cardWidth, cardHeight)
                val animation = tween<Float>(
                    durationMillis = animationDuration,
                    delayMillis = delay,
                    easing = LinearOutSlowInEasing
                )
                val (offsetAnimated, isFinish) = offsetAnimation(
                    fromOffset = fromOffset,
                    animation = animation,
                    noAnimation = !shouldAnimateLayout || index > count || !isFirstComposition
                )

                if (count != 0) {
                    if (index == count - 1 && isFinish) {
                        component.onCardLayoutAnimationEnd()
                    }
                    CardWidget(
                        modifier = Modifier
                            .padding(vertical = 8.dp)
                            .aspectRatio(cardAspectRatio)
                            .graphicsLayer(translationX = -offsetAnimated),
                        card = card,
                        onClick = { component.onCardSelect(it, questionTextLocalized) },
                    )
                }

                if (isFirstComposition && firstVisibleItemIndex >= columns) {
                    isFirstComposition = false
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun CardWidget(
    modifier: Modifier,
    card: CardWithCover,
    onClick: (Card) -> Unit,
) {
    val isInteractionEnabled = LocalInteractionEnabled.current
    Surface(
        modifier = modifier,
        color = CustomTheme.colors.background.segmentControl,
        shape = RoundedCornerShape(16.dp),
        onClick = { if (isInteractionEnabled) onClick(card.card) }
    ) {
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current)
                .data(card.coverUrl)
                .crossfade(MetaCardDefaults.CROSSFADE_DURATION_MILLIS)
                .build(),
            contentDescription = null,
            contentScale = ContentScale.Crop
        )
    }
}

@SuppressLint("StateFlowValueCalledInComposition")
@Preview
@Composable
private fun FlatCardSelectUiPreview() {
    AppTheme {
        val component = FakeCardSelectComponent()
        FlatSelectUi(
            cards = emptyList(),
            component = component
        )
    }
}