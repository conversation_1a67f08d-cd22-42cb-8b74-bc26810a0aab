package com.metacards.metacards.features.record.ui.create_record.main

import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.features.record.domain.camera.CardPhoto
import com.metacards.metacards.features.record.ui.model.FeelingItem
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakeCreateRecordComponent : CreateRecordComponent {
    override val inputSource = MutableSharedFlow<CardPhoto?>(
        extraBufferCapacity = 1,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )
    override val userSubscriptionState = MutableStateFlow(User.SubscriptionState.None(false, false))
    override val isFavourite: StateFlow<Boolean> = MutableStateFlow(true)
    override val cardPhoto = MutableStateFlow(null)
    override val question = MutableStateFlow("")
    override val comment = MutableStateFlow("")
    override val energyLevel = MutableStateFlow(null)
    override val moodLevel = MutableStateFlow(null)
    override val loadingSaveRecord = MutableStateFlow(false)

    override fun onFavoriteClick() = Unit
    override fun onAddCardClick() = Unit
    override fun onQuestionChange(value: String) = Unit
    override fun onCommentChange(value: String) = Unit
    override fun onMoodLevelChange(value: FeelingItem<Int>?) = Unit
    override fun onEnergyLevelChange(value: FeelingItem<Int>?) = Unit
    override fun onSaveClick(value: ByteArray?) = Unit
    override fun onBlockAreaClick() = Unit
}
