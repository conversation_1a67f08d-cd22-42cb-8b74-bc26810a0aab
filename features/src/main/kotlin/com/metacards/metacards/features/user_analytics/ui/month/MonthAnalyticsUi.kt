package com.metacards.metacards.features.user_analytics.ui.month

import androidx.compose.animation.core.AnimationState
import androidx.compose.animation.core.animateTo
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.OnEndReached
import com.metacards.metacards.core.utils.PagedData
import com.metacards.metacards.core.widget.ErrorPlaceholder
import com.metacards.metacards.core.widget.LceWidget
import com.metacards.metacards.features.R
import com.metacards.metacards.features.record.domain.Record
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.record.ui.record_list.widgets.RecordCard
import com.metacards.metacards.features.user_analytics.domain.month.MonthAnalyticsInfo
import com.metacards.metacards.features.user_analytics.ui.common_widgets.EmptyRecordsPlaceholder
import com.metacards.metacards.features.user_analytics.ui.month.widgets.MonthAnalyticsGraphs
import com.metacards.metacards.features.user_analytics.ui.month.widgets.MonthAnalyticsLevels
import com.metacards.metacards.features.user_analytics.ui.month.widgets.MonthAnalyticsSkeleton
import dev.chrisbanes.snapper.ExperimentalSnapperApi
import dev.chrisbanes.snapper.rememberSnapperFlingBehavior
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun MonthAnalyticsUi(
    component: MonthAnalyticsComponent,
    modifier: Modifier = Modifier
) {
    val analyticsInfoState by component.analyticsState.collectAsState()
    val hasData = analyticsInfoState.data != null
    val isPremiumUser by component.isPremiumUser.collectAsState()
    val animationState = remember {
        AnimationState(initialValue = if (hasData) 1.0f else 0.0f)
    }

    LaunchedEffect(hasData) {
        if (hasData) {
            animationState.animateTo(
                targetValue = 1.0f,
                animationSpec = tween(durationMillis = 900)
            )
        }
    }

    LceWidget(
        analyticsInfoState,
        onRetryClick = { /*TODO: error handling*/ },
        modifier = modifier,
        loadingProgress = {
            MonthAnalyticsSkeleton()
        },
        errorPlaceholder = { message, onRetry ->
            ErrorPlaceholder(message, onRetry, modifier.padding(bottom = 130.dp))
        }
    ) { pagedData, _ ->
        MonthAnalyticsContent(
            pagedData,
            loadingFirstPage = analyticsInfoState.loading,
            hasError = analyticsInfoState.error != null,
            animationRatio = animationState.value,
            onLoadMore = component::onLoadMore,
            onMonthPrev = component::onMonthPrev,
            onMonthNext = component::onMonthNext,
            onRecordClick = component::onRecordClick,
            isPremiumUser = isPremiumUser
        )
    }
}

@OptIn(ExperimentalSnapperApi::class)
@Composable
private fun MonthAnalyticsContent(
    pagedData: PagedData<MonthAnalyticsInfo>,
    loadingFirstPage: Boolean,
    hasError: Boolean,
    animationRatio: Float,
    onLoadMore: () -> Unit,
    onMonthPrev: () -> Unit,
    onMonthNext: () -> Unit,
    onRecordClick: (RecordId) -> Unit,
    isPremiumUser: Boolean,
    modifier: Modifier = Modifier
) {
    val lazyListState = rememberLazyListState()
    var previousIndex by remember(lazyListState) { mutableIntStateOf(lazyListState.firstVisibleItemIndex) }
    val currentIndex by remember { derivedStateOf { lazyListState.firstVisibleItemIndex } }

    DisposableEffect(currentIndex) {
        when {
            currentIndex > previousIndex -> onMonthNext()
            currentIndex < previousIndex -> onMonthPrev()
        }
        previousIndex = lazyListState.firstVisibleItemIndex
        onDispose { }
    }

    if (pagedData.hasNextPage && !loadingFirstPage && !pagedData.loadingNextPage) {
        lazyListState.OnEndReached(
            callback = onLoadMore,
            itemCountGap = 3,
            scrollingToEndRequired = hasError
        )
    }

    val flingBehavior = rememberSnapperFlingBehavior(
        lazyListState,
        snapIndex = { _, startIndex, targetIndex ->
            targetIndex.coerceIn(startIndex - 1, startIndex + 1)
        }
    )

    val blurModifier = if (!isPremiumUser) Modifier.blur(4.dp) else Modifier

    BoxWithConstraints(
        modifier = modifier
            .fillMaxSize()
            .then(blurModifier)
    ) {
        LazyRow(
            modifier = Modifier.fillMaxSize(),
            state = lazyListState,
            flingBehavior = flingBehavior,
            reverseLayout = true
        ) {
            items(items = pagedData.list, key = { it.id }) { analyticsInfo ->
                MonthAnalyticsPage(
                    modifier = modifier.width(maxWidth),
                    analyticsInfo = analyticsInfo,
                    animationRatio = animationRatio,
                    onRecordClick = onRecordClick
                )
            }

            if (pagedData.loadingNextPage) {
                item {
                    MonthAnalyticsSkeleton(
                        modifier = Modifier.width(maxWidth)
                    )
                }
            }
        }
    }
}

@Composable
private fun MonthAnalyticsPage(
    analyticsInfo: MonthAnalyticsInfo,
    animationRatio: Float,
    onRecordClick: (RecordId) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier,
        contentPadding = WindowInsets.navigationBars.asPaddingValues()
    ) {
        item {
            MonthAnalyticsGraphs(
                modifier = Modifier.padding(top = 4.dp),
                analyticsInfo = analyticsInfo,
                animationRatio = animationRatio
            )
        }

        if (analyticsInfo.moodMaxPercent != null || analyticsInfo.energyMaxPercent != null) {
            item {
                MonthAnalyticsLevels(
                    modifier = Modifier.padding(top = 32.dp),
                    analyticsInfo = analyticsInfo
                )
            }
        }

        item {
            Spacer(modifier = Modifier.height(24.dp))
        }

        val records = analyticsInfo.recordsWithDescendingDates
        if (records.isNotEmpty()) {
            itemsIndexed(
                records,
                key = { _, record -> record.id }
            ) { index, record ->
                RecordCard(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                        .padding(top = 8.dp),
                    record = record,
                    showDate = needShowDate(
                        record,
                        previousRecord = records.getOrNull(index - 1)
                    ),
                    revealEnabled = false,
                    onClick = { onRecordClick(record.id) }
                )
            }
        } else {
            item {
                EmptyRecordsPlaceholder(
                    modifier = Modifier.padding(top = 8.dp),
                    title = R.string.analytics_empty_month_records_title.strResDesc()
                        .localizedByLocal(),
                    message = R.string.analytics_empty_records_message_month.strResDesc().localizedByLocal()
                )
            }
        }
    }
}

private fun needShowDate(
    record: Record,
    previousRecord: Record?
): Boolean {
    return previousRecord == null || record.creationDate != previousRecord.creationDate
}

@Composable
@Preview(showSystemUi = true)
fun MonthAnalyticsUiPreview() {
    AppTheme {
        MonthAnalyticsUi(FakeMonthAnalyticsComponent())
    }
}