package com.metacards.metacards.features.course.domain.entity

import android.os.Parcelable
import com.metacards.metacards.core.localization.ui.LocalizableString
import kotlinx.parcelize.Parcelize

@Parcelize
data class CourseShortData(
    val courseId: CourseId,
    val name: LocalizableString,
    val shortDescription: LocalizableString? = null,
    val availability: CourseAvailability,
    val coverUrl: String,
    val status: CourseStatus,
    val themeId: CourseThemeId,
    val isCompleted: Boolean
) : Parcelable {

    companion object {
        val mock = CourseShortData(
            courseId = CourseId(""),
            name = LocalizableString.createNonLocalizable(""),
            shortDescription = null,
            coverUrl = "",
            status = CourseStatus.ACTIVE,
            availability = CourseAvailability.FREE,
            themeId = CourseThemeId(""),
            isCompleted = false
        )
    }
}