package com.metacards.metacards.features.account.ui.about_the_app.about_us

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService

class RealAboutUsComponent(
    componentContext: ComponentContext,
    private val analyticsService: AnalyticsService
) : ComponentContext by componentContext, AboutUsComponent {

    companion object {
        private const val NETWORK_INSTAGRAM = "Instargram"
        private const val NETWORK_FACEBOOK = "Facebook"
        private const val NETWORK_INTERNET = "Internet"
    }

    override fun onInstagramClick() {
        analyticsService.logEvent(AnalyticsEvent.AboutAppCreatorsSocialTapEvent(NETWORK_INSTAGRAM))
        // TODO
    }

    override fun onFacebookClick() {
        analyticsService.logEvent(AnalyticsEvent.AboutAppCreatorsSocialTapEvent(NETWORK_FACEBOOK))
        // TODO
    }

    override fun onInternetClick() {
        analyticsService.logEvent(AnalyticsEvent.AboutAppCreatorsSocialTapEvent(NETWORK_INTERNET))
        // TODO
    }
}