package com.metacards.metacards.features.record.ui.journal

import android.os.Parcelable
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.router.stack.childStack
import com.arkivanov.essenty.lifecycle.doOnCreate
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.paged_loading.PagedLoading
import com.metacards.metacards.core.utils.bringToFrontByValue
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.toStateFlow
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.record.createJournalTestsListComponent
import com.metacards.metacards.features.record.createRecordListComponent
import com.metacards.metacards.features.record.domain.Record
import com.metacards.metacards.features.record.domain.RecordListType
import com.metacards.metacards.features.record.domain.RecordSource
import com.metacards.metacards.features.record.domain.RecordsPagedLoading
import com.metacards.metacards.features.record.ui.journal.test_list.JournalTestListComponent
import com.metacards.metacards.features.record.ui.record_list.RecordListComponent
import kotlinx.coroutines.flow.StateFlow
import kotlinx.datetime.Instant
import kotlinx.parcelize.Parcelize

class RealJournalComponent(
    componentContext: ComponentContext,
    private val componentFactory: ComponentFactory,
    private val allRecordsPagedLoading: RecordsPagedLoading,
    private val favouriteRecordsPagedLoading: RecordsPagedLoading,
    private val analyticsService: AnalyticsService,
    private val onOutput: (JournalComponent.Output) -> Unit
) : ComponentContext by componentContext, JournalComponent {

    override val tabsState: StateFlow<TabsState> = computed(allRecordsPagedLoading.stateFlow) {
        getTabState(it)
    }

    private val navigation = StackNavigation<ChildConfig>()

    override val childStack = childStack(
        source = navigation,
        initialConfiguration = ChildConfig.RecordList(JournalComponent.Tab.Layouts),
        handleBackButton = false,
        childFactory = ::createChild
    ).toStateFlow(lifecycle)

    init {
        lifecycle.doOnCreate {
            favouriteRecordsPagedLoading.loadFirstPage() // preload Favourite
            analyticsService.logEvent(AnalyticsEvent.JournalAllEvent)
        }
    }

    override fun onCalendarClick() {
        analyticsService.logEvent(AnalyticsEvent.JournalCalendarEvent)
        onOutput(JournalComponent.Output.CalendarRequested)
    }

    override fun onArchiveClick() {
        analyticsService.logEvent(AnalyticsEvent.JournalArchiveEvent)
        onOutput(JournalComponent.Output.ArchiveRequested)
    }

    override fun onTabSelected(tab: JournalComponent.Tab) {
        when (tab) {
            JournalComponent.Tab.Layouts -> analyticsService.logEvent(AnalyticsEvent.JournalAllEvent)
            JournalComponent.Tab.Favourite -> analyticsService.logEvent(AnalyticsEvent.JournalFavsEvent)
            JournalComponent.Tab.Tests -> analyticsService.logEvent(AnalyticsEvent.JournalTestsEvent)
        }
        val config = when (tab) {
            JournalComponent.Tab.Layouts -> ChildConfig.RecordList(tab)
            JournalComponent.Tab.Tests -> ChildConfig.TestsList
            JournalComponent.Tab.Favourite -> ChildConfig.RecordList(tab)
        }
        navigation.bringToFrontByValue(config)
    }

    override fun onScrollToTopClick() {
        getCurrentListComponent()?.onScrollToTopClick()
    }

    override fun showJournalRecordsForDate(date: Instant) {
        (childStack.value.active.instance as? JournalComponent.Child.RecordList)
            ?.component
            ?.onStartTimeChosen(date)
    }

    private fun createChild(
        config: ChildConfig,
        componentContext: ComponentContext
    ): JournalComponent.Child = when (config) {
        is ChildConfig.RecordList -> {
            JournalComponent.Child.RecordList(
                componentFactory.createRecordListComponent(
                    componentContext,
                    config.tab.toRecordListType(),
                    ::onRecordListOutput
                ),
                config.tab
            )
        }

        ChildConfig.TestsList -> JournalComponent.Child.TestsList(
            componentFactory.createJournalTestsListComponent(componentContext, ::onTestsListOutput)
        )
    }

    private fun onRecordListOutput(output: RecordListComponent.Output) {
        when (output) {
            is RecordListComponent.Output.RecordDetailsRequested -> {
                onOutput(
                    JournalComponent.Output.RecordDetailsRequested(
                        RecordSource.Viewing(output.recordId),
                        output.recordType,
                        output.isDailyCardLayout,
                        output.courseId,
                        output.courseThemeId
                    )
                )
            }

            is RecordListComponent.Output.CreateRecordFlowRequested -> {
                onOutput(JournalComponent.Output.CreateRecordFlowRequested)
            }

            RecordListComponent.Output.PremiumSuggestingRequested ->
                onOutput(JournalComponent.Output.SubscriptionSuggestingRequested)
        }
    }

    private fun onTestsListOutput(output: JournalTestListComponent.Output) {
        when (output) {
            is JournalTestListComponent.Output.TestDetailsRequested -> onOutput(
                JournalComponent.Output.TestDetailsRequested(output.coursePassedTest)
            )
        }
    }

    private fun JournalComponent.Tab.toRecordListType() = when (this) {
        JournalComponent.Tab.Layouts -> RecordListType.All
        JournalComponent.Tab.Favourite -> RecordListType.Favourite
        JournalComponent.Tab.Tests -> RecordListType.All
    }

    private fun getTabState(loadingState: PagedLoading.State<Record>): TabsState {
        return when {
            loadingState.data != null -> TabsState.Normal
            loadingState.loadingStatus == PagedLoading.LoadingStatus.LoadingFirstPage -> TabsState.Skeleton
            else -> TabsState.Hidden
        }
    }

    private fun getCurrentListComponent(): RecordListComponent? {
        return (childStack.value.active.instance as? JournalComponent.Child.RecordList)?.component
    }

    sealed interface ChildConfig : Parcelable {

        @Parcelize
        data class RecordList(val tab: JournalComponent.Tab) : ChildConfig

        @Parcelize
        data object TestsList : ChildConfig
    }
}