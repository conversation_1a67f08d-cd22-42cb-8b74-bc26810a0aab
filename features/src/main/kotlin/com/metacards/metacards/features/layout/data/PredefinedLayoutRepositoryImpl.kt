package com.metacards.metacards.features.layout.data

import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.mapLoadable
import com.metacards.metacards.features.layout.data.dto.PredefinedLayoutDto
import com.metacards.metacards.features.layout.data.dto.PredefinedLayoutGroupDto
import com.metacards.metacards.features.layout.data.dto.toDomain
import com.metacards.metacards.features.layout.domain.PredefinedLayout
import com.metacards.metacards.features.layout.domain.PredefinedLayoutGroup
import com.metacards.metacards.features.layout.domain.PredefinedLayoutRepository
import kotlinx.coroutines.flow.Flow

class PredefinedLayoutRepositoryImpl(
    private val predefinedLayoutDataSource: PredefinedLayoutDataSource,
) : PredefinedLayoutRepository {

    override suspend fun getPredefinedLayouts(): List<PredefinedLayout> =
        predefinedLayoutDataSource.getPredefinedLayouts().map(PredefinedLayoutDto::toDomain)

    override fun getPredefinedLayoutGroups(): Flow<LoadableState<List<PredefinedLayoutGroup>>> =
        predefinedLayoutDataSource.getPredefinedLayoutGroups().mapLoadable { list ->
            list?.map(PredefinedLayoutGroupDto::toDomain) ?: emptyList()
        }
}
