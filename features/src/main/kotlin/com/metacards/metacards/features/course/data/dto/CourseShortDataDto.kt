package com.metacards.metacards.features.course.data.dto

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.course.domain.entity.CourseAvailability
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CourseShortData
import com.metacards.metacards.features.course.domain.entity.CourseStatus
import com.metacards.metacards.features.course.domain.entity.CourseThemeId

data class CourseShortDataDto(
    val availability: String = "",
    val courseId: String = "",
    val cover: String = "",
    val shortDescriptionLocalized: Map<String, String?>? = null,
    val nameLocalized: Map<String, String?> = emptyMap(),
    val status: String = ""
) {
    fun toDomain(themeId: String) = CourseShortData(
        courseId = CourseId(courseId),
        name = LocalizableString(nameLocalized),
        shortDescription = shortDescriptionLocalized?.let(::LocalizableString),
        availability = CourseAvailability.fromString(availability),
        coverUrl = cover,
        status = CourseStatus.fromString(status),
        themeId = CourseThemeId(themeId),
        isCompleted = false
    )
}