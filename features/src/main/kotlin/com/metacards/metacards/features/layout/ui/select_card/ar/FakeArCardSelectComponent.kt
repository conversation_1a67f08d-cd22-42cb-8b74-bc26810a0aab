package com.metacards.metacards.features.layout.ui.select_card.ar

import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardHint
import com.metacards.metacards.features.deck.domain.entity.CardWithFavoriteAndComment
import com.metacards.metacards.features.layout.domain.ar.GridSettings
import com.metacards.metacards.features.layout.domain.ar.StashSettings
import com.metacards.metacards.features.layout.ui.card_list.CardListComponent
import io.github.sceneview.math.Position
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow

class FakeArCardSelectComponent : ArCardSelectComponent {
    override val isPredefined: Boolean = false
    override val canSelectMoreCard: StateFlow<CardListComponent.CanSelectCard> =
        MutableStateFlow(CardListComponent.CanSelectCard.Can)
    override val stashCardEvent: SharedFlow<StashAction> = MutableSharedFlow()
    override val reloadEvent: SharedFlow<Unit> = MutableSharedFlow()
    override val stashSettings: StashSettings = StashSettings(1, 1f, Position())
    override val gridSettings: GridSettings = GridSettings(1, 1)
    override val selectedCard: StateFlow<CardWithFavoriteAndComment?> = MutableStateFlow(
        CardWithFavoriteAndComment.default(Card.mock(0))
    )
    override val cardHint: StateFlow<CardHint?> = MutableStateFlow(CardHint.MOCK)
    override val isShareLoading: MutableStateFlow<Boolean> = MutableStateFlow(false)

    override fun onToggleShare(cardUrl: String, questionText: String) = Unit
    override fun onToggleFavorite(isOn: Boolean) = Unit
    override fun onMoreCardClick() = Unit
    override fun onFinishClick() = Unit
    override fun selectCard(card: Card) = Unit
    override fun onCardsLoaded() = Unit
    override fun reload() = Unit
    override fun onCardCommentValueChanged(newComment: String) = Unit
}