package com.metacards.metacards.features.record.domain

import android.content.Context
import android.os.Parcelable
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.localization.ui.toStringByLocal
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardWithComment
import kotlinx.parcelize.Parcelize

@Parcelize
data class Question(
    val order: Int,
    val text: LocalizableString?,
    val cardsWithComment: List<CardWithComment>
) : Parcelable {

    companion object {
        fun createEmptyQuestion(order: Int = 0): Question =
            Question(order, text = null, cardsWithComment = emptyList())

        fun mock(order: Int) = Question(
            order = order,
            text = LocalizableString.createNonLocalizable("Some question $order"),
            cardsWithComment = Card.getMockList().map { CardWithComment(it, "") }
        )

        fun getMockList() = List(7, ::mock)
    }
}

fun List<Question>.localized(context: Context) = map {
    it.copy(
        text = LocalizableString.createNonLocalizable(it.text?.toStringByLocal(context) ?: "")
    )
}