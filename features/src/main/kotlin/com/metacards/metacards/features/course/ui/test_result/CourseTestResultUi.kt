package com.metacards.metacards.features.course.ui.test_result

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.navigation_bar.IconNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.LoadingIconNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.course.domain.entity.CourseResult
import com.metacards.metacards.features.course.domain.entity.LinkedVideo
import com.metacards.metacards.features.course.ui.widgets.CardVideoWrapper
import com.metacards.metacards.features.course.ui.widgets.CourseLoaderSkeleton
import com.metacards.metacards.features.course.ui.widgets.PauseAnotherVideosEvent
import com.metacards.metacards.features.course.ui.widgets.createPauseAnotherVideosEvent
import com.metacards.metacards.features.deck.ui.MetaCardDefaults
import com.metacards.metacards.features.layout.domain.LayoutId
import com.metacards.metacards.features.layout.domain.PredefinedLayoutWithAvailable
import com.metacards.metacards.features.layout.ui.predefined_layout.widgets.PredefinedLayoutItem
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun CourseTestResultUi(
    component: CourseTestResultComponent,
    modifier: Modifier = Modifier,
) {
    val isCourseResultSaved by component.isCourseResultSaved.collectAsState()
    val layouts by component.predefinedLayouts.collectAsState()
    val isShareInProgress by component.isShareInProgress.collectAsState()

    if (isCourseResultSaved) {
        TestResultContent(
            courseResult = component.courseResult,
            courseName = component.courseName,
            layouts = layouts,
            onBottomButtonClick = component::onGoForwardClick,
            bottomButtonText = component.bottomButtonText,
            onBlockContentClick = component::onBlockContentClick,
            onLayoutClick = component::onLayoutClick,
            onCloseClick = component::onCloseClick,
            onShareClick = component::onShareClick,
            onVideoFullScreenClick = component::onVideoFullScreenClick,
            isShareInProgress = isShareInProgress,
            title = component.title,
            modifier = modifier
        )
    } else {
        CourseLoaderSkeleton()
    }
}

@Composable
fun TestResultContent(
    courseResult: CourseResult,
    courseName: LocalizableString?,
    layouts: List<PredefinedLayoutWithAvailable>,
    bottomButtonText: String,
    onBottomButtonClick: () -> Unit,
    onLayoutClick: (LayoutId) -> Unit,
    onBlockContentClick: () -> Unit,
    onCloseClick: () -> Unit,
    onShareClick: () -> Unit,
    onVideoFullScreenClick: (Long, String) -> Unit,
    isShareInProgress: Boolean,
    title: LocalizableString,
    modifier: Modifier = Modifier,
) {
    val localDensity = LocalDensity.current
    var buttonHeightDp by remember { mutableStateOf(0.dp) }

    Column(modifier = modifier.fillMaxSize()) {
        TopNavigationBar(
            modifier = Modifier
                .fillMaxWidth()
                .statusBarsPadding(),
            title = courseName ?: title,
            leadingIcon = {
                IconNavigationItem(
                    iconRes = R.drawable.ic_24_close,
                    onClick = onCloseClick
                )
            },
            trailingContent = {
                LoadingIconNavigationItem(
                    iconRes = R.drawable.ic_24_share_outlined,
                    isLoading = isShareInProgress,
                    onClick = onShareClick
                )
            }
        )

        Box(Modifier.weight(1f)) {
            Column(
                modifier = Modifier
                    .verticalScroll(rememberScrollState())
                    .navigationBarsPadding()
                    .padding(bottom = 28.dp + buttonHeightDp)
            ) {
                Spacer(modifier = Modifier.height(16.dp))

                if (layouts.isNotEmpty()) {
                    Layouts(
                        title = courseResult.title,
                        layouts = layouts,
                        onLayoutClick = onLayoutClick,
                        onBlockContentClick = onBlockContentClick,
                        modifier = Modifier.padding(horizontal = 16.dp)
                    )

                    Spacer(modifier = Modifier.height(40.dp))
                }

                val pauseAnotherVideosEvent = remember {
                    createPauseAnotherVideosEvent()
                }

                if (!courseResult.linkedVideos.isNullOrEmpty()) {
                    LinkedVideos(
                        title = courseResult.title.takeIf { layouts.isEmpty() },
                        videos = courseResult.linkedVideos,
                        onVideoFullScreenClick = onVideoFullScreenClick,
                        pauseAnotherVideosEvent = pauseAnotherVideosEvent,
                        modifier = Modifier.padding(horizontal = 16.dp)
                    )

                    Spacer(modifier = Modifier.height(40.dp))
                }

                Results(
                    courseResult = courseResult,
                    onVideoFullScreenClick = onVideoFullScreenClick,
                    pauseAnotherVideosEvent = pauseAnotherVideosEvent,
                    modifier = Modifier.padding(horizontal = 16.dp)
                )
            }

            MetaAccentButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.BottomCenter)
                    .background(
                        brush = Brush.verticalGradient(
                            listOf(Color.Transparent, Color.Black)
                        )
                    )
                    .navigationBarsPadding()
                    .padding(horizontal = 16.dp)
                    .padding(bottom = 4.dp)
                    .onSizeChanged { size ->
                        buttonHeightDp = with(localDensity) { size.height.toDp() }
                    },
                text = bottomButtonText,
                onClick = onBottomButtonClick,
            )
        }
    }
}

@Composable
private fun Results(
    courseResult: CourseResult,
    onVideoFullScreenClick: (Long, String) -> Unit,
    pauseAnotherVideosEvent: PauseAnotherVideosEvent,
    modifier: Modifier = Modifier,
) {
    Text(
        text = R.string.course_result_test_results.strResDesc().localizedByLocal(),
        style = CustomTheme.typography.heading.additional,
        color = CustomTheme.colors.text.primary,
        modifier = modifier
    )

    Spacer(modifier = Modifier.height(16.dp))

    Column(
        modifier = modifier
            .background(CustomTheme.colors.background.primary, RoundedCornerShape(16.dp))
    ) {

        Spacer(modifier = Modifier.height(16.dp))

        if (courseResult.cover != null && courseResult.video == null) {
            AsyncImage(
                modifier = modifier
                    .fillMaxWidth()
                    .aspectRatio(1.69f)
                    .clip(RoundedCornerShape(16.dp)),
                model = ImageRequest.Builder(LocalContext.current)
                    .data(courseResult.cover)
                    .crossfade(MetaCardDefaults.CROSSFADE_DURATION_MILLIS)
                    .build(),
                contentDescription = null,
                contentScale = ContentScale.FillBounds
            )

            Spacer(modifier = Modifier.height(16.dp))
        } else if (courseResult.video != null) {
            val videoUrl = courseResult.video.localizedByLocal()
            CardVideoWrapper(
                modifier = modifier
                    .fillMaxWidth()
                    .aspectRatio(1.69f),
                shape = RoundedCornerShape(16.dp),
                border = null,
                context = LocalContext.current,
                videoUrl = videoUrl,
                videoCoverUrl = courseResult.cover,
                pauseAnotherVideosEvent = pauseAnotherVideosEvent,
                onFullScreenClick = { duration -> onVideoFullScreenClick(duration, videoUrl) }
            )

            Spacer(modifier = Modifier.height(16.dp))
        }

        courseResult.subtitle?.localizedByLocal()?.let {
            Text(
                text = it,
                style = CustomTheme.typography.heading.medium,
                color = CustomTheme.colors.text.primary,
                modifier = modifier
            )
        }

        courseResult.description?.localizedByLocal()?.let {
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = it,
                style = CustomTheme.typography.body.primary,
                color = CustomTheme.colors.text.secondary,
                modifier = modifier
            )
        }

        Spacer(modifier = Modifier.height(16.dp))
    }
}

@Composable
private fun Layouts(
    title: LocalizableString,
    layouts: List<PredefinedLayoutWithAvailable>,
    onLayoutClick: (LayoutId) -> Unit,
    onBlockContentClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val lc = LocalConfiguration.current
    val itemModifier = Modifier
        .width(lc.screenWidthDp.times(0.85f).dp)
        .aspectRatio(2f)
        .clip(RoundedCornerShape(16.dp))

    Text(
        text = title.localizedByLocal(),
        style = CustomTheme.typography.heading.additional,
        color = CustomTheme.colors.text.primary,
        modifier = modifier
    )

    Spacer(modifier = Modifier.height(8.dp))

    Text(
        text = R.string.course_result_layouts_recommendation.strResDesc().localizedByLocal(),
        style = CustomTheme.typography.body.primary,
        color = CustomTheme.colors.text.secondary,
        modifier = modifier
    )

    Spacer(modifier = Modifier.height(16.dp))

    LazyRow(
        modifier = Modifier.fillMaxWidth(),
        contentPadding = PaddingValues(horizontal = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(layouts) { layout ->
            PredefinedLayoutItem(
                layoutWithAvailable = layout,
                onLayoutClick = onLayoutClick,
                onBlockContentClick = onBlockContentClick,
                modifier = itemModifier
            )
        }
    }
}

@Composable
private fun LinkedVideos(
    title: LocalizableString?,
    videos: List<LinkedVideo>,
    onVideoFullScreenClick: (Long, String) -> Unit,
    pauseAnotherVideosEvent: PauseAnotherVideosEvent,
    modifier: Modifier = Modifier,
) {
    val lc = LocalConfiguration.current
    val itemModifier = Modifier
        .then(
            if (videos.size == 1) {
                Modifier.width(lc.screenWidthDp.dp - 32.dp)
            } else {
                Modifier.width(lc.screenWidthDp.times(0.85f).dp)
            }
        )
        .aspectRatio(1.77f)

    title?.let {
        Text(
            text = it.localizedByLocal(),
            style = CustomTheme.typography.heading.additional,
            color = CustomTheme.colors.text.primary,
            modifier = modifier
        )

        Spacer(modifier = Modifier.height(16.dp))
    }

    LazyRow(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        contentPadding = PaddingValues(horizontal = 16.dp)
    ) {
        items(videos) { video ->
            val videoUrl = video.video.localizedByLocal()
            CardVideoWrapper(
                context = LocalContext.current,
                modifier = itemModifier,
                shape = RoundedCornerShape(16.dp),
                videoUrl = videoUrl,
                videoCoverUrl = video.cover,
                pauseAnotherVideosEvent = pauseAnotherVideosEvent,
                onFullScreenClick = { duration -> onVideoFullScreenClick(duration, videoUrl) }
            )
        }
    }
}

@Preview(showSystemUi = true)
@Composable
private fun Preview() {
    AppTheme {
        CourseTestResultUi(
            component = FakeCourseTestResultComponent(),
        )
    }
}