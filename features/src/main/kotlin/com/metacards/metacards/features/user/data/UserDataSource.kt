package com.metacards.metacards.features.user.data

import android.content.Context
import co.touchlab.kermit.Logger
import com.google.firebase.Firebase
import com.google.firebase.auth.auth
import com.google.firebase.firestore.SetOptions
import com.google.firebase.firestore.toObject
import com.google.firebase.functions.functions
import com.metacards.metacards.core.functions.CloudFunctionsService
import com.metacards.metacards.core.functions.FunctionResult
import com.metacards.metacards.core.network.firestore.FirestoreService
import com.metacards.metacards.core.network.firestore.getFlow
import com.metacards.metacards.core.network.firestore.getFlowWithLoadable
import com.metacards.metacards.core.user.data.UserDto
import com.metacards.metacards.core.user.data.toDto
import com.metacards.metacards.core.user.domain.Email
import com.metacards.metacards.core.user.domain.FavoriteCard
import com.metacards.metacards.core.user.domain.UserId
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.mapData
import io.branch.referral.Branch
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.tasks.await

class UserDataSource(
    private val firestoreService: FirestoreService,
    private val cloudFunctionsService: CloudFunctionsService,
    private val context: Context,
) {

    fun getUserDecksFlow(userId: UserId): Flow<LoadableState<List<String>>> {
        val flow = firestoreService.db.collection(getDecksCollectionPath(userId))
            .getFlowWithLoadable(firestoreService)
        return flow
            .map { it.mapData { data -> data?.documents?.mapNotNull { it.id } } }
    }

    suspend fun getUserDecks(userId: UserId): List<String> {
        return firestoreService.db.collection(getDecksCollectionPath(userId))
            .get().await().documents.map { it.id }
    }

    suspend fun addDeckToPurchased(deckId: String): FunctionResult<Map<String, Any>> {
        return cloudFunctionsService.call<Map<String, Any>>("applyQR", mapOf("qrId" to deckId))
//        firestoreService.db.collection(getDecksCollectionPath(userId)).document(deckId)
//            .set(hashMapOf(DECK_PURCHASE_DATE_FIELD_NAME to Timestamp.now())).await()
    }

    suspend fun getUser(uuid: String): UserDto? {
        val ref = firestoreService.db.collection(USERS_COLLECTION_PATH).document(uuid).get().await()

        return ref.toObject()
    }

    fun getUserFlow(UUID: String): Flow<UserDto?> {
        val flow = firestoreService.db.collection(USERS_COLLECTION_PATH).document(UUID)
            .getFlow(firestoreService)

        return flow.map { snapshot -> snapshot.toObject() }
    }

    val logger = Logger.withTag("UserDataSource")

    private fun getPromoIdentifier(): String {
        val firstReferringParams = Branch.getAutoInstance(context).firstReferringParams
        return firstReferringParams.optString(CANONICAL_IDENTIFIER_FIELD_NAME, "")
    }

    suspend fun createUser(userId: UserId, email: Email?, data: Map<String, Any>) {
        logger.d("createUser: $userId, $email")
        val isUserExists =
            firestoreService.db.collection(USERS_COLLECTION_PATH).document(userId.value)
                .get().await().exists()

        if (!isUserExists) {
            val identifier = getPromoIdentifier()

            val userData = if (identifier.isEmpty()) {
                data
            } else {
                data + mapOf(PROMO_ID_FIELD_NAME to identifier)
            }

            val document =
                firestoreService.db.collection(USERS_COLLECTION_PATH).document(userId.value)

            if (email == null) {
                document.set(userData).await()
            } else {
                document.set(userData + mapOf(EMAIL_FIELD_NAME to email.value)).await()
            }
        }
    }

    suspend fun updateFavoriteCards(userId: UserId, favoriteCards: List<FavoriteCard>) {
        logger.d("updateFavoriteCards: $userId, $favoriteCards")
        val favoriteCardsDto = favoriteCards.map { it.toDto() }
        val document = firestoreService.db.collection(USERS_COLLECTION_PATH).document(userId.value)
        val newField = hashMapOf(FAVORITE_CARDS_FIELD_NAME to favoriteCardsDto)
        document.update(newField as Map<String, Any>).await()
    }

    suspend fun deleteUser(userId: UserId) {
        logger.d("deleteUser: $userId")
        val deleteFunction = Firebase.functions.getHttpsCallable(RECURSIVE_DELETE_FUNCTION)
        deleteFunction.call(
            hashMapOf(
                RECURSIVE_DELETE_DATA_KEY to firestoreService.db.collection(USERS_COLLECTION_PATH)
                    .document(userId.value).path
            )
        ).await()
    }

    suspend fun updateUser(userId: UserId, updatedUser: UserDto) {
        logger.w { "Firestore User:  ${Firebase.auth.currentUser?.email} // ${Firebase.auth.currentUser?.uid}" }
        firestoreService.db.collection(USERS_COLLECTION_PATH).document(userId.value)
            .set(updatedUser, SetOptions.merge())
            .await()
    }

    suspend fun updateUser(userId: UserId, updatedData: Map<String, Any>) {
        logger.w { "Firestore User:  ${Firebase.auth.currentUser?.email} // ${Firebase.auth.currentUser?.uid}" }
        firestoreService.db.collection(USERS_COLLECTION_PATH).document(userId.value)
            .update(updatedData)
            .await()
    }

    companion object {
        private const val USERS_COLLECTION_PATH = "users"
        private const val DECKS_COLLECTION_PATH = "decks"
        private const val EMAIL_FIELD_NAME = "email"
        private const val FAVORITE_CARDS_FIELD_NAME = "favCards"
        private const val DECK_PURCHASE_DATE_FIELD_NAME = "purchaseDate"
        private const val RECURSIVE_DELETE_FUNCTION = "recursiveDelete"
        private const val RECURSIVE_DELETE_DATA_KEY = "path"
        private const val PROMO_ID_FIELD_NAME = "promoIdentifier"
        private const val CANONICAL_IDENTIFIER_FIELD_NAME = "\$canonical_identifier"

        fun getDecksCollectionPath(userId: UserId): String {
            return USERS_COLLECTION_PATH + "/" + userId.value + "/" + DECKS_COLLECTION_PATH
        }
    }
}