package com.metacards.metacards.features.favorite_cards.ui.list

import com.metacards.metacards.core.user.domain.FavoriteCard
import com.metacards.metacards.features.deck.domain.entity.CardId
import kotlinx.coroutines.flow.StateFlow

interface FavoriteCardListComponent {

    val cards: StateFlow<List<FavoriteCard>>

    fun onCardClick(cardId: CardId)

    fun onGoToMainClick()

    sealed interface Output {
        data class CardDetailsRequested(val cardId: CardId) : Output
        data object GoToMainRequested : Output
    }
}