package com.metacards.metacards.features.user_analytics.ui.common_widgets

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.features.record.domain.LevelType
import com.metacards.metacards.features.record.ui.utils.feelingLevelToColor

@Composable
fun AnalyticsLevelCard(
    levelType: LevelType,
    level: Int?,
    text: String,
    description: String,
    modifier: Modifier = Modifier
) {
    Row(modifier = modifier) {
        Box(
            Modifier
                .padding(top = 6.dp)
                .size(12.dp)
                .background(
                    color = if (level != null) {
                        feelingLevelToColor(levelType, level)
                    } else {
                        CustomTheme.colors.icons.disabled
                    },
                    shape = CircleShape
                )
        )

        Spacer(modifier = Modifier.size(4.dp))

        Column {
            Text(
                text = text,
                color = CustomTheme.colors.text.primary,
                style = CustomTheme.typography.heading.primary
            )
            Spacer(modifier = Modifier.size(8.dp))
            Text(
                text = description,
                color = CustomTheme.colors.text.primary,
                style = CustomTheme.typography.caption.small
            )
        }
    }
}