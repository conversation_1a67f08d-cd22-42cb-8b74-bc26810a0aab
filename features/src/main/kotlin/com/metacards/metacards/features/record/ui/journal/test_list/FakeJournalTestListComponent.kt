package com.metacards.metacards.features.record.ui.journal.test_list

import com.metacards.metacards.features.course.domain.entity.CoursePassedTest
import kotlinx.coroutines.flow.MutableStateFlow

class FakeJournalTestListComponent : JournalTestListComponent {
    override val tests = MutableStateFlow<List<CoursePassedTest>>(emptyList())
    override fun onTestClick(coursePassedTest: CoursePassedTest) = Unit
}