package com.metacards.metacards.features.auth.ui.main_auth

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.features.BuildConfig
import com.metacards.metacards.features.R
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.auth.domain.LogoutInteractor
import com.metacards.metacards.features.auth.domain.SSOType
import com.metacards.metacards.features.auth.domain.SetOnSSOSignInDismissInteractor
import com.metacards.metacards.features.auth.domain.sso.google.SignInWithGoogleInteractor
import kotlinx.coroutines.flow.MutableStateFlow

class RealMainAuthComponent(
    componentContext: Component<PERSON>onte<PERSON><PERSON>,
    initialShouldAnimate: <PERSON><PERSON><PERSON>,
    private val errorHandler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    private val signInWithGoogleInteractor: SignInWithGoogleInteractor,
    private val setOnSSOSignInDismissInteractor: SetOnSSOSignInDismissInteractor,
    private val logoutInteractor: LogoutInteractor,
    private val analyticsService: AnalyticsService,
    private val onOutput: (MainAuthComponent.Output) -> Unit
) : ComponentContext by componentContext, MainAuthComponent {
    private var isAuthenticationStarted = false

    private val debounce = Debounce()

    override val shouldAnimate: MutableStateFlow<Boolean> = MutableStateFlow(initialShouldAnimate)
    override val ssoAuthVariants: MutableStateFlow<List<SSOType>> = MutableStateFlow(
        listOf(SSOType.Yandex, SSOType.Google, SSOType.VK, SSOType.OK)
    )

    override fun onSignInButtonClick() {
        logoutInteractor.execute()
        analyticsService.logEvent(AnalyticsEvent.AuthEvent)
        onOutput(MainAuthComponent.Output.SignInRequested)
    }

    override fun onSignUpButtonClick() {
        analyticsService.logEvent(AnalyticsEvent.RegistrationEvent)
        onOutput(MainAuthComponent.Output.SignUpRequested)
    }

    override fun onCloseButtonClick() {
        onOutput(MainAuthComponent.Output.MainPageRequested)
    }

    override fun onSSOClick(ssoType: SSOType) {
        DebounceClick(debounce, "onSSOClick") {
            if (!isAuthenticationStarted) {
                isAuthenticationStarted = true
                componentScope.safeLaunch(
                    errorHandler,
                    onErrorHandled = {
                        isAuthenticationStarted = false
                    }
                ) {
                    when (ssoType) {
                        is SSOType.Google -> {
                            analyticsService.logEvent(AnalyticsEvent.AuthGoogleEvent)
                            signInWithGoogleInteractor.execute(
                                afterSelection = {
                                    analyticsService.logEvent(AnalyticsEvent.AuthGoogleDoneEvent)
                                    onOutput(MainAuthComponent.Output.MainPageRequested)
                                }
                            )
                            isAuthenticationStarted = false
                        }

                        is SSOType.VK -> {
                            analyticsService.logEvent(AnalyticsEvent.AuthVkEvent)
                            setOnSSOSignInDismissInteractor.execute {
                                analyticsService.logEvent(AnalyticsEvent.AuthVkDoneEvent)
                                onOutput(MainAuthComponent.Output.MainPageRequested)
                            }

                            onOutput(
                                MainAuthComponent.Output.SignInViaWebViewRequested(
                                    BuildConfig.VK_AUTH_URL,
                                    R.string.auth_user_type_sso_vk
                                )
                            )
                            isAuthenticationStarted = false
                        }

                        is SSOType.OK -> {
                            analyticsService.logEvent(AnalyticsEvent.AuthOkEvent)
                            setOnSSOSignInDismissInteractor.execute {
                                analyticsService.logEvent(AnalyticsEvent.AuthOkDoneEvent)
                                onOutput(MainAuthComponent.Output.MainPageRequested)
                            }

                            onOutput(
                                MainAuthComponent.Output.SignInViaWebViewRequested(
                                    BuildConfig.OK_AUTH_URL,
                                    R.string.auth_user_type_sso_ok
                                )
                            )
                            isAuthenticationStarted = false
                        }

                        is SSOType.Yandex -> {
                            setOnSSOSignInDismissInteractor.execute {
                                onOutput(MainAuthComponent.Output.MainPageRequested)
                            }

                            onOutput(
                                MainAuthComponent.Output.SignInViaWebViewRequested(
                                    BuildConfig.YANDEX_AUTH_URL,
                                    R.string.auth_user_type_sso_yandex
                                )
                            )
                            isAuthenticationStarted = false
                        }
                    }
                }
            }
        }
    }

    override fun onAnimationEnd() {
        shouldAnimate.value = false
    }
}
