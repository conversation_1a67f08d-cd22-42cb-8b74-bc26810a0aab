package com.metacards.metacards.features.deck.ui.deck_info

import android.content.Context
import androidx.annotation.StringRes
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.domain.entity.DeckInfoWithCards
import com.metacards.metacards.features.payments.ui.PaymentComponent
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import java.net.URI
import java.net.URL

interface DeckInfoComponent {
    val currentCardPosition: StateFlow<Int>
    val deckInfo: StateFlow<LoadableState<DeckInfoWithCards>>
    val scrollToPageCommand: SharedFlow<Int>
    val actionButtonState: StateFlow<ButtonState>

    fun onActionButtonClick()
    fun onMaterialDeckBuyClick()
    fun onCardClick(cardPosition: Int)
    fun onCardSwipe(newPosition: Int)
    fun onVideoPlay()
    fun onShareClick(context: Context)
    fun scrollToPage(page: Int)

    sealed interface Output {
        data class DeckLayoutRequested(val deckId: DeckId) : Output
        data class FullScreenPreviewRequested(
            val cards: DeckInfoWithCards,
            val initialCardPosition: Int
        ) : Output

        data class MaterialDeckBuyRequested(val uri: URI) : Output
        data class WebViewRequested(val url: URL, @StringRes val title: Int) : Output
        data object AuthSuggestingRequested : Output
        data object PremiumSuggestingRequested : Output
        data object DecksListFallback : Output
    }

    val paymentComponent: PaymentComponent
}
