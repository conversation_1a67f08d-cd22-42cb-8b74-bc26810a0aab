package com.metacards.metacards.features.course.data.dto

import com.google.firebase.Timestamp
import com.metacards.metacards.features.course.domain.entity.CourseCompletedContent
import com.metacards.metacards.features.course.domain.entity.CourseContentId
import com.metacards.metacards.features.course.domain.entity.CourseContentType

data class CourseCompletedContentDto(
    val contentId: String = "",
    val type: String = "",
    val completeDate: Timestamp = Timestamp.now(),
    val score: Int? = null
) {
    fun toDomain() = CourseCompletedContent(
        contentId = CourseContentId(contentId),
        type = CourseContentType.fromString(type),
        completeDate = completeDate.toDate(),
        score = score
    )

    companion object {
        fun fromDomain(entity: CourseCompletedContent) = CourseCompletedContentDto(
            contentId = entity.contentId.value,
            type = entity.type.toString(),
            completeDate = Timestamp(entity.completeDate),
            score = entity.score
        )
    }
}