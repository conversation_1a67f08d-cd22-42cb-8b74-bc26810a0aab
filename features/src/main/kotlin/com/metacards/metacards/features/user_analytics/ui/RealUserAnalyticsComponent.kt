package com.metacards.metacards.features.user_analytics.ui

import android.os.Parcelable
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.router.stack.childStack
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeRun
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.bringToFrontByValue
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.toStateFlow
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.user.domain.GetUserSubscriptionStateInteractor
import com.metacards.metacards.features.user_analytics.createLayoutsAnalyticsComponent
import com.metacards.metacards.features.user_analytics.createMonthAnalyticsComponent
import com.metacards.metacards.features.user_analytics.createWeekAnalyticsComponent
import com.metacards.metacards.features.user_analytics.domain.ClearUserAnalyticsInteractor
import com.metacards.metacards.features.user_analytics.ui.layouts.LayoutsAnalyticsComponent
import com.metacards.metacards.features.user_analytics.ui.month.MonthAnalyticsComponent
import com.metacards.metacards.features.user_analytics.ui.week.WeekAnalyticsComponent
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.parcelize.Parcelize

class RealUserAnalyticsComponent(
    componentContext: ComponentContext,
    private val componentFactory: ComponentFactory,
    getUserSubscriptionStateInteractor: GetUserSubscriptionStateInteractor,
    private val analyticsService: AnalyticsService,
    clearUserAnalyticsInteractor: ClearUserAnalyticsInteractor,
    errorHandler: ErrorHandler,
    private val onOutput: (UserAnalyticsComponent.Output) -> Unit,
    initialTab: UserAnalyticsComponent.Tab = UserAnalyticsComponent.Tab.Layouts,
) : ComponentContext by componentContext, UserAnalyticsComponent {

    private val navigation = StackNavigation<ChildConfig>()

    override val childStack = childStack(
        source = navigation,
        initialConfiguration = initialTab.toConfig(),
        handleBackButton = false,
        childFactory = ::createChild
    ).toStateFlow(lifecycle)

    override val userSubscriptionState: StateFlow<User.SubscriptionState?> = getUserSubscriptionStateInteractor.execute()
        .stateIn(componentScope, SharingStarted.Eagerly, null)

    override val selectedTab = computed(childStack) { childStack ->
        when (childStack.active.instance) {
            is UserAnalyticsComponent.Child.Layouts -> UserAnalyticsComponent.Tab.Layouts
            is UserAnalyticsComponent.Child.Week -> UserAnalyticsComponent.Tab.Week
            is UserAnalyticsComponent.Child.Month -> UserAnalyticsComponent.Tab.Month
        }
    }

    init {
        logTabEvent(initialTab)
        safeRun(errorHandler) { clearUserAnalyticsInteractor.execute() }
    }

    override fun onTabSelected(tab: UserAnalyticsComponent.Tab) {
        logTabEvent(tab)
        navigation.bringToFrontByValue(tab.toConfig())
    }

    override fun onBlockContentClick() {
        when (userSubscriptionState.value) {
            is User.SubscriptionState.None -> onOutput(UserAnalyticsComponent.Output.PremiumSuggestingRequested)
            is User.SubscriptionState.Ongoing -> Unit
            null -> onOutput(UserAnalyticsComponent.Output.AuthSuggestingRequested)
        }
    }

    private fun UserAnalyticsComponent.Tab.toConfig() = when (this) {
        UserAnalyticsComponent.Tab.Layouts -> ChildConfig.Layouts
        UserAnalyticsComponent.Tab.Week -> ChildConfig.Week
        UserAnalyticsComponent.Tab.Month -> ChildConfig.Month
    }

    private fun createChild(
        config: ChildConfig,
        componentContext: ComponentContext
    ): UserAnalyticsComponent.Child = when (config) {

        is ChildConfig.Layouts -> UserAnalyticsComponent.Child.Layouts(
            componentFactory.createLayoutsAnalyticsComponent(
                componentContext,
                ::onLayoutAnalyticsOutput
            )
        )

        is ChildConfig.Week -> UserAnalyticsComponent.Child.Week(
            componentFactory.createWeekAnalyticsComponent(
                componentContext,
                ::onWeekAnalyticsOutput
            )
        )

        is ChildConfig.Month -> UserAnalyticsComponent.Child.Month(
            componentFactory.createMonthAnalyticsComponent(
                componentContext,
                ::onMonthAnalyticsOutput
            )
        )
    }

    private fun onWeekAnalyticsOutput(output: WeekAnalyticsComponent.Output) {
        when (output) {
            is WeekAnalyticsComponent.Output.RecordDetailsRequested -> {
                onOutput(UserAnalyticsComponent.Output.RecordDetailsRequested(output.recordId))
            }
        }
    }

    private fun onMonthAnalyticsOutput(output: MonthAnalyticsComponent.Output) {
        when (output) {
            is MonthAnalyticsComponent.Output.RecordDetailsRequested -> {
                onOutput(UserAnalyticsComponent.Output.RecordDetailsRequested(output.recordId))
            }
        }
    }

    private fun onLayoutAnalyticsOutput(output: LayoutsAnalyticsComponent.Output) {
        when (output) {
            is LayoutsAnalyticsComponent.Output.RecordDetailsRequested -> {
                onOutput(
                    UserAnalyticsComponent.Output.RecordDetailsRequested(output.recordId)
                )
            }
        }
    }

    private fun logTabEvent(tab: UserAnalyticsComponent.Tab) {
        analyticsService.logEvent(
            when (tab) {
                UserAnalyticsComponent.Tab.Layouts -> AnalyticsEvent.AnalyticsSetupsEvent
                UserAnalyticsComponent.Tab.Week -> AnalyticsEvent.AnalyticsWeekEvent
                UserAnalyticsComponent.Tab.Month -> AnalyticsEvent.AnalyticsMonthEvent
            }
        )
    }

    sealed interface ChildConfig : Parcelable {

        @Parcelize
        data object Layouts : ChildConfig

        @Parcelize
        data object Week : ChildConfig

        @Parcelize
        data object Month : ChildConfig
    }
}