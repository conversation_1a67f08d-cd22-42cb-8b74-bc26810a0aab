package com.metacards.metacards.features.favorite_cards.domain

import com.metacards.metacards.core.R
import com.metacards.metacards.core.user.domain.FavoriteCard
import com.metacards.metacards.core.utils.ErrorMessage
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.combineLoadableState
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.record.data.RecordRepository
import com.metacards.metacards.features.user.domain.UserRepository
import dev.icerock.moko.resources.desc.desc
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map

class GetFavoriteCardInfoInteractor(
    private val userRepository: UserRepository,
    private val recordRepository: RecordRepository
) {

    fun execute(cardId: CardId): Flow<LoadableState<FavoriteCardInfo>> {
        return combine(
            getFavoriteCard(cardId),
            recordRepository.getRecordsByCardId(cardId)
        ) { cardLoadable, recordsLoadable ->
            combineLoadableState(cardLoadable, recordsLoadable) { card, records ->
                if (card != null && records != null) {
                    FavoriteCardInfo(card, records)
                } else {
                    null
                }
            }
        }
    }

    private fun getFavoriteCard(cardId: CardId): Flow<LoadableState<FavoriteCard>> {
        return userRepository.user.map { user ->
            val card = user?.favoriteCards?.find { it.id == cardId.value }
            if (card != null) {
                LoadableState(data = card)
            } else {
                LoadableState(
                    error = ErrorMessage(
                        if (user == null) {
                            R.string.error_unauthorized.strResDesc()
                        } else {
                            "Favorite card with id ${cardId.value} is not found".desc()
                        }
                    )
                )
            }
        }
    }
}