package com.metacards.metacards.features.record.ui.record_details

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.bottom_sheet.ModalBottomSheet
import com.metacards.metacards.core.dialog.default_component.DefaultDialog
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.LceWidget
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.IconNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.core.widget.text_field.MetaTextField
import com.metacards.metacards.core.widget.text_field.MetaTextFieldDefaults
import com.metacards.metacards.features.R
import com.metacards.metacards.features.record.domain.Record
import com.metacards.metacards.features.record.ui.record_details.comment_details.CommentDetailsUi
import com.metacards.metacards.features.record.ui.record_details.widget.CardCommentWidget
import com.metacards.metacards.features.record.ui.record_details.widget.CommentWidget
import com.metacards.metacards.features.record.ui.record_details.widget.FeelingContent
import com.metacards.metacards.features.record.ui.record_details.widget.QuestionWidget
import com.metacards.metacards.features.record.ui.record_details.widget.RecordBottomButtons
import com.metacards.metacards.features.tutorial.ui.MessagePopupContent
import dev.icerock.moko.resources.StringResource
import dev.icerock.moko.resources.desc.ResourceFormattedStringDesc
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun RecordDetailsUi(
    component: RecordDetailsComponent,
    modifier: Modifier = Modifier
) {
    val record by component.record.collectAsState()
    val recordType = component.recordType
    val isFavorite by component.isRecordInFavorites.collectAsState()
    val courseName by component.courseName.collectAsState()
    val tutorialMessage by component.tutorialMessage.collectAsState()

    BoxWithFade(
        modifier = modifier
            .fillMaxSize()
            .safeDrawingPadding(),
        listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        val scrollState = rememberScrollState()

        LceWidget(loadingProgress = {}, state = record, onRetryClick = { }) { data, _ ->

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(scrollState)
            ) {
                val isRecordTypeNotInCreation = recordType != RecordDetailsComponent.RecordType.InCreation
                TopNavigationBar(
                    title = if (recordType != RecordDetailsComponent.RecordType.Archived) {
                        R.string.add_record_title.strResDesc()
                    } else {
                        R.string.archive_record_details_title.strResDesc()
                    },
                    leadingIcon = { if (isRecordTypeNotInCreation) BackNavigationItem() },
                    trailingContent = {
                        if (recordType != RecordDetailsComponent.RecordType.Archived) {
                            IconNavigationItem(
                                iconRes = if (isFavorite) {
                                    R.drawable.ic_24_favourite
                                } else {
                                    R.drawable.ic_24_no_favourite
                                },
                                onClick = {
                                    component.onFavoriteClick()
                                },
                                paddingValues = PaddingValues(horizontal = 16.dp)
                            )
                        }
                    },
                    contentPadding = PaddingValues(
                        top = 16.dp,
                        bottom = 16.dp,
                        start = if (isRecordTypeNotInCreation) 16.dp else 0.dp,
                        end = 0.dp
                    ),
                    textPadding = PaddingValues(horizontal = if (isRecordTypeNotInCreation) 24.dp else 16.dp),
                )

                courseName?.let {
                    Text(
                        text = ResourceFormattedStringDesc(
                            StringResource(R.string.record_list_course_title),
                            listOf(it.localizedByLocal())
                        ).localizedByLocal(),
                        style = CustomTheme.typography.heading.medium,
                        color = CustomTheme.colors.text.primary,
                        modifier = Modifier
                            .padding(start = 16.dp, end = 16.dp, top = 8.dp, bottom = 24.dp)
                    )
                }

                Content(Modifier.weight(1f), data, component)

                RecordBottomButtons(component)
            }
        }

        DefaultDialog(component.dialogControl)

        tutorialMessage?.let {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(CustomTheme.colors.background.disabledBackground)
                    .clickable(enabled = false) { },
                contentAlignment = Alignment.Center
            ) {
                MessagePopupContent(tutorialMessage = it)
            }
        }
    }

    ModalBottomSheet(control = component.bottomSheetControl) {
        CommentDetailsUi(component = it)
    }
}

@Composable
private fun Content(
    modifier: Modifier = Modifier,
    record: Record,
    component: RecordDetailsComponent
) {
    val recordType = component.recordType
    val userSubscriptionState by component.userSubscriptionState.collectAsState()
    val moodLevel by component.moodLevel.collectAsState()
    val energyLevel by component.energyLevel.collectAsState()
    val isMoodInteractionEnabled = recordType != RecordDetailsComponent.RecordType.Archived &&
        record.moodLevel == null
    val isEnergyInteractionEnabled = recordType != RecordDetailsComponent.RecordType.Archived &&
        record.energyLevel == null

    Column(modifier = modifier.verticalScroll(rememberScrollState())) {
        record.questions.forEachIndexed { index, question ->
            QuestionWidget(
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .padding(bottom = 16.dp),
                question = question,
                isEditable = question.text?.localizedByLocal().isNullOrBlank() &&
                    recordType != RecordDetailsComponent.RecordType.Archived,
                questionIndex = index,
                onCardClick = component::onCardClick,
                onValueChanged = component::onQuestionChanged,
                isDailyCardLayout = if (recordType == RecordDetailsComponent.RecordType.InCreation) {
                    component.isDailyCard
                } else {
                    record.isDailyCardLayout
                }
            )

            val cardWithCommentList = question.cardsWithComment.filter { it.comment.isNotEmpty() }
            if (cardWithCommentList.isNotEmpty()) {
                Text(
                    modifier = Modifier.padding(bottom = 8.dp, start = 32.dp),
                    text = R.string.common_record_card_comment_label.strResDesc().localizedByLocal(),
                    style = CustomTheme.typography.caption.medium,
                    color = CustomTheme.colors.text.secondary
                )

                cardWithCommentList.forEach {
                    if (it.comment.isNotEmpty()) {
                        CardCommentWidget(
                            cardImageUrl = it.card.imageUrl,
                            commentText = it.comment,
                            onClick = component::onCardCommentClick,
                            modifier = Modifier
                                .padding(horizontal = 16.dp)
                                .padding(bottom = 16.dp),
                        )
                    }
                }
            }
        }

        if (record.comments.isNotEmpty() || recordType != RecordDetailsComponent.RecordType.Archived) {
            Text(
                modifier = Modifier.padding(bottom = 8.dp, start = 32.dp),
                text = if (record.comments.isEmpty()) {
                    R.string.common_record_comment_label_empty.strResDesc().localizedByLocal()
                } else {
                    ResourceFormattedStringDesc(
                        StringResource(R.string.common_record_comment_label),
                        listOf(record.comments.size)
                    ).localizedByLocal()
                },
                style = CustomTheme.typography.caption.medium,
                color = CustomTheme.colors.text.secondary
            )
        }

        if (recordType != RecordDetailsComponent.RecordType.Archived) {
            MetaTextField(
                modifier = Modifier.padding(horizontal = 16.dp),
                heightMax = Dp.Infinity,
                inputControl = component.commentInputControl,
                placeholder = if (record.comments.isEmpty()) {
                    R.string.add_record_new_comment_hint.strResDesc().localizedByLocal()
                } else {
                    R.string.add_record_comment_hint.strResDesc().localizedByLocal()
                }
            )
        }

        record.comments.reversed().forEach { comment ->
            CommentWidget(
                comment = comment,
                modifier = Modifier
                    .padding(start = 16.dp, top = 8.dp, end = 16.dp)
                    .clip(MetaTextFieldDefaults.Shape)
                    .clickable { component.onCommentClick(comment) }
            )
        }

        FeelingContent(
            userSubscriptionState = userSubscriptionState,
            moodLevel = moodLevel,
            energyLevel = energyLevel,
            isMoodInteractionEnabled = isMoodInteractionEnabled,
            isEnergyInteractionEnabled = isEnergyInteractionEnabled,
            onMoodLevelChange = component::onMoodLevelChange,
            onEnergyLevelChange = component::onEnergyLevelChange,
            onBlockContentClick = component::onBlockAreaClick
        )
    }
}

@Preview
@Composable
private fun AddRecordUiPreview() {
    AppTheme {
        RecordDetailsUi(FakeRecordDetailsComponent())
    }
}
