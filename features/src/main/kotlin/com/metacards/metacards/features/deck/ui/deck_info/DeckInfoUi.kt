package com.metacards.metacards.features.deck.ui.deck_info

import android.content.Context
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ScaleFactor
import androidx.compose.ui.layout.lerp
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.navigationBarsPaddingDp
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.LceWidget
import com.metacards.metacards.core.widget.MetaClickableText
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.IconNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.domain.entity.DeckInfoWithCards
import com.metacards.metacards.features.deck.domain.entity.Lesson
import com.metacards.metacards.features.deck.ui.MetaCard
import com.metacards.metacards.features.deck.ui.deck_info.widgets.MetaVideoPreviewCard
import com.metacards.metacards.features.payments.ui.PaymentComponent
import dev.icerock.moko.resources.PluralsResource
import dev.icerock.moko.resources.StringResource
import dev.icerock.moko.resources.desc.PluralFormatted
import dev.icerock.moko.resources.desc.ResourceFormattedStringDesc
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.flow.SharedFlow
import java.net.URI
import kotlin.math.absoluteValue

private const val MATERIAL_TAG_LINK_TAG = "MATERIAL_TAG_LINK_TAG"

@Composable
fun DeckInfoUi(
    component: DeckInfoComponent,
    modifier: Modifier = Modifier,
) {
    val scrollState = rememberScrollState()
    val deckInfo by component.deckInfo.collectAsState()

    BoxWithFade(
        modifier = modifier
            .fillMaxSize()
            .statusBarsPadding(),
        listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        LceWidget(state = deckInfo, onRetryClick = { /*TODO*/ }) { data, _ ->
            BoxWithConstraints(modifier = Modifier.fillMaxSize()) {
                val width = maxWidth
                val padding = width * 0.2f
                Content(
                    scrollState,
                    data,
                    padding,
                    data.deckInfo.shopUrl,
                    component
                )
            }
        }
    }

    PaymentComponent(component = component.paymentComponent, modifier = Modifier.fillMaxSize())
}

@Composable
private fun Content(
    scrollState: ScrollState,
    data: DeckInfoWithCards,
    padding: Dp,
    shopUrl: URI?,
    component: DeckInfoComponent,
) {
    val context: Context = LocalContext.current
    val currentPage by component.currentCardPosition.collectAsState()
    val actionButtonState by component.actionButtonState.collectAsState()
    val localDensity = LocalDensity.current
    var buttonHeightDp by remember { mutableStateOf(0.dp) }

    Box(modifier = Modifier.fillMaxSize()) {
        Column {
            TopNavigationBar(
                modifier = Modifier.fillMaxWidth(),
                title = data.deckInfo.name,
                leadingIcon = { BackNavigationItem() },
                trailingContent = {
                    IconNavigationItem(
                        iconRes = R.drawable.ic_24_share_outlined,
                        onClick = {
                            component.onShareClick(context)
                        }
                    )
                }
            )

            Column(
                modifier = Modifier
                    .verticalScroll(scrollState)
                    .padding(bottom = 24.dp + buttonHeightDp + navigationBarsPaddingDp)
            ) {
                Text(
                    modifier = Modifier
                        .align(Alignment.CenterHorizontally)
                        .padding(vertical = 8.dp),
                    text = StringDesc.PluralFormatted(
                        PluralsResource(R.plurals.deck_info_cards_count),
                        data.cardCount,
                        data.cardCount
                    ).localizedByLocal(),
                    color = CustomTheme.colors.text.secondary,
                    style = CustomTheme.typography.caption.medium
                )

                CardCarousel(
                    padding = padding,
                    data = data,
                    currentPage = currentPage,
                    scrollCommand = component.scrollToPageCommand,
                    onNewCardSelected = component::onCardSwipe,
                    onCardClick = component::onCardClick
                )

                Text(
                    modifier = Modifier
                        .padding(horizontal = 32.dp)
                        .padding(top = 24.dp, bottom = 8.dp),
                    text = R.string.deck_info_about_deck_title.strResDesc().localizedByLocal(),
                    color = CustomTheme.colors.text.secondary,
                    style = CustomTheme.typography.caption.medium
                )

                Text(
                    modifier = Modifier.padding(horizontal = 32.dp),
                    text = data.deckInfo.description.localizedByLocal(),
                    color = CustomTheme.colors.text.primary,
                    style = CustomTheme.typography.body.primary
                )

                if (data.deckInfo.shopUrl != null) {
                    MetaClickableText(
                        modifier = Modifier.padding(horizontal = 32.dp),
                        mainSpanStyle = SpanStyle(color = CustomTheme.colors.text.primary),
                        textStyle = CustomTheme.typography.body.primary.merge(TextStyle(color = CustomTheme.colors.text.primary)),
                        text = R.string.deck_info_material_deck_buy_text.strResDesc()
                            .localizedByLocal(),
                        annotations = listOf(MATERIAL_TAG_LINK_TAG to shopUrl.toString()),
                        onTextClick = { component.onMaterialDeckBuyClick() },
                    )
                }

                if (data.deckInfo.videoLessons.isNotEmpty()) {
                    VideosBlock(
                        videoLessons = data.deckInfo.videoLessons,
                        onVideoPlay = component::onVideoPlay
                    )
                }
            }
        }

        val buttonTitle = if (data.deckAvailable) {
            R.string.deck_info_layout_deck_button.strResDesc().localizedByLocal()
        } else {
            ResourceFormattedStringDesc(
                StringResource(R.string.deck_info_buy_button),
                listOf(data.deckInfo.price.toDecimalString())
            ).localizedByLocal()
        }

        MetaAccentButton(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter)
                .background(
                    brush = Brush.verticalGradient(
                        listOf(Color.Transparent, Color.Black)
                    )
                )
                .navigationBarsPadding()
                .padding(horizontal = 16.dp)
                .padding(bottom = 4.dp)
                .onSizeChanged {
                    buttonHeightDp = localDensity.run { it.height.toDp() }
                },
            text = buttonTitle,
            onClick = component::onActionButtonClick,
            state = actionButtonState
        )
    }
}

@Composable
private fun VideosBlock(videoLessons: List<Lesson>, onVideoPlay: () -> Unit) {
    Text(
        modifier = Modifier
            .padding(horizontal = 32.dp)
            .padding(top = 24.dp, bottom = 8.dp),
        text = R.string.deck_info_videos_title.strResDesc().localizedByLocal(),
        color = CustomTheme.colors.text.secondary,
        style = CustomTheme.typography.caption.medium
    )

    Column(
        modifier = Modifier.padding(horizontal = 16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        videoLessons.forEachIndexed { _, videoLesson ->
            MetaVideoPreviewCard(
                modifier = Modifier
                    .fillMaxWidth(),
                videoLesson = videoLesson,
                onVideoPlay = onVideoPlay
            )
        }
    }
}

const val ENDLESS_PAGER_MULTIPLIER = 1000

@Composable
@OptIn(ExperimentalFoundationApi::class)
private fun CardCarousel(
    padding: Dp,
    data: DeckInfoWithCards,
    currentPage: Int,
    scrollCommand: SharedFlow<Int>,
    onNewCardSelected: (Int) -> Unit,
    onCardClick: (Int) -> Unit,
) {
    // Бесконечный пейджер реализуется с помощью очень большого количества страниц,
    // поэтому стартовая позиция будет середина
    var firstComposition by remember { mutableStateOf(true) }
    val pageCount = ENDLESS_PAGER_MULTIPLIER * data.cards.size
    val startIndex = pageCount / 2
    val pagerState = rememberPagerState(
        initialPage = startIndex + currentPage,
        initialPageOffsetFraction = 0f
    ) { pageCount }

    LaunchedEffect(key1 = pagerState.currentPage) {
        if (firstComposition) {
            firstComposition = false
            return@LaunchedEffect
        }

        val index = (pagerState.currentPage - startIndex).floorMod(data.cards.count())
        if (currentPage != index) {
            onNewCardSelected(index)
        }
    }

    LaunchedEffect(key1 = Unit) {
        scrollCommand.collect { index ->
            if (currentPage != index) {
                pagerState.animateScrollToPage(startIndex + index)
            }
        }
    }

    HorizontalPager(
        state = pagerState,
        pageSpacing = 16.dp,
        contentPadding = PaddingValues(horizontal = padding),
    ) { index ->
        val itemIndex = (index - startIndex).floorMod(data.cards.count())
        val card = data.cards[itemIndex]

        Box(
            modifier = Modifier
                .graphicsLayer {
                    val direction = pagerState.currentPage - index
                    val pageOffset =
                        (direction + pagerState.currentPageOffsetFraction).absoluteValue

                    val scale = lerp(
                        start = ScaleFactor(0.75f, 0.75f),
                        stop = ScaleFactor(1f, 1f),
                        fraction = 1f - pageOffset.coerceIn(0f, 1f)
                    )

                    scaleX = scale.scaleX
                    scaleY = scale.scaleY
                    translationX = (1 - scale.scaleX) * direction * size.width / 2f
                }
        ) {
            MetaCard(
                card = card,
                onCardClick = { onCardClick(itemIndex) },
                paddingValues = PaddingValues(0.dp),
                lockedPlaceholder = { LockedCardContent() }
            )
        }
    }
}

@Composable
fun BoxScope.LockedCardContent() {
    Box(
        modifier = Modifier
            .matchParentSize()
            .background(CustomTheme.colors.background.modal)
    ) {
        Column(
            modifier = Modifier.align(Alignment.Center),
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_24_locked),
                contentDescription = null,
                tint = CustomTheme.colors.icons.primary
            )
            Text(
                textAlign = TextAlign.Center,
                text = R.string.deck_info_locked_card_text.strResDesc().localizedByLocal(),
                color = CustomTheme.colors.text.primary,
                style = CustomTheme.typography.body.primary
            )
        }
    }
}

fun Int.floorMod(other: Int): Int = when (other) {
    0 -> this
    else -> this - floorDiv(other) * other
}

@Preview
@Composable
private fun DeckInfoUiPreview() {
    AppTheme {
        DeckInfoUi(FakeDeckInfoComponent())
    }
}
