package com.metacards.metacards.features.account.ui.about_the_app

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.getPackageInfoCompat
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.account.ui.widgets.AccountListItem
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun AboutTheAppUi(
    component: AboutTheAppComponent,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    val packageManager = context.packageManager
    val packageInfo = packageManager.getPackageInfoCompat(context.packageName)

    BoxWithFade(modifier = modifier, listOfColors = CustomTheme.colors.gradient.backgroundList) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            TopNavigationBar(
                title = R.string.about_the_app_title.strResDesc(),
                leadingIcon = { BackNavigationItem() },
                contentPadding = PaddingValues(top = 8.dp, bottom = 16.dp)
            )

            AboutTheAppComponent.AboutAppSection.entries.forEach { section ->
                AccountListItem(
                    modifier = Modifier
                        .clip(RoundedCornerShape(16.dp))
                        .background(CustomTheme.colors.background.primary),
                    leadingText = section.title,
                    onClick = { section.onClick(component) },
                    contentPadding = PaddingValues(16.dp)
                )
            }
        }

        Text(
            text = "${
                R.string.about_the_app_version.strResDesc().localizedByLocal()
            } ${packageInfo.versionName}",
            color = CustomTheme.colors.text.secondary,
            style = CustomTheme.typography.caption.small,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .navigationBarsPadding()
                .padding(16.dp)
        )
    }
}

@Preview
@Composable
private fun AboutTheAppUiPreview() {
    AppTheme {
        AboutTheAppUi(component = FakeAboutTheAppComponent())
    }
}
