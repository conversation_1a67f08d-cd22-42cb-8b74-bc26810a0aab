package com.metacards.metacards.features.user_analytics.ui.week

import android.os.Build
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.doOnStart
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.paged_loading.PagedLoading
import com.metacards.metacards.core.paged_loading.handleErrors
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.PagedData
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.toLoadableState
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.user.domain.GetUserSubscriptionStateInteractor
import com.metacards.metacards.features.user_analytics.domain.week.WeekAnalyticsInfo
import com.metacards.metacards.features.user_analytics.domain.week.WeekAnalyticsPagedLoading
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn

class RealWeekAnalyticsComponent(
    componentContext: ComponentContext,
    private val weekAnalyticsLoading: WeekAnalyticsPagedLoading,
    private val errorHandler: ErrorHandler,
    private val analyticsService: AnalyticsService,
    getUserSubscriptionStateInteractor: GetUserSubscriptionStateInteractor,
    private val onOutput: (WeekAnalyticsComponent.Output) -> Unit
) : ComponentContext by componentContext, WeekAnalyticsComponent {

    override val isPremiumUser = getUserSubscriptionStateInteractor.execute().map {
        it is User.SubscriptionState.Ongoing
    }.stateIn(componentScope, SharingStarted.Eagerly, false)

    override val analyticsState: StateFlow<LoadableState<PagedData<WeekAnalyticsInfo>>> =
        computed(weekAnalyticsLoading.stateFlow, isPremiumUser) { weekAnalytics, isPremiumUser ->
            val data = if (!isPremiumUser && (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S)) {
                PagedLoading.State.mock(listOf(WeekAnalyticsInfo.MOCK))
            } else {
                weekAnalytics
            }
            data.toLoadableState()
        }

    init {
        weekAnalyticsLoading.handleErrors(componentScope) {
            errorHandler.handleError(it.exception)
        }

        lifecycle.doOnStart {
            val pagingState = weekAnalyticsLoading.stateFlow.value
            if (pagingState.data == null && pagingState.loadingStatus == PagedLoading.LoadingStatus.None) {
                weekAnalyticsLoading.loadFirstPage()
            }
        }
    }

    override fun onLoadMore() {
        weekAnalyticsLoading.loadNext()
    }

    override fun onWeekPrev() {
        analyticsService.logEvent(AnalyticsEvent.AnalyticsWeekPrevEvent)
    }

    override fun onWeekNext() {
        analyticsService.logEvent(AnalyticsEvent.AnalyticsWeekNextEvent)
    }

    override fun onRecordClick(recordId: RecordId) {
        analyticsService.logEvent(AnalyticsEvent.AnalyticsWeekRecordTapEvent)
        onOutput(WeekAnalyticsComponent.Output.RecordDetailsRequested(recordId))
    }
}