package com.metacards.metacards.features.layout.ui.select_card.ar.sceneview

import co.touchlab.kermit.Logger
import com.google.android.filament.Engine
import com.google.android.filament.Material
import com.google.android.filament.MaterialInstance
import com.google.android.filament.Texture
import com.google.android.filament.TextureSampler
import com.google.ar.core.HitResult
import com.google.ar.core.Plane
import dev.romainguy.kotlin.math.times
import io.github.sceneview.ar.arcore.ArFrame
import io.github.sceneview.ar.arcore.position
import io.github.sceneview.ar.node.ArModelNode
import io.github.sceneview.ar.node.PlacementMode
import io.github.sceneview.math.Position
import io.github.sceneview.math.Rotation
import io.github.sceneview.math.Transform
import io.github.sceneview.math.quaternion
import io.github.sceneview.model.ModelInstance
import io.github.sceneview.node.CameraNode

class ArPlaneNode(engine: Engine, private val cameraNode: CameraNode) :
    ArModelNode(
        engine,
        modelGlbFileLocation = "models/plane.glb",
        scaleToUnits = 0.1f
    ) {
    private val logger = Logger.withTag("ArPlaneNode")
    private var isOnPlane = false
    private var isLayout = false
    private var material: Material? = null
    private var texture: Texture? = null
    private var activeTexture: Texture? = null
    private var materialInstance: MaterialInstance? = null
    private val initialModelRotation = Rotation(x = 90f)

    override val isVisibleInHierarchy: Boolean
        get() = parentNode?.isVisibleInHierarchy != false && isVisible

    init {
        screenPosition = Position(z = -0.5f)
        modelRotation = initialModelRotation
        followHitPosition = false
        applyPoseRotation = false
        isPositionEditable = false
        isRotationEditable = false
        isScaleEditable = false
    }

    override fun onArFrame(arFrame: ArFrame, isCameraTracking: Boolean) {
        super.onArFrame(arFrame, isCameraTracking)

        if (isLayout) return

        val hitResult = hitTest()

        val plane = (hitResult?.trackable as? Plane)
        val isCorrectPlane = plane?.type == Plane.Type.HORIZONTAL_UPWARD_FACING

        if (isCorrectPlane) {
            moveCardOnPlane(hitResult)
        }

        if (isOnPlane && hitResult == null) {
            planeLost()
        }

        if (!isOnPlane && isCorrectPlane) {
            planeDetected()
        }

        if (!isOnPlane) {
            moveCardInFrontOfCamera()
        }
    }

    override fun onModelLoaded(modelInstance: ModelInstance) {
        super.onModelLoaded(modelInstance)
        setupMaterial(material, texture)
    }

    override fun destroy() {
        materialInstance?.let { runCatching { engine.destroyMaterialInstance(it) } }
        materialInstance = null
        super.destroy()
    }

    fun addMaterial(material: Material?, texture: Texture?, activeTexture: Texture?) {
        this.material = material
        this.texture = texture
        this.activeTexture = activeTexture
    }

    fun tryStartLayout(callback: (transform: Transform) -> Unit = {}) {
        if (isOnPlane && !isLayout) {
            isLayout = true
            callback(worldTransform)
            isVisible = false
        }
    }

    private fun moveCardOnPlane(hitResult: HitResult?) {
        position = hitResult!!.hitPose.position
        rotation = rotation.copy(x = 0f, z = 0f, y = cameraNode.rotation.y)
    }

    private fun planeLost() {
        logger.i("Plane lost!")

        modelRotation = initialModelRotation
        isOnPlane = false

        materialInstance?.let { material ->
            texture?.let { texture ->
                material.setParameter("texture", texture, TextureSampler())
            }

            setMaterialInstance(material)
        }
    }

    private fun planeDetected() {
        logger.i("Plane detected")
        isOnPlane = true
        // TODO: add lerp to smooth rotation
        modelRotation = initialModelRotation.copy(x = 0f)
        placementMode = PlacementMode.PLANE_HORIZONTAL

        materialInstance?.let { material ->
            activeTexture?.let { texture ->
                material.setParameter("texture", texture, TextureSampler())
            }

            setMaterialInstance(material)
        }
    }

    private fun moveCardInFrontOfCamera() {
        val cameraTransform = cameraNode.transform
        val cameraPosition = cameraTransform.position
        val cameraForwardVector = cameraTransform.forward * -1f
        val newPosition = cameraPosition + (0.4f * cameraForwardVector)

        transform = Transform(newPosition, cameraTransform.quaternion, scale)
    }

    private fun setupMaterial(material: Material?, texture: Texture?) {
        materialInstance = material?.createInstance() ?: return

        materialInstance?.let { nonNullMaterial ->
            texture?.let { texture ->
                nonNullMaterial.setParameter("texture", texture, TextureSampler())
            }

            setMaterialInstance(nonNullMaterial)
        }
    }
}
