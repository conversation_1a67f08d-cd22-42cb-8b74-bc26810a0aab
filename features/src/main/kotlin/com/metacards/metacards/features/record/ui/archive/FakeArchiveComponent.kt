package com.metacards.metacards.features.record.ui.archive

import com.metacards.metacards.core.utils.createFakeChildStackStateFlow
import com.metacards.metacards.features.record.ui.record_list.FakeRecordListComponent

class FakeArchiveComponent : ArchiveComponent {
    override val childStack = createFakeChildStackStateFlow(
        ArchiveComponent.Child.ArchivedRecordList(FakeRecordListComponent())
    )
}