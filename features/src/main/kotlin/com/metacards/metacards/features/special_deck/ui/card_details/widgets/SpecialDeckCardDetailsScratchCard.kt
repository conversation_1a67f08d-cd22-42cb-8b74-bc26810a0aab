package com.metacards.metacards.features.special_deck.ui.card_details.widgets

import android.view.MotionEvent
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.ClipOp
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.clipPath
import androidx.compose.ui.input.pointer.pointerInteropFilter
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.ui.MetaCardDefaults
import dev.icerock.moko.resources.desc.strResDesc

private const val SCRATCH_LINE_SIZE = 28f

@ExperimentalComposeUiApi
@Composable
fun SpecialDeckCardDetailsScratchCard(
    normalImage: ImageBitmap?,
    blurredImage: ImageBitmap?,
    modifier: Modifier = Modifier,
    movedOffset: Offset?,
    onMovedOffset: (Offset) -> Unit,
    currentPath: Path,
) {

    SpecialDeckCardContainer(
        cardContent = {
            Canvas(
                modifier = modifier
                    .fillMaxSize()
                    .clip(MetaCardDefaults.shape)
                    .pointerInteropFilter {
                        when (it.action) {
                            MotionEvent.ACTION_DOWN -> {
                                currentPath.moveTo(it.x, it.y)
                            }

                            MotionEvent.ACTION_MOVE -> {
                                onMovedOffset(Offset(it.x, it.y))
                            }
                        }
                        true
                    }
            ) {
                val dstSize = IntSize(width = size.width.toInt(), height = size.height.toInt())

                blurredImage?.let { drawImage(image = it, dstSize = dstSize) }

                movedOffset?.let {
                    currentPath.addOval(Rect(it, (SCRATCH_LINE_SIZE * density)))
                }

                clipPath(path = currentPath, clipOp = ClipOp.Intersect) {
                    normalImage?.let { drawImage(image = it, dstSize = dstSize) }
                }
            }
        },
        hintContent = {
            val hintAlpha by animateFloatAsState(
                targetValue = if (blurredImage != null && normalImage != null) 1f else 0f,
                label = "hintAlpha"
            )

            Text(
                text = R.string.special_deck_details_text_scratch.strResDesc().localizedByLocal(),
                style = CustomTheme.typography.body.primary,
                color = CustomTheme.colors.text.primary,
                modifier = Modifier
                    .padding(top = 16.dp)
                    .alpha(hintAlpha)
            )
        },
        isLoading = blurredImage == null || normalImage == null
    )
}