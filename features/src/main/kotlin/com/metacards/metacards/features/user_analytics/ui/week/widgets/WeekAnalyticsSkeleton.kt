package com.metacards.metacards.features.user_analytics.ui.week.widgets

import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.TOTAL_DAYS_IN_WEEK
import com.metacards.metacards.core.widget.placeholder.PlaceholderHighlight
import com.metacards.metacards.core.widget.placeholder.placeholder
import com.metacards.metacards.core.widget.placeholder.shimmer

@Composable
fun WeekAnalyticsSkeleton(modifier: Modifier = Modifier) {
    Column(modifier) {
        Spacer(modifier = Modifier.size(16.dp))
        Row(
            modifier = Modifier
                .height(112.dp)
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.Bottom,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            repeat(TOTAL_DAYS_IN_WEEK) {
                Box(modifier = Modifier.padding(horizontal = 4.dp)) {
                    Box(
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .shimmer(width = 20.dp, height = 100.dp)
                    )
                }
            }
        }
        Divider(
            modifier = Modifier.padding(vertical = 2.dp),
            color = CustomTheme.colors.background.primary,
            thickness = 2.dp
        )
        Row(
            modifier = Modifier
                .height(112.dp)
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.Top,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            repeat(TOTAL_DAYS_IN_WEEK) {
                Box(modifier = Modifier.padding(horizontal = 4.dp)) {
                    Box(
                        modifier = Modifier
                            .align(Alignment.TopCenter)
                            .shimmer(width = 20.dp, height = 100.dp)
                    )
                }
            }
        }
        Spacer(modifier = Modifier.size(8.dp))
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.Bottom,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            repeat(TOTAL_DAYS_IN_WEEK) {
                Box {
                    Box(modifier = Modifier.shimmer(width = 28.dp, height = 28.dp))
                }
            }
        }
        Spacer(modifier = Modifier.size(44.dp))
        Box(
            modifier = Modifier
                .padding(start = 32.dp)
                .shimmer(width = 72.dp, height = 16.dp, shape = 8.dp)
        )
        Spacer(modifier = Modifier.size(8.dp))
        Box(
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .shimmer(width = 340.dp, height = 100.dp, shape = 16.dp)
        )
    }
}

private fun Modifier.shimmer(
    width: Dp,
    height: Dp,
    shape: Dp = 4.dp
): Modifier {
    return size(width = width, height = height)
        .placeholder(
            visible = true,
            color = CustomTheme.colors.background.placeholder,
            shape = RoundedCornerShape(shape),
            highlight = PlaceholderHighlight.shimmer(
                highlightColor = CustomTheme.colors.system.invert.copy(alpha = 0.6f)
            ),
            placeholderFadeTransitionSpec = { tween() },
            contentFadeTransitionSpec = { tween() }
        )
}
