package com.metacards.metacards.features.record.ui.create_record

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.arkivanov.decompose.extensions.compose.jetpack.stack.Children
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.features.record.ui.create_record.add_card.AddCardUi
import com.metacards.metacards.features.record.ui.create_record.camera_permission.CameraPermissionUi
import com.metacards.metacards.features.record.ui.create_record.main.CreateRecordUi
import com.metacards.metacards.features.record.ui.create_record.photo_confirm.ConfirmPhotoUi

@Composable
fun CreateRecordFlowUi(
    component: CreateRecordFlowComponent,
    modifier: Modifier = Modifier
) {
    val childStack by component.childStack.collectAsState()

    Box(
        modifier = modifier
            .safeDrawingPadding()
    ) {
        Children(stack = childStack, modifier = Modifier.fillMaxSize()) { child ->
            when (val instance = child.instance) {
                is CreateRecordFlowComponent.Child.CreateRecord -> {
                    CreateRecordUi(
                        component = instance.component,
                        modifier = Modifier.fillMaxSize()
                    )
                }

                is CreateRecordFlowComponent.Child.PermissionCamera -> {
                    CameraPermissionUi(
                        component = instance.component,
                        modifier = Modifier.fillMaxSize()
                    )
                }

                is CreateRecordFlowComponent.Child.AddCard -> {
                    AddCardUi(
                        component = instance.component,
                        modifier = Modifier.fillMaxSize()
                    )
                }

                is CreateRecordFlowComponent.Child.ConfirmPhoto -> {
                    ConfirmPhotoUi(
                        component = instance.component,
                        modifier = Modifier.fillMaxSize()
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun CreateRecordUiPreview() {
    AppTheme {
        CreateRecordFlowUi(component = FakeCreateRecordFlowComponent())
    }
}
