package com.metacards.metacards.features.showcase.ui.widgets

import androidx.compose.foundation.background
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.features.R
import com.metacards.metacards.features.account.domain.entity.LessonCategory
import dev.icerock.moko.resources.desc.strResDesc

private const val ROW_WIDTH = 312
private const val IMAGE_HEIGHT = 152

@Composable
fun ColumnScope.ShowcaseVideoItem(
    videoLessonCategoriesList: List<LessonCategory>,
    onVideoLessonCategoryClick: (LessonCategory) -> Unit,
) {
    ShowcaseItemContainer(
        title = R.string.showcase_video_title.strResDesc().localizedByLocal(),
        subtitle = R.string.showcase_video_subtitle.strResDesc().localizedByLocal()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .horizontalScroll(rememberScrollState())
        ) {
            val itemModifier = Modifier
                .width(ROW_WIDTH.dp)
                .background(CustomTheme.colors.background.primary, RoundedCornerShape(16.dp))

            Spacer(modifier = Modifier.width(16.dp))

            videoLessonCategoriesList.forEach { lessonCategory ->
                VideoItem(
                    modifier = itemModifier,
                    lessonCategory = lessonCategory,
                    onClick = { onVideoLessonCategoryClick(lessonCategory) }
                )
                Spacer(modifier = Modifier.width(8.dp))
            }

            Spacer(modifier = Modifier.width(8.dp))
        }
    }
}

@Composable
private fun VideoItem(
    lessonCategory: LessonCategory,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .clip(RoundedCornerShape(16.dp))
            .clickable(onClick = onClick)
    ) {
        Spacer(modifier = Modifier.height(16.dp))

        AsyncImage(
            model = lessonCategory.previewUrl,
            contentDescription = null,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .height(IMAGE_HEIGHT.dp)
                .clip(RoundedCornerShape(16.dp)),
            contentScale = ContentScale.Crop
        )

        Spacer(modifier = Modifier.height(12.dp))
        Text(
            text = lessonCategory.name.localizedByLocal(),
            style = CustomTheme.typography.heading.medium,
            color = CustomTheme.colors.text.primary,
            modifier = Modifier
                .padding(horizontal = 16.dp)
        )

        Spacer(modifier = Modifier.height(16.dp))
    }
}