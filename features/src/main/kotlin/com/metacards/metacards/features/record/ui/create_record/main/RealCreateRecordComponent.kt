package com.metacards.metacards.features.record.ui.create_record.main

import android.Manifest
import android.net.Uri
import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.adv.domain.YandexAdvHelper
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.error_handling.safeRun
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.permissions.PermissionService
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.user.domain.UserProvider
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.toInstant
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.deck.domain.entity.CardWithComment
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.record.domain.AddRecordInteractor
import com.metacards.metacards.features.record.domain.Comment
import com.metacards.metacards.features.record.domain.GetCardPhotoUriInteractor
import com.metacards.metacards.features.record.domain.NewRecord
import com.metacards.metacards.features.record.domain.Question
import com.metacards.metacards.features.record.domain.UserFeelings
import com.metacards.metacards.features.record.domain.camera.CardPhoto
import com.metacards.metacards.features.record.ui.model.FeelingItem
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import java.time.LocalDateTime

class RealCreateRecordComponent(
    componentContext: ComponentContext,
    override val inputSource: SharedFlow<CardPhoto?>,
    private val isDailyCardLayout: Boolean,
    userProvider: UserProvider,
    private val addRecordInteractor: AddRecordInteractor,
    private val errorHandler: ErrorHandler,
    private val permissionService: PermissionService,
    private val getCardPhotoUriInteractor: GetCardPhotoUriInteractor,
    private val analyticsService: AnalyticsService,
    private val yandexAdvHelper: YandexAdvHelper,
    private val onOutput: (CreateRecordComponent.Output) -> Unit
) : ComponentContext by componentContext, CreateRecordComponent {
    private val debounce = Debounce()
    private val user = userProvider.getUser()
    override val isFavourite = MutableStateFlow(false)
    override val cardPhoto = MutableStateFlow<CardPhoto?>(null)
    override val question = MutableStateFlow("")
    override val comment = MutableStateFlow("")
    override val energyLevel = MutableStateFlow<FeelingItem<Int>?>(null)
    override val moodLevel = MutableStateFlow<FeelingItem<Int>?>(null)
    override val loadingSaveRecord = MutableStateFlow(false)
    override val userSubscriptionState = computed(user) { it?.subscriptionState }

    private val userFeelings: StateFlow<UserFeelings> =
        computed(moodLevel, energyLevel) { mood, energy ->
            UserFeelings(
                mood = mood?.value?.let { UserFeelings.Mood(it) },
                energy = energy?.value?.let { UserFeelings.Energy(it) }
            )
        }

    override fun onFavoriteClick() {
        if (!isFavourite.value) {
            analyticsService.logEvent(AnalyticsEvent.ManualRecordFavAddEvent)
        }
        isFavourite.update { !it }
    }

    init {
        componentScope.safeLaunch(errorHandler) {
            inputSource.collect { cardPhoto.value = it }
        }
    }

    override fun onAddCardClick() {
        if (user.value == null) {
            onOutput(CreateRecordComponent.Output.AuthSuggestingRequested)
            return
        }

        if (userSubscriptionState.value !is User.SubscriptionState.Ongoing) {
            onOutput(CreateRecordComponent.Output.SubscriptionSuggestingRequested)
        } else {
            analyticsService.logEvent(AnalyticsEvent.ManualRecordCardAddEvent)
            safeRun(errorHandler) {
                if (permissionService.isPermissionGranted(CAMERA_PERMISSION)) {
                    onOutput(CreateRecordComponent.Output.CameraRequested)
                } else {
                    onOutput(CreateRecordComponent.Output.PermissionCameraRequested)
                }
            }
        }
    }

    override fun onQuestionChange(value: String) {
        question.update { value }
    }

    override fun onCommentChange(value: String) {
        comment.update { value }
    }

    override fun onMoodLevelChange(value: FeelingItem<Int>?) {
        moodLevel.update { value }
    }

    override fun onEnergyLevelChange(value: FeelingItem<Int>?) {
        energyLevel.update { value }
    }

    override fun onSaveClick(value: ByteArray?) {
        loadingSaveRecord.update { true }
        DebounceClick(
            debounce = debounce,
            id = "onSaveCreatedRecordClick",
            errorHandler = errorHandler,
            onErrorHandled = { loadingSaveRecord.update { false } }
        ) {
            if (value != null) {
                val uri = getCardPhotoUriInteractor.execute(value)

                if (uri != null) {
                    val newRecord = createNewRecord(
                        question = question.value,
                        comment = comment.value,
                        imageUri = uri.toString(),
                        userFeelings = userFeelings.value,
                        isFavourite = isFavourite.value,
                        isDailyCardLayout = isDailyCardLayout,
                        isViewedAds = yandexAdvHelper.isRewarded
                    )
                    yandexAdvHelper.run { if (isRewarded) clearData() }

                    analyticsService.logEvent(
                        AnalyticsEvent.ManualRecordSaveEvent(
                            isQuestionInput = newRecord.questions.isNotEmpty(),
                            isCommentInput = newRecord.comments.isNotEmpty(),
                            isEnergySet = newRecord.userFeelings.energy != null,
                            isMoodSet = newRecord.userFeelings.mood != null
                        )
                    )

                    addRecordInteractor.execute(newRecord)
                    onOutput(CreateRecordComponent.Output.JournalRequested)
                }
                loadingSaveRecord.update { false }
            }
        }
    }

    override fun onBlockAreaClick() {
        when (userSubscriptionState.value) {
            is User.SubscriptionState.None -> onOutput(CreateRecordComponent.Output.SubscriptionSuggestingRequested)
            is User.SubscriptionState.Ongoing -> Unit
            null -> onOutput(CreateRecordComponent.Output.AuthSuggestingRequested)
        }
    }
}

private fun createNewRecord(
    question: String,
    comment: String,
    imageUri: String,
    userFeelings: UserFeelings,
    isFavourite: Boolean,
    isDailyCardLayout: Boolean,
    isViewedAds: Boolean
): NewRecord {
    return NewRecord(
        questions = listOf(
            Question(
                order = 0,
                text = LocalizableString.createNonLocalizable(question),
                cardsWithComment = listOf(
                    CardWithComment(
                        Card(
                            id = CardId(value = ""),
                            deckId = DeckId(value = ""),
                            imageUrl = imageUri,
                            textureUri = Uri.EMPTY,
                            arObjectUrl = null,
                            gifUrl = null,
                            name = null,
                            help = null
                        ),
                        ""
                    )
                )
            )
        ),
        comments = listOf(
            Comment(
                text = comment,
                creationTime = LocalDateTime.now().toInstant()
            )
        ),
        userFeelings = userFeelings,
        isFavourite = isFavourite,
        isDailyCardLayout = isDailyCardLayout,
        isViewedAds = isViewedAds,
        courseId = null,
        courseThemeId = null

    )
}

private const val CAMERA_PERMISSION = Manifest.permission.CAMERA
