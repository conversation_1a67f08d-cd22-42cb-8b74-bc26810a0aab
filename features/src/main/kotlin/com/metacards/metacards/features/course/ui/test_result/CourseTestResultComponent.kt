package com.metacards.metacards.features.course.ui.test_result

import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CourseResult
import com.metacards.metacards.features.course.domain.entity.CourseThemeId
import com.metacards.metacards.features.layout.domain.CardSource
import com.metacards.metacards.features.layout.domain.LayoutId
import com.metacards.metacards.features.layout.domain.PredefinedLayoutWithAvailable
import kotlinx.coroutines.flow.StateFlow

interface CourseTestResultComponent {

    val courseName: LocalizableString?
    val courseResult: CourseResult
    val isCourseResultSaved: StateFlow<Boolean>
    val predefinedLayouts: StateFlow<List<PredefinedLayoutWithAvailable>>
    val bottomButtonText: String
    val isShareInProgress: StateFlow<Boolean>
    val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent>
    val title: LocalizableString

    fun onGoForwardClick()
    fun onCloseClick()
    fun onShareClick()
    fun onVideoFullScreenClick(position: Long, videoUrl: String)
    fun onLayoutClick(layoutId: LayoutId)
    fun onBlockContentClick()

    sealed interface Output {
        data class GoForwardRequested(val order: Int) : Output
        data object CloseRequested : Output
        data class SubscriptionBottomSheetRequested(val withAdv: Boolean) : Output
        data class LayoutDetailsRequested(
            val cardSource: CardSource,
            val courseId: CourseId?,
            val themeId: CourseThemeId?
        ) : Output
        data object AuthScreenRequested : Output
    }
}