package com.metacards.metacards.features.course.ui.details

import android.content.Context
import android.os.Parcelable
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.stack.ChildStack
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.router.stack.childStack
import com.arkivanov.decompose.router.stack.popWhile
import com.arkivanov.decompose.router.stack.push
import com.arkivanov.decompose.router.stack.replaceCurrent
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.localization.ui.toStringByLocal
import com.metacards.metacards.core.message.data.MessageService
import com.metacards.metacards.core.message.domain.Message
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.toStateFlow
import com.metacards.metacards.features.R
import com.metacards.metacards.features.course.createCourseLessonComponent
import com.metacards.metacards.features.course.createCoursePageComponent
import com.metacards.metacards.features.course.createCoursePassedTestResultComponent
import com.metacards.metacards.features.course.createCourseTestComponent
import com.metacards.metacards.features.course.createCourseTestResultComponent
import com.metacards.metacards.features.course.domain.entity.CourseContentId
import com.metacards.metacards.features.course.domain.entity.CourseContentType
import com.metacards.metacards.features.course.domain.entity.CourseData
import com.metacards.metacards.features.course.domain.entity.CourseDataContent
import com.metacards.metacards.features.course.domain.entity.CourseLesson
import com.metacards.metacards.features.course.domain.entity.CoursePassedTest
import com.metacards.metacards.features.course.domain.entity.CourseResult
import com.metacards.metacards.features.course.domain.entity.CourseTest
import com.metacards.metacards.features.course.domain.entity.CourseTestDocumentId
import com.metacards.metacards.features.course.domain.interactor.GetCourseDataInteractor
import com.metacards.metacards.features.course.ui.lesson.CourseLessonComponent
import com.metacards.metacards.features.course.ui.page.CoursePageComponent
import com.metacards.metacards.features.course.ui.passed_test_result.CoursePassedTestResultComponent
import com.metacards.metacards.features.course.ui.test.CourseTestComponent
import com.metacards.metacards.features.course.ui.test_result.CourseTestResultComponent
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.parcelize.Parcelize

class RealCourseDetailsComponent(
    componentContext: ComponentContext,
    private val componentFactory: ComponentFactory,
    private val onOutput: (CourseDetailsComponent.Output) -> Unit,
    screen: CourseDetailsComponent.Screen,
    private val messageService: MessageService,
    private val context: Context,
    getUserCourseDataInteractor: GetCourseDataInteractor,
) : ComponentContext by componentContext, CourseDetailsComponent {

    private val navigation = StackNavigation<ChildConfig>()

    override val courseQuery: CourseData.Query = screen.getQuery()

    override val courseData = getUserCourseDataInteractor.execute(courseQuery)
        .stateIn(componentScope, SharingStarted.Eagerly, null)

    override val childStack: StateFlow<ChildStack<*, CourseDetailsComponent.Child>> = childStack(
        source = navigation,
        initialConfiguration = screen.toChildConfig(),
        handleBackButton = true,
        childFactory = ::createChild
    ).toStateFlow(lifecycle)

    private fun createChild(
        config: ChildConfig,
        componentContext: ComponentContext
    ): CourseDetailsComponent.Child = when (config) {

        is ChildConfig.CoursePage -> CourseDetailsComponent.Child.CoursePage(
            componentFactory.createCoursePageComponent(
                componentContext,
                ::onCourseOutput,
                config.query
            )
        )

        is ChildConfig.Lesson -> CourseDetailsComponent.Child.Lesson(
            componentFactory.createCourseLessonComponent(
                componentContext,
                ::onLessonOutput,
                config.query,
                config.lastCardButtonText,
                config.order,
                courseData.value?.name,
            )
        )

        is ChildConfig.Test -> CourseDetailsComponent.Child.Test(
            componentFactory.createCourseTestComponent(
                componentContext,
                config.query,
                config.order,
                courseData.value?.name,
                ::onTestOutput
            )
        )

        is ChildConfig.TestResult -> CourseDetailsComponent.Child.TestResult(
            componentFactory.createCourseTestResultComponent(
                componentContext,
                ::onCourseResultOutput,
                config.result,
                config.dataToSave,
                config.buttonText,
                config.order,
                courseData.value?.name,
            )
        )

        is ChildConfig.PassedTestResult -> CourseDetailsComponent.Child.PassedTestResult(
            componentFactory.createCoursePassedTestResultComponent(
                componentContext,
                config.testId,
                config.testResultId,
                courseData.value?.name,
                ::onPassedTestResultOutput
            )
        )
    }

    private fun onPassedTestResultOutput(output: CoursePassedTestResultComponent.Output) {
        when (output) {
            CoursePassedTestResultComponent.Output.CloseRequested -> popToPageOrClose()
            is CoursePassedTestResultComponent.Output.RepeatTestRequested ->
                navigateToContentById(output.contentId)

            CoursePassedTestResultComponent.Output.AuthScreenRequested ->
                onOutput(CourseDetailsComponent.Output.AuthScreenRequested)

            is CoursePassedTestResultComponent.Output.LayoutDetailsRequested -> onOutput(
                CourseDetailsComponent.Output.LayoutDetailsRequested(
                    cardSource = output.cardSource,
                    courseId = output.courseId,
                    themeId = output.themeId
                )
            )

            is CoursePassedTestResultComponent.Output.SubscriptionBottomSheetRequested ->
                onOutput(CourseDetailsComponent.Output.SubscriptionBottomSheetRequested(output.withAdv))
        }
    }

    private fun onCourseResultOutput(output: CourseTestResultComponent.Output) {
        when (output) {
            is CourseTestResultComponent.Output.GoForwardRequested ->
                navigateToNextCoursePageContent(output.order)
            CourseTestResultComponent.Output.CloseRequested -> popToPageOrClose()

            CourseTestResultComponent.Output.AuthScreenRequested ->
                onOutput(CourseDetailsComponent.Output.AuthScreenRequested)

            is CourseTestResultComponent.Output.LayoutDetailsRequested -> onOutput(
                CourseDetailsComponent.Output.LayoutDetailsRequested(
                    cardSource = output.cardSource,
                    courseId = output.courseId,
                    themeId = output.themeId
                )
            )

            is CourseTestResultComponent.Output.SubscriptionBottomSheetRequested ->
                onOutput(CourseDetailsComponent.Output.SubscriptionBottomSheetRequested(output.withAdv))
        }
    }

    private fun onTestOutput(output: CourseTestComponent.Output) {
        when (output) {
            is CourseTestComponent.Output.CourseResultRequested ->
                navigation.replaceCurrent(
                    ChildConfig.TestResult(
                        result = output.result,
                        dataToSave = output.dataToSave,
                        buttonText = getCourseResultButtonText(output.order),
                        order = output.order
                    )
                )

            CourseTestComponent.Output.CourseResultNotFound -> {
                showToastSomethingWentWrong()
                popToPageOrClose()
            }

            CourseTestComponent.Output.CloseRequested -> popToPageOrClose()
        }
    }

    private fun onLessonOutput(output: CourseLessonComponent.Output) {
        when (output) {
            is CourseLessonComponent.Output.NextContentRequested ->
                navigateToNextCoursePageContent(output.order)
            CourseLessonComponent.Output.CloseRequested -> popToPageOrClose()
        }
    }

    private fun onCourseOutput(output: CoursePageComponent.Output) {
        when (output) {
            is CoursePageComponent.Output.TestRequested -> navigation.push(
                ChildConfig.Test(output.query, output.order)
            )
            is CoursePageComponent.Output.LessonRequested ->
                navigation.push(
                    ChildConfig.Lesson(
                        query = output.query,
                        lastCardButtonText = getLessonLastCardButtonText(output.order),
                        order = output.order
                    )
                )

            is CoursePageComponent.Output.TestResultsRequested ->
                navigation.push(ChildConfig.PassedTestResult(output.testId, output.testResultId))

            CoursePageComponent.Output.AuthSuggestingRequested ->
                onOutput(CourseDetailsComponent.Output.AuthScreenRequested)
            CoursePageComponent.Output.PremiumSuggestingRequested ->
                onOutput(CourseDetailsComponent.Output.SubscriptionBottomSheetRequested(false))
            CoursePageComponent.Output.CloseRequested ->
                onOutput(CourseDetailsComponent.Output.CloseRequested)
        }
    }

    private fun navigateToNextCoursePageContent(order: Int) {
        val nextContent = calculateNextContent(order)
        val config = nextContent?.let { content ->
            when (content.type) {
                CourseContentType.LESSON -> {
                    val query = CourseLesson.Query(
                        themeId = courseQuery.themeId,
                        courseId = courseQuery.courseId,
                        lessonId = content.contentId
                    )
                    ChildConfig.Lesson(query, getLessonLastCardButtonText(nextContent.order), nextContent.order)
                }

                CourseContentType.TEST -> {
                    val query = CourseTest.Query(
                        themeId = courseQuery.themeId,
                        courseId = courseQuery.courseId,
                        testId = content.contentId
                    )
                    ChildConfig.Test(query, nextContent.order)
                }
            }
        }
        config?.let {
            navigation.replaceCurrent(config)
        } ?: popToPageOrClose()
    }

    private fun navigateToContentById(contentId: CourseContentId) {
        val content = courseData.value
            ?.content?.find { it.contentId == contentId }
        val config = content?.let { c ->
            when (c.type) {
                CourseContentType.LESSON -> {
                    val query = CourseLesson.Query(
                        themeId = courseQuery.themeId,
                        courseId = courseQuery.courseId,
                        lessonId = c.contentId
                    )
                    ChildConfig.Lesson(query, getLessonLastCardButtonText(content.order), content.order)
                }

                CourseContentType.TEST -> {
                    val query = CourseTest.Query(
                        themeId = courseQuery.themeId,
                        courseId = courseQuery.courseId,
                        testId = c.contentId
                    )
                    ChildConfig.Test(query, content.order)
                }
            }
        }
        config?.let {
            navigation.replaceCurrent(config)
        } ?: popToPageOrClose()
    }

    private fun popToPageOrClose() {
        val pageInBackStack =
            childStack.value.backStack.any { it.instance is CourseDetailsComponent.Child.CoursePage }
        if (pageInBackStack) {
            navigation.popWhile { it !is ChildConfig.CoursePage }
        } else {
            onOutput(CourseDetailsComponent.Output.CloseRequested)
        }
    }

    private fun getLessonLastCardButtonText(order: Int): String {
        val nextContent = calculateNextContent(order)
        val resultRes = nextContent?.let { content ->
            when (content.type) {
                CourseContentType.LESSON -> R.string.course_to_next_lesson_button.strResDesc()

                CourseContentType.TEST -> R.string.course_to_test_button.strResDesc()
            }
        } ?: R.string.course_to_results_button.strResDesc()
        return resultRes.toStringByLocal(context)
    }

    private fun getCourseResultButtonText(order: Int): String {
        val nextContent = calculateNextContent(order)
        val resultRes = nextContent?.let { next ->
            when (next.type) {
                CourseContentType.LESSON ->
                    R.string.course_next_lesson_button.strResDesc()

                CourseContentType.TEST -> R.string.course_next_test_button.strResDesc()
            }
        } ?: R.string.course_finish_button.strResDesc()
        return resultRes.toStringByLocal(context)
    }

    private fun calculateNextContent(currentContentOrder: Int): CourseDataContent? =
        courseData.value?.content?.find { it.order == currentContentOrder + 1 }

    private fun showToastSomethingWentWrong() =
        messageService.showMessage(Message(text = StringDesc.Resource(com.metacards.metacards.core.R.string.error_unexpected)))

    private sealed interface ChildConfig : Parcelable {

        @Parcelize
        data class CoursePage(val query: CourseData.Query) : ChildConfig

        @Parcelize
        data class Lesson(
            val query: CourseLesson.Query,
            val lastCardButtonText: String,
            val order: Int
        ) : ChildConfig

        @Parcelize
        data class Test(
            val query: CourseTest.Query,
            val order: Int
        ) : ChildConfig

        @Parcelize
        data class TestResult(
            val result: CourseResult,
            val dataToSave: CoursePassedTest,
            val buttonText: String,
            val order: Int
        ) : ChildConfig

        @Parcelize
        data class PassedTestResult(
            val testId: CourseContentId,
            val testResultId: CourseTestDocumentId?,
        ) : ChildConfig
    }

    private fun CourseDetailsComponent.Screen.toChildConfig(): ChildConfig = when (this) {
        is CourseDetailsComponent.Screen.Course -> ChildConfig.CoursePage(courseQuery)
        is CourseDetailsComponent.Screen.CoursePassedTestResult -> ChildConfig.PassedTestResult(testId, testResultId)
    }

    private fun CourseDetailsComponent.Screen.getQuery(): CourseData.Query = when (this) {
        is CourseDetailsComponent.Screen.Course -> courseQuery
        is CourseDetailsComponent.Screen.CoursePassedTestResult -> courseQuery
    }
}