package com.metacards.metacards.features.account.data

import com.metacards.metacards.core.functions.FunctionResult
import com.metacards.metacards.features.account.data.data_source.PromocodesDataSource
import com.metacards.metacards.features.account.data.dto.toDomain
import com.metacards.metacards.features.account.domain.entity.Promocode
import com.metacards.metacards.features.account.domain.entity.PromocodeInfo
import com.metacards.metacards.features.account.domain.repository.PromocodesRepository

class PromocodesRepositoryImpl(
    private val promocodesDataSource: PromocodesDataSource
) : PromocodesRepository {
    override suspend fun getPromocodes(): List<PromocodeInfo> {
        return promocodesDataSource.getPromocodes().map { it.toDomain() }
    }

    override suspend fun activatePromocode(promocode: Promocode): FunctionResult<Map<String, Any>> {
        return promocodesDataSource.activatePromocode(promocode.value)
    }
}