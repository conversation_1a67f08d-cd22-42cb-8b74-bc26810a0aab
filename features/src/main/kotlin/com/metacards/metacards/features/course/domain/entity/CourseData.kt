package com.metacards.metacards.features.course.domain.entity

import android.os.Parcelable
import com.metacards.metacards.core.localization.ui.LocalizableString
import kotlinx.parcelize.Parcelize

data class CourseData(
    val name: LocalizableString,
    val description: LocalizableString?,
    val shortDescription: LocalizableString?,
    val profits: List<CourseProfit>?,
    val coverUrl: String,
    val duration: Int?,
    val availability: CourseAvailability,
    val lessonsAmount: Int?,
    val testsAmount: Int?,
    val content: List<CourseDataContent>,
    val status: CourseStatus
) {
    @Parcelize
    data class Query(
        val themeId: CourseThemeId,
        val courseId: CourseId
    ) : Parcelable {
        companion object {
            val mock = Query(themeId = CourseThemeId("1"), courseId = CourseId("1"))
        }
    }

    companion object {
        val mock = CourseData(
            name = LocalizableString.createNonLocalizable("Name"),
            description = LocalizableString.createNonLocalizable("description"),
            shortDescription = LocalizableString.createNonLocalizable("shortDescription"),
            profits = listOf(),
            coverUrl = "https://duckduckgo.com/?q=epicurei",
            duration = 5,
            availability = CourseAvailability.FREE,
            lessonsAmount = 4,
            testsAmount = 3,
            content = listOf(),
            status = CourseStatus.ACTIVE
        )
    }
}
