package com.metacards.metacards.features.user_analytics.ui.week.widgets

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.Divider
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.features.record.domain.LevelType
import com.metacards.metacards.features.record.domain.Record
import com.metacards.metacards.features.record.ui.model.FeelingItem.Companion.mars_list
import com.metacards.metacards.features.record.ui.model.FeelingItem.Companion.moon_list
import com.metacards.metacards.features.user_analytics.domain.week.WeekAnalyticsInfo
import com.metacards.metacards.features.user_analytics.ui.layouts.MAX_VISIBLE_CANDLES
import com.metacards.metacards.features.user_analytics.ui.layouts.wiget.AnalyticsCandle
import com.metacards.metacards.features.user_analytics.ui.layouts.wiget.MAX_LEVEL_TYPE
import com.metacards.metacards.features.user_analytics.ui.layouts.wiget.SHARE_CANDLE_WIDTH_ON_GRAPHICS
import com.metacards.metacards.features.user_analytics.ui.layouts.wiget.SHARE_DATE_WIDTH_ON_GRAPHICS
import com.metacards.metacards.features.user_analytics.ui.layouts.wiget.SHARE_GRAPHICS
import com.metacards.metacards.features.user_analytics.ui.layouts.wiget.SHARE_HALF_CANDLE_HEIGHT_ON_GRAPHICS
import kotlinx.datetime.LocalDate
import java.time.format.TextStyle

@Composable
fun WeekAnalyticsGraph(analyticsInfo: WeekAnalyticsInfo, modifier: Modifier = Modifier) {
    val locale = LocalContext.current.resources.configuration.locales[0]
    val boxHeight = (LocalConfiguration.current.screenHeightDp * SHARE_GRAPHICS).toInt()
    val boxWidth = ((LocalConfiguration.current.screenWidthDp) / MAX_VISIBLE_CANDLES).dp
    val candleHeight = (boxHeight * SHARE_HALF_CANDLE_HEIGHT_ON_GRAPHICS).toInt().dp

    Column(modifier) {
        LevelCandles(
            records = analyticsInfo.records,
            weekDays = analyticsInfo.weekDays,
            levelType = LevelType.Energy,
            oneLevelTypeHeight = candleHeight / MAX_LEVEL_TYPE,
            candleWidth = boxWidth / SHARE_CANDLE_WIDTH_ON_GRAPHICS,
            candleHeight = candleHeight
        )

        Divider(
            modifier = Modifier.padding(vertical = 2.dp),
            color = CustomTheme.colors.background.primary,
            thickness = 2.dp
        )

        LevelCandles(
            records = analyticsInfo.records,
            weekDays = analyticsInfo.weekDays,
            levelType = LevelType.Mood,
            oneLevelTypeHeight = candleHeight / MAX_LEVEL_TYPE,
            candleWidth = boxWidth / SHARE_CANDLE_WIDTH_ON_GRAPHICS,
            candleHeight = candleHeight
        )

        Spacer(modifier = Modifier.size(8.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.Bottom,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            analyticsInfo.weekDays.forEach { date ->
                Column(
                    modifier = Modifier
                        .padding(horizontal = boxWidth / SHARE_PADDING_DATE_ON_GRAPHICS)
                        .width(boxWidth / SHARE_DATE_WIDTH_ON_GRAPHICS),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = date.dayOfMonth.toString(),
                        color = CustomTheme.colors.text.secondary,
                        style = CustomTheme.typography.caption.small

                    )
                    Text(
                        text = date.month.getDisplayName(TextStyle.SHORT, locale),
                        maxLines = 1,
                        color = CustomTheme.colors.text.secondary,
                        style = CustomTheme.typography.caption.small
                    )
                }
            }
        }
    }
}

@Composable
private fun LevelCandles(
    records: List<Record>,
    weekDays: List<LocalDate>,
    levelType: LevelType,
    oneLevelTypeHeight: Dp,
    candleWidth: Dp,
    candleHeight: Dp
) {
    Row(
        modifier = Modifier
            .height(candleHeight)
            .fillMaxWidth(),
        verticalAlignment = when (levelType) {
            LevelType.Mood -> Alignment.Top
            LevelType.Energy -> Alignment.Bottom
        },
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        weekDays.forEach { date ->
            val record = when (levelType) {
                LevelType.Mood -> records.lastOrNull { it.creationDate == date }
                LevelType.Energy -> records.lastOrNull { it.creationDate == date }
            }

            val icon = when (levelType) {
                LevelType.Mood -> moon_list.firstOrNull { it.value == record?.moodLevel }?.imageResource
                LevelType.Energy -> mars_list.firstOrNull { it.value == record?.energyLevel }?.imageResource
            }

            val candleLevel = when (levelType) {
                LevelType.Mood -> record?.moodLevel
                LevelType.Energy -> record?.energyLevel
            }

            Box(modifier = Modifier.padding(horizontal = candleWidth)) {
                if (candleLevel != null && icon != null) {
                    AnalyticsCandle(
                        candleLevel = candleLevel,
                        levelType = levelType,
                        oneLevelTypeHeight = oneLevelTypeHeight,
                        candleWidth = candleWidth
                    )

                    Image(
                        modifier = Modifier
                            .size(candleWidth)
                            .align(
                                when (levelType) {
                                    LevelType.Mood -> Alignment.BottomCenter
                                    LevelType.Energy -> Alignment.TopCenter
                                }
                            ),
                        painter = painterResource(id = icon),
                        contentDescription = null
                    )
                } else {
                    Spacer(modifier = Modifier.size(candleWidth))
                }
            }
        }
    }
}

private const val SHARE_PADDING_DATE_ON_GRAPHICS = 4
