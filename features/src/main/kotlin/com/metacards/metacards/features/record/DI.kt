package com.metacards.metacards.features.record

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CourseThemeId
import com.metacards.metacards.features.record.data.CameraDataSource
import com.metacards.metacards.features.record.data.CameraRepositoryImpl
import com.metacards.metacards.features.record.data.RecordRepository
import com.metacards.metacards.features.record.data.RecordRepositoryImpl
import com.metacards.metacards.features.record.data.RecordsDataSource
import com.metacards.metacards.features.record.domain.AddRecordInteractor
import com.metacards.metacards.features.record.domain.GetCardPhotoUriInteractor
import com.metacards.metacards.features.record.domain.GetRecordByIdInteractor
import com.metacards.metacards.features.record.domain.GetRecordsBetweenTimesInteractor
import com.metacards.metacards.features.record.domain.GetUserPassedTestsInteractor
import com.metacards.metacards.features.record.domain.RecordData
import com.metacards.metacards.features.record.domain.RecordListType
import com.metacards.metacards.features.record.domain.RecordSource
import com.metacards.metacards.features.record.domain.RecordsPagedLoading
import com.metacards.metacards.features.record.domain.UpdateRecordInteractor
import com.metacards.metacards.features.record.domain.camera.CameraRepository
import com.metacards.metacards.features.record.domain.camera.CardPhoto
import com.metacards.metacards.features.record.domain.camera.DeletePhotoFromGalleryInteractor
import com.metacards.metacards.features.record.domain.camera.TakePhotoInteractor
import com.metacards.metacards.features.record.ui.archive.ArchiveComponent
import com.metacards.metacards.features.record.ui.archive.RealArchiveComponent
import com.metacards.metacards.features.record.ui.calendar.CalendarComponent
import com.metacards.metacards.features.record.ui.calendar.RealCalendarComponent
import com.metacards.metacards.features.record.ui.create_record.CreateRecordFlowComponent
import com.metacards.metacards.features.record.ui.create_record.RealCreateRecordFlowComponent
import com.metacards.metacards.features.record.ui.create_record.add_card.AddCardComponent
import com.metacards.metacards.features.record.ui.create_record.add_card.RealAddCardComponent
import com.metacards.metacards.features.record.ui.create_record.add_card.image_tracking.RealImageTrackingComponent
import com.metacards.metacards.features.record.ui.create_record.camera_permission.CameraPermissionComponent
import com.metacards.metacards.features.record.ui.create_record.camera_permission.RealCameraPermissionComponent
import com.metacards.metacards.features.record.ui.create_record.main.CreateRecordComponent
import com.metacards.metacards.features.record.ui.create_record.main.RealCreateRecordComponent
import com.metacards.metacards.features.record.ui.create_record.photo_confirm.ConfirmPhotoComponent
import com.metacards.metacards.features.record.ui.create_record.photo_confirm.RealConfirmPhotoComponent
import com.metacards.metacards.features.record.ui.journal.JournalComponent
import com.metacards.metacards.features.record.ui.journal.RealJournalComponent
import com.metacards.metacards.features.record.ui.journal.test_list.JournalTestListComponent
import com.metacards.metacards.features.record.ui.journal.test_list.RealJournalTestListComponent
import com.metacards.metacards.features.record.ui.record_details.RealRecordDetailsComponent
import com.metacards.metacards.features.record.ui.record_details.RecordDetailsComponent
import com.metacards.metacards.features.record.ui.record_details.comment_details.CommentDetailsComponent
import com.metacards.metacards.features.record.ui.record_details.comment_details.RealCommentDetailsComponent
import com.metacards.metacards.features.record.ui.record_list.RealRecordListComponent
import com.metacards.metacards.features.record.ui.record_list.RecordListComponent
import com.metacards.metacards.features.user.ui.subscription.subscription_bottom_sheet.RealSubscriptionBottomSheetComponent
import com.metacards.metacards.features.user.ui.subscription.subscription_bottom_sheet.SubscriptionBottomSheetComponent
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import org.koin.core.component.get
import org.koin.core.qualifier.qualifier
import org.koin.dsl.module

val recordModule = module {
    single { RecordsDataSource(get(), get(), get()) }
    single { CameraDataSource(get()) }
    single<RecordRepository> { RecordRepositoryImpl(get(), get(), lazy { get() }) }
    single<CameraRepository> { CameraRepositoryImpl(get()) }
    RecordListType.entries.forEach { listType ->
        single(qualifier(listType)) { RecordsPagedLoading(listType, get(), get()) }
    }
    factory { AddRecordInteractor(get()) }
    factory { GetRecordsBetweenTimesInteractor(get()) }
    factory { GetRecordByIdInteractor(get(), get()) }
    factory { UpdateRecordInteractor(get(), get()) }
    factory { TakePhotoInteractor(get()) }
    factory { DeletePhotoFromGalleryInteractor(get()) }
    factory { GetCardPhotoUriInteractor(get()) }
    factory { GetUserPassedTestsInteractor(get()) }
}

fun ComponentFactory.createJournalComponent(
    componentContext: ComponentContext,
    onOutput: (JournalComponent.Output) -> Unit
): JournalComponent {
    return RealJournalComponent(
        componentContext,
        get(),
        allRecordsPagedLoading = get(qualifier(RecordListType.All)),
        favouriteRecordsPagedLoading = get(qualifier(RecordListType.Favourite)),
        get(),
        onOutput
    )
}

fun ComponentFactory.createRecordListComponent(
    componentContext: ComponentContext,
    listType: RecordListType,
    onOutput: (RecordListComponent.Output) -> Unit
): RecordListComponent {
    return RealRecordListComponent(
        componentContext,
        get(),
        listType,
        get(qualifier(listType)),
        get(),
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createRecordDetailsComponent(
    componentContext: ComponentContext,
    componentFactory: ComponentFactory,
    recordType: RecordDetailsComponent.RecordType,
    isDailyCard: Boolean,
    recordSource: RecordSource,
    recordData: RecordData?,
    courseId: CourseId?,
    courseThemeId: CourseThemeId?,
    onOutput: (RecordDetailsComponent.Output) -> Unit,
): RecordDetailsComponent = RealRecordDetailsComponent(
    componentContext,
    componentFactory,
    get(),
    get(),
    recordType,
    isDailyCard,
    recordSource,
    recordData,
    courseId,
    courseThemeId,
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    onOutput
)

fun ComponentFactory.createCalendarComponent(
    componentContext: ComponentContext,
    onOutput: (CalendarComponent.Output) -> Unit
): CalendarComponent {
    return RealCalendarComponent(
        componentContext,
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createArchiveComponent(
    componentContext: ComponentContext,
    componentFactory: ComponentFactory,
    onOutput: (ArchiveComponent.Output) -> Unit
): ArchiveComponent {
    return RealArchiveComponent(
        componentContext,
        componentFactory,
        onOutput
    )
}

fun ComponentFactory.createSubscriptionBottomSheetComponent(
    componentContext: ComponentContext,
    isSpecialDeckCardRequested: Boolean,
    isWithAdv: Boolean,
    onOutput: (SubscriptionBottomSheetComponent.Output) -> Unit
): SubscriptionBottomSheetComponent {
    return RealSubscriptionBottomSheetComponent(
        componentContext,
        isSpecialDeckCardRequested = isSpecialDeckCardRequested,
        isWithAdv = isWithAdv,
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createCommentDetailsComponent(
    componentContext: ComponentContext,
    commentText: String,
    commentCreationDate: String?,
    cardImageUrl: String?,
    onCardClick: (() -> Unit)?,
    onOutput: (CommentDetailsComponent.Output) -> Unit
): CommentDetailsComponent {
    return RealCommentDetailsComponent(
        componentContext,
        commentText,
        commentCreationDate,
        cardImageUrl,
        onCardClick,
        onOutput
    )
}

fun ComponentFactory.createCreateRecordComponent(
    componentContext: ComponentContext,
    inputSource: SharedFlow<CardPhoto?>,
    isDailyCard: Boolean,
    onOutput: (CreateRecordComponent.Output) -> Unit
): CreateRecordComponent {
    return RealCreateRecordComponent(
        componentContext,
        inputSource,
        isDailyCard,
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createCreateRecordFlowComponent(
    componentContext: ComponentContext,
    screenToShow: CreateRecordFlowComponent.Screen,
    onOutput: (CreateRecordFlowComponent.Output) -> Unit
): RealCreateRecordFlowComponent {
    return RealCreateRecordFlowComponent(
        componentContext,
        screenToShow,
        get(),
        onOutput
    )
}

fun ComponentFactory.createCameraPermissionComponent(
    componentContext: ComponentContext,
    onOutput: (CameraPermissionComponent.Output) -> Unit
): RealCameraPermissionComponent {
    return RealCameraPermissionComponent(
        componentContext,
        get(),
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createCameraComponent(
    componentContext: ComponentContext,
    onOutput: (AddCardComponent.Output) -> Unit
): RealAddCardComponent {
    return RealAddCardComponent(
        componentContext,
        lazy { RealImageTrackingComponent() },
        get(),
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createConfirmPhotoComponent(
    componentContext: ComponentContext,
    cardPhoto: CardPhoto,
    onOutput: (ConfirmPhotoComponent.Output) -> Unit
): RealConfirmPhotoComponent {
    return RealConfirmPhotoComponent(
        componentContext,
        MutableStateFlow(cardPhoto),
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createJournalTestsListComponent(
    componentContext: ComponentContext,
    output: (JournalTestListComponent.Output) -> Unit
): JournalTestListComponent = RealJournalTestListComponent(
    componentContext,
    get(),
    output
)
