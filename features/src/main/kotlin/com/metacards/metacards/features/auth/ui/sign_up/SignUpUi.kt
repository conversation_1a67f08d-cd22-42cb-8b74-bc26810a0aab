package com.metacards.metacards.features.auth.ui.sign_up

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.arkivanov.decompose.extensions.compose.jetpack.stack.Children
import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.IconNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.auth.ui.auth_type.email.EmailEnterUi
import com.metacards.metacards.features.auth.ui.auth_type.email.message.SentMessageToEmailUi
import com.metacards.metacards.features.auth.ui.auth_type.pass.create_pass.CreatePasswordUi
import dev.icerock.moko.resources.desc.strResDesc
import com.metacards.metacards.core.R as CoreR

@Composable
fun SignUpUi(component: SignUpComponent) {
    val childStack by component.childStack.collectAsState()

    BoxWithFade(
        modifier = Modifier
            .fillMaxSize()
            .imePadding(),
        listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        Column(
            modifier = Modifier.padding(horizontal = 16.dp),
            horizontalAlignment = Alignment.Start,
        ) {
            val icon = getToolbarIcon(childStack)

            TopNavigationBar(
                contentPadding = PaddingValues(vertical = 16.dp),
                title = R.string.sign_up_toolbar_text.strResDesc(),
                leadingIcon = icon
            )

            Children(stack = childStack) { child ->
                when (val instance = child.instance) {
                    is SignUpComponent.Child.EmailEnter -> {
                        EmailEnterContent(instance)
                    }

                    is SignUpComponent.Child.CreatePassword -> {
                        CreatePasswordUi(component = instance.component)
                    }

                    is SignUpComponent.Child.SentMessageToEmail -> SentMessageToEmailUi(component = instance.component)
                }
            }
        }
    }
}

@Composable
private fun EmailEnterContent(
    instance: SignUpComponent.Child.EmailEnter
) {
    Column {
        Text(
            text = R.string.auth_user_type_email.strResDesc().localizedByLocal(),
            style = CustomTheme.typography.caption.large,
            color = CustomTheme.colors.text.primary,
            modifier = Modifier.padding(bottom = 16.dp, top = 60.dp)
        )

        EmailEnterUi(
            component = instance.component,
            isSignIn = false,
            nextButtonText = CoreR.string.common_proceed.strResDesc().localizedByLocal()
        )
    }
}

@Composable
private fun getToolbarIcon(
    childStack: ChildStack<*, SignUpComponent.Child>
): @Composable (RowScope.() -> Unit) =
    when (val instance = childStack.active.instance) {
        is SignUpComponent.Child.SentMessageToEmail -> {
            {
                IconNavigationItem(
                    iconRes = R.drawable.ic_24_close,
                    onClick = instance.component::onCloseButtonClick
                )
            }
        }

        else -> @Composable {
            { BackNavigationItem() }
        }
    }

@Preview
@Composable
fun SignUpUiPreview() {
    val coroutineScope = rememberCoroutineScope()
    AppTheme {
        SignUpUi(component = FakeSignUpComponent(coroutineScope))
    }
}