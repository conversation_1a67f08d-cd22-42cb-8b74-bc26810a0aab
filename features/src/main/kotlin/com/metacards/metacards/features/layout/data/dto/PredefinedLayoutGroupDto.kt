package com.metacards.metacards.features.layout.data.dto

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.layout.domain.LayoutGroupId
import com.metacards.metacards.features.layout.domain.LayoutId
import com.metacards.metacards.features.layout.domain.PredefinedLayoutGroup

data class PredefinedLayoutGroupDto(
    val id: String = "",
    val layouts: List<String> = emptyList(),
    val nameLocalized: Map<String, String?> = emptyMap(),
)

fun PredefinedLayoutGroupDto.toDomain() = PredefinedLayoutGroup(
    id = LayoutGroupId(id),
    layoutIds = layouts.map(::LayoutId),
    name = LocalizableString(nameLocalized)
)
