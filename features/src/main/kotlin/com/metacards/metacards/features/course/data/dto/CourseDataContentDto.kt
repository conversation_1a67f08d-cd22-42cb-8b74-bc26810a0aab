package com.metacards.metacards.features.course.data.dto

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.course.domain.entity.CourseContentId
import com.metacards.metacards.features.course.domain.entity.CourseContentType
import com.metacards.metacards.features.course.domain.entity.CourseDataContent

data class CourseDataContentDto(
    val contentId: String = "",
    val name: Map<String, String?> = emptyMap(),
    val order: Int = 0,
    val type: String = ""
) {
    fun toDomain() = CourseDataContent(
        contentId = CourseContentId(contentId),
        name = LocalizableString(name),
        order = order,
        type = CourseContentType.fromString(type),
        isCompleted = false
    )
}