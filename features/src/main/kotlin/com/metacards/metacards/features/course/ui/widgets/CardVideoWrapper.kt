package com.metacards.metacards.features.course.ui.widgets

import android.annotation.SuppressLint
import android.content.Context
import android.net.Uri
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.ui.MetaCardDefaults
import com.metacards.metacards.features.video_player.VideoPlayerActivity
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import java.util.UUID

typealias PauseAnotherVideosEvent = MutableSharedFlow<UUID>

fun createPauseAnotherVideosEvent() = MutableSharedFlow<UUID>(
    extraBufferCapacity = 1,
    onBufferOverflow = BufferOverflow.DROP_OLDEST
)

@androidx.annotation.OptIn(UnstableApi::class)
@SuppressLint("OpaqueUnitKey")
@Composable
fun CardVideoWrapper(
    context: Context,
    videoUrl: String,
    videoCoverUrl: String?,
    onFullScreenClick: (Long) -> Unit,
    modifier: Modifier = Modifier,
    pauseAnotherVideosEvent: PauseAnotherVideosEvent? = null,
    shape: Shape = RoundedCornerShape(30.dp),
    border: BorderStroke? = BorderStroke(0.5.dp, CustomTheme.colors.stroke.secondary),
) {
    key(videoUrl) {
        var onPlayClicked by remember { mutableStateOf(false) }

        if (!onPlayClicked) {
            CardVideoPreview(
                context = context,
                videoCoverUrl = videoCoverUrl,
                onPlayClick = { onPlayClicked = true },
                shape = shape,
                border = border,
                modifier = modifier
            )
        } else {
            val playerUUID = remember { UUID.randomUUID() }

            val exoPlayer = remember {
                ExoPlayer.Builder(context).build().apply {
                    setMediaItem(MediaItem.fromUri(Uri.parse(videoUrl)))
                    playWhenReady = true
                    prepare()
                    addListener(
                        object : Player.Listener {
                            override fun onIsPlayingChanged(isPlaying: Boolean) {
                                if (isPlaying) {
                                    pauseAnotherVideosEvent?.tryEmit(playerUUID)
                                }
                            }
                        }
                    )
                }
            }

            LaunchedEffect(Unit) {
                VideoPlayerActivity.finishPositionCallBack
                    .onEach { (url, position) ->
                        if (url == videoUrl) {
                            exoPlayer.seekTo(position)
                            exoPlayer.play()
                        }
                    }
                    .launchIn(this)
            }

            val hidePlayerControllerCommand = remember {
                MutableSharedFlow<Unit>(
                    extraBufferCapacity = 1,
                    onBufferOverflow = BufferOverflow.DROP_OLDEST
                )
            }

            LaunchedEffect(pauseAnotherVideosEvent) {
                pauseAnotherVideosEvent
                    ?.onEach { anotherPlayerUUID ->
                        if (playerUUID != anotherPlayerUUID) {
                            exoPlayer.pause()
                            hidePlayerControllerCommand.tryEmit(Unit)
                        }
                    }
                    ?.launchIn(this)
            }

            DisposableEffect(
                CardVideo(
                    context = context,
                    onFullScreenClick = {
                        exoPlayer.pause()
                        onFullScreenClick(exoPlayer.currentPosition)
                    },
                    exoPlayer = exoPlayer,
                    hidePlayerControllerCommand = hidePlayerControllerCommand,
                    shape = shape,
                    border = border,
                    modifier = modifier
                )
            ) { onDispose { exoPlayer.release() } }
        }
    }
}

@Composable
private fun CardVideoPreview(
    context: Context,
    videoCoverUrl: String?,
    onPlayClick: () -> Unit,
    shape: Shape,
    border: BorderStroke?,
    modifier: Modifier
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black, shape)
            .then(if (border != null) Modifier.border(border, shape) else Modifier)
    ) {
        AsyncImage(
            model = ImageRequest.Builder(context)
                .data(videoCoverUrl)
                .crossfade(MetaCardDefaults.CROSSFADE_DURATION_MILLIS)
                .build(),
            contentScale = ContentScale.FillBounds,
            contentDescription = null,
            modifier = Modifier
                .fillMaxSize()
                .clip(shape)
                .then(if (border != null) Modifier.border(border, shape) else Modifier)
        )

        IconButton(
            onClick = onPlayClick,
            modifier = Modifier
                .align(Alignment.Center)
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_32_play),
                contentDescription = "play",
                tint = CustomTheme.colors.icons.primary
            )
        }
    }
}

@androidx.annotation.OptIn(UnstableApi::class)
@Composable
private fun CardVideo(
    context: Context,
    onFullScreenClick: () -> Unit,
    exoPlayer: ExoPlayer,
    shape: Shape,
    border: BorderStroke?,
    hidePlayerControllerCommand: Flow<Unit>,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black, shape)
            .then(if (border != null) Modifier.border(border, shape) else Modifier)
    ) {
        val coroutineScope = rememberCoroutineScope()

        AndroidView(
            modifier = Modifier
                .fillMaxSize()
                .clip(shape),
            factory = {
                PlayerView(context).apply {
                    player = exoPlayer
                    setShowNextButton(false)
                    setShowPreviousButton(false)
                    controllerAutoShow = false
                    setShowBuffering(PlayerView.SHOW_BUFFERING_ALWAYS)
                    setFullscreenButtonClickListener { onFullScreenClick() }

                    hidePlayerControllerCommand
                        .onEach { hideController() }
                        .launchIn(coroutineScope)
                }
            }
        )
    }
}