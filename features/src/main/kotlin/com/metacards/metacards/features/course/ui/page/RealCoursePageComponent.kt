package com.metacards.metacards.features.course.ui.page

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.deeplink.CourseLinkInfo
import com.metacards.metacards.core.deeplink.LinkGenerator
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeRun
import com.metacards.metacards.core.external_apps.ExternalAppService
import com.metacards.metacards.core.message.data.MessageService
import com.metacards.metacards.core.message.domain.Message
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.features.R
import com.metacards.metacards.features.course.domain.entity.CourseAvailability
import com.metacards.metacards.features.course.domain.entity.CourseContentType
import com.metacards.metacards.features.course.domain.entity.CourseData
import com.metacards.metacards.features.course.domain.entity.CourseDataContent
import com.metacards.metacards.features.course.domain.entity.CourseLesson
import com.metacards.metacards.features.course.domain.entity.CourseTest
import com.metacards.metacards.features.course.domain.interactor.GetCourseDataInteractor
import com.metacards.metacards.features.user.domain.GetUserSubscriptionStateInteractor
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.desc
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn

class RealCoursePageComponent(
    componentContext: ComponentContext,
    private val onOutput: (CoursePageComponent.Output) -> Unit,
    override val courseQuery: CourseData.Query,
    getUserCourseDataInteractor: GetCourseDataInteractor,
    private val errorHandler: ErrorHandler,
    private val messageService: MessageService,
    private val linkGenerator: LinkGenerator,
    private val externalAppService: ExternalAppService,
    getUserSubscriptionStateInteractor: GetUserSubscriptionStateInteractor,
) : ComponentContext by componentContext, CoursePageComponent {

    override val courseData = getUserCourseDataInteractor.execute(courseQuery)
        .stateIn(componentScope, SharingStarted.Eagerly, null)

    override val bottomButtonText: StateFlow<StringDesc> = computed(courseData) { data ->
        when {
            data?.content == null -> R.string.course_page_button_start.strResDesc()
            data.content.none { it.isCompleted } -> R.string.course_page_button_start.strResDesc()
            data.content.all { it.isCompleted } -> R.string.course_page_button_start_again.strResDesc()
            else -> R.string.course_page_button_continue.strResDesc()
        }
    }

    private val userSubscriptionState: StateFlow<User.SubscriptionState?> = getUserSubscriptionStateInteractor.execute()
        .stateIn(componentScope, SharingStarted.Eagerly, null)

    override val shouldBlur = computed(courseData, userSubscriptionState) { course, subscription ->
        course?.availability == CourseAvailability.SUB && subscription !is User.SubscriptionState.Ongoing
    }

    private val availableOrderOfContent = courseData.map { data ->
        data?.content?.filter { it.isCompleted }?.maxByOrNull { it.order }?.order?.plus(1) ?: 1
    }.stateIn(componentScope, SharingStarted.Eagerly, 1)

    override fun onStartClick() {
        val content = courseData.value?.content ?: return
        val passed = content.filter { it.isCompleted }
        val nextContent = if (passed.size == content.size) {
            content.firstOrNull() ?: return
        } else {
            calculateNextContent() ?: return
        }
        onContentClick(nextContent)
    }

    override fun onContentClick(courseDataContent: CourseDataContent) {
        if (courseDataContent.order > availableOrderOfContent.value) return
        when (courseDataContent.type) {
            CourseContentType.LESSON -> {
                val query = CourseLesson.Query(
                    themeId = courseQuery.themeId,
                    courseId = courseQuery.courseId,
                    lessonId = courseDataContent.contentId
                )
                onOutput(CoursePageComponent.Output.LessonRequested(query, courseDataContent.order))
            }

            CourseContentType.TEST -> {
                val query = CourseTest.Query(
                    themeId = courseQuery.themeId,
                    courseId = courseQuery.courseId,
                    testId = courseDataContent.contentId
                )
                onOutput(CoursePageComponent.Output.TestRequested(query, courseDataContent.order))
            }
        }
    }

    private fun calculateNextContent(): CourseDataContent? =
        courseData.value?.content?.firstOrNull { !it.isCompleted }

    override fun onTestResultClick(courseDataContent: CourseDataContent) {
        if (courseDataContent.type != CourseContentType.TEST || !courseDataContent.isCompleted) return
        onOutput(CoursePageComponent.Output.TestResultsRequested(courseDataContent.contentId, null))
    }

    override fun onShareClick() {
        safeRun(errorHandler) {
            val courseData = courseData.value ?: return@safeRun

            val linkInfo = CourseLinkInfo(
                themeId = courseQuery.themeId.value,
                courseId = courseQuery.courseId.value,
                title = courseData.name,
                coverUrl = courseData.coverUrl
            )

            linkGenerator.generateCourseLink(linkInfo) { url: String?, error: String? ->
                when {
                    error != null -> {
                        messageService.showMessage(
                            Message(text = error.desc(), isNotification = false)
                        )
                    }

                    url == null -> {
                        messageService.showMessage(
                            Message(
                                text = com.metacards.metacards.core.R.string.error_unexpected.strResDesc(),
                                isNotification = false
                            )
                        )
                    }

                    else -> {
                        externalAppService.openShareSheetForText(url)
                    }
                }
            }
        }
    }

    override fun onBlockContentClick() {
        when (userSubscriptionState.value) {
            is User.SubscriptionState.None -> onOutput(CoursePageComponent.Output.PremiumSuggestingRequested)
            is User.SubscriptionState.Ongoing -> Unit
            null -> onOutput(CoursePageComponent.Output.AuthSuggestingRequested)
        }
    }

    override fun onToOtherCoursesClick() {
        onOutput(CoursePageComponent.Output.CloseRequested)
    }
}