package com.metacards.metacards.features.deeplink.data

import android.content.Context
import android.util.Log
import com.metacards.metacards.core.deeplink.CourseLinkInfo
import com.metacards.metacards.core.deeplink.DeckLinkInfo
import com.metacards.metacards.core.deeplink.LinkGenerator
import com.metacards.metacards.core.localization.data.AppLanguageService
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.deeplink.domain.DeeplinkData
import io.branch.indexing.BranchUniversalObject
import io.branch.referral.util.LinkProperties

class BranchIOLinkGenerator(
    private val context: Context,
    private val appLanguageService: AppLanguageService
) : LinkGenerator {

    override fun generateDeckLink(
        linkInfo: DeckLinkInfo,
        onGenerated: (url: String?, error: String?) -> Unit
    ) {
        generateBranchLink(
            deeplinkPath = "${DeeplinkData.Deeplink.DECK_ID}/${linkInfo.deckId}",
            title = linkInfo.title,
            coverUrl = linkInfo.coverUrl,
            onGenerated = onGenerated
        )
    }

    override fun generateCourseLink(
        linkInfo: CourseLinkInfo,
        onGenerated: (url: String?, error: String?) -> Unit
    ) {
        generateBranchLink(
            deeplinkPath = "${DeeplinkData.Deeplink.COURSE}/${linkInfo.themeId}/${linkInfo.courseId}",
            title = linkInfo.title,
            coverUrl = linkInfo.coverUrl,
            onGenerated = onGenerated
        )
    }

    private fun generateBranchLink(
        deeplinkPath: String,
        title: LocalizableString,
        coverUrl: String,
        onGenerated: (url: String?, error: String?) -> Unit
    ) {
        val appLanguage = appLanguageService.currentAppLanguage.value

        val buo = BranchUniversalObject()
            .setCanonicalIdentifier(deeplinkPath)
            .setTitle(title.toString(appLanguage))
            .setContentIndexingMode(BranchUniversalObject.CONTENT_INDEX_MODE.PUBLIC)
            .setLocalIndexMode(BranchUniversalObject.CONTENT_INDEX_MODE.PUBLIC)
            .setContentImageUrl(coverUrl)
            .setCanonicalUrl(DeeplinkData.Deeplink.create(deeplinkPath).value)

        buo.generateShortUrl(context, LinkProperties()) { url, error ->
            if (error == null) {
                Log.i("BRANCH SDK", "got my Branch link to share: $url")
            }
            onGenerated.invoke(url, error?.message)
        }
    }
}