package com.metacards.metacards.features.favorite_cards.ui.list

import com.metacards.metacards.core.user.domain.FavoriteCard
import com.metacards.metacards.features.deck.domain.entity.CardId
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakeFavoriteCardListComponent : FavoriteCardListComponent {

    override val cards: StateFlow<List<FavoriteCard>> =
        MutableStateFlow(emptyList())

    override fun onCardClick(cardId: CardId) = Unit

    override fun onGoToMainClick() = Unit
}