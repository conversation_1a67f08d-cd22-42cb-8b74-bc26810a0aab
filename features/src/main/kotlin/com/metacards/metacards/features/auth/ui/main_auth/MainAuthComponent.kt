package com.metacards.metacards.features.auth.ui.main_auth

import androidx.annotation.StringRes
import com.metacards.metacards.core.user.domain.Email
import com.metacards.metacards.features.auth.domain.SSOType
import kotlinx.coroutines.flow.StateFlow

interface MainAuthComponent {
    val ssoAuthVariants: StateFlow<List<SSOType>>
    val shouldAnimate: StateFlow<Boolean>

    fun onSignInButtonClick()
    fun onSignUpButtonClick()
    fun onCloseButtonClick()
    fun onSSOClick(ssoType: SSOType)
    fun onAnimationEnd()

    sealed interface Output {
        data object MainPageRequested : Output
        data object SignInRequested : Output
        data object SignUpRequested : Output
        data class SignInViaWebViewRequested(val url: String, @StringRes val title: Int) : Output
        data class EmailVerificationRequested(val email: Email) : Output
    }
}