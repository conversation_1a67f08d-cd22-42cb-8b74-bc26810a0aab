package com.metacards.metacards.features.record.ui.model

import androidx.annotation.DrawableRes
import com.metacards.metacards.features.R

data class FeelingItem<T : Any>(
    val value: T,
    @DrawableRes val imageResource: Int
) {

    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        return when {
            other === this -> true
            other is FeelingItem<*> -> value == other.value
            else -> false
        }
    }

    override fun hashCode(): Int {
        return value.hashCode()
    }

    companion object {
        val mars_list = listOf(
            FeelingItem(1, R.drawable.mars1),
            FeelingItem(2, R.drawable.mars2),
            FeelingItem(3, R.drawable.mars3),
            FeelingItem(4, R.drawable.mars4),
            FeelingItem(5, R.drawable.mars5),
        )

        val moon_list = listOf(
            FeelingItem(1, R.drawable.moon1),
            FeelingItem(2, R.drawable.moon2),
            FeelingItem(3, R.drawable.moon3),
            FeelingItem(4, R.drawable.moon4),
            FeelingItem(5, R.drawable.moon5),
        )
    }
}