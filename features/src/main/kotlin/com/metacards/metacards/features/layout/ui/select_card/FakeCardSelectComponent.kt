package com.metacards.metacards.features.layout.ui.select_card

import com.google.android.filament.Engine
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.layout.domain.PredefinedQuestion
import com.metacards.metacards.features.layout.ui.select_card.ar.ArCardSelectComponent
import com.metacards.metacards.features.layout.ui.select_card.ar.FakeArCardSelectComponent
import com.metacards.metacards.features.tutorial.data.TutorialStep
import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.desc
import io.github.sceneview.Filament
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakeCardSelectComponent : CardSelectComponent {
    override val cards = MutableStateFlow<LoadableState<CardsWithQuestionId>>(LoadableState())
    override val tutorialStep: StateFlow<TutorialStep> = MutableStateFlow(TutorialStep.DECKS)
    override val tutorialMessage: StateFlow<TutorialMessage?> = MutableStateFlow(null)
    override val tabs = emptyList<CardSelectComponent.Tabs>()
    override val selectedTab = MutableStateFlow(CardSelectComponent.Tabs.Flat)
    override val arComponent: ArCardSelectComponent = FakeArCardSelectComponent()
    override val isInteractionEnabled: StateFlow<Boolean> = MutableStateFlow(false)
    override val questionText: StateFlow<StringDesc> = MutableStateFlow("".desc())
    override val activeQuestionIndex: StateFlow<Int> = MutableStateFlow(0)
    override val predefinedQuestions: List<PredefinedQuestion> = emptyList()
    override val isPredefinedLayout: Boolean = true
    override val arEngine: Engine = Filament.engine
    override val isCameraPermissionGranted = MutableStateFlow(false)

    override fun onTabChange(tabModel: CardSelectComponent.Tabs) = Unit
    override fun onCloseClick(isArCardSelect: Boolean) = Unit
    override fun onCardSelect(card: Card, questionText: String?) = Unit
    override fun onCardLayoutAnimationEnd() = Unit
    override fun openSettings() = Unit
}