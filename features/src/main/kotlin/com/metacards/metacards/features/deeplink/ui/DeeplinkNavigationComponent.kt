package com.metacards.metacards.features.deeplink.ui

import com.metacards.metacards.features.course.domain.entity.CourseData
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.user_analytics.ui.UserAnalyticsComponent

interface DeeplinkNavigationComponent {
    fun readyToReceiveEvents()

    sealed interface Output {
        data class DeckInfoPageRequested(
            val deckId: DeckId,
            val isPaymentCompleted: Boolean = false
        ) : Output

        data class SubscriptionPageRequested(val isPaymentCompleted: <PERSON>olean = false) : Output
        data class AnalyticsPageRequested(val tab: UserAnalyticsComponent.Tab) : Output
        data object HomePageRequested : Output
        data object CloseWebViewAuthRequested : Output
        data object SubscriptionPaymentCompleted : Output
        data class CourseRequested(val courseQuery: CourseData.Query) : Output
    }
}