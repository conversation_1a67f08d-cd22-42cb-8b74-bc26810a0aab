package com.metacards.metacards.features.user_analytics.ui.layouts

import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.PagedData
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.record.ui.record_list.ScrollCommand
import com.metacards.metacards.features.user_analytics.domain.layout.LayoutRecord
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow

interface LayoutsAnalyticsComponent {
    val analyticsState: StateFlow<LoadableState<PagedData<LayoutRecord>>>
    val selectedRecord: StateFlow<LayoutRecord?>
    val scrollToRecordCommand: SharedFlow<ScrollCommand?>
    val isPremiumUser: StateFlow<Boolean>

    fun updateSelectedRecord(index: Int?)
    fun onRecordClick(recordId: RecordId)
    fun onLoadMore()

    sealed interface Output {
        data class RecordDetailsRequested(val recordId: RecordId) : Output
    }
}
