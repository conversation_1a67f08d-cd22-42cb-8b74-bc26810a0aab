package com.metacards.metacards.features.account.ui.profile.password.create_new

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.core.widget.text_field.DefaultTextFieldFooterMessage
import com.metacards.metacards.core.widget.text_field.MetaTextField
import com.metacards.metacards.features.R
import com.metacards.metacards.features.auth.ui.auth_type.pass.TextVisibilityIcon
import dev.icerock.moko.resources.desc.strResDesc
import ru.mobileup.kmm_form_validation.toCompose
import com.metacards.metacards.core.R as CoreR

@Composable
fun ProfileNewPasswordUi(
    component: ProfileNewPasswordComponent,
    modifier: Modifier = Modifier
) {
    var showPass by remember { mutableStateOf(false) }
    var showConfirmPass by remember { mutableStateOf(false) }
    val saveButtonState by component.saveButtonState.collectAsState()

    BoxWithFade(modifier, listOfColors = CustomTheme.colors.gradient.backgroundList) {
        Column(
            modifier = Modifier
                .matchParentSize()
                .verticalScroll(rememberScrollState())
        ) {
            TopNavigationBar(
                title = R.string.account_profile_edit_password_title.strResDesc(),
                leadingIcon = { BackNavigationItem() },
                modifier = Modifier.padding(bottom = 44.dp)
            )

            MetaTextField(
                modifier = Modifier
                    .padding(bottom = 40.dp)
                    .padding(horizontal = 16.dp),
                placeholder = R.string.account_profile_edit_password_placeholder.strResDesc()
                    .localizedByLocal(),
                inputControl = component.newPasswordInputControl,
                trailingIcon = { TextVisibilityIcon(showPass) { showPass = !showPass } },
                visualTransformation = if (!showPass) component.newPasswordInputControl.visualTransformation.toCompose() else VisualTransformation.None,
                headerMessage = {
                    Text(
                        text = R.string.account_profile_edit_password_new_header.strResDesc()
                            .localizedByLocal(),
                        color = CustomTheme.colors.text.primary,
                        style = CustomTheme.typography.caption.large,
                        modifier = Modifier.padding(bottom = 24.dp)
                    )
                },
                footerMessage = {
                    DefaultTextFieldFooterMessage(message = R.string.password_create_hint.strResDesc())
                }
            )

            MetaTextField(
                modifier = Modifier.padding(horizontal = 16.dp),
                placeholder = R.string.account_profile_edit_password_placeholder.strResDesc()
                    .localizedByLocal(),
                inputControl = component.confirmNewPasswordInputControl,
                trailingIcon = {
                    TextVisibilityIcon(showConfirmPass) {
                        showConfirmPass = !showConfirmPass
                    }
                },
                visualTransformation = if (!showConfirmPass) {
                    component.confirmNewPasswordInputControl.visualTransformation.toCompose()
                } else {
                    VisualTransformation.None
                },
                headerMessage = {
                    Text(
                        text = R.string.account_profile_edit_password_confirm_header.strResDesc()
                            .localizedByLocal(),
                        color = CustomTheme.colors.text.primary,
                        style = CustomTheme.typography.caption.large,
                        modifier = Modifier.padding(bottom = 24.dp)
                    )
                }
            )

            Spacer(modifier = Modifier.weight(1f))

            MetaAccentButton(
                state = saveButtonState,
                text = CoreR.string.common_save.strResDesc().localizedByLocal(),
                onClick = component::onSaveClick,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 24.dp)
            )
        }
    }
}

@Preview
@Composable
fun ProfileNewPasswordUiPreview() {
    AppTheme {
        ProfileNewPasswordUi(component = FakeProfileNewPasswordComponent())
    }
}