package com.metacards.metacards.features.course.ui.test

import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.course.domain.entity.CoursePassedTest
import com.metacards.metacards.features.course.domain.entity.CourseQuestion
import com.metacards.metacards.features.course.domain.entity.CourseResult
import com.metacards.metacards.features.course.domain.entity.CourseTest
import kotlinx.coroutines.flow.StateFlow

interface CourseTestComponent {

    val courseName: LocalizableString?
    val test: StateFlow<CourseTest?>
    val shouldShowTutorial: StateFlow<Boolean>
    val closeDialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent>

    fun onLastQuestionAnswered()
    fun onQuestionAnswered(dragAnchor: CourseTestDragAnchors, question: CourseQuestion)
    fun onRevertClick(previousQuestion: CourseQuestion)
    fun onCloseTutorialClick()
    fun onCloseClick()
    fun onUserInteracted()

    sealed interface Output {
        data class CourseResultRequested(
            val result: CourseResult,
            val dataToSave: CoursePassedTest,
            val order: Int
        ) : Output
        data object CourseResultNotFound : Output
        data object CloseRequested : Output
    }
}
