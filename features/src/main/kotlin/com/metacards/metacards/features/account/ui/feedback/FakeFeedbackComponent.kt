package com.metacards.metacards.features.account.ui.feedback

import com.metacards.metacards.features.account.domain.entity.QuestionSubject
import kotlinx.coroutines.flow.MutableStateFlow

class FakeFeedbackComponent : FeedbackComponent {
    override val questionSubjects = MutableStateFlow(listOf(QuestionSubject("")))
    override val selectedQuestionSubject = MutableStateFlow(QuestionSubject(""))
    override val email = MutableStateFlow("<EMAIL>")
    override val question = MutableStateFlow("Other")
    override val emailError = MutableStateFlow(true)
    override val questionError = MutableStateFlow(true)
    override val loading = MutableStateFlow(false)

    override fun onSendClick() = Unit
    override fun selectQuestionSubject(value: QuestionSubject) = Unit
    override fun changeEmail(value: String) = Unit
    override fun changeQuestion(value: String) = Unit
}
