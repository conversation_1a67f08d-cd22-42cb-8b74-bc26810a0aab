package com.metacards.metacards.features.layout.ui.predefined_layout.widgets

import androidx.compose.animation.animateColorAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable

@Composable
fun PredefinedLayoutGroupItem(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val chipColor by animateColorAsState(
        targetValue = if (isSelected) {
            CustomTheme.colors.icons.primary
        } else {
            CustomTheme.colors.button.secondary
        },
        label = "cheapColor"
    )

    val textColor by animateColorAsState(
        targetValue = if (isSelected) {
            CustomTheme.colors.text.inverted
        } else {
            CustomTheme.colors.text.primary
        },
        label = "textColor"
    )

    Text(
        modifier = modifier
            .clip(shape = RoundedCornerShape(30.dp))
            .background(color = chipColor, shape = RoundedCornerShape(30.dp))
            .clickable(onClick = onClick)
            .padding(vertical = 8.5.dp, horizontal = 16.dp),
        text = text,
        color = textColor,
        style = CustomTheme.typography.button.small,
    )
}

@Preview
@Composable
private fun PredefinedLayoutGroupItemPreview() {
    AppTheme {
        PredefinedLayoutGroupItem(
            text = "Test",
            isSelected = true,
            onClick = {}
        )
    }
}