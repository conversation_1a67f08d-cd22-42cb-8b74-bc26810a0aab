package com.metacards.metacards.features.user_analytics.ui.month.widgets

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.features.R
import com.metacards.metacards.features.record.domain.LevelType
import com.metacards.metacards.features.user_analytics.domain.month.MonthAnalyticsInfo
import com.metacards.metacards.features.user_analytics.ui.common_widgets.AnalyticsLevelCard
import dev.icerock.moko.resources.desc.strResDesc
import kotlin.math.roundToInt

@Composable
fun MonthAnalyticsLevels(
    analyticsInfo: MonthAnalyticsInfo,
    modifier: Modifier = Modifier
) {
    val moodMaxPercent = analyticsInfo.moodMaxPercent
    val energyMaxPercent = analyticsInfo.energyMaxPercent

    Row(modifier = modifier.padding(horizontal = 16.dp)) {

        AnalyticsLevelCard(
            levelType = LevelType.Energy,
            level = energyMaxPercent?.level,
            text = getText(energyMaxPercent?.percent),
            description = getEnergyDescriptionStringResource(energyMaxPercent?.level)
                .strResDesc().localizedByLocal(),
            modifier = Modifier.weight(1f).padding(end = 4.dp)
        )

        AnalyticsLevelCard(
            levelType = LevelType.Mood,
            level = moodMaxPercent?.level,
            text = getText(moodMaxPercent?.percent),
            description = getMoodDescriptionStringResource(moodMaxPercent?.level)
                .strResDesc().localizedByLocal(),
            modifier = Modifier.weight(1f).padding(end = 4.dp)
        )
    }
}

private fun getText(percent: Float?): String {
    return when (percent) {
        null -> "0"
        else -> "${percent.roundToInt()}%"
    }
}

@Composable
@ReadOnlyComposable
private fun getMoodDescriptionStringResource(level: Int?): Int {
    return when (level) {
        null -> R.string.analytics_month_level_description_no_records
        1 -> R.string.analytics_month_level_description_mood_1
        2 -> R.string.analytics_month_level_description_mood_2
        3 -> R.string.analytics_month_level_description_mood_3
        4 -> R.string.analytics_month_level_description_mood_4
        else -> R.string.analytics_month_level_description_mood_5
    }
}

@Composable
@ReadOnlyComposable
private fun getEnergyDescriptionStringResource(level: Int?): Int {
    return when (level) {
        null -> R.string.analytics_month_level_description_no_records
        1 -> R.string.analytics_month_level_description_energy_1
        2 -> R.string.analytics_month_level_description_energy_2
        3 -> R.string.analytics_month_level_description_energy_3
        4 -> R.string.analytics_month_level_description_energy_4
        else -> R.string.analytics_month_level_description_energy_5
    }
}