package com.metacards.metacards.features.account.ui.lessons.list

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.activity.ActivityProvider
import com.metacards.metacards.core.localization.data.AppLanguageService
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.stateIn
import com.metacards.metacards.features.account.domain.entity.LessonCategory
import com.metacards.metacards.features.account.domain.entity.LessonCategoryId
import com.metacards.metacards.features.account.domain.interactor.GetCategoryLessonsInteractor
import com.metacards.metacards.features.account.domain.repository.LessonRepository
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.deck.domain.entity.Lesson
import com.metacards.metacards.features.video_player.VideoPlayerActivity
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

class RealLessonListComponent(
    componentContext: ComponentContext,
    lessonCategoryId: LessonCategoryId,
    lessonsRepository: LessonRepository,
    getCategoryLessonsInteractor: GetCategoryLessonsInteractor,
    private val analyticsService: AnalyticsService,
    private val activityProvider: ActivityProvider,
    private val appLanguageService: AppLanguageService,
    private val onOutput: (LessonListComponent.Output) -> Unit
) : ComponentContext by componentContext, LessonListComponent {

    override val lessonsState: StateFlow<LoadableState<List<Lesson>>> =
        getCategoryLessonsInteractor
            .execute(lessonCategoryId)
            .stateIn(this, LoadableState())

    override val lessonCategoryState: StateFlow<LoadableState<LessonCategory>> =
        lessonsRepository
            .getLessonCategoryByIdFlow(lessonCategoryId)
            .stateIn(this, LoadableState())

    override fun onLessonDetailsClick(lesson: Lesson) {
        onOutput(LessonListComponent.Output.LessonDetailsRequested(lesson))
    }

    override fun onVideoPlay(lesson: Lesson) {
        componentScope.launch {
            analyticsService.logEvent(
                AnalyticsEvent.EduVideoEvent()
            )
            activityProvider.awaitActivity().run {
                val url = lesson.videoUrl.toString(appLanguageService.getLanguage())
                VideoPlayerActivity.start(url, this)
            }
        }
    }
}
