package com.metacards.metacards.features.course.domain.interactor

import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.features.course.domain.entity.CourseStatus
import com.metacards.metacards.features.course.domain.entity.CourseTheme
import com.metacards.metacards.features.course.domain.repository.CourseRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update

class GetCourseThemesInteractor(
    coroutineScope: CoroutineScope,
    courseRepository: CourseRepository,
    errorHandler: ErrorHandler,
) {

    private val stateFlow = MutableStateFlow<List<CourseTheme>>(emptyList())

    init {
        val themesFlow = courseRepository.getCourseThemesFlow()
        val userPassedCoursesFlow = courseRepository.getUserPassedCoursesFlow()
        combine(themesFlow, userPassedCoursesFlow) { themes, userPassedCourses ->
            val userPassedCoursesIds = userPassedCourses
                .filter { it.isCompleted }
                .map { it.courseId }
                .toHashSet()

            themes.map { theme ->
                theme.copy(
                    courses = theme.courses.map {
                        it.copy(isCompleted = it.courseId in userPassedCoursesIds)
                    }
                )
            }
        }
            .catch { e -> errorHandler.handleError(Exception(e)) }
            .onEach { list ->
                stateFlow.update { list.filter { course -> course.status != CourseStatus.INACTIVE } }
            }
            .launchIn(coroutineScope)
    }

    fun execute() = stateFlow.asStateFlow()
}