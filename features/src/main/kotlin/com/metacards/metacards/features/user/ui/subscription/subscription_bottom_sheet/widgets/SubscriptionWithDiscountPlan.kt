package com.metacards.metacards.features.user.ui.subscription.subscription_bottom_sheet.widgets

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.user.domain.SubscriptionType
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.StringResource
import dev.icerock.moko.resources.desc.Raw
import dev.icerock.moko.resources.desc.ResourceFormattedStringDesc
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

@Composable
fun SubscriptionWithDiscountPlan(
    id: SubscriptionType,
    titleText: StringDesc,
    priceWithDiscountString: String,
    basePriceString: String,
    selectedSubscriptionTypeFlow: StateFlow<SubscriptionType>,
    onSelect: (SubscriptionType) -> Unit,
    discountPercent: Int,
    modifier: Modifier = Modifier,
    monthPaymentString: String? = null,
) {
    val isSelected = id == selectedSubscriptionTypeFlow.collectAsState().value

    Card(
        shape = RoundedCornerShape(16.dp),
        elevation = 0.dp,
        backgroundColor = CustomTheme.colors.background.primary,
        modifier = modifier
            .padding(bottom = 8.dp)
            .clip(RoundedCornerShape(16.dp))
            .clickable { onSelect(id) }
    ) {
        Row(
            modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth(),
        ) {
            Icon(
                painter = painterResource(
                    id = if (isSelected) {
                        R.drawable.ic_24_radio_button_on
                    } else {
                        R.drawable.ic_24_radio_button_off
                    }
                ),
                contentDescription = null,
                tint = CustomTheme.colors.icons.primary,
                modifier = Modifier.padding(end = 12.dp)
            )
            Column {
                Text(
                    text = titleText.localizedByLocal(),
                    style = CustomTheme.typography.heading.small,
                    color = CustomTheme.colors.text.primary
                )

                Row {
                    Text(
                        text = priceWithDiscountString,
                        style = CustomTheme.typography.heading.small,
                        color = CustomTheme.colors.text.primary
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    monthPaymentString?.let {
                        Text(
                            text = "($it)",
                            style = CustomTheme.typography.body.primary,
                            color = CustomTheme.colors.text.secondary
                        )
                    }
                }

                Text(
                    text = basePriceString,
                    color = CustomTheme.colors.accent.primary,
                    style = CustomTheme.typography.caption.bigSemiBold,
                    modifier = Modifier
                        .padding(top = 2.dp)
                        .drawWithContent {
                            drawContent()
                            drawLine(
                                color = Color(CustomTheme.colors.accent.primary.toArgb()),
                                Offset(0f, size.height.times(0.6f)),
                                Offset(size.width, size.height.times(0.6f)),
                                strokeWidth = density
                            )
                        }
                )

                Surface(
                    shape = RoundedCornerShape(4.dp),
                    color = CustomTheme.colors.accent.secondary,
                    modifier = Modifier.padding(top = 8.dp)
                ) {
                    Text(
                        text = ResourceFormattedStringDesc(
                            StringResource(R.string.account_subscription_plan_caption_economy),
                            listOf(discountPercent)
                        ).localizedByLocal(),
                        color = CustomTheme.colors.text.caption,
                        style = CustomTheme.typography.caption.bigSemiBold,
                        modifier = Modifier
                            .padding(horizontal = 4.dp, vertical = 2.dp)
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun Preview() {
    SubscriptionWithDiscountPlan(
        id = SubscriptionType.SUBSCRIPTION_PLAN_YEAR,
        titleText = StringDesc.Raw("Годовая подписка"),
        priceWithDiscountString = "6547 rub",
        selectedSubscriptionTypeFlow = MutableStateFlow(SubscriptionType.SUBSCRIPTION_PLAN_THREE_MONTH),
        onSelect = {},
        modifier = Modifier,
        monthPaymentString = "777.00 руб/мес",
        discountPercent = 26,
        basePriceString = "6990 p"
    )
}