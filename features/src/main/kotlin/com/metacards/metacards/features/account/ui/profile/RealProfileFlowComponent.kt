package com.metacards.metacards.features.account.ui.profile

import android.os.Parcelable
import androidx.annotation.StringRes
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.router.stack.childStack
import com.arkivanov.decompose.router.stack.pop
import com.arkivanov.decompose.router.stack.push
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.toStateFlow
import com.metacards.metacards.features.account.createProfileConfirmPasswordComponent
import com.metacards.metacards.features.account.createProfileEmailComponent
import com.metacards.metacards.features.account.createProfileMainComponent
import com.metacards.metacards.features.account.createProfileNameComponent
import com.metacards.metacards.features.account.createProfilePasswordComponent
import com.metacards.metacards.features.account.ui.profile.email.ProfileEmailComponent
import com.metacards.metacards.features.account.ui.profile.main.ProfileMainComponent
import com.metacards.metacards.features.account.ui.profile.name.ProfileNameComponent
import com.metacards.metacards.features.account.ui.profile.password.ProfilePasswordComponent
import com.metacards.metacards.features.account.ui.profile.password.confirm.ProfileConfirmPasswordComponent
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.parcelize.Parcelize

class RealProfileFlowComponent(
    componentContext: ComponentContext,
    screen: ProfileFlowComponent.Screen,
    private val componentFactory: ComponentFactory,
    private val onOutput: (ProfileFlowComponent.Output) -> Unit
) : ComponentContext by componentContext, ProfileFlowComponent {

    private val navigation = StackNavigation<ChildConfig>()

    override val childStack = childStack(
        source = navigation,
        initialConfiguration = screen.toChildConfig(),
        handleBackButton = true,
        childFactory = ::createChild
    ).toStateFlow(lifecycle)

    private fun createChild(
        childConfig: ChildConfig,
        componentContext: ComponentContext
    ): ProfileFlowComponent.Child = when (childConfig) {
        is ChildConfig.Main -> ProfileFlowComponent.Child.Main(
            componentFactory.createProfileMainComponent(
                componentContext,
                ::onProfileMainOutput
            )
        )

        is ChildConfig.Name -> ProfileFlowComponent.Child.Name(
            componentFactory.createProfileNameComponent(
                componentContext,
                childConfig.initialUserName,
                ::onNameOutput
            )
        )

        is ChildConfig.Email -> ProfileFlowComponent.Child.Email(
            componentFactory.createProfileEmailComponent(
                componentContext,
                ::onProfileEmailOutput
            )
        )

        is ChildConfig.Password -> ProfileFlowComponent.Child.Password(
            componentFactory.createProfilePasswordComponent(
                componentContext,
                ::onProfilePasswordOutput
            )
        )

        is ChildConfig.ConfirmPassword -> ProfileFlowComponent.Child.ConfirmPassword(
            componentFactory.createProfileConfirmPasswordComponent(
                componentContext,
                toolbarText = StringDesc.Resource(childConfig.toolbarTextRes),
                textFieldHeader = StringDesc.Resource(childConfig.textFieldHeaderRes),
                textFieldCaption = null,
                bottomButtonText = StringDesc.Resource(childConfig.bottomButtonTextRes),
                ::onPasswordConfirmOutput
            )
        )
    }

    private fun onPasswordConfirmOutput(output: ProfileConfirmPasswordComponent.Output) {
        when (output) {
            is ProfileConfirmPasswordComponent.Output.PasswordConfirmed -> {
                navigation.pop()
                (childStack.value.active.instance as? ProfileFlowComponent.Child.Main)
                    ?.component
                    ?.onPasswordConfirmed()
            }
        }
    }

    private fun onProfilePasswordOutput(output: ProfilePasswordComponent.Output) {
        when (output) {
            is ProfilePasswordComponent.Output.DismissRequested -> {
                navigation.pop()
            }
        }
    }

    private fun onProfileEmailOutput(output: ProfileEmailComponent.Output) {
        when (output) {
            is ProfileEmailComponent.Output.AuthScreenRequested -> {
                onOutput(ProfileFlowComponent.Output.AuthScreenRequested)
            }
            is ProfileEmailComponent.Output.MainScreenRequested -> {
                onOutput(ProfileFlowComponent.Output.MainScreenRequested)
            }
        }
    }

    private fun onNameOutput(output: ProfileNameComponent.Output) {
        when (output) {
            is ProfileNameComponent.Output.DismissRequested -> navigation.pop()
        }
    }

    private fun onProfileMainOutput(output: ProfileMainComponent.Output) {
        when (output) {
            is ProfileMainComponent.Output.ProfileFlowScreenRequested -> {
                navigation.push(output.screen.toChildConfig())
            }
            is ProfileMainComponent.Output.AuthScreenRequested -> {
                onOutput(ProfileFlowComponent.Output.AuthScreenRequested)
            }
            is ProfileMainComponent.Output.WebViewDismissRequested -> {
                onOutput(ProfileFlowComponent.Output.WebViewDismissRequested)
            }
            is ProfileMainComponent.Output.SignInViaWebViewRequested -> {
                onOutput(ProfileFlowComponent.Output.SignInViaWebViewRequested(output.url, output.title))
            }

            ProfileMainComponent.Output.SubscriptionRequested -> {
                onOutput(ProfileFlowComponent.Output.SubscriptionRequested)
            }
        }
    }

    sealed interface ChildConfig : Parcelable {
        @Parcelize
        data object Main : ChildConfig

        @Parcelize
        data class Name(val initialUserName: String) : ChildConfig

        @Parcelize
        data object Email : ChildConfig

        @Parcelize
        data object Password : ChildConfig

        @Parcelize
        data class ConfirmPassword(
            @StringRes val toolbarTextRes: Int,
            @StringRes val textFieldHeaderRes: Int,
            @StringRes val bottomButtonTextRes: Int
        ) : ChildConfig
    }

    private fun ProfileFlowComponent.Screen.toChildConfig(): ChildConfig {
        return when (this) {
            is ProfileFlowComponent.Screen.Main -> ChildConfig.Main
            is ProfileFlowComponent.Screen.Name -> ChildConfig.Name(initialUserName)
            is ProfileFlowComponent.Screen.Email -> ChildConfig.Email
            is ProfileFlowComponent.Screen.Password -> ChildConfig.Password
            is ProfileFlowComponent.Screen.ConfirmPassword -> ChildConfig.ConfirmPassword(
                toolbarTextRes,
                textFieldHeaderRes,
                bottomButtonTextRes
            )
        }
    }
}