package com.metacards.metacards.features.deck.domain.entity

import android.net.Uri
import android.os.Parcelable
import androidx.core.net.toUri
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.user.domain.FavoriteCard
import com.metacards.metacards.features.deck.domain.entity.CardId.Companion.LOCKED_CARD_ID
import kotlinx.parcelize.Parcelize
import kotlin.random.Random

@JvmInline
@Parcelize
value class CardId(val value: String) : Parcelable {
    companion object {
        val LOCKED_CARD_ID = CardId("locked")
    }
}

@Parcelize
data class Card(
    val id: CardId,
    val deckId: DeckId,
    val imageUrl: String,
    val textureUri: Uri,
    val arObjectUrl: String?,
    val gifUrl: String?,
    val name: LocalizableString?,
    val help: LocalizableString?
) : Parcelable {
    companion object {

        fun mock(id: Int) = Card(
            id = CardId(id.toString()),
            deckId = DeckId("0"),
            imageUrl = "https://storage.googleapis.com/luapp-8150d.appspot.com/decks/lRTPDAcZDdoc7mAO6Mg1/cards/${Random.nextInt(50)}.jpg",
            textureUri = "https://storage.googleapis.com/luapp-8150d.appspot.com/decks/lRTPDAcZDdoc7mAO6Mg1/arObjects/${Random.nextInt(50)}.jpg".toUri(),
            arObjectUrl = null,
            gifUrl = null,
            name = null,
            help = null
        )

        fun fromImageOnly(imageUrl: String) = mock(0).copy(imageUrl = imageUrl)

        fun getMockList() = List(7, ::mock)

        fun lockedCard(deckId: DeckId) = Card(
            LOCKED_CARD_ID,
            deckId,
            "locked",
            Uri.EMPTY,
            null,
            null,
            null,
            null
        )
    }
}

data class CardWithFavoriteAndComment(
    val card: Card,
    val isFavorite: Boolean,
    val comment: String = "",
) {

    fun toggleFavorite() = copy(isFavorite = !isFavorite)

    fun toCardWithComment() = CardWithComment(card, comment)

    companion object {
        fun default(card: Card) = CardWithFavoriteAndComment(card, false, "")

        val LIST_MOCK = Card.getMockList().map { default(it) }
    }
}

fun Card.toFavorite(): FavoriteCard {
    return FavoriteCard(id.value, imageUrl, arObjectUrl, gifUrl)
}

@Parcelize
data class CardWithComment(
    val card: Card,
    val comment: String,
) : Parcelable

@Parcelize
data class DailyCard(
    val cardId: CardId,
    val imageUrl: String,
    val hint: CardHint,
    val gifUrl: String?
) : Parcelable {

    fun toCard() = Card(
        id = cardId,
        deckId = DeckId(""),
        imageUrl = imageUrl,
        textureUri = "".toUri(),
        arObjectUrl = null,
        gifUrl = gifUrl,
        name = null,
        help = null
    )
}