package com.metacards.metacards.features.account.ui.feedback

import com.metacards.metacards.core.message.domain.Message
import com.metacards.metacards.core.widget.navigation_bar.NavigationPage
import com.metacards.metacards.features.account.domain.entity.QuestionSubject
import kotlinx.coroutines.flow.StateFlow

interface FeedbackComponent {

    companion object {
        const val MAX_QUESTION_CHARS = 500
    }

    val questionSubjects: StateFlow<List<QuestionSubject>>
    val selectedQuestionSubject: StateFlow<QuestionSubject>
    val email: StateFlow<String>
    val question: StateFlow<String>
    val emailError: StateFlow<Boolean>
    val questionError: StateFlow<Boolean>
    val loading: StateFlow<Boolean>

    fun onSendClick()
    fun selectQuestionSubject(value: QuestionSubject)
    fun changeEmail(value: String)
    fun changeQuestion(value: String)

    sealed interface Output {
        data class MainScreenRequested(val navigationPage: NavigationPage) : Output
        data class ShowMessageRequested(val message: Message) : Output
    }
}
