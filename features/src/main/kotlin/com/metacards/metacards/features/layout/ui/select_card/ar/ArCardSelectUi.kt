package com.metacards.metacards.features.layout.ui.select_card.ar

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInWindow
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.keyboardAsState
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.button.MetaTransparentButton
import com.metacards.metacards.core.widget.navigation_bar.IconNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.LoadingIconNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.core.widget.text_field.MetaTextField
import com.metacards.metacards.core.widget.text_field.MetaTextFieldDefaults
import com.metacards.metacards.features.R
import com.metacards.metacards.features.layout.ui.card_list.CardListComponent
import com.metacards.metacards.features.layout.ui.select_card.CardSelectComponent
import com.metacards.metacards.features.layout.ui.select_card.CardsWithQuestionId
import com.metacards.metacards.features.layout.ui.select_card.FakeCardSelectComponent
import com.metacards.metacards.features.layout.ui.select_card.LocalInteractionEnabled
import com.metacards.metacards.features.layout.ui.select_card.ar.sceneview.ArCardActor
import com.metacards.metacards.features.layout.ui.select_card.ar.sceneview.ArCardNode
import com.metacards.metacards.features.layout.ui.select_card.ar.sceneview.rememberArResourceLoader
import com.metacards.metacards.features.layout.ui.select_card.ar.widget.ARCardLayoutState
import com.metacards.metacards.features.layout.ui.select_card.ar.widget.ArCardLayoutScene
import com.metacards.metacards.features.layout.ui.select_card.ar.widget.rememberARCardLayoutState
import com.metacards.metacards.features.layout.ui.select_card.widget.HintBlock
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.strResDesc
import io.github.sceneview.math.Scale
import io.github.sceneview.math.size

private const val CARD_COMMENT_INPUT_MIN_LINES = 1
private const val CARD_COMMENT_INPUT_MAX_LINES = 3
private const val TEXT_LINE_HEIGHT_DP = 18
private const val ROW_BUTTON_BLOCK_HEIGHT_DP = 1000

@Composable
fun ArCardSelectUi(
    modifier: Modifier = Modifier,
    baseComponent: CardSelectComponent,
    cards: CardsWithQuestionId,
    topContent: @Composable () -> Unit
) {
    val arResourceLoader = rememberArResourceLoader(baseComponent.arEngine)
    val arCardLayoutState = rememberARCardLayoutState(cards, arResourceLoader)
    val compState = arCardLayoutState.currentState.collectAsState()
    val question by baseComponent.questionText.collectAsState()
    val selectedCard by baseComponent.arComponent.selectedCard.collectAsState()
    val isShareLoading by baseComponent.arComponent.isShareLoading.collectAsState()
    val ctx = LocalContext.current

    DisposableEffect(key1 = Unit) {
        onDispose {
            arResourceLoader.destroy(baseComponent.arEngine)
        }
    }

    Box(modifier = modifier) {
        ArCardLayoutScene(
            modifier = Modifier.fillMaxSize(),
            state = arCardLayoutState,
            gridSettings = baseComponent.arComponent.gridSettings,
            stashSettings = baseComponent.arComponent.stashSettings,
        )

        LaunchedEffect(key1 = compState.value) {
            when (val currentState = compState.value) {
                is ARCardLayoutState.State.Loaded -> LocalInteractionEnabled.provides(true)
                is ARCardLayoutState.State.NotLoaded -> LocalInteractionEnabled.provides(false)
                is ARCardLayoutState.State.Layout -> baseComponent.arComponent.onCardsLoaded()
                is ARCardLayoutState.State.Previewed ->
                    baseComponent.arComponent.selectCard(currentState.node.card)
                else -> Unit
            }
        }

        LaunchedEffect(key1 = Unit) {
            baseComponent.arComponent.stashCardEvent.collect {
                arCardLayoutState.stashCard(it)
            }
        }

        AnimatedContent(
            modifier = Modifier.fillMaxSize(),
            targetState = compState.value,
            label = "ARLayoutOverlay"
        ) {
            when (it) {
                is ARCardLayoutState.State.NotLoaded -> LoadingOverlay()
                ARCardLayoutState.State.Loaded -> PlaneSelectionOverlay()
                ARCardLayoutState.State.Layout -> Unit
                is ARCardLayoutState.State.Previewed -> CardPreviewOverlay(
                    component = baseComponent.arComponent,
                    cardNode = it.node,
                    questionText = question,
                    modifier = Modifier.imePadding()
                )
                is ARCardLayoutState.State.Error -> ErrorOverlay()
                ARCardLayoutState.State.CameraPermissionDenied -> { } // TODO: камера
            }
        }

        AnimatedContent(
            modifier = Modifier.align(Alignment.TopCenter),
            targetState = compState.value,
            label = "ARLayoutNavigationBar"
        ) { state ->
            when (state) {
                ARCardLayoutState.State.Layout -> {
                    TopNavigationBar(
                        trailingContent = {
                            IconNavigationItem(
                                iconRes = R.drawable.ic_24_close,
                                onClick = { baseComponent.onCloseClick(true) }
                            )
                        },
                        modifier = Modifier.statusBarsPadding()
                    )
                }

                is ARCardLayoutState.State.Previewed -> {
                    selectedCard?.let { card ->
                        TopNavigationBar(
                            trailingContent = {
                                LoadingIconNavigationItem(
                                    paddingValues = PaddingValues(0.dp, 0.dp, 12.dp, 0.dp),
                                    isLoading = isShareLoading,
                                    iconRes = R.drawable.ic_24_share_outlined,
                                    onClick = {
                                        val questionText = baseComponent.questionText.value.toString(ctx)
                                        baseComponent.arComponent.onToggleShare(
                                            cardUrl = card.card.imageUrl,
                                            questionText = questionText
                                        )
                                    }
                                )

                                IconNavigationItem(
                                    iconRes = if (card.isFavorite) {
                                        R.drawable.ic_24_favourite
                                    } else {
                                        R.drawable.ic_24_no_favourite
                                    },
                                    onClick = {
                                        baseComponent.arComponent.onToggleFavorite(!card.isFavorite)
                                    }
                                )
                            },
                            modifier = Modifier.statusBarsPadding()
                        )
                    }
                }

                ARCardLayoutState.State.NotLoaded,
                ARCardLayoutState.State.Loaded,
                is ARCardLayoutState.State.Error -> topContent()

                ARCardLayoutState.State.CameraPermissionDenied -> Unit // TODO: camera permission
            }
        }
    }
}

@Composable
private fun BoxScope.Message(message: StringDesc) {
    Surface(
        modifier = Modifier
            .align(Alignment.BottomCenter)
            .padding(24.dp)
            .fillMaxWidth(),
        color = CustomTheme.colors.system.basic,
        shape = RoundedCornerShape(16.dp)
    ) {
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            textAlign = TextAlign.Center,
            style = CustomTheme.typography.heading.medium,
            color = CustomTheme.colors.text.caption,
            text = message.localizedByLocal()
        )
    }
}

@Composable
private fun LoadingOverlay() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .navigationBarsPadding()
            .background(CustomTheme.colors.background.disabled),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator()
        Message(R.string.deck_layout_ar_loading_message.strResDesc())
    }
}

@Composable
private fun PlaneSelectionOverlay() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .navigationBarsPadding(),
        contentAlignment = Alignment.Center,
    ) {
        Message(R.string.deck_layout_ar_layout_message.strResDesc())
    }
}

@Composable
private fun ErrorOverlay() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center,
    ) {
        Message(com.metacards.metacards.core.R.string.error_unexpected.strResDesc())
    }
}

@Composable
private fun CardPreviewOverlay(
    component: ArCardSelectComponent,
    cardNode: ArCardNode,
    questionText: StringDesc,
    modifier: Modifier = Modifier
) {
    val isKeyboardOpen by keyboardAsState()
    val configuration = LocalConfiguration.current
    val screenHeightDpValue = configuration.screenHeightDp
    val density = LocalDensity.current
    val focusManager = LocalFocusManager.current
    val selectedCard by component.selectedCard.collectAsState()
    val canSelectCard by component.canSelectMoreCard.collectAsState()
    val cardHint by component.cardHint.collectAsState()
    var isCardHintExpanded by remember { mutableStateOf(true) }
    var commentTextFieldHasFocus by remember { mutableStateOf(false) }
    var cardPartHeightDp by remember { mutableStateOf(0.dp) }
    var cardPartPositionDp by remember { mutableStateOf(0.dp) }
    val commentTextFieldLines by remember {
        derivedStateOf {
            if (commentTextFieldHasFocus) {
                CARD_COMMENT_INPUT_MAX_LINES
            } else {
                CARD_COMMENT_INPUT_MIN_LINES
            }
        }
    }
    val textFieldHeight by animateDpAsState(
        targetValue = (commentTextFieldLines * TEXT_LINE_HEIGHT_DP).dp,
        label = "textFieldHeight"
    )
    val questionTextLocalized = questionText.localizedByLocal()

    LaunchedEffect(key1 = isKeyboardOpen) {
        if (!isKeyboardOpen) focusManager.clearFocus()
    }

    LaunchedEffect(cardPartHeightDp) {
        cardNode.onSmoothEnd = {
            val arScreenHeightPx = cardNode.model?.boundingBox?.size?.y?.times(ArCardActor.CARD_Z_FACTOR)

            arScreenHeightPx?.let {
                val questionTextAdjustment = if (questionTextLocalized.isNotBlank()) 0.002f else 0f
                val newScaleValue = cardPartHeightDp.value / screenHeightDpValue
                val arCardHeightPx = arScreenHeightPx * newScaleValue
                val newPositionY = arScreenHeightPx / 2f -
                    (arScreenHeightPx * cardPartPositionDp.value / screenHeightDpValue) -
                    arCardHeightPx / 2f +
                    questionTextAdjustment

                cardNode.smooth(
                    position = cardNode.position.copy(y = newPositionY),
                    scale = Scale(newScaleValue),
                    rotation = cardNode.rotation,
                    speed = 20f
                )
            }
        }
    }

    Column(
        modifier = modifier
            .fillMaxSize()
            .statusBarsPadding()
            .pointerInput(Unit) { detectTapGestures { focusManager.clearFocus() } }
    ) {
        if (questionTextLocalized.isNotBlank()) {
            Text(
                text = questionTextLocalized,
                style = CustomTheme.typography.body.secondary,
                color = CustomTheme.colors.text.primary,
                modifier = Modifier
                    .statusBarsPadding()
                    .padding(horizontal = 24.dp)
                    .offset(y = 45.dp)
                    .padding(bottom = 16.dp)
            )
        }

        Spacer(
            modifier = Modifier
                .weight(1f)
                .onGloballyPositioned {
                    cardPartPositionDp = with(density) { it.positionInWindow().y.toDp() }
                    cardPartHeightDp = with(density) { it.size.height.toDp() }
                }
        )

        cardHint?.let { hint ->
            HintBlock(
                card = selectedCard?.card,
                defaultCardHint = hint,
                isExpanded = isCardHintExpanded,
                onExpansion = { isCardHintExpanded = !isCardHintExpanded },
                isAr = true,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 24.dp)
            )
        }

        MetaTextField(
            value = selectedCard?.comment.orEmpty(),
            singleLine = false,
            onValueChanged = { value -> component.onCardCommentValueChanged(value) },
            minLines = commentTextFieldLines,
            maxLines = commentTextFieldLines,
            onFocusChanged = { commentTextFieldHasFocus = it.isFocused },
            placeholder = R.string.add_record_new_card_hint.strResDesc().localizedByLocal(),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
            heightMin = textFieldHeight +
                MetaTextFieldDefaults.InnerPaddings.calculateBottomPadding() +
                MetaTextFieldDefaults.InnerPaddings.calculateTopPadding(),
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp, horizontal = 16.dp),
            keyboardActions = KeyboardActions(onDone = { focusManager.clearFocus() })
        )

        AnimatedContent(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(
                    max = with(density) {
                        (ROW_BUTTON_BLOCK_HEIGHT_DP - WindowInsets.ime.getBottom(this)).toDp()
                    }
                ),
            targetState = canSelectCard,
            label = "ButtonVisibility"
        ) {
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp, horizontal = 16.dp)
                    .navigationBarsPadding()
            ) {
                MetaAccentButton(
                    modifier = Modifier.weight(1f),
                    text = if (component.isPredefined) {
                        com.metacards.metacards.core.R.string.common_proceed.strResDesc().localizedByLocal()
                    } else {
                        R.string.deck_layout_finish.strResDesc().localizedByLocal()
                    },
                    onClick = component::onFinishClick
                )

                when (it) {
                    CardListComponent.CanSelectCard.Can,
                    CardListComponent.CanSelectCard.CanLast -> {
                        MetaTransparentButton(
                            modifier = Modifier.weight(1f),
                            text = R.string.deck_layout_next_card.strResDesc().localizedByLocal(),
                            onClick = {
                                focusManager.clearFocus()
                                component.onMoreCardClick()
                            }
                        )
                    }

                    CardListComponent.CanSelectCard.Cant -> {}
                }
            }
        }
    }
}

@Preview
@Composable
fun ArCardSelectUiPreview() {
    AppTheme {
        ArCardSelectUi(
            modifier = Modifier.fillMaxSize(),
            baseComponent = FakeCardSelectComponent(),
            cards = CardsWithQuestionId(emptyList(), 0),
            topContent = {}
        )
    }
}
