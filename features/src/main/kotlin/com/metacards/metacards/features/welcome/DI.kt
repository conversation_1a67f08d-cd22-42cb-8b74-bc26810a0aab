package com.metacards.metacards.features.welcome

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.features.welcome.ui.RealWelcomeScreensComponent
import com.metacards.metacards.features.welcome.ui.WelcomeScreensComponent
import org.koin.core.component.get

fun ComponentFactory.createWelcomeScreensComponent(
    componentContext: ComponentContext,
    onOutput: (WelcomeScreensComponent.Output) -> Unit
): WelcomeScreensComponent {
    return RealWelcomeScreensComponent(componentContext, get(), onOutput)
}