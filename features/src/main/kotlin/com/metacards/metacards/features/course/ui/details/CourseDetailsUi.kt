package com.metacards.metacards.features.course.ui.details

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.arkivanov.decompose.extensions.compose.jetpack.stack.Children
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.features.course.ui.lesson.CourseLessonUi
import com.metacards.metacards.features.course.ui.page.CoursePageUi
import com.metacards.metacards.features.course.ui.passed_test_result.CoursePassedTestResultUi
import com.metacards.metacards.features.course.ui.test.CourseTestUi
import com.metacards.metacards.features.course.ui.test_result.CourseTestResultUi

@Composable
fun CourseDetailsUi(
    component: CourseDetailsComponent,
    modifier: Modifier = Modifier
) {
    val childStack by component.childStack.collectAsState()

    Children(childStack, modifier) { child ->
        when (val instance = child.instance) {
            is CourseDetailsComponent.Child.CoursePage -> CoursePageUi(instance.component)
            is CourseDetailsComponent.Child.Lesson -> CourseLessonUi(instance.component)
            is CourseDetailsComponent.Child.Test -> CourseTestUi(instance.component)
            is CourseDetailsComponent.Child.TestResult -> CourseTestResultUi(instance.component)
            is CourseDetailsComponent.Child.PassedTestResult -> CoursePassedTestResultUi(instance.component)
        }
    }
}

@Preview(showSystemUi = true)
@Composable
private fun Preview() {
    AppTheme {
        CourseDetailsUi(component = FakeCourseDetailsComponent())
    }
}