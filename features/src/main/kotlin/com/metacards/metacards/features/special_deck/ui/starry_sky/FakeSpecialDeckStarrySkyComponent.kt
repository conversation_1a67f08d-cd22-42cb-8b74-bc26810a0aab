package com.metacards.metacards.features.special_deck.ui.starry_sky

import com.metacards.metacards.core.bottom_sheet.BottomSheetControl
import com.metacards.metacards.core.bottom_sheet.FakeBottomSheetControl
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.FakeDialogControl
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCard
import com.metacards.metacards.features.special_deck.domain.entity.UserSpecialDeckInfo
import com.metacards.metacards.features.special_deck.ui.card_opening_cooldown.FakeSpecialDeckCardOpeningCooldownComponent
import com.metacards.metacards.features.special_deck.ui.card_opening_cooldown.SpecialDeckCardOpeningCooldownComponent
import com.metacards.metacards.features.special_deck.ui.swipe_tutorial.FakeSpecialDeckSwipeTutorialComponent
import com.metacards.metacards.features.special_deck.ui.swipe_tutorial.SpecialDeckSwipeTutorialComponent
import kotlinx.coroutines.flow.MutableStateFlow

class FakeSpecialDeckStarrySkyComponent : SpecialDeckStarrySkyComponent {
    override val deckInfo = MutableStateFlow(null)
    override val deckCards = MutableStateFlow<List<SpecialDeckCard>>(emptyList())
    override val userSpecialDeckInfo = MutableStateFlow(UserSpecialDeckInfo.mock())
    override val swipeTutorialControl:
        DialogControl<SpecialDeckSwipeTutorialComponent.Config, SpecialDeckSwipeTutorialComponent> =
        FakeDialogControl(FakeSpecialDeckSwipeTutorialComponent())
    override val cooldownBottomSheetControl: BottomSheetControl<
        SpecialDeckCardOpeningCooldownComponent.Config,
        SpecialDeckCardOpeningCooldownComponent
        > = FakeBottomSheetControl(FakeSpecialDeckCardOpeningCooldownComponent())
    override val isUserStoppedInteraction = MutableStateFlow(false)

    override fun onCardClick(card: SpecialDeckCard) = Unit
    override fun onUserInteracted() = Unit
    override fun onCloseClick() = Unit
    override fun onLayoutClick() = Unit
}