package com.metacards.metacards.features.special_deck.ui.starry_sky.widgets

import androidx.compose.foundation.layout.offset
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.unit.IntOffset
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeck

@Composable
fun SpecialDeckStarrySkyConstellationNames(
    deckInfo: SpecialDeck?,
    receivedCards: Set<CardId>,
    scaleCoefficient: Float
) {
    deckInfo?.let { info ->
        info.constellations.forEach { cons ->
            val isCollected = receivedCards.containsAll(cons.cardIds)
            if (isCollected) {
                Text(
                    text = cons.name.localizedByLocal(),
                    color = CustomTheme.colors.star.open,
                    modifier = Modifier
                        .offset {
                            IntOffset(
                                x = cons.nameAxisX
                                    .times(scaleCoefficient)
                                    .toInt(),
                                y = cons.nameAxisY
                                    .times(scaleCoefficient)
                                    .toInt()
                            )
                        }
                        .rotate(cons.nameAngle.toFloat())
                )
            }
        }
    }
}