package com.metacards.metacards.features.course.ui.passed_test_result

import com.ark<PERSON>nov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.activity.ActivityProvider
import com.metacards.metacards.core.adv.domain.YandexAdvHelper
import com.metacards.metacards.core.auth_suggestion_dialog.createAuthSuggestion
import com.metacards.metacards.core.createDefaultDialogComponent
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.model.DialogData
import com.metacards.metacards.core.dialog.dialogControl
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.localization.data.AppLanguageService
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.sharing.SharingManager
import com.metacards.metacards.core.user.domain.UserProvider
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.withProgress
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.course.domain.entity.CourseContentId
import com.metacards.metacards.features.course.domain.entity.CoursePassedTest
import com.metacards.metacards.features.course.domain.entity.CourseTestDocumentId
import com.metacards.metacards.features.course.domain.repository.CourseRepository
import com.metacards.metacards.features.layout.domain.CardSource
import com.metacards.metacards.features.layout.domain.GetPredefinedLayoutsInteractor
import com.metacards.metacards.features.layout.domain.LayoutId
import com.metacards.metacards.features.layout.domain.PredefinedLayoutWithAvailable
import com.metacards.metacards.features.video_player.VideoPlayerActivity
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

class RealCoursePassedTestResultComponent(
    componentContext: ComponentContext,
    componentFactory: ComponentFactory,
    override val testId: CourseContentId,
    private val testResultId: CourseTestDocumentId?,
    override val courseName: LocalizableString?,
    private val onOutput: (CoursePassedTestResultComponent.Output) -> Unit,
    courseRepository: CourseRepository,
    private val errorHandler: ErrorHandler,
    getPredefinedLayoutsInteractor: GetPredefinedLayoutsInteractor,
    private val userProvider: UserProvider,
    private val analyticsService: AnalyticsService,
    private val yandexAdvHelper: YandexAdvHelper,
    private val sharingManager: SharingManager,
    private val appLanguageService: AppLanguageService,
    private val activityProvider: ActivityProvider
) : ComponentContext by componentContext, CoursePassedTestResultComponent {

    private val coursePassedTest = MutableStateFlow<CoursePassedTest?>(null)

    override val courseResult = coursePassedTest.map { it?.toCourseResult() }
        .stateIn(componentScope, SharingStarted.Eagerly, null)

    override val title: StateFlow<LocalizableString> = coursePassedTest
        .map { it?.name ?: LocalizableString.empty }
        .stateIn(componentScope, SharingStarted.Eagerly, LocalizableString.empty)

    override val predefinedLayouts: StateFlow<List<PredefinedLayoutWithAvailable>> = computed(
        getPredefinedLayoutsInteractor.execute(), courseResult
    ) { allLayouts, result ->
        (result?.linkedLayouts ?: emptyList()).let { linkedLayoutIds ->
            allLayouts.filter { layout -> layout.layout.id in linkedLayoutIds }
        }
    }

    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        dialogControl(dialogComponentFactory = { config, componentContext, dialogControl ->
            componentFactory.createDefaultDialogComponent(
                componentContext,
                config.data,
                dialogControl::dismiss
            )
        })

    private var clickedLayoutId: LayoutId? = null

    override val isShareInProgress = MutableStateFlow(false)

    init {
        componentScope.safeLaunch(errorHandler) {
            val result = courseRepository.getUserPassedTestById(testResultId, testId)
            if (result == null) {
                onOutput(CoursePassedTestResultComponent.Output.CloseRequested)
            } else {
                coursePassedTest.value = result
            }
        }
    }

    override fun onCloseClick() {
        onOutput(CoursePassedTestResultComponent.Output.CloseRequested)
    }

    override fun onShareClick() {
        val cover = courseResult.value?.cover
        val title = courseResult.value?.subtitle ?: return
        val description = courseResult.value?.description ?: return
        val appLanguage = appLanguageService.currentAppLanguage.value
        componentScope.safeLaunch(errorHandler) {
            withProgress(isShareInProgress) {
                sharingManager.shareCourseTestResult(
                    imageUrl = cover,
                    title = title.toString(appLanguage),
                    text = description.toString(appLanguage)
                )
            }
        }
    }

    override fun onVideoFullScreenClick(position: Long, videoUrl: String) {
        componentScope.launch {
            activityProvider.awaitActivity().run {
                VideoPlayerActivity.start(
                    videoUrl = videoUrl,
                    context = this,
                    startWithLandscape = true,
                    playOnStart = true,
                    position = position
                )
            }
        }
    }

    override fun onRepeatTestClick() {
        onOutput(CoursePassedTestResultComponent.Output.RepeatTestRequested(testId))
    }

    override fun onLayoutClick(layoutId: LayoutId) {
        predefinedLayouts.value.find { it.layout.id == layoutId }?.let {
            if (userProvider.getUser().value?.subscriptionState?.canLayout == false) {
                clickedLayoutId = layoutId
                subscribeForAdvReward()
                onOutput(CoursePassedTestResultComponent.Output.SubscriptionBottomSheetRequested(withAdv = true))
            } else {
                analyticsService.logEvent(
                    AnalyticsEvent.LayoutChooseEvent(
                        layoutId = layoutId.value,
                        layoutName = it.layout.name.toString(appLanguageService.getLanguage())
                    )
                )
                onOutput(
                    CoursePassedTestResultComponent.Output.LayoutDetailsRequested(
                        CardSource.Questions(it.layout.questions),
                        coursePassedTest.value?.courseId,
                        coursePassedTest.value?.themeId
                    )
                )
            }
        }
    }

    override fun onBlockContentClick() {
        if (userProvider.getUser().value == null) {
            dialogControl.show(
                DefaultDialogComponent.Config(
                    DialogData.createAuthSuggestion(
                        cancelAction = dialogControl::dismiss,
                        acceptAction = {
                            analyticsService.logEvent(AnalyticsEvent.AccountAuthEvent)
                            onOutput(CoursePassedTestResultComponent.Output.AuthScreenRequested)
                        }
                    )
                )
            )
        } else {
            onOutput(CoursePassedTestResultComponent.Output.SubscriptionBottomSheetRequested(withAdv = false))
        }
    }

    private fun subscribeForAdvReward() {
        yandexAdvHelper.subscribeForReward {
            predefinedLayouts.value.find { it.layout.id == clickedLayoutId }?.let {
                analyticsService.logEvent(
                    AnalyticsEvent.LayoutChooseEvent(
                        layoutId = it.layout.id.value,
                        layoutName = it.layout.name.toString(appLanguageService.getLanguage())
                    )
                )
                onOutput(
                    CoursePassedTestResultComponent.Output.LayoutDetailsRequested(
                        CardSource.Questions(it.layout.questions),
                        coursePassedTest.value?.courseId,
                        coursePassedTest.value?.themeId
                    )
                )
                clickedLayoutId = null
            }
        }
    }
}