package com.metacards.metacards.features.auth.domain.email

import com.metacards.metacards.core.user.domain.Email
import com.metacards.metacards.features.auth.domain.Password

interface EmailAuthRepository {
    suspend fun hasAccount(email: Email): Boolean
    suspend fun auth(email: Em<PERSON>, password: Password)
    fun logout()
    suspend fun resetPassword(email: Email)
    suspend fun sendEmailVerifyMessage()
    suspend fun create(email: Email, password: Password)
    suspend fun updateEmail(email: Email, password: Password)
    suspend fun tryAuthWithPassword(password: String)
    suspend fun updatePassword(oldPassword: String, newPassword: String)
}