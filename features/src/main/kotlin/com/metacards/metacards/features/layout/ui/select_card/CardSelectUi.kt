package com.metacards.metacards.features.layout.ui.select_card

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.RoundRect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.ClipOp
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.clipPath
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.layout.positionInRoot
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.LceWidget
import com.metacards.metacards.core.widget.WidgetPosition
import com.metacards.metacards.core.widget.button.MetaSecondaryButton
import com.metacards.metacards.core.widget.navigation_bar.IconNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.layout.ui.select_card.ar.ArCardSelectUi
import com.metacards.metacards.features.layout.ui.select_card.flat.FlatSelectUi
import com.metacards.metacards.features.layout.ui.select_card.widget.TabsLayout
import com.metacards.metacards.features.tutorial.data.TutorialStep
import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import com.metacards.metacards.features.tutorial.ui.MessagePopupContent
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.desc
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import com.metacards.metacards.core.R as CoreR

val LocalInteractionEnabled = compositionLocalOf { true }
private const val TAB_VERTICAL_PADDING = 8

@Composable
fun CardSelectUi(
    component: CardSelectComponent,
    modifier: Modifier = Modifier
) {
    val selectedTab by component.selectedTab.collectAsState()
    val cards by component.cards.collectAsState()
    val tutorialStep by component.tutorialStep.collectAsState()
    val tutorialMessage by component.tutorialMessage.collectAsState()
    val questionText by component.questionText.collectAsState()
    val isInteractionEnabled by component.isInteractionEnabled.collectAsState()
    var tabBarSize by remember { mutableStateOf(WidgetPosition.initial) }
    val ld = LocalDensity.current

    CompositionLocalProvider(LocalInteractionEnabled provides isInteractionEnabled) {
        BoxWithFade(
            modifier = modifier,
            listOfColors = CustomTheme.colors.gradient.backgroundList
        ) {
            LceWidget(cards, {}) { data, _ ->
                BaseSelectUi(
                    modifier = Modifier.fillMaxSize(),
                    selectedTab = selectedTab,
                    onChange = component::onTabChange,
                    tabs = component.tabs,
                    cards = data,
                    question = questionText,
                    baseComponent = component,
                    onCloseClick = component::onCloseClick,
                    onTabMeasured = { tabBarSize = it }
                )
            }
            tutorialMessage?.let {
                when (tutorialStep) {
                    TutorialStep.CARD_SELECT_TABS -> SelectTabTutorMessage(it, ld, tabBarSize)
                    TutorialStep.CARD_SELECT -> CardSelectTutorMessage(ld, tabBarSize, it)
                    else -> Unit
                }
            }
        }
    }
}

@Composable
private fun BaseSelectUi(
    selectedTab: CardSelectComponent.Tabs,
    onChange: (CardSelectComponent.Tabs) -> Unit,
    tabs: List<CardSelectComponent.Tabs>,
    onCloseClick: (Boolean) -> Unit,
    cards: CardsWithQuestionId,
    baseComponent: CardSelectComponent,
    question: StringDesc,
    onTabMeasured: (WidgetPosition) -> Unit,
    modifier: Modifier = Modifier
) {
    LaunchedEffect(Unit) {
        baseComponent.arComponent.reloadEvent.onEach {
            onChange(CardSelectComponent.Tabs.Flat)
        }.launchIn(this)
    }

    CardSelectContent(
        selectedTab = selectedTab,
        cards = cards,
        baseComponent = baseComponent,
        onChange = onChange,
        tabs = tabs,
        onCloseClick = onCloseClick,
        question = question,
        onTabMeasured = onTabMeasured,
        modifier = modifier
    )
}

@Composable
private fun CardSelectContent(
    selectedTab: CardSelectComponent.Tabs,
    cards: CardsWithQuestionId,
    baseComponent: CardSelectComponent,
    onChange: (CardSelectComponent.Tabs) -> Unit,
    tabs: List<CardSelectComponent.Tabs>,
    onCloseClick: (Boolean) -> Unit,
    question: StringDesc,
    onTabMeasured: (WidgetPosition) -> Unit,
    modifier: Modifier = Modifier,
) {
    if (cards.cards.isEmpty()) return
    val isCameraPermissionGranted by baseComponent.isCameraPermissionGranted.collectAsState()

    when (selectedTab) {
        CardSelectComponent.Tabs.Flat -> {
            Column(modifier) {
                CardSelectHeader(
                    selectedTab = selectedTab,
                    onChange = onChange,
                    tabs = tabs,
                    onCloseClick = { onCloseClick(false) },
                    baseComponent = baseComponent,
                    question = question,
                    onTabMeasured = onTabMeasured
                )

                FlatSelectUi(
                    cards = cards.cards,
                    component = baseComponent,
                    modifier = Modifier
                        .imePadding()
                )
            }
        }

        CardSelectComponent.Tabs.AR -> {
            if (isCameraPermissionGranted) {
                ArCardSelectUi(
                    modifier = Modifier.fillMaxSize(),
                    baseComponent = baseComponent,
                    cards = cards,
                    topContent = {
                        CardSelectHeader(
                            selectedTab = selectedTab,
                            onChange = onChange,
                            tabs = tabs,
                            onCloseClick = {
                                onCloseClick(true)
                            },
                            baseComponent = baseComponent,
                            question = question,
                            onTabMeasured = onTabMeasured
                        )
                    }
                )
            } else {
                CameraPermissionRequiredUi(
                    selectedTab = selectedTab,
                    onChange = onChange,
                    tabs = tabs,
                    onCloseClick = {
                        onCloseClick(true)
                    },
                    baseComponent = baseComponent,
                    question = question,
                    onTabMeasured = onTabMeasured
                )
            }
        }
    }
}

@Composable
private fun CardSelectHeader(
    selectedTab: CardSelectComponent.Tabs,
    onChange: (CardSelectComponent.Tabs) -> Unit,
    tabs: List<CardSelectComponent.Tabs>,
    onCloseClick: () -> Unit,
    baseComponent: CardSelectComponent,
    question: StringDesc,
    onTabMeasured: (WidgetPosition) -> Unit,
    modifier: Modifier = Modifier,
) {
    val predefinedQuestions = baseComponent.predefinedQuestions
    val activeQuestionIndex by baseComponent.activeQuestionIndex.collectAsState()

    Column(modifier = modifier.statusBarsPadding()) {
        TopNavigationBar(
            title = if (baseComponent.isPredefinedLayout) question else "".desc(),
            leadingIcon = {
                IconNavigationItem(iconRes = CoreR.drawable.ic_24_close, onClick = onCloseClick)
            }
        )

        if (baseComponent.isPredefinedLayout) {
            Text(
                text = predefinedQuestions[activeQuestionIndex].text.localizedByLocal(),
                style = CustomTheme.typography.body.secondary,
                color = CustomTheme.colors.text.primary,
                modifier = Modifier.padding(horizontal = 24.dp, vertical = 8.dp)
            )
        } else if (question.localizedByLocal().isNotBlank()) {
            Text(
                text = question.localizedByLocal(),
                style = CustomTheme.typography.body.secondary,
                color = CustomTheme.colors.text.primary,
                modifier = Modifier.padding(horizontal = 24.dp, vertical = 8.dp)
            )
        }

        TabsLayout(
            tabs = tabs,
            selectedTab = selectedTab,
            onChange = onChange,
            paddingValues = PaddingValues(vertical = TAB_VERTICAL_PADDING.dp),
            innerPaddingValues = PaddingValues(vertical = 7.5.dp),
            modifier = Modifier
                .padding(horizontal = 24.dp)
                .onGloballyPositioned { layoutCoordinates ->
                    val positionInRoot = layoutCoordinates.positionInRoot()
                    onTabMeasured(
                        WidgetPosition(
                            xOffset = positionInRoot.x,
                            yOffset = positionInRoot.y,
                            layoutCoordinates.size
                        )
                    )
                }
        )
    }
}

@Composable
private fun CameraPermissionRequiredUi(
    selectedTab: CardSelectComponent.Tabs,
    onChange: (CardSelectComponent.Tabs) -> Unit,
    tabs: List<CardSelectComponent.Tabs>,
    onCloseClick: () -> Unit,
    baseComponent: CardSelectComponent,
    question: StringDesc,
    onTabMeasured: (WidgetPosition) -> Unit,
) {
    CardSelectHeader(
        selectedTab = selectedTab,
        onChange = onChange,
        tabs = tabs,
        onCloseClick = onCloseClick,
        baseComponent = baseComponent,
        question = question,
        onTabMeasured = onTabMeasured
    )
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            painter = painterResource(id = R.drawable.ic_64_cube),
            tint = CustomTheme.colors.icons.disabled,
            contentDescription = null
        )

        Spacer(modifier = Modifier.size(16.dp))

        Text(
            text = R.string.deck_layout_camera_permission_denied_header.strResDesc().localizedByLocal(),
            style = CustomTheme.typography.heading.medium,
            color = CustomTheme.colors.text.primary,
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth()
        )

        Spacer(modifier = Modifier.size(8.dp))
        Text(
            modifier = Modifier.fillMaxWidth(),
            text = R.string.camera_permission_description.strResDesc().localizedByLocal(),
            color = CustomTheme.colors.text.primary,
            style = CustomTheme.typography.body.primary,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.size(16.dp))
        MetaSecondaryButton(
            modifier = Modifier.fillMaxWidth(),
            text = R.string.camera_permission_button_title.strResDesc().localizedByLocal(),
            onClick = baseComponent::openSettings
        )
    }
}

@Composable
private fun CardSelectTutorMessage(
    ld: Density,
    tabBarSize: WidgetPosition,
    tutorialMessage: TutorialMessage
) {
    var popupHeight by remember { mutableStateOf(0.dp) }
    val popupYOffset = with(ld) {
        tabBarSize.yOffset.toDp() + tabBarSize.size.height.toDp() - popupHeight + 8.dp
    }
    val nonClickZoneHeight = popupYOffset + popupHeight
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(nonClickZoneHeight)
            .background(CustomTheme.colors.background.disabledBackground)
            .clickable(enabled = false) { },
        contentAlignment = Alignment.TopCenter
    ) {
        val alpha by animateFloatAsState(targetValue = if (popupHeight == 0.dp) 0f else 1f, label = "")
        MessagePopupContent(
            tutorialMessage = tutorialMessage,
            modifier = Modifier
                .offset(y = popupYOffset)
                .onSizeChanged { with((ld)) { popupHeight = it.height.toDp() } }
                .alpha(alpha)
        )
    }
}

@Composable
private fun SelectTabTutorMessage(
    tutorialMessage: TutorialMessage,
    ld: Density,
    tabBarSize: WidgetPosition
) {
    val messageYOffset = tabBarSize.yOffset + tabBarSize.size.height
    Box(modifier = Modifier.fillMaxSize()) {
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .clickable(enabled = false) { }
        ) {
            drawIntoCanvas {
                clipPath(
                    Path().apply {
                        addRoundRect(
                            RoundRect(
                                Rect(
                                    offset = Offset(
                                        y = tabBarSize.yOffset + (TAB_VERTICAL_PADDING * ld.density),
                                        x = tabBarSize.xOffset
                                    ),
                                    size = Size(
                                        width = tabBarSize.size.width.toFloat(),
                                        height = tabBarSize.size.height - (TAB_VERTICAL_PADDING * ld.density * 2)
                                    )
                                ),
                                cornerRadius = CornerRadius(8f * ld.density)
                            )
                        )
                    },
                    ClipOp.Difference
                ) {
                    drawRoundRect(
                        color = CustomTheme.colors.background.disabledBackground,
                        cornerRadius = CornerRadius(1f)
                    )
                }
            }
        }
        MessagePopupContent(
            tutorialMessage = tutorialMessage,
            modifier = Modifier
                .align(Alignment.TopCenter)
                .offset(
                    y = with(ld) {
                        messageYOffset
                            .toDp()
                            .plus(4.dp)
                    }
                )
        )
    }
}

@Preview
@Composable
private fun CardSelectUiPreview() {
    AppTheme {
        CardSelectUi(component = FakeCardSelectComponent())
    }
}