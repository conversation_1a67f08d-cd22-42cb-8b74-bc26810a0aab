package com.metacards.metacards.features.special_deck.ui.deck_obtained

import com.arkivanov.decompose.ComponentContext

class RealSpecialDeckDeckObtainedComponent(
    componentContext: ComponentContext,
    private val onOutput: (SpecialDeckDeckObtainedComponent.Output) -> Unit
) : ComponentContext by componentContext, SpecialDeckDeckObtainedComponent {

    override fun onGoToStarrySkyClick() {
        onOutput(SpecialDeckDeckObtainedComponent.Output.StarrySkyRequested)
    }
}