package com.metacards.metacards.features.record.ui.journal

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.arkivanov.decompose.extensions.compose.jetpack.stack.Children
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.Fade
import com.metacards.metacards.core.widget.SkeletonElement
import com.metacards.metacards.core.widget.navigation_bar.HeaderNavigationBar
import com.metacards.metacards.core.widget.navigation_bar.IconNavigationItem
import com.metacards.metacards.core.widget.tab.MetaTabs
import com.metacards.metacards.features.R
import com.metacards.metacards.features.record.ui.journal.test_list.JournalTestListUi
import com.metacards.metacards.features.record.ui.record_list.RecordListUi
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun JournalUi(
    component: JournalComponent,
    navBarHeight: Dp,
    modifier: Modifier = Modifier
) {
    val tabsState by component.tabsState.collectAsState()
    val childStack by component.childStack.collectAsState()

    BoxWithFade(modifier, listOfColors = CustomTheme.colors.gradient.backgroundList) {
        Column(Modifier.matchParentSize()) {
            HeaderNavigationBar(
                title = R.string.journal_title.strResDesc(),
                trailingIcon = {
                    IconNavigationItem(
                        R.drawable.ic_24_calendar,
                        onClick = component::onCalendarClick
                    )

                    Spacer(modifier = Modifier.width(10.dp))

                    IconNavigationItem(
                        R.drawable.ic_24_archive,
                        onClick = component::onArchiveClick
                    )
                }
            )

            JournalTabs(
                tabsState,
                selectedTab = when (val instance = childStack.active.instance) {
                    is JournalComponent.Child.RecordList -> instance.tab
                    is JournalComponent.Child.TestsList -> JournalComponent.Tab.Tests
                },
                onTabSelected = component::onTabSelected
            )

            Children(stack = childStack) { child ->
                when (val instance = child.instance) {
                    is JournalComponent.Child.RecordList -> RecordListUi(
                        modifier = modifier
                            .fillMaxWidth()
                            .weight(1.0f),
                        component = instance.component
                    )

                    is JournalComponent.Child.TestsList -> JournalTestListUi(
                        component = instance.component,
                        navBarHeight = navBarHeight,
                        modifier = modifier
                            .fillMaxWidth()
                            .weight(1.0f)
                    )
                }
            }
        }

        Fade(
            modifier = Modifier
                .fillMaxWidth()
                .height(121.dp)
                .align(Alignment.BottomCenter)
        )
    }
}

@Composable
private fun JournalTabs(
    tabsState: TabsState,
    selectedTab: JournalComponent.Tab,
    onTabSelected: (JournalComponent.Tab) -> Unit,
    modifier: Modifier = Modifier
) {
    when (tabsState) {

        TabsState.Normal -> {
            MetaTabs(
                modifier = modifier.padding(start = 10.dp, end = 10.dp, top = 4.dp, bottom = 12.dp),
                selectedTabIndex = selectedTab.toIndex(),
                tabItems = listOf(
                    R.string.journal_tab_layouts.strResDesc(),
                    R.string.journal_tab.strResDesc(),
                    R.string.journal_tab_favourite.strResDesc()
                ),
                onTabClicked = {
                    onTabSelected(it.toTab())
                }
            )
        }

        TabsState.Skeleton -> {
            Row(
                modifier = modifier.padding(start = 10.dp, end = 10.dp, top = 8.dp, bottom = 12.dp)
            ) {
                SkeletonElement(
                    cornerRadius = 8.dp,
                    modifier = Modifier.size(70.dp, 28.dp)
                )

                SkeletonElement(
                    cornerRadius = 8.dp,
                    modifier = Modifier
                        .padding(start = 12.dp)
                        .size(120.dp, 28.dp)
                )
            }
        }

        TabsState.Hidden -> {
            Spacer(
                modifier = modifier
                    .fillMaxWidth()
                    .height(44.dp)
            )
        }
    }
}

private fun JournalComponent.Tab.toIndex(): Int = when (this) {
    JournalComponent.Tab.Layouts -> 0
    JournalComponent.Tab.Tests -> 1
    JournalComponent.Tab.Favourite -> 2
}

private fun Int.toTab(): JournalComponent.Tab = when (this) {
    0 -> JournalComponent.Tab.Layouts
    1 -> JournalComponent.Tab.Tests
    else -> JournalComponent.Tab.Favourite
}

@Preview(showSystemUi = true)
@Composable
fun JournalUiPreview() {
    AppTheme {
        JournalUi(FakeJournalComponent(), navBarHeight = 0.dp)
    }
}