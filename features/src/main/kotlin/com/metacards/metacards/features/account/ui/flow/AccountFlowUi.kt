package com.metacards.metacards.features.account.ui.flow

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.arkivanov.decompose.extensions.compose.jetpack.stack.Children
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.features.account.ui.about_the_app.AboutTheAppUi
import com.metacards.metacards.features.account.ui.about_the_app.about_us.AboutUsUi
import com.metacards.metacards.features.account.ui.feedback.FeedbackUi
import com.metacards.metacards.features.account.ui.lessons.LessonsUi
import com.metacards.metacards.features.account.ui.profile.ProfileFlowUi
import com.metacards.metacards.features.account.ui.promocodes.PromocodesUi
import com.metacards.metacards.features.account.ui.shop.ShopUi
import com.metacards.metacards.features.user.ui.subscription.SubscriptionUi

@Composable
fun AccountFlowUi(
    component: AccountFlowComponent,
    modifier: Modifier = Modifier,
) {
    val childStack by component.childStack.collectAsState()

    Box(
        modifier = modifier
            .statusBarsPadding()
    ) {
        Children(stack = childStack, modifier = Modifier.fillMaxSize()) { child ->
            when (val instance = child.instance) {
                is AccountFlowComponent.Child.Subscription -> SubscriptionUi(
                    instance.component,
                    Modifier.fillMaxSize()
                )

                is AccountFlowComponent.Child.Lessons -> LessonsUi(
                    instance.component,
                    Modifier.fillMaxSize()
                )

                is AccountFlowComponent.Child.AboutTheApp -> AboutTheAppUi(
                    instance.component,
                    Modifier.fillMaxSize()
                )

                is AccountFlowComponent.Child.AboutUs -> AboutUsUi(
                    Modifier.fillMaxSize()
                )

                is AccountFlowComponent.Child.Shop -> ShopUi(
                    instance.component,
                    Modifier.fillMaxSize()
                )

                is AccountFlowComponent.Child.ProfileFlow -> ProfileFlowUi(
                    instance.component,
                    Modifier.fillMaxSize()
                )

                is AccountFlowComponent.Child.Feedback -> FeedbackUi(
                    instance.component,
                    Modifier.fillMaxSize()
                )

                is AccountFlowComponent.Child.Promocodes -> {
                    PromocodesUi(Modifier.fillMaxSize(), instance.component)
                }
            }
        }
    }
}

@Preview
@Composable
fun AccountFlowUiPreview() {
    AppTheme {
        AccountFlowUi(component = FakeAccountFlowComponent())
    }
}
