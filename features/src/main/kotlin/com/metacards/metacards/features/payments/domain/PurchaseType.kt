package com.metacards.metacards.features.payments.domain

import com.metacards.metacards.features.payments.data.BillingServiceImpl.Companion.SUBSCRIPTION_ID

sealed class PurchaseType(open val storeId: String, open val firebaseId: String) {
    sealed class Subscription(id: String = SUBSCRIPTION_ID, firebaseId: String) :
        PurchaseType(id, firebaseId) {
        data class New(
            val basePlanId: String,
            val discountPlanId: String?,
            override val firebaseId: String
        ) :
            Subscription(firebaseId = firebaseId)

        data class Upgrade(
            val oldBasePlanId: String,
            val newBasePlanId: String,
            val discountPlanId: String?,
            override val firebaseId: String
        ) : Subscription(firebaseId = firebaseId)
    }

    data class Deck(
        override val storeId: String,
        override val firebaseId: String,
        val name: String,
    ) : PurchaseType(storeId, firebaseId)
}