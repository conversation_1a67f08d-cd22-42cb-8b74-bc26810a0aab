package com.metacards.metacards.features.user.ui.subscription.widget

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.widget.Fade
import com.metacards.metacards.core.widget.FadeDefaults
import dev.icerock.moko.resources.desc.StringDesc

@Composable
fun SubscriptionOptionItem(
    iconRes: Int,
    iconTint: Color,
    iconBackgroundColorsList: List<Color>,
    titleText: StringDesc,
    mainText: StringDesc,
    modifier: Modifier = Modifier,
    iconBackgroundFadeDirection: FadeDefaults.Direction = FadeDefaults.Direction.Horizontal
) {
    Card(
        shape = RoundedCornerShape(16.dp),
        backgroundColor = CustomTheme.colors.background.modal,
        elevation = 0.dp,
        modifier = modifier
    ) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .size(48.dp)
                    .clip(CircleShape)
            ) {
                Fade(
                    modifier = modifier.matchParentSize(),
                    listOfColors = iconBackgroundColorsList,
                    direction = iconBackgroundFadeDirection
                )

                Icon(
                    painter = painterResource(id = iconRes),
                    contentDescription = null,
                    tint = iconTint
                )
            }

            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier.fillMaxHeight()
            ) {
                Text(
                    text = titleText.localizedByLocal(),
                    color = CustomTheme.colors.text.primary,
                    style = CustomTheme.typography.heading.medium
                )

                Text(
                    text = mainText.localizedByLocal(),
                    color = CustomTheme.colors.text.secondary,
                    style = CustomTheme.typography.body.primary
                )
            }
        }
    }
}