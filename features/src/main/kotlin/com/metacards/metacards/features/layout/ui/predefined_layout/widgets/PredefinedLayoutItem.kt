package com.metacards.metacards.features.layout.ui.predefined_layout.widgets

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.navigation_bar.IconNavigationItem
import com.metacards.metacards.features.R
import com.metacards.metacards.features.home.ui.widget.ActionButton
import com.metacards.metacards.features.layout.domain.LayoutId
import com.metacards.metacards.features.layout.domain.PredefinedLayoutWithAvailable
import dev.icerock.moko.resources.desc.StringDesc

@Composable
fun PredefinedLayoutItem(
    layoutWithAvailable: PredefinedLayoutWithAvailable,
    onLayoutClick: (LayoutId) -> Unit,
    onBlockContentClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    if (!layoutWithAvailable.isAvailable) {
        BoxWithFade(
            modifier = modifier
                .clip(RoundedCornerShape(16.dp)),
            listOfColors = CustomTheme.colors.gradient.backgroundList.map {
                it.copy(alpha = 0.75f)
            },
            behindContent = {
                PredefinedLayoutContent(
                    predefinedLayout = layoutWithAvailable,
                    onLayoutClick = onLayoutClick
                )
            }
        ) {
            Box(
                modifier = Modifier
                    .matchParentSize()
                    .clickable(onClick = onBlockContentClick)
                    .align(Alignment.TopCenter)
            ) {
                ActionButton(
                    modifier = Modifier
                        .align(Alignment.TopCenter)
                        .padding(top = 64.dp),
                    text = StringDesc.Resource(R.string.add_record_premium_action_button),
                    borderStroke = BorderStroke(
                        0.5.dp,
                        CustomTheme.colors.stroke.secondary
                    ),
                    leadingIcon = {
                        IconNavigationItem(iconRes = R.drawable.ic_24_locked)
                    }
                )
            }
        }
    } else {
        PredefinedLayoutContent(
            predefinedLayout = layoutWithAvailable,
            onLayoutClick = onLayoutClick,
            modifier = modifier
        )
    }
}