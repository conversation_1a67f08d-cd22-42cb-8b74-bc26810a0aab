package com.metacards.metacards.features.special_deck.ui

import android.os.Parcelable
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.stack.ChildStack
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.router.stack.bringToFront
import com.arkivanov.decompose.router.stack.childStack
import com.arkivanov.decompose.router.stack.pop
import com.arkivanov.decompose.router.stack.popWhile
import com.arkivanov.decompose.router.stack.push
import com.arkivanov.decompose.router.stack.replaceAll
import com.arkivanov.decompose.router.stack.replaceCurrent
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.dialogControl
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.toStateFlow
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.special_deck.createSpecialDeckConstellationObtainedComponent
import com.metacards.metacards.features.special_deck.createSpecialDeckDeckObtainedComponent
import com.metacards.metacards.features.special_deck.createSpecialDeckFirstCardObtainedComponent
import com.metacards.metacards.features.special_deck.createStarDeckOnboardingComponent
import com.metacards.metacards.features.special_deck.createStarDetailsComponent
import com.metacards.metacards.features.special_deck.createStarrySkyComponent
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCard
import com.metacards.metacards.features.special_deck.domain.entity.UserSpecialDeckInfo
import com.metacards.metacards.features.special_deck.domain.repository.SpecialDeckRepository
import com.metacards.metacards.features.special_deck.ui.card_details.SpecialDeckCardDetailsComponent
import com.metacards.metacards.features.special_deck.ui.constellation_obtained.SpecialDeckConstellationObtainedComponent
import com.metacards.metacards.features.special_deck.ui.deck_obtained.SpecialDeckDeckObtainedComponent
import com.metacards.metacards.features.special_deck.ui.first_card_obtained.SpecialDeckFirstCardObtainedComponent
import com.metacards.metacards.features.special_deck.ui.starry_sky.SpecialDeckStarrySkyComponent
import com.metacards.metacards.features.special_deck.ui.tutorial.SpecialDeckOnboardingComponent
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.parcelize.Parcelize

class RealSpecialDeckComponent(
    componentContext: ComponentContext,
    private val componentFactory: ComponentFactory,
    private val specialDeckRepository: SpecialDeckRepository,
    private val errorHandler: ErrorHandler,
    private val onOutput: (SpecialDeckComponent.Output) -> Unit,
    private val deckId: DeckId,
    private val forceOnboarding: Boolean
) : ComponentContext by componentContext, SpecialDeckComponent {

    private val navigation = StackNavigation<ChildConfig>()

    override val childStack: StateFlow<ChildStack<*, SpecialDeckComponent.Child>> = childStack(
        source = navigation,
        initialConfiguration = ChildConfig.StarrySky,
        handleBackButton = true,
        childFactory = ::createChild
    ).toStateFlow(lifecycle)

    private val userSpecialDeckInfo = specialDeckRepository
        .getUserSpecialDeckInfoByIdFlow(deckId)
        .stateIn(componentScope, SharingStarted.Eagerly, null)

    override val constellationObtainedControl:
        DialogControl<SpecialDeckConstellationObtainedComponent.Config, SpecialDeckConstellationObtainedComponent> =
        dialogControl(
            key = "constellationObtainedControl",
            dialogComponentFactory = { config, componentContext, _ ->
                componentFactory.createSpecialDeckConstellationObtainedComponent(
                    componentContext,
                    config.imageUrl,
                    config.header,
                    config.description,
                    ::onConstellationObtainedOutput
                )
            }
        )

    override val firstCardObtainedControl:
        DialogControl<SpecialDeckFirstCardObtainedComponent.Config, SpecialDeckFirstCardObtainedComponent> =
        dialogControl(
            key = "firstCardObtainedControl",
            dialogComponentFactory = { config, componentContext, _ ->
                componentFactory.createSpecialDeckFirstCardObtainedComponent(
                    componentContext,
                    config.imageUrl,
                    ::onFirstCardObtainedOutput
                )
            }
        )

    private fun onConstellationObtainedOutput(output: SpecialDeckConstellationObtainedComponent.Output) {
        when (output) {
            is SpecialDeckConstellationObtainedComponent.Output.DismissRequested -> {
                constellationObtainedControl.dismiss()
                if (userSpecialDeckInfo.value?.isCompleted == true) {
                    navigation.push(ChildConfig.DeckObtained)
                }
            }
        }
    }

    private fun onFirstCardObtainedOutput(output: SpecialDeckFirstCardObtainedComponent.Output) {
        when (output) {
            SpecialDeckFirstCardObtainedComponent.Output.DismissRequested -> firstCardObtainedControl.dismiss()
        }
    }

    init {
        componentScope.safeLaunch(errorHandler) {
            val userSpecialDeckInfoCards = specialDeckRepository
                .getUserSpecialDeckInfoById(deckId)
                ?.cards
                .orEmpty()
            val specialDeck = specialDeckRepository
                .getSpecialDeckById(deckId)
                ?: return@safeLaunch
            if (forceOnboarding || userSpecialDeckInfoCards.isEmpty()) {
                navigation.replaceAll(ChildConfig.Onboarding(specialDeck.name))
            }
        }
    }

    private fun createChild(
        childConfig: ChildConfig,
        componentContext: ComponentContext
    ) = when (childConfig) {
        is ChildConfig.CardDetails -> SpecialDeckComponent.Child.StarDetails(
            componentFactory.createStarDetailsComponent(
                componentContext,
                deckId,
                childConfig.card,
                childConfig.userSpecialDeckInfo,
                childConfig.cardEffectType,
                ::onStarDetailsOutput
            )
        )

        is ChildConfig.Onboarding -> SpecialDeckComponent.Child.Onboarding(
            componentFactory.createStarDeckOnboardingComponent(
                componentContext,
                childConfig.deckTitle,
                ::onOnboardingOutput
            )
        )

        is ChildConfig.StarrySky -> SpecialDeckComponent.Child.StarrySky(
            componentFactory.createStarrySkyComponent(componentContext, ::onStarrySkyComponentOutput, deckId)
        )

        is ChildConfig.DeckObtained -> SpecialDeckComponent.Child.DeckObtained(
            componentFactory.createSpecialDeckDeckObtainedComponent(
                componentContext,
                ::onDeckObtainedOutput
            )
        )
    }

    private fun onDeckObtainedOutput(output: SpecialDeckDeckObtainedComponent.Output) {
        when (output) {
            is SpecialDeckDeckObtainedComponent.Output.StarrySkyRequested -> {
                navigation.bringToFront(ChildConfig.StarrySky)
            }
        }
    }

    private fun onOnboardingOutput(output: SpecialDeckOnboardingComponent.Output) {
        when (output) {
            is SpecialDeckOnboardingComponent.Output.OnboardingCompleted -> {
                navigation.replaceCurrent(ChildConfig.StarrySky)
            }
        }
    }

    private fun onStarDetailsOutput(output: SpecialDeckCardDetailsComponent.Output) {
        when (output) {
            is SpecialDeckCardDetailsComponent.Output.CardReleased -> {
                componentScope.safeLaunch(errorHandler) {
                    val userSpecialDeckInfo = userSpecialDeckInfo.value
                        ?: return@safeLaunch
                    val specialDeck = specialDeckRepository
                        .getSpecialDeckById(deckId)
                        ?: return@safeLaunch
                    val isFirstCard = userSpecialDeckInfo.cards.size == 1
                    val isDeckCompleted = userSpecialDeckInfo.isCompleted

                    val constellationOfOpenedCard = specialDeck
                        .constellations
                        .find { it.cardIds.contains(output.card.id) }

                    val isConstellationCompleted = constellationOfOpenedCard
                        ?.let { constellation ->
                            userSpecialDeckInfo
                                .cards
                                .map { it.cardId }
                                .containsAll(constellation.cardIds)
                        }
                        ?: false

                    when {
                        isFirstCard -> {
                            navigation.popWhile { it !is ChildConfig.StarrySky }
                            firstCardObtainedControl.show(
                                SpecialDeckFirstCardObtainedComponent.Config(
                                    imageUrl = output.card.imageUrl
                                )
                            )
                        }

                        isConstellationCompleted -> {
                            val constellation = constellationOfOpenedCard ?: return@safeLaunch
                            navigation.popWhile { it !is ChildConfig.StarrySky }
                            constellationObtainedControl.show(
                                SpecialDeckConstellationObtainedComponent.Config(
                                    imageUrl = constellation.bannerImg,
                                    header = constellation.bannerTitleLocalized,
                                    description = constellation.bannerDescLocalized
                                )
                            )
                        }

                        isDeckCompleted -> {
                            navigation.replaceCurrent(ChildConfig.DeckObtained)
                        }

                        else -> {
                            navigation.popWhile { it !is ChildConfig.StarrySky }
                        }
                    }
                }
            }

            is SpecialDeckCardDetailsComponent.Output.CardNotReleased -> {
                navigation.pop()
            }
        }
    }

    private fun onStarrySkyComponentOutput(output: SpecialDeckStarrySkyComponent.Output) {
        when (output) {
            is SpecialDeckStarrySkyComponent.Output.SpecialDeckCardDetailsRequested -> navigation.push(
                ChildConfig.CardDetails(
                    output.card,
                    output.userSpecialDeckInfo,
                    SpecialDeckCardDetailsComponent.CardEffectType.random()
                )
            )

            is SpecialDeckStarrySkyComponent.Output.CloseRequested -> {
                onOutput(SpecialDeckComponent.Output.CloseRequested)
            }

            is SpecialDeckStarrySkyComponent.Output.LayoutRequested -> {
                onOutput(SpecialDeckComponent.Output.LayoutRequested(deckId))
            }

            is SpecialDeckStarrySkyComponent.Output.SubscriptionBottomSheetRequested -> onOutput(
                SpecialDeckComponent.Output.SubscriptionBottomSheetRequested(output.isSpecialDeckCardRequested)
            )
        }
    }

    @Parcelize
    sealed interface ChildConfig : Parcelable {
        @Parcelize
        data class CardDetails(
            val card: SpecialDeckCard,
            val userSpecialDeckInfo: UserSpecialDeckInfo,
            val cardEffectType: SpecialDeckCardDetailsComponent.CardEffectType
        ) : ChildConfig

        @Parcelize
        data class Onboarding(val deckTitle: LocalizableString) : ChildConfig

        @Parcelize
        data object StarrySky : ChildConfig

        @Parcelize
        data object DeckObtained : ChildConfig
    }
}