package com.metacards.metacards.features.account.ui.lessons.categories

import com.metacards.metacards.features.account.domain.entity.LessonCategory
import com.metacards.metacards.features.account.domain.entity.LessonCategoryId
import kotlinx.coroutines.flow.StateFlow

interface LessonCategoriesComponent {

    val categoriesState: StateFlow<List<LessonCategory>>

    fun onCategoryClick(lessonCategoryId: LessonCategoryId)

    fun onTutorialClick()

    sealed interface Output {
        data object TutorialRequested : Output
        data class CategorySelected(val lessonCategoryId: LessonCategoryId) : Output
    }
}