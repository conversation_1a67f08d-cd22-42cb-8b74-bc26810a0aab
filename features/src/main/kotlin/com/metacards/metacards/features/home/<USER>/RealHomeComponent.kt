package com.metacards.metacards.features.home.ui

import android.Manifest
import android.os.Build
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.doOnStart
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.adv.domain.YandexAdvHelper
import com.metacards.metacards.core.alert.domain.ShakerPopupState
import com.metacards.metacards.core.auth_suggestion_dialog.createAuthSuggestion
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.createDefaultDialogComponent
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.model.DialogData
import com.metacards.metacards.core.dialog.dialogControl
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.localization.data.AppLanguageService
import com.metacards.metacards.core.permissions.PermissionService
import com.metacards.metacards.core.preferences.AppLaunchCounterService
import com.metacards.metacards.core.preferences.PreferencesService
import com.metacards.metacards.core.preferences.models.TutorialState
import com.metacards.metacards.core.user.domain.UserState
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.mutableStateIn
import com.metacards.metacards.features.advbanner.data.BannerDataSource
import com.metacards.metacards.features.advbanner.domain.GetHomeScreenAdvBannerInteractor
import com.metacards.metacards.features.advbanner.domain.entity.HomeAdvBanner
import com.metacards.metacards.features.advbanner.domain.entity.HomeBannerType
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.domain.entity.DeckWithAvailable
import com.metacards.metacards.features.deck.domain.interactor.GetDailyCardInteractor
import com.metacards.metacards.features.home.domain.GetHomeScreenDecksInteractor
import com.metacards.metacards.features.tutorial.data.TutorialMessageService
import com.metacards.metacards.features.tutorial.data.TutorialRepository
import com.metacards.metacards.features.tutorial.data.TutorialStep
import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import com.metacards.metacards.features.user.domain.GetUserSubscriptionStateInteractor
import com.metacards.metacards.features.user.domain.UserRepository
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn

class RealHomeComponent(
    componentContext: ComponentContext,
    componentFactory: ComponentFactory,
    private val onOutput: (HomeComponent.Output) -> Unit,
    private val tutorialMessageService: TutorialMessageService,
    tutorialRepository: TutorialRepository,
    userRepository: UserRepository,
    private val preferencesService: PreferencesService,
    private val appLaunchCounterService: AppLaunchCounterService,
    private val permissionService: PermissionService,
    private val analyticsService: AnalyticsService,
    private val appLanguageService: AppLanguageService,
    getHomeScreenDecksInteractor: GetHomeScreenDecksInteractor,
    getHomeScreenAdvBannerInteractor: GetHomeScreenAdvBannerInteractor,
    getUserSubscriptionStateInteractor: GetUserSubscriptionStateInteractor,
    private val bannerDataSource: BannerDataSource,
    private val errorHandler: ErrorHandler,
    getDailyCardInteractor: GetDailyCardInteractor,
    private val yandexAdvHelper: YandexAdvHelper,
) : ComponentContext by componentContext, HomeComponent {

    companion object {
        private const val MAIN_SCREEN_LAUNCH_COUNTER_SHOW_SHAKER_POPUP = 3
    }

    private val debounce = Debounce()

    private val userSubscription = getUserSubscriptionStateInteractor.execute()
        .stateIn(componentScope, SharingStarted.Eagerly, null)

    override val homeAdvBanner = getHomeScreenAdvBannerInteractor.execute()
        .mutableStateIn(componentScope, null)
    override val dailyCardState = getDailyCardInteractor.execute()
        .stateIn(componentScope, SharingStarted.Eagerly, null)

    override val deckList: StateFlow<List<DeckWithAvailable>> = getHomeScreenDecksInteractor.execute()
        .stateIn(componentScope, SharingStarted.Eagerly, emptyList())

    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        dialogControl(dialogComponentFactory = { config, componentContext, dialogControl ->
            componentFactory.createDefaultDialogComponent(
                componentContext,
                config.data,
                dialogControl::dismiss
            )
        })

    override val tutorialMessage: StateFlow<TutorialMessage?> =
        tutorialMessageService.tutorialMessageFlow
            .stateIn(componentScope, SharingStarted.Eagerly, null)

    override val tutorialStep: StateFlow<TutorialStep> = tutorialMessageService.tutorialStepFlow

    private val userState = userRepository.user
        .map {
            if (it == null) {
                UserState.None
            } else {
                UserState.Success(it)
            }
        }.stateIn(componentScope, SharingStarted.Eagerly, UserState.Loading)

    private var deckToOpenAfterAdv: DeckId? = null

    init {
        lifecycle.doOnStart {
            tutorialMessageService.handleHomeComponentTutorial()

            componentScope.safeLaunch(errorHandler) {

                appLaunchCounterService.incrementHomeScreenLaunchCounter()

                var shakerPopupState = preferencesService.getShakerPopupState()
                // workaround для проблемы, когда попап показываестся повторно
                if (shakerPopupState == ShakerPopupState.VISIBLE) {
                    shakerPopupState = ShakerPopupState.OVER
                }

                val shouldShowShakerPopUp = appLaunchCounterService.getMainScreenLaunchCounter() ==
                    MAIN_SCREEN_LAUNCH_COUNTER_SHOW_SHAKER_POPUP &&
                    shakerPopupState == ShakerPopupState.NONE &&
                    preferencesService.getTutorialState() == TutorialState.COMPLETED

                if (shouldShowShakerPopUp) {
                    preferencesService.setShakerPopupState(ShakerPopupState.VISIBLE)
                    onOutput(HomeComponent.Output.ShowShakerPopup)
                }
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                componentScope.safeLaunch(errorHandler) {
                    permissionService.requestPermission(Manifest.permission.POST_NOTIFICATIONS)
                }
            }
        }

        componentScope.safeLaunch(errorHandler) { bannerDataSource.cleanHiddenBannerStorage() }
    }

    override fun onDeckInfoButtonClick(deckId: DeckId) {
        DebounceClick(debounce, "onDeckInfoButtonClick") {
            val deck = deckList.value.find { it.deck.id == deckId }
            require(deck != null)

            analyticsService.logEvent(
                AnalyticsEvent.MainDeckInfoEvent(
                    deckId = deckId.value,
                    deckName = deck.deck.name.toString(appLanguageService.getLanguage())
                )
            )
            if (!deck.deck.isSpecial) {
                onOutput(HomeComponent.Output.DeckScreenRequested(deck.deck.id))
            } else {
                when (userState.value) {
                    UserState.Loading -> Unit
                    UserState.None -> onOutput(HomeComponent.Output.AuthSuggestingRequested)
                    is UserState.Success -> onOutput(
                        HomeComponent.Output.SpecialDeckRequested(
                            deck.deck.id, true
                        )
                    )
                }
            }
        }
    }

    override fun onDeckClick(deckId: DeckId) {
        DebounceClick(debounce, "onDeckClick") {
            val deck = deckList.value.find { it.deck.id == deckId }
            require(deck != null)

            analyticsService.logEvent(
                AnalyticsEvent.MainDeckChooseEvent(
                    deckId = deckId.value,
                    deckName = deck.deck.name.toString(appLanguageService.getLanguage())
                )
            )

            when {
                !deck.available -> {
                    onOutput(HomeComponent.Output.DeckScreenRequested(deck.deck.id))
                }

                deck.deck.isSpecial -> {
                    when (userState.value) {
                        UserState.Loading -> Unit
                        UserState.None -> onOutput(HomeComponent.Output.AuthSuggestingRequested)
                        is UserState.Success -> onOutput(
                            HomeComponent.Output.SpecialDeckRequested(
                                deck.deck.id, false
                            )
                        )
                    }
                }

                userSubscription.value?.canLayout == false -> {
                    deckToOpenAfterAdv = deckId
                    yandexAdvHelper.subscribeForReward {
                        deckToOpenAfterAdv?.let {
                            onOutput(HomeComponent.Output.NewDeckLayoutRequested(it))
                            deckToOpenAfterAdv = null
                        }
                    }
                    onOutput(HomeComponent.Output.SubscriptionBottomSheetRequested)
                }

                else -> onOutput(HomeComponent.Output.NewDeckLayoutRequested(deckId))
            }
        }
    }

    override fun onAdvBannerClick(homeAdvBanner: HomeAdvBanner) {
        when (homeAdvBanner.type) {
            HomeBannerType.SUB -> onOutput(HomeComponent.Output.SubscriptionScreenRequested)
            HomeBannerType.DECK -> when (userState.value) {
                UserState.None -> {
                    dialogControl.show(
                        DefaultDialogComponent.Config(
                            DialogData.createAuthSuggestion(
                                cancelAction = {
                                    dialogControl.dismiss()
                                },
                                acceptAction = {
                                    analyticsService.logEvent(AnalyticsEvent.AccountAuthEvent)
                                    onOutput(HomeComponent.Output.AuthScreenRequested)
                                }
                            )
                        )
                    )
                }

                is UserState.Success -> {
                    analyticsService.logEvent(
                        AnalyticsEvent.BannerTapEvent(
                            bannerType = homeAdvBanner.type.name,
                            bannerId = homeAdvBanner.id,
                            bannerName = homeAdvBanner.title.toString()
                        )
                    )
                    homeAdvBanner.deckId?.let { deckId ->
                        onOutput(HomeComponent.Output.DeckScreenRequested(deckId))
                    }
                }

                UserState.Loading -> Unit
            }
        }
    }

    override fun onCardOfTheDayClick() {
        onOutput(HomeComponent.Output.DailyCardRequested)
    }

    override fun onAdvBannerDismiss() {
        componentScope.safeLaunch(errorHandler) {
            homeAdvBanner.value?.let { homeBanner ->
                analyticsService.logEvent(
                    AnalyticsEvent.BannerCloseEvent(
                        bannerType = homeBanner.type.name,
                        bannerId = homeBanner.deckId?.value ?: "",
                        bannerName = homeBanner.title.toString()
                    )
                )
                bannerDataSource.hideBanner(homeBanner.id)
            }
        }
    }

    override fun onLayoutBannerClick() {
        analyticsService.logEvent(AnalyticsEvent.LayoutTapEvent)
        onOutput(HomeComponent.Output.PredefinedLayoutsRequested)
    }
}
