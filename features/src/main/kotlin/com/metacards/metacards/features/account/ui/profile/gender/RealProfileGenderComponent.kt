package com.metacards.metacards.features.account.ui.profile.gender

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.user.data.UserDto
import com.metacards.metacards.core.user.domain.UserProvider
import com.metacards.metacards.features.user.domain.UpdateUserInteractor
import kotlinx.coroutines.flow.MutableStateFlow

class RealProfileGenderComponent(
    componentContext: ComponentContext,
    selectedGender: UserDto.Gender?,
    private val userProvider: UserProvider,
    private val updateUserInteractor: UpdateUserInteractor,
    private val onOutput: (ProfileGenderComponent.Output) -> Unit
) : ComponentContext by componentContext, ProfileGenderComponent {

    private val debounce = Debounce()

    override val selectedGenderFlow: MutableStateFlow<UserDto.Gender?> = MutableStateFlow(selectedGender)

    override fun onGenderClick(gender: UserDto.Gender) {
        selectedGenderFlow.value = gender
    }

    override fun onSaveClick() {
        DebounceClick(debounce, "onGenderSaveClick") {
            val user = userProvider.getUser().value
            val gender = selectedGenderFlow.value ?: return@DebounceClick

            user?.let {
                updateUserInteractor.updateGender(
                    it.userId,
                    gender
                )
                onOutput(ProfileGenderComponent.Output.DismissRequested)
            }
        }
    }

    override fun onDismiss() {
        onOutput(ProfileGenderComponent.Output.DismissRequested)
    }
}