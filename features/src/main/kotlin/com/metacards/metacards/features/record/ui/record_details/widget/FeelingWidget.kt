package com.metacards.metacards.features.record.ui.record_details.widget

import android.view.HapticFeedbackConstants
import androidx.annotation.DrawableRes
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.draggable
import androidx.compose.foundation.gestures.rememberDraggableState
import androidx.compose.foundation.interaction.DragInteraction
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.features.R
import com.metacards.metacards.features.record.ui.model.FeelingItem
import kotlin.math.roundToInt

object FeelingWidgetDefaults {
    val shape: Shape = RoundedCornerShape(30.dp)
    val background: Color = CustomTheme.colors.background.primary
    val padding: Dp = 8.dp
    val selectedItemSize: Dp = 48.dp
    val baseItemSize: Dp = 16.dp
    val sizeStep: Dp = 4.dp
    val minHeight: Dp = selectedItemSize + padding * 2
    val paddingValues: PaddingValues = PaddingValues(padding)

    val defaultModifier = Modifier
        .clip(shape)
        .background(background)
        .heightIn(min = minHeight)
        .padding(paddingValues)
}

data class Values(
    val parentWidth: Dp,
    val pointCount: Int,
    val baseSize: Dp = FeelingWidgetDefaults.baseItemSize,
    val sizeStep: Dp = FeelingWidgetDefaults.sizeStep,
    val selectedSize: Dp = FeelingWidgetDefaults.selectedItemSize,
) {
    private val availableWidth = parentWidth - selectedSize
    val gap: Dp = availableWidth / (pointCount - 1)

    val maxOffset = availableWidth
    val minOffset = 0.dp

    data class Item(val index: Int, val values: Values) {
        private val defaultSize = values.baseSize + values.sizeStep * index
        private val maxPadding = minOf((values.gap * index), values.maxOffset)
        val padding = maxOf(0.dp, maxPadding)
        val paddingValues = PaddingValues(start = padding)

        private fun getSize(isSelected: Boolean) =
            if (isSelected) values.selectedSize else defaultSize

        @Composable
        fun getAnimatedSize(isSelected: Boolean) =
            animateDpAsState(targetValue = getSize(isSelected), label = "feelingWidgetValuesAnimation")
    }

    fun getMultiplier(dragOffset: Float, density: Density): Int = with(density) {
        (dragOffset.roundToInt() + gap.roundToPx() / 2) / gap.roundToPx()
    }

    fun getOffset(dragOffset: Float, density: Density): Float = with(density) {
        val multiplier = getMultiplier(dragOffset, density)
        return getOffset(multiplier, density)
    }

    fun getOffset(position: Int, density: Density): Float = with(density) {
        return maxOf(
            position * gap.toPx(),
            minOffset.toPx()
        )
    }

    fun coerceOffset(dragOffset: Float, delta: Float, density: Density): Float = with(density) {
        val newOffset = dragOffset + delta
        return newOffset.coerceIn(minOffset.toPx(), maxOffset.toPx())
    }
}

@Composable
fun FeelingWidget(
    modifier: Modifier = Modifier,
    feelingItems: List<FeelingItem<Int>>,
    @DrawableRes backgroundResource: Int = R.drawable.bg_mars,
    drawDebugPoint: Boolean = false,
    interactionEnabled: Boolean = true,
    selectedItem: FeelingItem<Int>?,
    onSelectItem: (FeelingItem<Int>) -> Unit
) {
    val view = LocalView.current
    val density = LocalDensity.current

    BoxWithConstraints(
        modifier = modifier,
        contentAlignment = Alignment.CenterStart
    ) {
        val count = feelingItems.size
        val values = Values(maxWidth, count)
        val itemsValues = List(count) { Values.Item(it, values) }

        var dragOffset by remember { mutableFloatStateOf(with(density) { values.minOffset.toPx() }) }
        val interactionSource = remember { MutableInteractionSource() }

        /**
         * Background image
         */
        Image(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .align(Alignment.Center),
            painter = painterResource(id = backgroundResource),
            contentDescription = null,
            contentScale = ContentScale.FillWidth
        )

        Box(modifier = Modifier.padding(horizontal = 0.dp)) {
            feelingItems.forEachIndexed { index, item ->
                val valuesItem = itemsValues[index]

                LaunchedEffect(key1 = Unit) {
                    if (selectedItem == item) {
                        dragOffset = values.getOffset(index, density)
                    }
                }

                Item(
                    feelingItem = item,
                    valuesItem = valuesItem,
                    isSelected = selectedItem == item,
                    interactionEnabled = interactionEnabled,
                    onClick = {
                        onSelectItem(item)
                        dragOffset = values.getOffset(index, density)
                        view.performHapticFeedback(
                            HapticFeedbackConstants.LONG_PRESS,
                            HapticFeedbackConstants.FLAG_IGNORE_VIEW_SETTING
                        )
                    }
                )
            }
        }

        /**
         * Haptic feedback
         */
        LaunchedEffect(key1 = interactionSource, key2 = interactionEnabled) {
            if (!interactionEnabled) return@LaunchedEffect

            interactionSource.interactions.collect {
                when (it) {
                    is DragInteraction.Stop -> {
                        dragOffset = values.getOffset(dragOffset, density)
                        view.performHapticFeedback(
                            HapticFeedbackConstants.LONG_PRESS,
                            HapticFeedbackConstants.FLAG_IGNORE_VIEW_SETTING
                        )
                    }
                }
            }
        }

        /**
         * Interaction item
         */
        Box(modifier = Modifier
            .size(values.selectedSize)
            .offset {
                IntOffset(dragOffset.roundToInt(), 0)
            }
            .clip(CircleShape)
            .alpha(if (drawDebugPoint) 0.3f else 0f)
            .background(Color.Blue)
            .draggable(
                orientation = Orientation.Horizontal,
                interactionSource = interactionSource,
                state = rememberDraggableState { delta ->
                    if (!interactionEnabled) return@rememberDraggableState

                    dragOffset = values.coerceOffset(dragOffset, delta, density)

                    val point = values.getMultiplier(dragOffset, density)

                    onSelectItem(feelingItems[point])

                    if (selectedItem != feelingItems[point]) {
                        view.performHapticFeedback(
                            HapticFeedbackConstants.VIRTUAL_KEY,
                            HapticFeedbackConstants.FLAG_IGNORE_VIEW_SETTING
                        )
                    }
                }
            )
            .clickable {
                val point = values.getMultiplier(dragOffset, density)

                onSelectItem(feelingItems[point])

                if (selectedItem != feelingItems[point]) {
                    view.performHapticFeedback(
                        HapticFeedbackConstants.VIRTUAL_KEY,
                        HapticFeedbackConstants.FLAG_IGNORE_VIEW_SETTING
                    )
                }
            }
        )
    }
}

@Composable
private fun Item(
    valuesItem: Values.Item,
    feelingItem: FeelingItem<*>,
    isSelected: Boolean,
    interactionEnabled: Boolean,
    onClick: () -> Unit
) {
    val size by valuesItem.getAnimatedSize(isSelected = isSelected)

    Box(
        modifier = Modifier
            .padding(valuesItem.paddingValues)
            .requiredSize(valuesItem.values.selectedSize),
        contentAlignment = Alignment.Center
    ) {
        Image(
            modifier = Modifier
                .size(size)
                .clickable(false, enabled = interactionEnabled) { onClick() },
            painter = painterResource(id = feelingItem.imageResource),
            contentDescription = null,
            contentScale = ContentScale.Crop
        )
    }
}

@Preview
@Composable
private fun FeelingWidgetPreview() {
    AppTheme {
        val items = remember { FeelingItem.mars_list }

        var selectedItem by remember { mutableStateOf(items.last()) }

        FeelingWidget(
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .then(FeelingWidgetDefaults.defaultModifier),
            selectedItem = selectedItem,
            onSelectItem = { selectedItem = it },
            feelingItems = items
        )
    }
}