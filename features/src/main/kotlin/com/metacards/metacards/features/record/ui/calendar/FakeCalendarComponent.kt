package com.metacards.metacards.features.record.ui.calendar

import com.kizitonwose.calendar.core.CalendarDay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.datetime.Instant
import java.time.YearMonth

class FakeCalendarComponent : CalendarComponent {
    override val isDataLoaded: StateFlow<Boolean> = MutableStateFlow(false)
    override val daysWithRecords: StateFlow<List<CalendarDay>> = MutableStateFlow(listOf())
    override val startMonth: StateFlow<YearMonth> = MutableStateFlow(YearMonth.now())
    override val endMonth: StateFlow<YearMonth> = MutableStateFlow(YearMonth.now())

    override fun onDateClick(date: Instant) = Unit
    override fun onCalendarPageSettled(page: Int) = Unit
}