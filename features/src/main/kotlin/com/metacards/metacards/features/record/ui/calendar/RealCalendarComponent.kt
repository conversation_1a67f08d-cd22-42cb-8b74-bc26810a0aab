package com.metacards.metacards.features.record.ui.calendar

import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.doOnCreate
import com.kizitonwose.calendar.core.CalendarDay
import com.kizitonwose.calendar.core.DayPosition
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.utils.APPLICATION_START_DATE
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.endToInstant
import com.metacards.metacards.core.utils.startToInstant
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.record.domain.GetRecordsBetweenTimesInteractor
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.datetime.Instant
import kotlinx.datetime.toJavaLocalDate
import java.time.YearMonth

class RealCalendarComponent(
    componentContext: ComponentContext,
    private val errorHandler: ErrorHandler,
    private val getRecordsBetweenTimesInteractor: GetRecordsBetweenTimesInteractor,
    private val analyticsService: AnalyticsService,
    private val onOutput: (CalendarComponent.Output) -> Unit
) : ComponentContext by componentContext, CalendarComponent {

    init {
        lifecycle.doOnCreate {
            val startTime: Instant
            val endTime: Instant
            val currentMonth = YearMonth.now()
            isCurrentMonthEven.value = currentMonth.monthValue % 2 == 0

            if (!isCurrentMonthEven.value) {
                endMonth.value.plusMonths(1)
            }

            startTime = if (isCurrentMonthEven.value) {
                currentMonth.minusMonths(1).startToInstant()
            } else {
                currentMonth.startToInstant()
            }

            endTime = if (isCurrentMonthEven.value) {
                currentMonth.endToInstant()
            } else {
                currentMonth.plusMonths(1).endToInstant()
            }

            loadDaysThatHaveRecords(startTime, endTime)
        }
    }

    override val isDataLoaded = MutableStateFlow(false)

    override val daysWithRecords = MutableStateFlow<List<CalendarDay>>(listOf())

    override val startMonth = MutableStateFlow(
        YearMonth.of(APPLICATION_START_DATE.year, APPLICATION_START_DATE.month)
    )

    override val endMonth = MutableStateFlow(YearMonth.now())

    private val isCurrentMonthEven = MutableStateFlow(false)

    private fun loadDaysThatHaveRecords(startTime: Instant, endTime: Instant) {
        componentScope.safeLaunch(errorHandler) {
            val records = getRecordsBetweenTimesInteractor.execute(startTime, endTime)
            daysWithRecords.value = records.map {
                CalendarDay(
                    it.creationDate.toJavaLocalDate(),
                    DayPosition.MonthDate
                )
            }
            isDataLoaded.value = true
        }
    }

    override fun onDateClick(date: Instant) {
        analyticsService.logEvent(AnalyticsEvent.JournalCalendarChooseEvent)
        onOutput(CalendarComponent.Output.RecordsViewRequested(date))
    }

    override fun onCalendarPageSettled(page: Int) {
        isDataLoaded.value = false

        val currentMonth = YearMonth.now()
        val startTime = if (isCurrentMonthEven.value) {
            currentMonth.minusMonths((1 + 2 * page).toLong()).startToInstant()
        } else {
            currentMonth.minusMonths((2 * page).toLong()).startToInstant()
        }
        val endTime = if (isCurrentMonthEven.value) {
            currentMonth.minusMonths((2 * page).toLong()).endToInstant()
        } else {
            currentMonth.minusMonths((2 * page - 1).toLong()).endToInstant()
        }

        loadDaysThatHaveRecords(startTime, endTime)
    }
}