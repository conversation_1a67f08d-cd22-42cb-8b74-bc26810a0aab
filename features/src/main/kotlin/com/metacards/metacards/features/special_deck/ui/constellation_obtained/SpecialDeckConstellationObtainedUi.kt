package com.metacards.metacards.features.special_deck.ui.constellation_obtained

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun SpecialDeckConstellationObtainedUi(
    component: SpecialDeckConstellationObtainedComponent,
    modifier: Modifier = Modifier
) {

    Surface(
        shape = RoundedCornerShape(16.dp),
        color = CustomTheme.colors.background.modal,
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
            .padding(bottom = 8.dp)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(vertical = 40.dp, horizontal = 24.dp)
        ) {
            AsyncImage(
                model = component.imageUrl,
                contentDescription = null,
                modifier = Modifier
                    .size(
                        width = 215.dp,
                        height = 230.dp
                    )
                    .clip(RoundedCornerShape(16.dp))
                    .padding(bottom = 24.dp)
            )

            Text(
                text = component.header.localizedByLocal(),
                textAlign = TextAlign.Center,
                color = CustomTheme.colors.text.primary,
                style = CustomTheme.typography.heading.medium,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp)
            )

            Text(
                text = component.description.localizedByLocal(),
                textAlign = TextAlign.Center,
                color = CustomTheme.colors.text.primary,
                style = CustomTheme.typography.body.primary,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp)
            )

            MetaAccentButton(
                text = R.string.special_deck_constellation_obtained_button_wow
                    .strResDesc()
                    .localizedByLocal(),
                onClick = component::onWowClick,
                modifier = Modifier
                    .fillMaxWidth()
            )
        }
    }
}

@Preview
@Composable
private fun SpecialDeckConstellationObtainedPreview() {
    AppTheme {
        SpecialDeckConstellationObtainedUi(FakeSpecialDeckConstellationObtainedComponent())
    }
}