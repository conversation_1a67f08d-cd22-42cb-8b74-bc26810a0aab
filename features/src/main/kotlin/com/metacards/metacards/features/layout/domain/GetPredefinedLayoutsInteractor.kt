package com.metacards.metacards.features.layout.domain

import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.features.user.domain.UserRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update

class GetPredefinedLayoutsInteractor(
    coroutineScope: CoroutineScope,
    errorHandler: <PERSON>rrorHandler,
    userRepository: UserRepository,
    private val predefinedLayoutRepository: PredefinedLayoutRepository
) {

    private val stateFlow = MutableStateFlow<List<PredefinedLayoutWithAvailable>>(emptyList())

    init {
        userRepository.user.map { user ->
            val hasSubscription = user?.subscriptionState is User.SubscriptionState.Ongoing

            predefinedLayoutRepository.getPredefinedLayouts().map {
                PredefinedLayoutWithAvailable(
                    layout = it,
                    isAvailable = it.isFree || hasSubscription
                )
            }.sortedBy { !it.isAvailable }
        }
            .catch { e -> errorHandler.handleError(Exception(e)) }
            .onEach { list -> stateFlow.update { list } }
            .launchIn(coroutineScope)
    }

    fun execute(): StateFlow<List<PredefinedLayoutWithAvailable>> = stateFlow.asStateFlow()
}