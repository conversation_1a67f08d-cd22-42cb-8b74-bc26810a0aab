package com.metacards.metacards.features.showcase.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.user.domain.FavoriteCard
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.features.R
import com.metacards.metacards.features.account.domain.entity.LessonCategory
import com.metacards.metacards.features.course.domain.entity.CourseShortData
import com.metacards.metacards.features.course.domain.entity.CourseTheme
import com.metacards.metacards.features.deck.domain.entity.DeckInfoWithCards
import com.metacards.metacards.features.layout.domain.PredefinedLayoutWithAvailable
import com.metacards.metacards.features.showcase.ui.widgets.ShowcaseCourseThemesItem
import com.metacards.metacards.features.showcase.ui.widgets.ShowcaseCoursesItem
import com.metacards.metacards.features.showcase.ui.widgets.ShowcaseFavoriteCardsItem
import com.metacards.metacards.features.showcase.ui.widgets.ShowcaseGameItem
import com.metacards.metacards.features.showcase.ui.widgets.ShowcasePredefinedLayouts
import com.metacards.metacards.features.showcase.ui.widgets.ShowcaseShopItem
import com.metacards.metacards.features.showcase.ui.widgets.ShowcaseStatItem
import com.metacards.metacards.features.showcase.ui.widgets.ShowcaseVideoItem
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun ShowcaseUi(
    component: ShowcaseComponent,
    navBarHeight: Dp,
    modifier: Modifier = Modifier
) {

    val decksForPurchaseInfoWithCards by component.decksForPurchaseInfoWithCards.collectAsState()
    val predefinedLayouts by component.predefinedLayouts.collectAsState()
    val videoLessonCategoriesList by component.videoLessonCategories.collectAsState()
    val favoriteCards by component.favoriteCards.collectAsState()
    val courses by component.courses.collectAsState()
    val isPremiumUser by component.isPremiumUser.collectAsState()

    Content(
        onGameBannerClick = component::onGameBannerClick,
        onAnalyticsBannerClick = component::onAnalyticsBannerClick,
        decksForPurchaseInfoWithCards = decksForPurchaseInfoWithCards,
        favoriteCards = favoriteCards,
        videoLessonCategoriesList = videoLessonCategoriesList,
        predefinedLayouts = predefinedLayouts,
        courseThemes = courses,
        onDeckClick = component::onShopDeckClick,
        onPredefinedLayoutClick = component::onPredefinedLayoutClick,
        onPredefinedLayoutBlockContentClick = component::onPredefinedLayoutBlockContentClick,
        onVideoLessonCategoryClick = component::onVideoLessonCategoryClick,
        onFavoriteCardClick = component::onFavoriteCardClick,
        onThemeClick = component::onCourseThemeClick,
        onCourseClick = component::onCourseClick,
        navBarHeight = navBarHeight,
        isPremiumUser = isPremiumUser,
        modifier = modifier
    )
}

@Composable
private fun Content(
    onGameBannerClick: () -> Unit,
    onAnalyticsBannerClick: () -> Unit,
    decksForPurchaseInfoWithCards: List<DeckInfoWithCards>,
    predefinedLayouts: List<PredefinedLayoutWithAvailable>,
    videoLessonCategoriesList: List<LessonCategory>,
    favoriteCards: List<FavoriteCard>,
    courseThemes: List<CourseTheme>,
    onDeckClick: (DeckInfoWithCards) -> Unit,
    onPredefinedLayoutClick: (PredefinedLayoutWithAvailable) -> Unit,
    onPredefinedLayoutBlockContentClick: () -> Unit,
    onVideoLessonCategoryClick: (LessonCategory) -> Unit,
    onFavoriteCardClick: (FavoriteCard) -> Unit,
    onThemeClick: (CourseTheme) -> Unit,
    onCourseClick: (CourseShortData) -> Unit,
    navBarHeight: Dp,
    isPremiumUser: Boolean,
    modifier: Modifier = Modifier
) {

    BoxWithFade(
        modifier = modifier,
        listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
        ) {
            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 24.dp, horizontal = 16.dp),
                text = R.string.showcase_title.strResDesc().localizedByLocal(),
                style = CustomTheme.typography.heading.primary,
                color = CustomTheme.colors.text.primary
            )
            Column(
                modifier = Modifier
                    .weight(1f)
                    .verticalScroll(rememberScrollState())
            ) {

                Spacer(modifier = Modifier.height(24.dp))

                ShowcaseGameItem(
                    onGameBannerClick = onGameBannerClick
                )

                ShowcaseShopItem(
                    decksForPurchaseInfoWithCards = decksForPurchaseInfoWithCards,
                    onDeckClick = onDeckClick,
                )

                if (courseThemes.size > 1) {
                    ShowcaseCourseThemesItem(
                        themes = courseThemes,
                        onThemeClick = onThemeClick
                    )
                } else {
                    courseThemes.firstOrNull()?.let { theme ->
                        ShowcaseCoursesItem(
                            courses = theme.courses,
                            onCourseClick = onCourseClick,
                            isPremiumUser = isPremiumUser
                        )
                    }
                }

                ShowcasePredefinedLayouts(
                    predefinedLayouts = predefinedLayouts,
                    onLayoutClick = onPredefinedLayoutClick,
                    onBlockContentClick = onPredefinedLayoutBlockContentClick,
                )

                ShowcaseVideoItem(
                    videoLessonCategoriesList = videoLessonCategoriesList,
                    onVideoLessonCategoryClick = onVideoLessonCategoryClick,
                )

                ShowcaseStatItem(onAnalyticsBannerClick = onAnalyticsBannerClick)

                ShowcaseFavoriteCardsItem(
                    cards = favoriteCards,
                    onFavoriteCardClick = onFavoriteCardClick
                )

                Spacer(modifier = Modifier.height(navBarHeight))
            }
        }
    }
}

@Preview(showSystemUi = true)
@Composable
private fun Preview() {
    AppTheme {
        ShowcaseUi(
            component = FakeShowcaseComponent(),
            navBarHeight = 64.dp
        )
    }
}