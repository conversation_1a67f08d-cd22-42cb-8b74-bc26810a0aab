package com.metacards.metacards.features.record.domain

import com.metacards.metacards.core.paged_loading.DuplicateRemovingDataMerger
import com.metacards.metacards.core.paged_loading.Page
import com.metacards.metacards.core.paged_loading.PagedLoader
import com.metacards.metacards.core.paged_loading.PagedLoading
import com.metacards.metacards.features.record.data.RecordRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.datetime.Instant

class RecordsPagedLoading(
    listType: RecordListType,
    coroutineScope: CoroutineScope,
    recordRepository: RecordRepository
) : PagedLoading<Record> by createPagedLoading(listType, coroutineScope, recordRepository) {

    companion object {
        private const val PAGE_SIZE = 20

        private fun createPagedLoading(
            listType: RecordListType,
            coroutineScope: CoroutineScope,
            recordRepository: RecordRepository
        ): PagedLoading<Record> {
            return PagedLoading(
                scope = coroutineScope,
                loader = RecordsPagedLoader(listType, PAGE_SIZE, recordRepository),
                dataMerger = DuplicateRemovingDataMerger(keySelector = { it.id })
            )
        }
    }

    private val recordsLoader = (loader as RecordsPagedLoader)

    var startTime by recordsLoader::startTime
}

private class RecordsPagedLoader(
    listType: RecordListType,
    private val pageSize: Int,
    private val recordRepository: RecordRepository
) : PagedLoader<Record> {

    private val filter: RecordFilter = RecordFilter.fromRecordListType(listType)

    var startTime: Instant? = null

    override suspend fun loadFirstPage(): Page<Record> = coroutineScope {
        val startTime = startTime
        if (startTime != null) {
            return@coroutineScope loadPageFromTime(startTime)
        } else {
            return@coroutineScope getNewestRecords()
        }
    }

    private suspend fun getNewestRecords(): Page<Record> = coroutineScope {
        val records = recordRepository.getNewestRecords(filter, limit = pageSize)
        return@coroutineScope Page(
            records,
            hasNextPage = records.size >= pageSize,
            hasPreviousPage = false
        )
    }

    private suspend fun loadPageFromTime(startTime: Instant): Page<Record> = coroutineScope {
        // Грузим в обе стороны по pageSize записей, чтоб пользователь смог листать в любую сторону.
        val newerRecordsDeferred = async {
            recordRepository.getRecordsNewerThan(startTime, filter, pageSize)
        }
        val olderRecordsDeferred = async {
            // "inclusive = true" нужен на случай, если startTime в точности совпадет с creationDate одной из записей
            recordRepository.getRecordsOlderThan(startTime, filter, pageSize, inclusive = true)
        }

        val newerRecords = newerRecordsDeferred.await()
        val olderRecords = olderRecordsDeferred.await()

        return@coroutineScope Page(
            data = newerRecords + olderRecords,
            hasNextPage = olderRecords.size >= pageSize,
            hasPreviousPage = newerRecords.size >= pageSize
        )
    }

    override suspend fun reloadPage(loadedData: List<Record>): Page<Record> {
        val firstTime = loadedData.firstOrNull()?.creationTime
        return if (firstTime != null) {
            loadPageFromTime(firstTime)
        } else {
            getNewestRecords()
        }
    }

    override suspend fun loadPreviousPage(loadedData: List<Record>): Page<Record> {
        val firstTime = loadedData.firstOrNull()?.creationTime
        val records = if (firstTime != null) {
            recordRepository.getRecordsNewerThan(firstTime, filter, limit = pageSize)
        } else {
            emptyList()
        }

        return Page(
            records,
            hasNextPage = true,
            hasPreviousPage = records.size >= pageSize
        )
    }

    override suspend fun loadNextPage(loadedData: List<Record>): Page<Record> {
        val lastTime = loadedData.lastOrNull()?.creationTime
        val records = if (lastTime != null) {
            recordRepository.getRecordsOlderThan(lastTime, filter, limit = pageSize)
        } else {
            emptyList()
        }

        return Page(
            records,
            hasNextPage = records.size >= pageSize,
            hasPreviousPage = true
        )
    }
}