package com.metacards.metacards.features.special_deck.ui.constellation_obtained

import android.os.Parcelable
import com.metacards.metacards.core.localization.ui.LocalizableString
import kotlinx.parcelize.Parcelize

interface SpecialDeckConstellationObtainedComponent {

    val imageUrl: String
    val header: LocalizableString
    val description: LocalizableString

    fun onWowClick()

    @Parcelize
    data class Config(
        val imageUrl: String,
        val header: LocalizableString,
        val description: LocalizableString
    ) : Parcelable

    sealed interface Output {
        data object DismissRequested : Output
    }
}