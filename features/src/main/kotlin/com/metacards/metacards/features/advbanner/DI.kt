package com.metacards.metacards.features.advbanner

import com.metacards.metacards.features.advbanner.data.BannerDataSource
import com.metacards.metacards.features.advbanner.domain.GetAccountScreenAdvBannerInteractor
import com.metacards.metacards.features.advbanner.domain.GetHomeScreenAdvBannerInteractor
import org.koin.dsl.module

val advBannerModule = module {
    single { BannerDataSource(get(), get()) }
    single { GetHomeScreenAdvBannerInteractor(get(), get()) }
    single { GetAccountScreenAdvBannerInteractor(get(), get()) }
}