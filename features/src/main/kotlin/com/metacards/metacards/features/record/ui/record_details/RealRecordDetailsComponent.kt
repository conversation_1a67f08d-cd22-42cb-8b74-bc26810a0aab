package com.metacards.metacards.features.record.ui.record_details

import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.doOnCreate
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.adv.domain.YandexAdvHelper
import com.metacards.metacards.core.bottom_sheet.BottomSheetControl
import com.metacards.metacards.core.bottom_sheet.bottomSheetControl
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.createDefaultDialogComponent
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.model.DialogData
import com.metacards.metacards.core.dialog.dialogControl
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.localization.data.AppLanguageService
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.message.data.MessageService
import com.metacards.metacards.core.message.domain.Message
import com.metacards.metacards.core.preferences.models.TutorialState
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.user.domain.UserProvider
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.getDisplayName
import com.metacards.metacards.core.utils.stateIn
import com.metacards.metacards.features.R
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CourseThemeId
import com.metacards.metacards.features.course.domain.repository.CourseRepository
import com.metacards.metacards.features.record.createCommentDetailsComponent
import com.metacards.metacards.features.record.domain.AddRecordInteractor
import com.metacards.metacards.features.record.domain.Comment
import com.metacards.metacards.features.record.domain.GetRecordByIdInteractor
import com.metacards.metacards.features.record.domain.NewRecord
import com.metacards.metacards.features.record.domain.Question
import com.metacards.metacards.features.record.domain.Record
import com.metacards.metacards.features.record.domain.RecordData
import com.metacards.metacards.features.record.domain.RecordSource
import com.metacards.metacards.features.record.domain.UpdateRecordInteractor
import com.metacards.metacards.features.record.domain.UserFeelings
import com.metacards.metacards.features.record.ui.model.FeelingItem
import com.metacards.metacards.features.record.ui.record_details.comment_details.CommentDetailsComponent
import com.metacards.metacards.features.tutorial.data.TutorialMessageService
import com.metacards.metacards.features.tutorial.data.TutorialRepository
import com.metacards.metacards.features.tutorial.data.TutorialStep
import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import ru.mobileup.kmm_form_validation.control.InputControl
import ru.mobileup.kmm_form_validation.options.KeyboardCapitalization
import ru.mobileup.kmm_form_validation.options.KeyboardOptions

class RealRecordDetailsComponent(
    componentContext: ComponentContext,
    componentFactory: ComponentFactory,
    private val errorHandler: ErrorHandler,
    private val messageService: MessageService,
    override val recordType: RecordDetailsComponent.RecordType,
    override val isDailyCard: Boolean,
    recordSource: RecordSource,
    private val recordData: RecordData?,
    private val courseId: CourseId?,
    private val courseThemeId: CourseThemeId?,
    private val tutorialMessageService: TutorialMessageService,
    private val tutorialRepository: TutorialRepository,
    userProvider: UserProvider,
    private val addRecordInteractor: AddRecordInteractor,
    getRecordByIdInteractor: GetRecordByIdInteractor,
    private val updateRecordInteractor: UpdateRecordInteractor,
    private val appLanguageService: AppLanguageService,
    private val analyticsService: AnalyticsService,
    private val yandexAdvHelper: YandexAdvHelper,
    courseRepository: CourseRepository,
    private val onOutput: (RecordDetailsComponent.Output) -> Unit
) : ComponentContext by componentContext, RecordDetailsComponent {

    companion object {
        private const val COMMENT_DETAILS_CREATION_TIME_DISPLAY_PATTERN = "dd MMM, HH:mm"
        private const val COMMENT_MAX_LENGTH = 3000
    }

    private val debounce = Debounce()
    private val user = userProvider.getUser()

    override val record: StateFlow<LoadableState<Record>> = when (recordSource) {
        is RecordSource.Creation -> {
            MutableStateFlow(
                LoadableState(data = Record.empty().copy(questions = recordSource.questions))
            )
        }

        is RecordSource.Viewing -> {
            getRecordByIdInteractor
                .execute(recordSource.recordId)
                .stateIn(this, LoadableState())
        }
    }

    private val recordToUpdate = MutableStateFlow(record.value.data)

    override val courseName = MutableStateFlow<LocalizableString?>(null)

    override val isRecordInFavorites: StateFlow<Boolean> = recordToUpdate
        .map { it?.isFavourite ?: false }
        .stateIn(componentScope, SharingStarted.Eagerly, false)

    override val energyLevel: MutableStateFlow<FeelingItem<Int>?> = MutableStateFlow(null)

    override val moodLevel: MutableStateFlow<FeelingItem<Int>?> = MutableStateFlow(null)

    private val tutorialState: StateFlow<TutorialState> =
        tutorialRepository.tutorialStateFlow

    override val bottomSheetControl: BottomSheetControl<CommentDetailsComponent.Config, CommentDetailsComponent> =
        bottomSheetControl(
            bottomSheetComponentFactory = { config: CommentDetailsComponent.Config, context: ComponentContext, _ ->
                componentFactory.createCommentDetailsComponent(
                    context,
                    config.commentText,
                    config.commentCreationDate,
                    config.cardImageUrl,
                    config.onCardClick,
                    ::onCommentDetailsOutput
                )
            },
            halfExpandingSupported = false,
            hidingSupported = true,
            handleBackButton = true
        )

    override val tutorialMessage: StateFlow<TutorialMessage?> = combine(
        tutorialMessageService.tutorialStepFlow, tutorialMessageService.tutorialMessageFlow
    ) { step, message ->
        if (step == TutorialStep.CARD_SELECT_RECORD) message else null
    }.stateIn(componentScope, SharingStarted.Lazily, null)

    private fun onCommentDetailsOutput(output: CommentDetailsComponent.Output) {
        when (output) {
            CommentDetailsComponent.Output.DismissRequested -> {
                bottomSheetControl.dismiss()
            }
        }
    }

    override val userSubscriptionState = computed(user) { user ->
        user?.subscriptionState
    }

    override val commentInputControl: InputControl = InputControl(
        coroutineScope = componentScope,
        singleLine = false,
        keyboardOptions = KeyboardOptions(KeyboardCapitalization.Sentences),
        maxLength = COMMENT_MAX_LENGTH
    )

    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        dialogControl(dialogComponentFactory = { config, componentContext, dialogControl ->
            componentFactory.createDefaultDialogComponent(
                componentContext,
                config.data,
                dialogControl::dismiss
            )
        })

    override val isDataChanged =
        computed(record, recordToUpdate, commentInputControl.text) { old, new, comment ->
            val hasNewComment =
                if (recordType == RecordDetailsComponent.RecordType.Common) comment.isNotBlank() else false
            when {
                hasNewComment -> true
                new == null -> false
                else -> old.data != new
            }
        }

    override val isLoading = MutableStateFlow(false)

    override val buttonState =
        computed(isDataChanged, isLoading) { isDataChanged, isLoading ->
            when {
                isLoading -> ButtonState.Loading
                isDataChanged || recordType != RecordDetailsComponent.RecordType.Common -> ButtonState.Enabled
                else -> ButtonState.Disabled
            }
        }

    private val userFeelings: StateFlow<UserFeelings> =
        computed(moodLevel, energyLevel) { mood, energy ->
            UserFeelings(
                mood = mood?.value?.let { UserFeelings.Mood(it) },
                energy = energy?.value?.let { UserFeelings.Energy(it) }
            )
        }

    init {
        lifecycle.doOnCreate {
            tutorialMessageService.handleRecordDetailsComponentTutorial(componentScope)
        }
        var previousLoadable: LoadableState<Record> = LoadableState(data = null)
        record.onEach { loadable ->
            moodLevel.value = loadable.data?.moodLevel?.let {
                FeelingItem.mars_list.getOrNull(it - 1)
            }
            energyLevel.value = loadable.data?.energyLevel?.let {
                FeelingItem.moon_list.getOrNull(it - 1)
            }
            if (previousLoadable.data == null && loadable.data != null) {
                recordToUpdate.value = loadable.data
            }
            previousLoadable = loadable
        }.launchIn(componentScope)

        componentScope.safeLaunch(errorHandler) {
            if (courseId != null && courseThemeId != null) {
                courseName.value = courseRepository.getCourseData(courseThemeId, courseId)?.name
            }
        }
    }

    override fun onCardClick(cardIndex: Int, questionIndex: Int) {
        record.value.data?.let { record ->
            onOutput(
                RecordDetailsComponent.Output.FullScreenCardsRequested(
                    record.questions[questionIndex].cardsWithComment.map { it.card },
                    cardIndex
                )
            )
        }
    }

    override fun onFavoriteClick() {
        when (userSubscriptionState.value) {
            is User.SubscriptionState.None -> onOutput(RecordDetailsComponent.Output.SubscriptionSuggestingRequested)
            is User.SubscriptionState.Ongoing -> {
                updateRecord {
                    when (recordType) {
                        RecordDetailsComponent.RecordType.InCreation -> {
                            analyticsService.logEvent(AnalyticsEvent.NewRecordFavAddEvent)
                        }

                        RecordDetailsComponent.RecordType.Common -> {
                            analyticsService.logEvent(AnalyticsEvent.RecordAddFavEvent)
                        }

                        else -> {
                            // Do nothing
                        }
                    }
                    it.copy(isFavourite = !it.isFavourite)
                }
            }

            null -> onOutput(RecordDetailsComponent.Output.AuthSuggestingRequested)
        }
    }

    override fun onBlockAreaClick() {
        when (userSubscriptionState.value) {
            is User.SubscriptionState.None -> onOutput(RecordDetailsComponent.Output.SubscriptionSuggestingRequested)
            is User.SubscriptionState.Ongoing -> Unit
            null -> onOutput(RecordDetailsComponent.Output.AuthSuggestingRequested)
        }
    }

    override fun onCommentClick(comment: Comment) {
        analyticsService.logEvent(AnalyticsEvent.RecordCommentShowEvent)
        bottomSheetControl.show(
            CommentDetailsComponent.Config(
                commentText = comment.text,
                commentCreationDate = comment.creationTime.getDisplayName(
                    COMMENT_DETAILS_CREATION_TIME_DISPLAY_PATTERN
                )
            )
        )
    }

    override fun onCardCommentClick(cardImageUrl: String, commentText: String) {
        bottomSheetControl.show(
            CommentDetailsComponent.Config(
                commentText = commentText,
                cardImageUrl = cardImageUrl,
                onCardClick = {
                    val card = record.value.data?.questions?.flatMap {
                        it.cardsWithComment.map { it.card }
                    }?.find { it.imageUrl == cardImageUrl }

                    card?.let {
                        onOutput(
                            RecordDetailsComponent.Output.FullScreenCardsRequested(
                                cards = listOf(card),
                                initialCardPosition = 0
                            )
                        )
                    }
                }
            )
        )
    }

    override fun onAuthButtonClick() {
        updateTutorialState(TutorialState.COMPLETED)
        onOutput(RecordDetailsComponent.Output.AuthRequested)
    }

    override fun onSkipButtonClick() {
        analyticsService.logEvent(AnalyticsEvent.NewRecordSkipEvent)
        dialogControl.show(skipDialogConfig)
    }

    override fun onCompleteButtonClick() {
        isLoading.update { true }
        DebounceClick(debounce, "onCompleteButtonClick", errorHandler) {
            when (recordType) {
                RecordDetailsComponent.RecordType.InCreation -> {
                    completeInCreation()
                }

                RecordDetailsComponent.RecordType.Common -> {
                    completeCommon()
                }

                RecordDetailsComponent.RecordType.Archived -> {
                    completeArchived()
                }
            }
        }
    }

    private fun completeArchived() {
        componentScope.safeLaunch(errorHandler) {
            updateRecord {
                it.copy(isArchived = false)
            }

            recordToUpdate.value?.let {
                updateRecordInteractor.execute(it)
            }

            onOutput(RecordDetailsComponent.Output.CloseComponentRequested)
            analyticsService.logEvent(AnalyticsEvent.ArchiveRecordRestoreEvent)
            messageService.showMessage(
                Message(
                    text = R.string.record_list_popup_restored_from_archive.strResDesc(),
                    isNotification = true
                )
            )
        }
    }

    private suspend fun completeCommon() {
        val newComment = commentInputControl.text.value
        val commentsCount = record.value.data?.comments?.size

        if (newComment.isNotBlank()
            && commentsCount != null
            && commentsCount >= 1
            && userSubscriptionState.value !is User.SubscriptionState.Ongoing
        ) {
            onOutput(RecordDetailsComponent.Output.SubscriptionSuggestingRequested)
            isLoading.update { false }
            return
        }

        if (newComment.isNotBlank()) {
            updateRecord {
                it.copy(
                    comments = it.comments.toMutableList().apply {
                        add(Comment.create(commentInputControl.text.value))
                    }
                )
            }
        }

        recordToUpdate.value?.let { newRecord ->
            val oldRecord = record.value.data

            if (newRecord.comments.size != oldRecord?.comments?.size) {
                analyticsService.logEvent(AnalyticsEvent.RecordCommentAddEvent)
            }
            if (newRecord.energyLevel != oldRecord?.energyLevel) {
                analyticsService.logEvent(AnalyticsEvent.RecordEnergyAddEvent)
            }
            if (newRecord.moodLevel != oldRecord?.moodLevel) {
                analyticsService.logEvent(AnalyticsEvent.RecordMoodAddEvent)
            }
            updateRecordInteractor.execute(newRecord)
        }

        onOutput(RecordDetailsComponent.Output.CloseComponentRequested)
    }

    private suspend fun completeInCreation() {
        val subscriptionState = userSubscriptionState.value

        val isQuestionInput =
            recordToUpdate.value?.mainQuestion?.text?.toString(appLanguageService.getLanguage())
                ?.isNotEmpty() ?: false

        analyticsService.logEvent(
            AnalyticsEvent.NewRecordSaveEvent(
                setupType = recordData?.setupType ?: "",
                cardsNumber = recordData?.cardsNumber ?: 0,
                isQuestionInput = isQuestionInput,
                isCommentInput = commentInputControl.value.value.isNotEmpty(),
                isEnergySet = energyLevel.value != null,
                isMoodSet = moodLevel.value != null,
                deckId = recordData?.deckId ?: ""
            )
        )
        when {
            (subscriptionState is User.SubscriptionState.None && subscriptionState.canLayout)
                || subscriptionState is User.SubscriptionState.Ongoing
                || yandexAdvHelper.isRewarded -> {
                val commentText = commentInputControl.text.value
                val comment = if (commentText.isBlank()) {
                    emptyList()
                } else {
                    listOf(Comment.create(commentText))
                }
                val newRecord = NewRecord(
                    recordToUpdate.value?.questions ?: listOf(Question.createEmptyQuestion()),
                    comment,
                    userFeelings.value,
                    recordToUpdate.value?.isFavourite ?: false,
                    isDailyCard,
                    yandexAdvHelper.isRewarded,
                    courseId,
                    courseThemeId
                )
                addRecordInteractor.execute(newRecord)
                yandexAdvHelper.run { if (isRewarded) clearData() }
                onOutput(RecordDetailsComponent.Output.CloseComponentRequested)
            }

            subscriptionState == null -> onOutput(RecordDetailsComponent.Output.AuthSuggestingRequested)
            else -> {
                onOutput(RecordDetailsComponent.Output.SubscriptionSuggestingRequested)
                isLoading.update { false }
            }
        }
        updateTutorialState(TutorialState.JOURNAL)
    }

    override fun onQuestionChanged(text: LocalizableString) {
        val nullableText = if (text.toString().isBlank()) null else text
        updateRecord {
            it.copy(
                questions = listOf(
                    it.questions.first().copy(text = nullableText)
                )
            )
        }
    }

    override fun onMoodLevelChange(newItem: FeelingItem<Int>?) {
        updateRecord { it.copy(moodLevel = newItem?.value) }
        moodLevel.value = newItem
    }

    override fun onEnergyLevelChange(newItem: FeelingItem<Int>?) {
        updateRecord { it.copy(energyLevel = newItem?.value) }
        energyLevel.value = newItem
    }

    private val skipDialogConfig by lazy {
        DefaultDialogComponent.Config(
            DialogData(
                title = StringDesc.Resource(R.string.add_record_skip_dialog_title),
                message = StringDesc.Resource(R.string.add_record_skip_dialog_message),
                buttons = listOf(
                    DialogData.Button(
                        title = StringDesc.Resource(R.string.add_record_skip_dialog_cancel_button),
                        action = {
                            analyticsService.logEvent(AnalyticsEvent.NewRecordSkipCancelEvent)
                            dialogControl.dismiss()
                        }
                    ),
                    DialogData.Button(
                        title = StringDesc.Resource(R.string.add_record_skip_dialog_skip_button),
                        action = {
                            analyticsService.logEvent(AnalyticsEvent.NewRecordSkipConfirmEvent)
                            updateTutorialState(TutorialState.JOURNAL)
                            onOutput(RecordDetailsComponent.Output.CloseComponentRequested)
                        }
                    )
                )
            )
        )
    }

    private fun updateRecord(newData: (Record) -> Record) {
        if (recordToUpdate.value == null) {
            recordToUpdate.value = record.value.data
        }

        recordToUpdate.value?.let {
            val updatedRecord = newData(it)
            recordToUpdate.value = updatedRecord
        }
    }

    private fun updateTutorialState(state: TutorialState) {
        if (tutorialState.value == TutorialState.SELECT_CARD) {
            tutorialRepository.updateTutorialState(state)
        }
    }
}