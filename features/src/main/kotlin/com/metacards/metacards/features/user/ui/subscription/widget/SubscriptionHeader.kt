package com.metacards.metacards.features.user.ui.subscription.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun SubscriptionHeader() {
    Box(Modifier.padding(bottom = 8.dp)) {
        Image(
            painter = painterResource(id = R.drawable.bg_subscription),
            contentDescription = null,
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(30.dp))
                .aspectRatio(1.43f)
        )

        Text(
            text = R.string.account_subscription_image_title.strResDesc()
                .localizedByLocal(),
            color = CustomTheme.colors.text.primary,
            style = CustomTheme.typography.heading.secondary,
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(20.dp)
        )

        Column(
            modifier = Modifier
                .align(Alignment.BottomStart)
                .padding(20.dp)
        ) {
            Text(
                text = R.string.account_subscription_image_text_1.strResDesc()
                    .localizedByLocal(),
                color = CustomTheme.colors.text.primary,
                style = CustomTheme.typography.heading.medium,
            )
            Text(
                text = R.string.account_subscription_image_text_2.strResDesc()
                    .localizedByLocal(),
                color = CustomTheme.colors.text.primary,
                style = CustomTheme.typography.heading.medium,
            )
        }
    }
}
