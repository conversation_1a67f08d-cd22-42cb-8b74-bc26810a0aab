package com.metacards.metacards.features.deck.domain.interactor

import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.combineLoadableStateFlow
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.domain.entity.DeckInfoWithCards
import com.metacards.metacards.features.deck.domain.entity.isDeckAvailable
import com.metacards.metacards.features.deck.domain.repository.DecksRepository
import com.metacards.metacards.features.user.domain.UserRepository
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlin.math.max
import kotlin.math.min

class GetDeckInfoWithCardsInteractor(
    private val decksRepository: DecksRepository,
    private val userRepository: UserRepository,
) {
    @OptIn(ExperimentalCoroutinesApi::class)
    fun execute(deckId: DeckId): Flow<LoadableState<DeckInfoWithCards>> {
        val userFlow = userRepository.user
        val cardsFlow = decksRepository.getCardsByDeckFlow(deckId)
        val deckFlow = decksRepository.getDeckInfoByIdFlow(deckId)

        return userFlow.flatMapLatest { user ->
            val purchasedDecks = userRepository.getPurchasedDecksIdFlow()
            val hasSubscription = user?.subscriptionState is User.SubscriptionState.Ongoing

            combineLoadableStateFlow(
                deckFlow,
                cardsFlow,
                purchasedDecks
            ) { deck, cards, purchasedDeck ->
                if (deck == null || cards == null || purchasedDeck == null) {
                    return@combineLoadableStateFlow null
                } else {
                    val isAvailable =
                        deck.deckId.isDeckAvailable(
                            deck.availability,
                            hasSubscription,
                            purchasedDeck.map { DeckId(it) }
                        )

                    val resultCards = if (isAvailable) {
                        cards
                    } else {
                        val lastIndex = max(0, min(cards.size - 1, 10))
                        cards.subList(0, lastIndex) + Card.lockedCard(deckId)
                    }

                    DeckInfoWithCards(deck, resultCards, isAvailable, cards.size)
                }
            }.map {
                // force loading state because we're not having any error message or state for user to show,
                // so if any empty data or error occurs in that particular case, we're launching infinite loading view
                if (!it.loading && it.data == null) {
                    it.copy(loading = true)
                } else {
                    it
                }
            }
        }
    }
}
