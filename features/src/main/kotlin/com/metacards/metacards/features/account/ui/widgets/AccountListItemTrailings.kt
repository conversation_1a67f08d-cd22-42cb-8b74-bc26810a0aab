package com.metacards.metacards.features.account.ui.widgets

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.mirrorForRtl
import com.metacards.metacards.features.R

@Composable
fun AccountListItemTrailing(trailingText: String? = null) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        trailingText?.let { text ->
            Text(
                text = text,
                color = CustomTheme.colors.text.secondary,
                style = CustomTheme.typography.heading.small,
                modifier = Modifier.padding(end = 4.dp)
            )
        }

        Icon(
            painter = painterResource(id = R.drawable.ic_24_right),
            contentDescription = null,
            tint = CustomTheme.colors.icons.tertiary,
            modifier = Modifier.mirrorForRtl()
        )
    }
}