package com.metacards.metacards.features.account.ui.profile

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.imePadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.arkivanov.decompose.extensions.compose.jetpack.stack.Children
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.features.account.ui.profile.email.ProfileEmailUi
import com.metacards.metacards.features.account.ui.profile.main.ProfileMainUi
import com.metacards.metacards.features.account.ui.profile.name.ProfileNameUi
import com.metacards.metacards.features.account.ui.profile.password.ProfilePasswordUi
import com.metacards.metacards.features.account.ui.profile.password.confirm.ProfileConfirmPasswordUi

@Composable
fun ProfileFlowUi(
    component: ProfileFlowComponent,
    modifier: Modifier = Modifier,
) {
    val childStack by component.childStack.collectAsState()

    Box(modifier = modifier.imePadding()) {
        Children(stack = childStack, modifier = Modifier.fillMaxSize()) { child ->
            when (val instance = child.instance) {
                is ProfileFlowComponent.Child.Main -> ProfileMainUi(
                    instance.component,
                    Modifier.fillMaxSize()
                )

                is ProfileFlowComponent.Child.Name -> ProfileNameUi(
                    instance.component,
                    Modifier.fillMaxSize()
                )

                is ProfileFlowComponent.Child.Email -> ProfileEmailUi(
                    instance.component,
                    Modifier.fillMaxSize()
                )

                is ProfileFlowComponent.Child.Password -> ProfilePasswordUi(
                    instance.component,
                    Modifier.fillMaxSize()
                )

                is ProfileFlowComponent.Child.ConfirmPassword -> ProfileConfirmPasswordUi(
                    instance.component,
                    Modifier.fillMaxSize()
                )
            }
        }
    }
}

@Preview
@Composable
fun ProfileFlowUiPreview() {
    AppTheme {
        ProfileFlowUi(component = FakeProfileFlowComponent())
    }
}