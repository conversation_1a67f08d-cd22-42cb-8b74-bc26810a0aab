package com.metacards.metacards.features.special_deck.domain.entity

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.deck.domain.entity.CardId

data class SpecialDeckConstellation(
    val bannerDescLocalized: LocalizableString,
    val bannerImg: String,
    val bannerTitleLocalized: LocalizableString,
    val cardIds: List<CardId>,
    val nameAngle: Int,
    val nameAxisX: Int,
    val nameAxisY: Int,
    val name: LocalizableString,
)