package com.metacards.metacards.features.auth.domain

import com.metacards.metacards.core.user.domain.UserId

data class AuthUser(
    val userId: UserId,
    val authLoginTypes: Set<LoginType>,
    val isEmailVerified: Boolean
) {
    inline fun <reified T : LoginType> getLoginByType(): T? =
        getLoginsByType<T>().firstOrNull()

    inline fun <reified T : LoginType> getLoginsByType(): List<T> =
        authLoginTypes.filterIsInstance<T>()
}
