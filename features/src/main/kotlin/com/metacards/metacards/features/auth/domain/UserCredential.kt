package com.metacards.metacards.features.auth.domain

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

private const val ID_GOOGLE = "google"
private const val ID_VK = "vk"
private const val ID_OK = "odnoklassniki"
private const val ID_YANDEX = "yandex"

@Parcelize
sealed class LoginType(open val value: String?) : Parcelable {
    class Email(override val value: String) : LoginType(value)
    class SSO(val ssoType: SSOType, val email: String? = null) : LoginType(email)
}

@JvmInline
value class Password(val value: String)

@Parcelize
sealed class SSOType(val id: String) : Parcelable {
    data object Google : SSOType(ID_GOOGLE)
    data object VK : SSOType(ID_VK)
    data object OK : SSOType(ID_OK)
    data object Yandex : SSOType(ID_YANDEX)
}