package com.metacards.metacards.features.layout.domain

import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.combineLoadableStateFlow
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.Deck
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.domain.repository.DecksRepository
import com.metacards.metacards.features.special_deck.domain.entity.toCard
import com.metacards.metacards.features.special_deck.domain.repository.SpecialDeckRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map

class GetCardsForLayoutInteractor(
    private val decksRepository: DecksRepository,
    private val specialDeckRepository: SpecialDeckRepository
) {
    fun execute(deckId: DeckId?): Flow<LoadableState<List<CardWithCover>>> {
        if (deckId == null) return flowOf(LoadableState(loading = true))

        val deckCardsFlow = decksRepository.getCardsByDeckFlow(deckId)
        val deckFlow = decksRepository.getDeckById(deckId)
        val specialDeckCardsFlow = specialDeckRepository
            .getSpecialDeckCardsByIdFlow(deckId)
            .map { specialDeckCards -> specialDeckCards.map { it.toCard(deckId) } }
        val specialDeckFlow = specialDeckRepository
            .getSpecialDeckByIdFlow(deckId)

        val deckCardsWithCoverFlow =
            combineLoadableStateFlow(deckCardsFlow, deckFlow) { cards, deck ->
                cards?.mapToCardsWithCover(deck)?.shuffled()
            }

        val specialDeckCardsWithCoverFlow =
            combine(specialDeckCardsFlow, specialDeckFlow) { cards, deck ->
                if (cards.isEmpty() || deck == null) {
                    LoadableState()
                } else {
                    LoadableState(
                        data = cards.mapToCardsWithCover(deck.toDeck()).shuffled()
                    )
                }
            }
        return combine(deckCardsWithCoverFlow, specialDeckCardsWithCoverFlow) { deckCards, specialDeckCards ->
            when {
                !deckCards.data.isNullOrEmpty() -> deckCards
                !specialDeckCards.data.isNullOrEmpty() -> specialDeckCards
                deckCards.error != null -> deckCards
                specialDeckCards.error != null -> specialDeckCards
                else -> LoadableState(loading = true)
            }
        }
    }
}

private fun List<Card>.mapToCardsWithCover(deck: Deck?): List<CardWithCover> {
    return map { it.withCover(deck) }
}
