package com.metacards.metacards.features.course.domain.entity

import android.os.Parcelable
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.layout.domain.LayoutId
import kotlinx.parcelize.Parcelize

@Parcelize
data class CoursePassedTestResult(
    val title: LocalizableString,
    val subtitle: LocalizableString?,
    val description: LocalizableString?,
    val coverUrl: String?,
    val videoUrl: LocalizableString?,
    val linkedLayouts: List<LayoutId>?,
    val linkedVideos: List<LinkedVideo>?
) : Parcelable {
    companion object {
        val empty = CoursePassedTestResult(
            title = LocalizableString.createNonLocalizable(""),
            subtitle = null,
            description = null,
            coverUrl = null,
            videoUrl = null,
            linkedLayouts = null,
            linkedVideos = null
        )
    }
}