package com.metacards.metacards.features.deck.data.dto

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.deck.domain.entity.CardHint
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
class CardHintDto(
    @SerialName("promptLocalized")
    val promptLocalized: Map<String, String?> = emptyMap()
)

fun CardHintDto.toDomain(): CardHint = CardHint(LocalizableString(promptLocalized))