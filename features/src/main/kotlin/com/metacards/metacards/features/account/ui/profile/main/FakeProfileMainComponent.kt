package com.metacards.metacards.features.account.ui.profile.main

import com.metacards.metacards.core.bottom_sheet.BottomSheetControl
import com.metacards.metacards.core.bottom_sheet.FakeBottomSheetControl
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.FakeDialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.FakeDefaultDialogComponent
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.features.account.ui.profile.birth_year.FakeProfileBirthYearComponent
import com.metacards.metacards.features.account.ui.profile.birth_year.ProfileBirthYearComponent
import com.metacards.metacards.features.account.ui.profile.delete.FakeProfileDeleteAccountComponent
import com.metacards.metacards.features.account.ui.profile.delete.ProfileDeleteAccountComponent
import com.metacards.metacards.features.account.ui.profile.gender.FakeProfileGenderComponent
import com.metacards.metacards.features.account.ui.profile.gender.ProfileGenderComponent
import com.metacards.metacards.features.auth.domain.LoginType
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakeProfileMainComponent : ProfileMainComponent {
    override val user: StateFlow<User?> = MutableStateFlow(null)
    override val userEmail: StateFlow<String?> = MutableStateFlow(null)
    override val userLoginTypes: StateFlow<Set<LoginType>?> = MutableStateFlow(null)
    override val deleteAccountButtonState = MutableStateFlow(ButtonState.Enabled)
    override val operationInProgress: StateFlow<Boolean> = MutableStateFlow(false)
    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        FakeDialogControl(FakeDefaultDialogComponent())
    override val birthYearBottomSheetControl:
            BottomSheetControl<ProfileBirthYearComponent.Config, ProfileBirthYearComponent> =
        FakeBottomSheetControl(FakeProfileBirthYearComponent())
    override val genderBottomSheetControl: BottomSheetControl<ProfileGenderComponent.Config, ProfileGenderComponent> =
        FakeBottomSheetControl(FakeProfileGenderComponent())
    override val deleteAccountComponent: ProfileDeleteAccountComponent = FakeProfileDeleteAccountComponent()
    override fun onNameClick() = Unit
    override fun onGenderClick() = Unit
    override fun onBirthYearClick() = Unit
    override fun onEmailClick() = Unit
    override fun onChangePasswordClick() = Unit
    override fun onLogoutClick() = Unit
    override fun onDeleteAccountClick() = Unit
    override fun onPasswordConfirmed() = Unit
    override fun onSubscriptionStatusClick() = Unit
    override fun onSubscriptionStatusLongPress() = Unit
}
