package com.metacards.metacards.features.layout.domain

import android.os.Parcelable
import com.metacards.metacards.features.deck.domain.entity.CardWithComment
import com.metacards.metacards.features.record.domain.Question
import kotlinx.parcelize.Parcelize

@Parcelize
data class PredefinedQuestionsWithCards(
    val question: PredefinedQuestion,
    val cardsWithComments: List<CardWithComment>
) : Parcelable {
    fun toQuestion(): Question {
        return Question(
            order = question.order,
            text = question.text,
            cardsWithComment = cardsWithComments
        )
    }
}