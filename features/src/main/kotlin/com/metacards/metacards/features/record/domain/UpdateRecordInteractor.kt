package com.metacards.metacards.features.record.domain

import com.metacards.metacards.features.record.data.RecordRepository
import com.metacards.metacards.features.user.domain.UserRepository

class UpdateRecordInteractor(
    private val userRepository: UserRepository,
    private val recordRepository: RecordRepository
) {

    suspend fun execute(record: Record) {
        val userId = userRepository.user.value?.userId
            ?: throw IllegalArgumentException("User Id was not found")

        recordRepository.updateRecord(userId, record)
    }
}