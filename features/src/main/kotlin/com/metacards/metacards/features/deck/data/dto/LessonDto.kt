package com.metacards.metacards.features.deck.data.dto

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.deck.domain.entity.Lesson
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
class LessonDto(
    @SerialName("nameLocalized")
    val nameLocalized: Map<String, String?> = emptyMap(),
    @SerialName("order")
    val order: Int = 0,
    @SerialName("videoUrlLocalized")
    val videoUrlLocalized: Map<String, String?> = emptyMap(),
    @SerialName("descriptionLocalized")
    val descriptionLocalized: Map<String, String?> = emptyMap(),
    @SerialName("previewUrl")
    val previewUrl: String? = null,
) {
    fun copy(
        name: Map<String, String?> = this.nameLocalized,
        order: Int = this.order,
        videoUrl: Map<String, String?> = this.videoUrlLocalized,
        description: Map<String, String?> = this.descriptionLocalized
    ): LessonDto = LessonDto(name, order, videoUrl, description)
}

fun LessonDto.toDomain(): Lesson {
    return Lesson(
        name = LocalizableString(nameLocalized),
        order = order,
        videoUrl = LocalizableString(videoUrlLocalized),
        description = LocalizableString(descriptionLocalized),
        previewUrl = previewUrl
    )
}