package com.metacards.metacards.features.payments.ui

import androidx.annotation.StringRes
import com.metacards.metacards.features.payments.domain.PurchaseType
import dev.icerock.moko.resources.desc.StringDesc
import java.net.URL

/**
 * Есть несколько реализаций интерфейса, для разных flavour
 */
interface PaymentComponent {
    fun startPayment(purchaseType: PurchaseType)
    fun cancelPaymentFlow()
    fun cancelPurchase(id: String): Boolean

    sealed interface PaymentState {
        data object NotStarted : PaymentState
        data object Started : PaymentState
        data object Purchased : PaymentState
        data class Error(val message: StringDesc) : PaymentState
    }

    sealed interface Output {
        data class WebStoreRequested(val url: URL, @StringRes val title: Int) : Output
        data object Canceled : Output
        data object Succeed : Output
    }
}