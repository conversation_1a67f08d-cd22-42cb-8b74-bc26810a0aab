package com.metacards.metacards.features.showcase.ui.widgets

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.features.R
import com.metacards.metacards.features.course.domain.entity.CourseAvailability
import com.metacards.metacards.features.course.domain.entity.CourseShortData
import com.metacards.metacards.features.course.domain.entity.CourseStatus
import com.metacards.metacards.features.course.ui.list.PremiumBadge
import dev.icerock.moko.resources.desc.strResDesc

private const val ROW_WIDTH = 312
private const val IMAGE_HEIGHT = 152

@Composable
fun ColumnScope.ShowcaseCoursesItem(
    courses: List<CourseShortData>,
    onCourseClick: (CourseShortData) -> Unit,
    isPremiumUser: Boolean
) {
    ShowcaseItemContainer(
        title = R.string.showcase_programs_title.strResDesc().localizedByLocal(),
        subtitle = R.string.showcase_programs_subtitle.strResDesc().localizedByLocal()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .horizontalScroll(rememberScrollState())
        ) {
            val itemModifier = Modifier
                .width(ROW_WIDTH.dp)
                .background(CustomTheme.colors.background.primary, RoundedCornerShape(16.dp))

            Spacer(modifier = Modifier.width(16.dp))

            courses.forEach { course ->
                val shouldBlur = course.availability == CourseAvailability.SUB && !isPremiumUser
                CourseItem(
                    course = course,
                    shouldBlur = shouldBlur,
                    modifier = itemModifier
                        .clip(RoundedCornerShape(16.dp))
                        .clickable { onCourseClick(course) }
                )

                Spacer(modifier = Modifier.width(8.dp))
            }
        }
    }
}

@Composable
private fun CourseItem(
    course: CourseShortData,
    shouldBlur: Boolean,
    modifier: Modifier = Modifier
) {
    Box {
        Column(
            modifier = modifier
                .blur(if (shouldBlur)4.dp else 0.dp)
        ) {
            Spacer(modifier = Modifier.height(16.dp))
            Box {
                AsyncImage(
                    model = course.coverUrl,
                    contentDescription = null,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                        .height(IMAGE_HEIGHT.dp)
                        .clip(RoundedCornerShape(16.dp)),
                    contentScale = ContentScale.Crop
                )

                if (course.status == CourseStatus.SOON) {
                    Box(
                        modifier = Modifier
                            .padding(horizontal = 32.dp, vertical = 16.dp)
                            .background(CustomTheme.colors.button.small, RoundedCornerShape(30.dp)),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = R.string.showcase_programs_mock_item_hint.strResDesc()
                                .localizedByLocal(),
                            style = CustomTheme.typography.button.small,
                            fontSize = 13.sp,
                            color = CustomTheme.colors.text.caption,
                            modifier = Modifier
                                .padding(vertical = 8.dp, horizontal = 24.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = course.name.localizedByLocal(),
                style = CustomTheme.typography.heading.medium,
                color = CustomTheme.colors.text.primary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier
                    .padding(horizontal = 16.dp)
            )

            val description = course.shortDescription?.localizedByLocal() ?: ""
            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = description,
                style = CustomTheme.typography.caption.medium,
                color = CustomTheme.colors.text.secondary,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .heightIn(min = 34.dp)
            )

            Spacer(modifier = Modifier.height(16.dp))
        }

        if (shouldBlur) {
            PremiumBadge(
                modifier = Modifier
                    .align(Alignment.Center)
            )
        }
    }
}
