package com.metacards.metacards.features.special_deck

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.special_deck.data.data_source.SpecialDeckDataSource
import com.metacards.metacards.features.special_deck.data.repository.SpecialDeckRepositoryImpl
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCard
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCooldownTime
import com.metacards.metacards.features.special_deck.domain.entity.UserSpecialDeckInfo
import com.metacards.metacards.features.special_deck.domain.repository.SpecialDeckRepository
import com.metacards.metacards.features.special_deck.ui.RealSpecialDeckComponent
import com.metacards.metacards.features.special_deck.ui.SpecialDeckComponent
import com.metacards.metacards.features.special_deck.ui.card_opening_cooldown.RealSpecialDeckCardOpeningCooldownComponent
import com.metacards.metacards.features.special_deck.ui.card_opening_cooldown.SpecialDeckCardOpeningCooldownComponent
import com.metacards.metacards.features.special_deck.ui.constellation_obtained.RealSpecialDeckConstellationObtainedComponent
import com.metacards.metacards.features.special_deck.ui.constellation_obtained.SpecialDeckConstellationObtainedComponent
import com.metacards.metacards.features.special_deck.ui.deck_obtained.RealSpecialDeckDeckObtainedComponent
import com.metacards.metacards.features.special_deck.ui.deck_obtained.SpecialDeckDeckObtainedComponent
import com.metacards.metacards.features.special_deck.ui.first_card_obtained.RealSpecialDeckFirstCardObtainedComponent
import com.metacards.metacards.features.special_deck.ui.first_card_obtained.SpecialDeckFirstCardObtainedComponent
import com.metacards.metacards.features.special_deck.ui.card_details.RealSpecialDeckCardDetailsComponent
import com.metacards.metacards.features.special_deck.ui.card_details.SpecialDeckCardDetailsComponent
import com.metacards.metacards.features.special_deck.ui.starry_sky.RealSpecialDeckStarrySkyComponent
import com.metacards.metacards.features.special_deck.ui.starry_sky.SpecialDeckStarrySkyComponent
import com.metacards.metacards.features.special_deck.ui.swipe_tutorial.RealSpecialDeckSwipeTutorialComponent
import com.metacards.metacards.features.special_deck.ui.swipe_tutorial.SpecialDeckSwipeTutorialComponent
import com.metacards.metacards.features.special_deck.ui.tutorial.RealSpecialDeckOnboardingComponent
import com.metacards.metacards.features.special_deck.ui.tutorial.SpecialDeckOnboardingComponent
import org.koin.core.component.get
import org.koin.dsl.module

val specialDeckModule = module {
    single { SpecialDeckDataSource(get()) }
    single<SpecialDeckRepository> { SpecialDeckRepositoryImpl(get(), get()) }
}

fun ComponentFactory.createStarDeckComponent(
    componentContext: ComponentContext,
    onOutput: (SpecialDeckComponent.Output) -> Unit,
    deckId: DeckId,
    forceOnboarding: Boolean
): SpecialDeckComponent = RealSpecialDeckComponent(
    componentContext,
    get(),
    get(),
    get(),
    onOutput,
    deckId,
    forceOnboarding
)

fun ComponentFactory.createStarDetailsComponent(
    componentContext: ComponentContext,
    deckId: DeckId,
    card: SpecialDeckCard,
    userSpecialDeckInfo: UserSpecialDeckInfo,
    cardEffectType: SpecialDeckCardDetailsComponent.CardEffectType,
    onOutput: (SpecialDeckCardDetailsComponent.Output) -> Unit
): SpecialDeckCardDetailsComponent = RealSpecialDeckCardDetailsComponent(
    componentContext,
    get(),
    deckId,
    card,
    userSpecialDeckInfo,
    cardEffectType,
    get(),
    get(),
    get(),
    onOutput
)

fun ComponentFactory.createStarDeckOnboardingComponent(
    componentContext: ComponentContext,
    deckTitle: LocalizableString,
    onOutput: (SpecialDeckOnboardingComponent.Output) -> Unit
): SpecialDeckOnboardingComponent = RealSpecialDeckOnboardingComponent(
    componentContext,
    deckTitle,
    onOutput
)

fun ComponentFactory.createStarrySkyComponent(
    componentContext: ComponentContext,
    output: (SpecialDeckStarrySkyComponent.Output) -> Unit,
    deckId: DeckId,
): SpecialDeckStarrySkyComponent = RealSpecialDeckStarrySkyComponent(
    componentContext,
    get(),
    output,
    deckId,
    get(),
    get(),
    get(),
    get()
)

fun ComponentFactory.createSpecialDeckDeckObtainedComponent(
    componentContext: ComponentContext,
    onOutput: (SpecialDeckDeckObtainedComponent.Output) -> Unit
): SpecialDeckDeckObtainedComponent {
    return RealSpecialDeckDeckObtainedComponent(
        componentContext,
        onOutput
    )
}

fun ComponentFactory.createSpecialDeckConstellationObtainedComponent(
    componentContext: ComponentContext,
    imageUrl: String,
    header: LocalizableString,
    description: LocalizableString,
    onOutput: (SpecialDeckConstellationObtainedComponent.Output) -> Unit
): SpecialDeckConstellationObtainedComponent {
    return RealSpecialDeckConstellationObtainedComponent(
        componentContext,
        imageUrl,
        header,
        description,
        onOutput
    )
}

fun ComponentFactory.createSpecialDeckCardOpeningCooldownComponent(
    componentContext: ComponentContext,
    deckCooldownTime: SpecialDeckCooldownTime,
    onOutput: (SpecialDeckCardOpeningCooldownComponent.Output) -> Unit
): SpecialDeckCardOpeningCooldownComponent {
    return RealSpecialDeckCardOpeningCooldownComponent(
        componentContext,
        deckCooldownTime = deckCooldownTime,
        onOutput
    )
}

fun ComponentFactory.createSpecialDeckSwipeTutorialComponent(
    componentContext: ComponentContext,
    onOutput: (SpecialDeckSwipeTutorialComponent.Output) -> Unit
): SpecialDeckSwipeTutorialComponent {
    return RealSpecialDeckSwipeTutorialComponent(
        componentContext,
        onOutput
    )
}

fun ComponentFactory.createSpecialDeckFirstCardObtainedComponent(
    componentContext: ComponentContext,
    imageUrl: String,
    onOutput: (SpecialDeckFirstCardObtainedComponent.Output) -> Unit
): SpecialDeckFirstCardObtainedComponent = RealSpecialDeckFirstCardObtainedComponent(
    componentContext,
    imageUrl,
    onOutput
)
