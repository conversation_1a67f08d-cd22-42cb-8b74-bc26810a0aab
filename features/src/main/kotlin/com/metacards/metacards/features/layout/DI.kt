package com.metacards.metacards.features.layout

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.clipboard.ClipboardManager
import com.metacards.metacards.core.sharing.SharingManager
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CourseThemeId
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.ui.card_preview.CardsPreviewComponent
import com.metacards.metacards.features.deck.ui.card_preview.RealCardsPreviewComponent
import com.metacards.metacards.features.layout.data.PredefinedLayoutDataSource
import com.metacards.metacards.features.layout.data.PredefinedLayoutRepositoryImpl
import com.metacards.metacards.features.layout.domain.CardListViewData
import com.metacards.metacards.features.layout.domain.CardSource
import com.metacards.metacards.features.layout.domain.GetCardsForLayoutInteractor
import com.metacards.metacards.features.layout.domain.GetDailyCardRecordIdInteractor
import com.metacards.metacards.features.layout.domain.GetPredefinedLayoutGroupsInteractor
import com.metacards.metacards.features.layout.domain.GetPredefinedLayoutsInteractor
import com.metacards.metacards.features.layout.domain.PredefinedLayoutRepository
import com.metacards.metacards.features.layout.ui.DeckLayoutComponent
import com.metacards.metacards.features.layout.ui.RealDeckLayoutComponent
import com.metacards.metacards.features.layout.ui.card_list.CardListComponent
import com.metacards.metacards.features.layout.ui.card_list.RealCardListComponent
import com.metacards.metacards.features.layout.ui.predefined_layout.PredefinedLayoutComponent
import com.metacards.metacards.features.layout.ui.predefined_layout.RealPredefinedLayoutComponent
import com.metacards.metacards.features.layout.ui.question_for_layout.QuestionForLayoutComponent
import com.metacards.metacards.features.layout.ui.question_for_layout.RealQuestionForLayoutComponent
import com.metacards.metacards.features.layout.ui.select_card.CardSelectComponent
import com.metacards.metacards.features.layout.ui.select_card.RealCardSelectComponent
import com.metacards.metacards.features.layout.ui.select_card.ar.ArCardSelectComponent
import com.metacards.metacards.features.layout.ui.select_card.ar.RealArCardSelectComponent
import org.koin.core.component.get
import org.koin.dsl.module

val layoutModule = module {
    single { PredefinedLayoutDataSource(get()) }
    single<PredefinedLayoutRepository> { PredefinedLayoutRepositoryImpl(get()) }
    factory { GetCardsForLayoutInteractor(get(), get()) }
    single { GetPredefinedLayoutsInteractor(get(), get(), get(), get()) }
    factory { GetDailyCardRecordIdInteractor(get()) }
    single { SharingManager(get(), get(), get()) }
    single { ClipboardManager(get(), get()) }
    single { GetPredefinedLayoutGroupsInteractor(get(), get()) }
}

fun ComponentFactory.createDeckLayoutComponent(
    componentContext: ComponentContext,
    componentFactory: ComponentFactory,
    cardSource: CardSource,
    questionText: String?,
    courseId: CourseId?,
    courseThemeId: CourseThemeId?,
    onOutput: (DeckLayoutComponent.Output) -> Unit
): DeckLayoutComponent = RealDeckLayoutComponent(
    componentContext,
    componentFactory,
    cardSource,
    questionText,
    courseId,
    courseThemeId,
    get(),
    get(),
    onOutput
)

fun ComponentFactory.createQuestionForLayoutComponent(
    componentContext: ComponentContext,
    deckId: DeckId,
    onOutput: (QuestionForLayoutComponent.Output) -> Unit
): QuestionForLayoutComponent {
    return RealQuestionForLayoutComponent(
        componentContext,
        deckId,
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createBaseCardSelectComponent(
    componentContext: ComponentContext,
    cardSource: CardSource,
    questionText: String?,
    output: (CardSelectComponent.Output) -> Unit
): CardSelectComponent = RealCardSelectComponent(
    componentContext,
    this,
    cardSource,
    get(),
    get(),
    questionText,
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    output
)

fun ComponentFactory.createCardListComponent(
    componentContext: ComponentContext,
    cardListViewData: CardListViewData,
    onOutput: (CardListComponent.Output) -> Unit
): CardListComponent = RealCardListComponent(
    componentContext,
    cardListViewData,
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    onOutput
)

fun ComponentFactory.createCardPreviewComponent(
    componentContext: ComponentContext,
    cards: List<Card>,
    initialPosition: Int,
    shouldShowGif: Boolean,
    onOutput: (CardsPreviewComponent.Output) -> Unit
): CardsPreviewComponent = RealCardsPreviewComponent(
    componentContext,
    cards,
    shouldShowGif,
    initialPosition,
    onOutput,
    get(),
    get(),
    get(),
)

fun ComponentFactory.createPredefinedLayoutComponent(
    componentContext: ComponentContext,
    onOutput: (PredefinedLayoutComponent.Output) -> Unit
): PredefinedLayoutComponent = RealPredefinedLayoutComponent(
    componentContext,
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    onOutput
)

fun ComponentFactory.createArCardSelectComponent(
    componentContext: ComponentContext,
    isPredefined: Boolean,
    output: (ArCardSelectComponent.Output) -> Unit
): ArCardSelectComponent = RealArCardSelectComponent(
    componentContext,
    isPredefined,
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    output
)