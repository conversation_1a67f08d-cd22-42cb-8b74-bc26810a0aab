package com.metacards.metacards.features.special_deck.data.dto

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeck

data class SpecialDeckDto(
    val id: String = "",
    val cover: String = "",
    val hasAR: Boolean = false,
    val constellations: List<SpecialDeckConstellationDto> = emptyList(),
    val nameLocalized: Map<String, String?> = emptyMap(),
    val descriptionLocalized: Map<String, String?> = emptyMap(),
) {
    fun toDomain() = SpecialDeck(
        id = DeckId(id),
        coverUrl = cover,
        hasAR = hasAR,
        constellations = constellations.map { it.toDomain() },
        name = LocalizableString(nameLocalized),
        description = LocalizableString(descriptionLocalized)
    )
}