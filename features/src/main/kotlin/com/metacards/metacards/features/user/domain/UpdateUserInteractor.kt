package com.metacards.metacards.features.user.domain

import com.metacards.metacards.core.user.data.UserDto
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.user.domain.UserId

class UpdateUserInteractor(
    private val userRepository: UserRepository
) {
    suspend fun execute(userId: UserId, updatedUser: User) {
        userRepository.updateUser(userId, updatedUser)
    }

    suspend fun updateUserInfoOnAppLaunch(userId: UserId) {
        userRepository.updateUserInfoOnAppLaunch(userId)
    }

    suspend fun updateYearOfBirth(userId: UserId, yearOfBirth: Int) {
        userRepository.updateYearOfBirth(userId, yearOfBirth)
    }

    suspend fun updateGender(userId: UserId, gender: UserDto.Gender) {
        userRepository.updateGender(userId, gender)
    }

    suspend fun updateName(userId: UserId, name: String) {
        userRepository.updateName(userId, name)
    }
}