package com.metacards.metacards.features.user_analytics.ui.month

import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.PagedData
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.user_analytics.domain.month.MonthAnalyticsInfo
import kotlinx.coroutines.flow.MutableStateFlow

class FakeMonthAnalyticsComponent : MonthAnalyticsComponent {

    override val analyticsState = MutableStateFlow(
        LoadableState(loading = true, PagedData(MonthAnalyticsInfo.LIST_MOCK))
    )
    override val isPremiumUser = MutableStateFlow(false)

    override fun onLoadMore() = Unit

    override fun onMonthPrev() = Unit

    override fun onMonthNext() = Unit

    override fun onRecordClick(recordId: RecordId) = Unit
}