package com.metacards.metacards.features.record.ui.create_record.camera_permission

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.dialog.default_component.DefaultDialog
import com.metacards.metacards.core.dialog.default_component.model.DialogData
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.button.MetaSecondaryButton
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.desc
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun CameraPermissionUi(
    component: CameraPermissionComponent,
    modifier: Modifier = Modifier
) {
    val permissionDialogViewed by component.permissionDialogViewed.collectAsState()

    BoxWithFade(
        modifier, listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        Column {
            TopNavigationBar(leadingIcon = { BackNavigationItem() })

            if (permissionDialogViewed) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_64_photo),
                        tint = CustomTheme.colors.icons.disabled,
                        contentDescription = null
                    )

                    Spacer(modifier = Modifier.size(16.dp))
                    Text(
                        text = R.string.camera_permission_title.strResDesc().localizedByLocal(),
                        color = CustomTheme.colors.text.primary,
                        style = CustomTheme.typography.heading.medium
                    )

                    Spacer(modifier = Modifier.size(8.dp))
                    Text(
                        modifier = Modifier.fillMaxWidth(),
                        text = R.string.camera_permission_description.strResDesc().localizedByLocal(),
                        color = CustomTheme.colors.text.primary,
                        style = CustomTheme.typography.body.primary,
                        textAlign = TextAlign.Center
                    )

                    Spacer(modifier = Modifier.size(16.dp))
                    MetaSecondaryButton(
                        modifier = Modifier.fillMaxWidth(),
                        text = R.string.camera_permission_button_title.strResDesc().localizedByLocal(),
                        onClick = component::openSettings
                    )
                }
            } else {
                DefaultDialog(
                    title = R.string.camera_permission_dialog_title.strResDesc().localizedByLocal(),
                    message = R.string.camera_permission_dialog_description.strResDesc()
                        .localizedByLocal(),
                    buttons = listOf(
                        DialogData.Button(
                            title = R.string.camera_permission_dialog_dismiss_button.strResDesc()
                                .localizedByLocal()
                                .uppercase()
                                .desc(),
                            action = component::dismissDialog
                        ),
                        DialogData.Button(
                            title = R.string.camera_permission_dialog_confirm_button.strResDesc()
                                .localizedByLocal()
                                .uppercase()
                                .desc(),
                            action = component::confirmDialog
                        ),
                    ),
                    canDismissed = false,
                    onDismiss = { /*can't dismissed*/ }
                )
            }
        }
    }
}

@Preview
@Composable
private fun CreateRecordUiPreview() {
    AppTheme {
        CameraPermissionUi(component = FakeCameraPermissionComponent())
    }
}
