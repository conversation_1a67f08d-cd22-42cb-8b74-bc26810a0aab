package com.metacards.metacards.features.user_analytics.ui.layouts.wiget

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.features.record.domain.LevelType

@Composable
fun BoxScope.AnalyticsCandle(
    candleLevel: Int,
    levelType: LevelType,
    oneLevelTypeHeight: Dp,
    candleWidth: Dp
) {
    val modifier = when (levelType) {
        LevelType.Mood -> Modifier
            .align(Alignment.BottomCenter)
            .padding(bottom = 10.dp)
            .background(
                brush = Brush.linearGradient(
                    CustomTheme.colors.gradient.scaleMoonList,
                    start = Offset(100f, 100f),
                    end = Offset(0f, 0f)
                )
            )

        else -> Modifier
            .align(Alignment.TopCenter)
            .padding(top = 10.dp)
            .background(
                brush = Brush.linearGradient(
                    CustomTheme.colors.gradient.scaleMarsList,
                    start = Offset(0f, 0f),
                    end = Offset(100f, 100f)
                )
            )
    }

    Box(
        modifier = modifier.size(
            width = candleWidth,
            height = (oneLevelTypeHeight * candleLevel)
        )
    )
}
