package com.metacards.metacards.features.account.ui.flow

import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.core.utils.createFakeChildStackStateFlow
import com.metacards.metacards.features.user.ui.subscription.FakeSubscriptionComponent
import kotlinx.coroutines.flow.StateFlow

class FakeAccountFlowComponent : AccountFlowComponent {
    override val childStack: StateFlow<ChildStack<*, AccountFlowComponent.Child>> =
        createFakeChildStackStateFlow(
            AccountFlowComponent.Child.Subscription(
                FakeSubscriptionComponent()
            )
        )
}