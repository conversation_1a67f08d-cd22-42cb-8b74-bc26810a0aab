package com.metacards.metacards.features.account.ui.profile.email

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.arkivanov.decompose.extensions.compose.jetpack.stack.Children
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.navigation_bar.IconNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.account.ui.profile.email.new_email.ProfileNewEmailUi
import com.metacards.metacards.features.account.ui.profile.password.confirm.ProfileConfirmPasswordUi
import com.metacards.metacards.features.auth.ui.auth_type.email.message.SentMessageToEmailComponent
import com.metacards.metacards.features.auth.ui.auth_type.email.message.SentMessageToEmailUi
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun ProfileEmailUi(
    component: ProfileEmailComponent,
    modifier: Modifier = Modifier
) {
    val childStack by component.childStack.collectAsState()

    Box(modifier = modifier.navigationBarsPadding()) {
        Children(stack = childStack, modifier = Modifier.fillMaxSize()) { child ->
            when (val instance = child.instance) {
                is ProfileEmailComponent.Child.ConfirmPassword -> ProfileConfirmPasswordUi(
                    instance.component,
                    Modifier.fillMaxSize()
                )
                is ProfileEmailComponent.Child.NewEmail -> ProfileNewEmailUi(
                    instance.component,
                    Modifier.fillMaxSize()
                )
                is ProfileEmailComponent.Child.VerifyEmail -> VerifyEmailUi(
                    instance.component,
                    Modifier.fillMaxSize()
                )
            }
        }
    }
}

@Composable
private fun VerifyEmailUi(
    component: SentMessageToEmailComponent,
    modifier: Modifier = Modifier
) {
    BoxWithFade(modifier, listOfColors = CustomTheme.colors.gradient.backgroundList) {
        Column(modifier = Modifier.fillMaxWidth()) {
            TopNavigationBar(
                title = R.string.account_profile_edit_title.strResDesc(),
                leadingIcon = {
                    IconNavigationItem(
                        iconRes = R.drawable.ic_24_close,
                        onClick = component::onCloseButtonClick
                    )
                },
                modifier = Modifier.padding(bottom = 44.dp)
            )

            Box(modifier = Modifier.fillMaxSize()) {
                SentMessageToEmailUi(component = component)
            }
        }
    }
}

@Preview
@Composable
fun ProfileEmailUiPreview() {
    AppTheme {
        ProfileEmailUi(component = FakeProfileEmailComponent())
    }
}