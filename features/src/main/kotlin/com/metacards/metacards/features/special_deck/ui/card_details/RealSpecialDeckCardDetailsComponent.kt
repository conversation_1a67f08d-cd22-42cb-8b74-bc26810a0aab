package com.metacards.metacards.features.special_deck.ui.card_details

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Matrix
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.asImageBitmap
import androidx.core.graphics.drawable.toBitmap
import coil.imageLoader
import coil.request.ImageRequest
import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.createDefaultDialogComponent
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.model.DialogData
import com.metacards.metacards.core.dialog.dialogControl
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.utils.BlurTransformation
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCard
import com.metacards.metacards.features.special_deck.domain.entity.UserSpecialDeckInfo
import com.metacards.metacards.features.special_deck.domain.entity.ids
import com.metacards.metacards.features.special_deck.domain.repository.SpecialDeckRepository
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.stateIn

class RealSpecialDeckCardDetailsComponent(
    componentContext: ComponentContext,
    componentFactory: ComponentFactory,
    deckId: DeckId,
    override val card: SpecialDeckCard,
    override val userSpecialDeckInfo: UserSpecialDeckInfo,
    override val cardEffectType: SpecialDeckCardDetailsComponent.CardEffectType,
    private val specialDeckRepository: SpecialDeckRepository,
    private val errorHandler: ErrorHandler,
    context: Context,
    private val onOutput: (SpecialDeckCardDetailsComponent.Output) -> Unit
) : ComponentContext by componentContext, SpecialDeckCardDetailsComponent {

    override val blurredImage = MutableStateFlow<ImageBitmap?>(null)
    override val normalImage = MutableStateFlow<ImageBitmap?>(null)
    override val mirrorImage = MutableStateFlow<ImageBitmap?>(null)
    override val isCardAlreadyReceived: Boolean = card.id in userSpecialDeckInfo.cards.ids()
    private val specialDeckCards = specialDeckRepository
        .getSpecialDeckCardsByIdFlow(deckId)
        .stateIn(componentScope, SharingStarted.Eagerly, null)

    private val debounce = Debounce()

    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        dialogControl(dialogComponentFactory = { config, componentContext, dialogControl ->
            componentFactory.createDefaultDialogComponent(
                componentContext,
                config.data,
                dialogControl::dismiss
            )
        })

    private val closeDialogConfig by lazy {
        DefaultDialogComponent.Config(
            DialogData(
                title = StringDesc.Resource(R.string.special_deck_close_dialog_title),
                message = StringDesc.Resource(R.string.special_deck_close_dialog_text),
                buttons = listOf(
                    DialogData.Button(
                        title = StringDesc.Resource(R.string.special_deck_close_dialog_btn_dismiss),
                        action = dialogControl::dismiss
                    ),
                    DialogData.Button(
                        title = StringDesc.Resource(R.string.special_deck_close_dialog_btn_confirm),
                        action = {
                            DebounceClick(debounce, "onCloseStarDetailsClick") {
                                dialogControl.dismiss()
                                onOutput(SpecialDeckCardDetailsComponent.Output.CardNotReleased)
                            }
                        }
                    )
                )
            )
        )
    }

    init {
        componentScope.safeLaunch(errorHandler) {
            val request = ImageRequest.Builder(context)
                .data(card.imageUrl)
                .transformations(BlurTransformation(context))
                .build()
            blurredImage.value =
                context.imageLoader.execute(request).drawable?.toBitmap()?.asImageBitmap()
        }

        componentScope.safeLaunch(errorHandler) {
            val request = ImageRequest.Builder(context)
                .data(card.imageUrl)
                .build()
            val normalBitmap = context.imageLoader.execute(request).drawable?.toBitmap()
            normalImage.value = normalBitmap?.asImageBitmap()
            val matrix = Matrix().apply { preScale(-1f, 1f) }
            if (normalBitmap != null) {
                val mirroredBitmap = Bitmap.createBitmap(
                    normalBitmap, 0, 0, normalBitmap.width, normalBitmap.height, matrix, true
                )
                mirrorImage.value = mirroredBitmap.asImageBitmap()
            }
        }
    }

    override fun onReadyClick() = specialDeckFlowSucceeded()

    override fun onCloseClick(isCardReleased: Boolean) {
        if (isCardReleased) {
            specialDeckFlowSucceeded()
        } else {
            dialogControl.show(closeDialogConfig)
        }
    }

    override fun onCardReleased() {
        componentScope.safeLaunch(errorHandler) {
            val newCardsList = userSpecialDeckInfo.cards.toMutableList().apply {
                add(card.toUserSpecialDeckCardInfo())
            }
            val isCompleted = specialDeckCards.value
                ?.let { specialDeckCards ->
                    newCardsList
                        .map { it.cardId }
                        .containsAll(specialDeckCards.map { it.id })
                }
                ?: false
            specialDeckRepository.updateUserSpecialDeckInfo(
                userSpecialDeckInfo.copy(
                    cards = newCardsList,
                    isCompleted = isCompleted
                )
            )
        }
    }

    private fun specialDeckFlowSucceeded() {
        DebounceClick(debounce, "specialDeckFlowSucceeded") {
            componentScope.safeLaunch(errorHandler) {
                if (isCardAlreadyReceived) {
                    onOutput(SpecialDeckCardDetailsComponent.Output.CardNotReleased)
                } else {
                    onOutput(SpecialDeckCardDetailsComponent.Output.CardReleased(card))
                }
            }
        }
    }
}