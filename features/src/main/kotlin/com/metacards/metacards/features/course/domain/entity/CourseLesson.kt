package com.metacards.metacards.features.course.domain.entity

import android.os.Parcelable
import com.metacards.metacards.core.localization.ui.LocalizableString
import kotlinx.parcelize.Parcelize

data class CourseLesson(
    val name: LocalizableString,
    val cards: List<CourseLessonCard>
) {
    @Parcelize
    data class Query(
        val themeId: CourseThemeId,
        val courseId: CourseId,
        val lessonId: CourseContentId
    ) : Parcelable

    companion object {
        val mock = CourseLesson(
            name = LocalizableString.createNonLocalizable("Lesson"),
            cards = emptyList()
        )
    }
}