package com.metacards.metacards.features.user_analytics.domain.layout

import com.metacards.metacards.features.record.domain.Record
import kotlinx.datetime.LocalDate

data class LayoutRecord(
    val date: LocalDate?,
    val record: Record,
    val showDate: Boolean
) {
    companion object {
        fun empty() = LayoutRecord(
            date = null,
            record = Record.empty(),
            showDate = false
        )
    }
}
