package com.metacards.metacards.features.favorite_cards.domain

import com.metacards.metacards.core.user.domain.FavoriteCard
import com.metacards.metacards.features.user.domain.UserRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update

class GetFavoriteCardsInteractor(
    coroutineScope: CoroutineScope,
    userRepository: UserRepository
) {

    private val stateFlow = MutableStateFlow<List<FavoriteCard>>(emptyList())

    init {
        userRepository.user
            .onEach { user ->
                stateFlow.update { user?.favoriteCards.orEmpty() }
            }.launchIn(coroutineScope)
    }

    fun execute() = stateFlow.asStateFlow()
}