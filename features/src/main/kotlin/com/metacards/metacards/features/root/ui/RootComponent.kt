package com.metacards.metacards.features.root.ui

import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.core.adv.ui.AdvUserConsentComponent
import com.metacards.metacards.core.bottom_sheet.BottomSheetControl
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.localization.domain.AppLanguage
import com.metacards.metacards.core.message.ui.MessageComponent
import com.metacards.metacards.core.web_view.WebViewComponent
import com.metacards.metacards.features.account.ui.flow.AccountFlowComponent
import com.metacards.metacards.features.auth.ui.AuthComponent
import com.metacards.metacards.features.deck.ui.card_preview.CardsPreviewComponent
import com.metacards.metacards.features.deck.ui.deck_info.DeckInfoComponent
import com.metacards.metacards.features.deck.ui.scanning.DeckScanningComponent
import com.metacards.metacards.features.favorite_cards.ui.FavoriteCardsComponent
import com.metacards.metacards.features.layout.ui.DeckLayoutComponent
import com.metacards.metacards.features.layout.ui.predefined_layout.PredefinedLayoutComponent
import com.metacards.metacards.features.layout.ui.question_for_layout.QuestionForLayoutComponent
import com.metacards.metacards.features.main.ui.MainComponent
import com.metacards.metacards.features.payments.ui.PaymentComponent
import com.metacards.metacards.features.course.ui.CourseComponent
import com.metacards.metacards.features.record.ui.archive.ArchiveComponent
import com.metacards.metacards.features.record.ui.calendar.CalendarComponent
import com.metacards.metacards.features.record.ui.create_record.CreateRecordFlowComponent
import com.metacards.metacards.features.record.ui.record_details.RecordDetailsComponent
import com.metacards.metacards.features.special_deck.ui.SpecialDeckComponent
import com.metacards.metacards.features.splash.ui.SplashComponent
import com.metacards.metacards.features.tutorial.ui.TutorialMessageComponent
import com.metacards.metacards.features.user.ui.subscription.subscription_bottom_sheet.SubscriptionBottomSheetComponent
import com.metacards.metacards.features.user_analytics.ui.UserAnalyticsComponent
import com.metacards.metacards.features.welcome.ui.WelcomeScreensComponent
import kotlinx.coroutines.flow.StateFlow

/**
 * A root of a Decompose component tree.
 *
 * Note: Try to minimize child count in RootComponent. It should operate by flows of screens rather than separate screens.
 */
interface RootComponent {
    val childStack: StateFlow<ChildStack<*, Child>>
    val messageComponent: MessageComponent
    val bottomSheetControl: BottomSheetControl<QuestionForLayoutComponent.Config, QuestionForLayoutComponent>
    val subscriptionBottomSheetControl:
        BottomSheetControl<SubscriptionBottomSheetComponent.Config, SubscriptionBottomSheetComponent>
    val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent>
    val appLanguage: StateFlow<AppLanguage>
    val tutorialMessageComponent: TutorialMessageComponent
    val paymentComponent: PaymentComponent
    val isShouldBecomeInvisible: StateFlow<Boolean>

    fun onShakeDetected()

    sealed interface Child {
        class MainAuth(val component: AuthComponent) : Child
        class Main(val component: MainComponent) : Child
        class Splash(val component: SplashComponent) : Child
        class DeckLayout(val component: DeckLayoutComponent) : Child
        class JournalRecordDetails(val component: RecordDetailsComponent) : Child
        class DeckInfo(val component: DeckInfoComponent) : Child
        class DeckScanning(val component: DeckScanningComponent) : Child
        class CardPreview(val component: CardsPreviewComponent) : Child
        class PredefinedLayouts(val component: PredefinedLayoutComponent) : Child
        class Calendar(val component: CalendarComponent) : Child
        class JournalArchive(val component: ArchiveComponent) : Child
        class AccountFlow(val component: AccountFlowComponent) : Child
        class FavoriteCards(val component: FavoriteCardsComponent) : Child
        class WebView(val component: WebViewComponent) : Child
        class WelcomeScreens(val component: WelcomeScreensComponent) : Child
        class CreateRecordFlow(val component: CreateRecordFlowComponent) : Child
        class AdvUserConsent(val component: AdvUserConsentComponent) : Child
        class SpecialDeck(val component: SpecialDeckComponent) : Child
        class UserAnalytics(val component: UserAnalyticsComponent) : Child
        class Course(val component: CourseComponent) : Child
    }
}
