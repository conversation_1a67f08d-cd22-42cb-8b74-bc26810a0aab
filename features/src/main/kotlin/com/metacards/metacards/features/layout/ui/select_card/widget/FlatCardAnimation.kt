package com.metacards.metacards.features.layout.ui.select_card.widget

import androidx.compose.animation.core.FiniteAnimationSpec
import androidx.compose.animation.core.MutableTransitionState
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.updateTransition
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp

private enum class State { PLACING, PLACED }

@Composable
fun calculateDelay(
    index: Int,
    columnCount: Int,
    rowDelayMultiplayer: Int = 200,
    columnDelayMultiplayer: Int = 50
): Int {
    val row = index / columnCount
    val column = index % columnCount
    val rowDelay = rowDelayMultiplayer * row
    val columnDelay = column * columnDelayMultiplayer

    return rowDelay + columnDelay
}

@Composable
fun calculateOffset(
    index: Int,
    columnCount: Int,
    cardWidth: Dp,
    cardHeight: Dp
): Float {
    val row = index / columnCount
    val column = index % columnCount
    val rowDelay = cardHeight * row
    val columnDelay = cardWidth * column
    val result =
        rowDelay + columnDelay + cardWidth * (index % columnCount + 1)
    return with(LocalDensity.current) { result.toPx() }
}

@Composable
fun offsetAnimation(
    fromOffset: Float,
    animation: FiniteAnimationSpec<Float>,
    noAnimation: Boolean
): Pair<Float, Boolean> {
    if (noAnimation) return 0f to true

    val transitionState =
        remember { MutableTransitionState(State.PLACING).apply { targetState = State.PLACED } }
    val transition = updateTransition(transitionState, label = "transition")
    val offset by transition.animateFloat(
        transitionSpec = { animation },
        label = "offset"
    ) { state ->
        when (state) {
            State.PLACING -> fromOffset
            State.PLACED -> 0f
        }
    }

    return offset to (transitionState.isIdle)
}