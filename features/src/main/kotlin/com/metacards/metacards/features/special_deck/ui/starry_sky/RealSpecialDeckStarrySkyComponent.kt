package com.metacards.metacards.features.special_deck.ui.starry_sky

import android.os.CountDownTimer
import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.adv.domain.YandexAdvHelper
import com.metacards.metacards.core.bottom_sheet.BottomSheetControl
import com.metacards.metacards.core.bottom_sheet.bottomSheetControl
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.dialogControl
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.special_deck.createSpecialDeckCardOpeningCooldownComponent
import com.metacards.metacards.features.special_deck.createSpecialDeckSwipeTutorialComponent
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeck
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCard
import com.metacards.metacards.features.special_deck.domain.entity.UserSpecialDeckInfo
import com.metacards.metacards.features.special_deck.domain.entity.ids
import com.metacards.metacards.features.special_deck.domain.repository.SpecialDeckRepository
import com.metacards.metacards.features.special_deck.ui.card_opening_cooldown.SpecialDeckCardOpeningCooldownComponent
import com.metacards.metacards.features.special_deck.ui.swipe_tutorial.SpecialDeckSwipeTutorialComponent
import com.metacards.metacards.features.user.domain.GetUserSubscriptionStateInteractor
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn

class RealSpecialDeckStarrySkyComponent(
    componentContext: ComponentContext,
    componentFactory: ComponentFactory,
    private val onOutput: (SpecialDeckStarrySkyComponent.Output) -> Unit,
    deckId: DeckId,
    specialDeckRepository: SpecialDeckRepository,
    getUserSubscriptionStateInteractor: GetUserSubscriptionStateInteractor,
    errorHandler: ErrorHandler,
    private val yandexAdvHelper: YandexAdvHelper
) : ComponentContext by componentContext, SpecialDeckStarrySkyComponent {

    private companion object {
        const val USER_INTERACTION_TIMEOUT = 30_000L
    }

    override val deckInfo: StateFlow<SpecialDeck?> =
        specialDeckRepository.getSpecialDeckByIdFlow(deckId)
            .stateIn(componentScope, SharingStarted.Eagerly, null)

    override val deckCards: StateFlow<List<SpecialDeckCard>> =
        specialDeckRepository.getSpecialDeckCardsByIdFlow(deckId)
            .stateIn(componentScope, SharingStarted.Eagerly, emptyList())

    override val userSpecialDeckInfo: StateFlow<UserSpecialDeckInfo> =
        specialDeckRepository.getUserSpecialDeckInfoByIdFlow(deckId)
            .stateIn(componentScope, SharingStarted.Eagerly, UserSpecialDeckInfo.mock())

    override val cooldownBottomSheetControl: BottomSheetControl<
        SpecialDeckCardOpeningCooldownComponent.Config,
        SpecialDeckCardOpeningCooldownComponent
        > = bottomSheetControl(
        key = "cooldownBottomSheetControl",
        bottomSheetComponentFactory = { config, context, _ ->
            componentFactory.createSpecialDeckCardOpeningCooldownComponent(
                context,
                config.deckCooldownTime,
                ::onSpecialDeckCardOpeningCooldownOutput
            )
        },
        halfExpandingSupported = false,
        hidingSupported = true,
        handleBackButton = true
    )

    override val isUserStoppedInteraction = MutableStateFlow(false)

    private val userInteractionTimer =
        object : CountDownTimer(USER_INTERACTION_TIMEOUT, USER_INTERACTION_TIMEOUT) {
            override fun onTick(millisUntilFinished: Long) = Unit
            override fun onFinish() { isUserStoppedInteraction.value = true }
        }

    override val swipeTutorialControl:
        DialogControl<SpecialDeckSwipeTutorialComponent.Config, SpecialDeckSwipeTutorialComponent> =
        dialogControl(
            key = "swipeTutorialControl",
            dialogComponentFactory = { _, componentContext, _ ->
                componentFactory.createSpecialDeckSwipeTutorialComponent(
                    componentContext,
                    ::onSwipeTutorialOutput
                )
            }
        )

    private val isCanLayout = getUserSubscriptionStateInteractor.execute().map {
        it?.canLayout ?: false
    }.stateIn(componentScope, SharingStarted.Eagerly, false)

    private val isPremiumUser = getUserSubscriptionStateInteractor.execute().map {
        it is User.SubscriptionState.Ongoing
    }.stateIn(componentScope, SharingStarted.Eagerly, false)

    private var cardToOpenAfterAdv: SpecialDeckCard? = null

    init {
        userInteractionTimer.start()

        componentScope.safeLaunch(errorHandler) {
            if (specialDeckRepository.getUserSpecialDeckInfoById(deckId)?.cards?.size == 1) {
                swipeTutorialControl.show(SpecialDeckSwipeTutorialComponent.Config)
            }
        }
    }

    override fun onCardClick(card: SpecialDeckCard) {
        with(userSpecialDeckInfo.value) {
            when {
                card.id in cards.ids() -> onOutput(
                    SpecialDeckStarrySkyComponent.Output.SpecialDeckCardDetailsRequested(
                        card,
                        userSpecialDeckInfo.value
                    )
                )
                isCanSelectNewCard() -> {
                    if (isPremiumUser.value || isFirstCardToday()) {
                        onOutput(
                            SpecialDeckStarrySkyComponent.Output.SpecialDeckCardDetailsRequested(
                                card,
                                userSpecialDeckInfo.value
                            )
                        )
                    } else {
                        cardToOpenAfterAdv = card
                        subscribeForAdvReward()
                        onOutput(
                            SpecialDeckStarrySkyComponent.Output
                                .SubscriptionBottomSheetRequested(isSpecialDeckCardRequested = true)
                        )
                    }
                }
                else -> {
                    cooldownBottomSheetControl.show(
                        SpecialDeckCardOpeningCooldownComponent.Config(
                            userSpecialDeckInfo.value.cooldownTime
                        )
                    )
                }
            }
        }
    }

    override fun onUserInteracted() {
        isUserStoppedInteraction.value = false
        userInteractionTimer.cancel()
        userInteractionTimer.start()
    }

    override fun onCloseClick() {
        onOutput(SpecialDeckStarrySkyComponent.Output.CloseRequested)
    }

    override fun onLayoutClick() {
        if (isCanLayout.value) {
            onOutput(SpecialDeckStarrySkyComponent.Output.LayoutRequested)
        } else {
            cardToOpenAfterAdv = null
            subscribeForAdvReward()
            onOutput(
                SpecialDeckStarrySkyComponent.Output
                    .SubscriptionBottomSheetRequested(isSpecialDeckCardRequested = false)
            )
        }
    }

    private fun onSwipeTutorialOutput(output: SpecialDeckSwipeTutorialComponent.Output) {
        when (output) {
            is SpecialDeckSwipeTutorialComponent.Output.DismissRequested -> {
                swipeTutorialControl.dismiss()
            }
        }
    }

    private fun onSpecialDeckCardOpeningCooldownOutput(
        output: SpecialDeckCardOpeningCooldownComponent.Output
    ) {
        when (output) {
            SpecialDeckCardOpeningCooldownComponent.Output.DismissRequested ->
                cooldownBottomSheetControl.dismiss()
        }
    }

    private fun subscribeForAdvReward() {
        yandexAdvHelper.subscribeForReward {
            cardToOpenAfterAdv?.let {
                onOutput(
                    SpecialDeckStarrySkyComponent.Output.SpecialDeckCardDetailsRequested(
                        it,
                        userSpecialDeckInfo.value
                    )
                )
                cardToOpenAfterAdv = null
            } ?: run {
                onOutput(SpecialDeckStarrySkyComponent.Output.LayoutRequested)
            }
        }
    }
}
