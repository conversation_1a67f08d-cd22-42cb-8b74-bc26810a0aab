package com.metacards.metacards.features.record.ui.record_details.widget

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.navigation_bar.IconNavigationItem
import com.metacards.metacards.features.R
import com.metacards.metacards.features.home.ui.widget.ActionButton
import com.metacards.metacards.features.record.ui.model.FeelingItem
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun FeelingContent(
    userSubscriptionState: User.SubscriptionState?,
    moodLevel: FeelingItem<Int>?,
    energyLevel: FeelingItem<Int>?,
    isMoodInteractionEnabled: Boolean,
    isEnergyInteractionEnabled: Boolean,
    onMoodLevelChange: (FeelingItem<Int>?) -> Unit,
    onEnergyLevelChange: (FeelingItem<Int>?) -> Unit,
    onBlockContentClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        if (userSubscriptionState !is User.SubscriptionState.Ongoing) {
            BoxWithFade(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = 8.dp),
                listOfColors = CustomTheme.colors.gradient.backgroundList.map {
                    it.copy(alpha = 0.75f)
                },
                behindContent = {
                    FeelingsBlock(
                        moodLevel = moodLevel,
                        energyLevel = energyLevel,
                        onMoodLevelChange = onMoodLevelChange,
                        onEnergyLevelChange = onEnergyLevelChange,
                        isMoodInteractionEnabled = isMoodInteractionEnabled,
                        isEnergyInteractionEnabled = isEnergyInteractionEnabled,
                        modifier = Modifier
                            .padding(top = 8.dp)
                            .padding(horizontal = 16.dp)
                    )
                }
            ) {
                Box(
                    modifier = Modifier
                        .matchParentSize()
                        .clickable { onBlockContentClick() }
                        .align(Alignment.TopCenter)
                ) {
                    ActionButton(
                        modifier = Modifier
                            .align(Alignment.TopCenter)
                            .padding(top = 100.dp),
                        text = StringDesc.Resource(R.string.add_record_premium_action_button),
                        borderStroke = BorderStroke(
                            0.5.dp,
                            CustomTheme.colors.stroke.secondary
                        ),
                        leadingIcon = {
                            IconNavigationItem(iconRes = R.drawable.ic_24_locked)
                        }
                    )
                }
            }
        } else {
            FeelingsBlock(
                moodLevel = moodLevel,
                energyLevel = energyLevel,
                onMoodLevelChange = onMoodLevelChange,
                onEnergyLevelChange = onEnergyLevelChange,
                isMoodInteractionEnabled = isMoodInteractionEnabled,
                isEnergyInteractionEnabled = isEnergyInteractionEnabled,
                modifier = Modifier
                    .padding(top = 16.dp)
                    .padding(horizontal = 16.dp)
            )
        }
    }
}

@Composable
private fun FeelingsBlock(
    moodLevel: FeelingItem<Int>?,
    energyLevel: FeelingItem<Int>?,
    isMoodInteractionEnabled: Boolean,
    isEnergyInteractionEnabled: Boolean,
    onMoodLevelChange: (FeelingItem<Int>?) -> Unit,
    onEnergyLevelChange: (FeelingItem<Int>?) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            modifier = Modifier.padding(bottom = 8.dp, start = 16.dp),
            text = R.string.add_record_feeling_energy_title.strResDesc().localizedByLocal(),
            style = CustomTheme.typography.caption.medium,
            color = CustomTheme.colors.text.secondary
        )

        FeelingWidget(
            modifier = Modifier
                .fillMaxWidth()
                .then(FeelingWidgetDefaults.defaultModifier),
            feelingItems = FeelingItem.mars_list,
            backgroundResource = R.drawable.bg_mars,
            selectedItem = energyLevel,
            interactionEnabled = isEnergyInteractionEnabled,
            onSelectItem = onEnergyLevelChange
        )
        Text(
            modifier = Modifier
                .padding(bottom = 8.dp, top = 16.dp, start = 16.dp),
            text = R.string.add_record_feeling_mood_title.strResDesc().localizedByLocal(),
            style = CustomTheme.typography.caption.medium,
            color = CustomTheme.colors.text.secondary
        )

        FeelingWidget(
            modifier = Modifier
                .fillMaxWidth()
                .then(FeelingWidgetDefaults.defaultModifier),
            feelingItems = FeelingItem.moon_list,
            backgroundResource = R.drawable.bg_moon,
            selectedItem = moodLevel,
            interactionEnabled = isMoodInteractionEnabled,
            onSelectItem = onMoodLevelChange
        )
    }
}

@Preview
@Composable
private fun FeelingContentPreview() {
    AppTheme {
        FeelingContent(
            userSubscriptionState = User.SubscriptionState.None(hadLayoutToday = true, hadSubscription = true),
            moodLevel = FeelingItem(0, 0),
            energyLevel = FeelingItem(0, 0),
            isMoodInteractionEnabled = true,
            isEnergyInteractionEnabled = true,
            onMoodLevelChange = {},
            onEnergyLevelChange = {},
            onBlockContentClick = {}
        )
    }
}
