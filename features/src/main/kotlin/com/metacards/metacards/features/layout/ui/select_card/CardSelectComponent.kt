package com.metacards.metacards.features.layout.ui.select_card

import com.google.android.filament.Engine
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardHint
import com.metacards.metacards.features.deck.domain.entity.CardWithComment
import com.metacards.metacards.features.layout.domain.PredefinedQuestion
import com.metacards.metacards.features.layout.ui.select_card.ar.ArCardSelectComponent
import com.metacards.metacards.features.tutorial.data.TutorialStep
import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.flow.StateFlow

interface CardSelectComponent {
    val cards: StateFlow<LoadableState<CardsWithQuestionId>>
    val tutorialMessage: StateFlow<TutorialMessage?>
    val tutorialStep: StateFlow<TutorialStep>
    val tabs: List<Tabs>
    val selectedTab: StateFlow<Tabs>
    val arComponent: ArCardSelectComponent
    val isInteractionEnabled: StateFlow<Boolean>
    val questionText: StateFlow<StringDesc>
    val activeQuestionIndex: StateFlow<Int>
    val predefinedQuestions: List<PredefinedQuestion>
    val isPredefinedLayout: Boolean
    val arEngine: Engine
    val isCameraPermissionGranted: StateFlow<Boolean>

    fun onTabChange(tabModel: Tabs)
    fun onCloseClick(isArCardSelect: Boolean)
    fun onCardSelect(card: Card, questionText: String?)
    fun onCardLayoutAnimationEnd()
    fun openSettings()

    sealed interface Output {
        data class OnCardSelected(
            val card: Card,
            val availableCards: List<Card>,
            val isLastPredefined: Boolean = false,
            val questionIndex: Int,
            val cardHints: List<CardHint>,
            val questionText: LocalizableString?
        ) : Output

        data class OnCloseRequested(val isArCardSelect: Boolean) : Output

        data class OnArFinished(val cardsWithComment: List<CardWithComment>) : Output

        data class OnArPredefinedFinished(
            val cardsWithComment: List<CardWithComment>,
            val isLastPredefined: Boolean
        ) : Output

        data object AuthSuggestingScreenRequested : Output

        data object SubscriptionSuggestingScreenRequested : Output
    }

    enum class Tabs {
        Flat, AR;

        fun toStringDesc(): StringDesc {
            return when (this) {
                Flat -> R.string.deck_layout_flat_tab.strResDesc()
                AR -> R.string.deck_layout_ar_tab.strResDesc()
            }
        }
    }
}