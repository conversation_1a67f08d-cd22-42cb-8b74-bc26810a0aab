package com.metacards.metacards.features.record.ui.create_record.add_card.widgets

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.ContentDrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.ResourceStringDesc
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun ColumnScope.CardFrame(text: ResourceStringDesc, isHideFrame: Boolean) {
    Box(
        modifier = Modifier
            .alpha(if (isHideFrame) 0f else 1f)
            .padding(horizontal = 32.dp)
            .background(
                color = CustomTheme.colors.button.small,
                shape = RoundedCornerShape(30.dp)
            )
            .fillMaxWidth()
            .weight(1f)
            .drawWithContent {
                drawContent()
                border(
                    color = CustomTheme.colors.icons.primary,
                    cornerRadius = 30.dp,
                    strokeWidth = 3.dp,
                    verticalPadding = 16.dp
                )
            },
        contentAlignment = Alignment.Center

    ) {
        Text(
            textAlign = TextAlign.Center,
            text = text.localizedByLocal(),
            color = CustomTheme.colors.text.caption,
            style = CustomTheme.typography.heading.medium
        )
    }
}

private fun ContentDrawScope.border(
    color: Color,
    cornerRadius: Dp,
    strokeWidth: Dp,
    verticalPadding: Dp
) {
    // top line
    drawLine(
        color,
        start = Offset(cornerRadius.toPx(), y = 0f),
        end = Offset(size.width - cornerRadius.toPx(), 0f),
        strokeWidth = strokeWidth.toPx()
    )

    // topLeft corner
    drawArc(
        color = color,
        startAngle = 180f,
        sweepAngle = 90f,
        useCenter = false,
        topLeft = Offset(x = 0f, y = 0f),
        size = Size(cornerRadius.toPx() * 2, cornerRadius.toPx() * 2),
        style = Stroke(width = strokeWidth.toPx())
    )

    // top left line
    drawLine(
        color,
        start = Offset(0f, y = cornerRadius.toPx()),
        end = Offset(0f, y = cornerRadius.toPx() + verticalPadding.toPx()),
        strokeWidth = strokeWidth.toPx()
    )

    // topRight corner
    drawArc(
        color = color,
        startAngle = 270f,
        sweepAngle = 90f,
        useCenter = false,
        topLeft = Offset(x = size.width - (cornerRadius.toPx() * 2), y = 0f),
        size = Size(cornerRadius.toPx() * 2, cornerRadius.toPx() * 2),
        style = Stroke(width = strokeWidth.toPx())
    )

    // top right line
    drawLine(
        color,
        start = Offset(x = size.width, y = cornerRadius.toPx()),
        end = Offset(x = size.width, y = cornerRadius.toPx() + verticalPadding.toPx()),
        strokeWidth = strokeWidth.toPx()
    )

    // bottom line
    drawLine(
        color,
        start = Offset(cornerRadius.toPx(), y = size.height),
        end = Offset(size.width - cornerRadius.toPx(), size.height),
        strokeWidth = strokeWidth.toPx()
    )

    // bottomLeft corner
    drawArc(
        color = color,
        startAngle = 90f,
        sweepAngle = 90f,
        useCenter = false,
        topLeft = Offset(x = 0f, y = size.height - (cornerRadius.toPx() * 2)),
        size = Size(cornerRadius.toPx() * 2, cornerRadius.toPx() * 2),
        style = Stroke(width = strokeWidth.toPx())
    )

    // bottom left line
    drawLine(
        color,
        start = Offset(x = 0f, y = size.height - cornerRadius.toPx()),
        end = Offset(
            x = 0f,
            y = size.height - cornerRadius.toPx() - verticalPadding.toPx()
        ),
        strokeWidth = strokeWidth.toPx()
    )

    // bottomRight corner
    drawArc(
        color = color,
        startAngle = 0f,
        sweepAngle = 90f,
        useCenter = false,
        topLeft = Offset(
            x = size.width - (cornerRadius.toPx() * 2),
            y = size.height - (cornerRadius.toPx() * 2)
        ),
        size = Size(cornerRadius.toPx() * 2, cornerRadius.toPx() * 2),
        style = Stroke(width = strokeWidth.toPx())
    )

    // bottom right line
    drawLine(
        color,
        start = Offset(x = size.width, y = size.height - cornerRadius.toPx()),
        end = Offset(
            x = size.width,
            y = size.height - cornerRadius.toPx() - verticalPadding.toPx()
        ),
        strokeWidth = strokeWidth.toPx()
    )
}

@Preview
@Composable
private fun CardFramePreview() {
    AppTheme {
        Column {
            CardFrame(R.string.image_tracking_frame_hint.strResDesc(), false)
        }
    }
}
