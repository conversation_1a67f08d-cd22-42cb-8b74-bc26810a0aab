package com.metacards.metacards.features.home.ui

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBars
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.RoundRect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.ClipOp
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.clipPath
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.layout.positionInRoot
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.dialog.default_component.DefaultDialog
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.WidgetPosition
import com.metacards.metacards.features.R
import com.metacards.metacards.features.advbanner.ui.HomeAdvBannerUi
import com.metacards.metacards.features.home.ui.widget.DecksCard
import com.metacards.metacards.features.home.ui.widget.LayoutBanner
import com.metacards.metacards.features.tutorial.data.TutorialStep
import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import com.metacards.metacards.features.tutorial.ui.MessagePopupContent
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun HomeUi(
    component: HomeComponent,
    modifier: Modifier = Modifier,
) {
    val deckList by component.deckList.collectAsState()
    val homeAdvBanner by component.homeAdvBanner.collectAsState()
    val cardOfTheDayState by component.dailyCardState.collectAsState()
    val tutorialStep by component.tutorialStep.collectAsState()
    val tutorialMessage by component.tutorialMessage.collectAsState()
    val ld = LocalDensity.current
    var deckTutorHeight by remember { mutableStateOf(0.dp) }
    val statusBarInset = WindowInsets.statusBars.getTop(ld)
    var predefinedButtonPosition by remember { mutableStateOf(WidgetPosition.initial) }

    BoxWithFade(
        modifier = modifier,
        listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        Column(modifier = Modifier.matchParentSize()) {
            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 24.dp),
                text = R.string.home_deck_header_title.strResDesc().localizedByLocal(),
                style = CustomTheme.typography.heading.primary,
                color = CustomTheme.colors.text.primary
            )

            AnimatedVisibility(visible = homeAdvBanner != null) {
                HomeAdvBannerUi(
                    banner = homeAdvBanner,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    onClick = component::onAdvBannerClick,
                    onDismiss = component::onAdvBannerDismiss
                )
            }

            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 12.dp)
                    .padding(bottom = 20.dp)
            ) {
                LayoutBanner(
                    headerText = R.string.home_deck_layout_title.strResDesc().localizedByLocal(),
                    descriptionText = R.string.home_deck_layout_subtitle.strResDesc().localizedByLocal(),
                    actionButtonText = R.string.home_deck_layout_action_button_text.strResDesc().localizedByLocal(),
                    onClick = component::onLayoutBannerClick,
                    modifier = Modifier
                        .weight(1f)
                        .onGloballyPositioned { layoutCoordinates ->
                            val position = layoutCoordinates.positionInRoot()
                            predefinedButtonPosition = WidgetPosition(
                                xOffset = position.x,
                                yOffset = position.y,
                                size = layoutCoordinates.size
                            )
                        }
                )

                LayoutBanner(
                    headerText = R.string.home_card_of_the_day_title.strResDesc().localizedByLocal(),
                    descriptionText = R.string.home_card_of_the_day_subtitle.strResDesc().localizedByLocal(),
                    actionButtonText = R.string.home_card_of_the_day_action_button_text.strResDesc().localizedByLocal(),
                    customBackground = cardOfTheDayState?.imageUrl,
                    blurBackground = true,
                    onClick = component::onCardOfTheDayClick,
                    modifier = Modifier.weight(1f)
                )
            }

            // deck tutor anchor, 24dp = 2 card gap
            Spacer(
                modifier = Modifier
                    .onGloballyPositioned { layoutCoordinates ->
                        val positionInRoot = layoutCoordinates.positionInRoot()
                        deckTutorHeight =
                            with(ld) { (positionInRoot.y - statusBarInset).toDp().plus(24.dp) }
                    }
            )

            BoxWithConstraints {
                DecksCard(
                    parentWidth = maxWidth,
                    deckList = deckList,
                    component = component,
                    availableHeight = maxHeight,
                    isTutorial = tutorialStep == TutorialStep.DECKS
                )
            }
        }

        DefaultDialog(dialogControl = component.dialogControl)

        tutorialMessage?.let {
            when (tutorialStep) {
                TutorialStep.START -> TutorialStart(it)
                TutorialStep.DECKS -> TutorialDecks(it, deckTutorHeight)
                TutorialStep.PREDEFINED -> TutorialPredefined(
                    tutorialMessage = it,
                    ld = ld,
                    predefinedButton = predefinedButtonPosition,
                    statusBarInset = statusBarInset
                )
                else -> Unit
            }
        }
    }
}

@Composable
private fun TutorialStart(tutorialMessage: TutorialMessage) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(CustomTheme.colors.background.disabledBackground)
            .clickable(enabled = false) { },
        contentAlignment = Alignment.Center
    ) {
        MessagePopupContent(tutorialMessage = tutorialMessage)
    }
}

@Composable
private fun TutorialDecks(
    tutorialMessage: TutorialMessage,
    deckTutorHeight: Dp
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(deckTutorHeight)
            .background(CustomTheme.colors.background.disabledBackground)
            .clickable(enabled = false) {}
    ) {
        MessagePopupContent(
            tutorialMessage = tutorialMessage,
            modifier = Modifier
                .align(Alignment.BottomCenter)
        )
    }
}

@Composable
private fun TutorialPredefined(
    tutorialMessage: TutorialMessage,
    ld: Density,
    predefinedButton: WidgetPosition,
    statusBarInset: Int
) {
    var messageWidth by remember { mutableIntStateOf(0) }
    val messageYOffset = predefinedButton.yOffset + predefinedButton.size.height -
        statusBarInset + (12 * ld.density)
    val messageXOffset =
        with(ld) {
            predefinedButton.xOffset.toDp() + predefinedButton.size.width.toDp().div(2) -
                20.dp // arrow padding start
        }
    Box(modifier = Modifier.fillMaxSize()) {
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .clickable(enabled = false) { }
        ) {
            drawIntoCanvas {
                clipPath(
                    Path().apply {
                        addRoundRect(
                            RoundRect(
                                Rect(
                                    offset = Offset(
                                        y = predefinedButton.yOffset - statusBarInset,
                                        x = predefinedButton.xOffset
                                    ),
                                    size = Size(
                                        width = predefinedButton.size.width.toFloat(),
                                        height = predefinedButton.size.height.toFloat()
                                    )
                                ),
                                cornerRadius = CornerRadius(8f * ld.density)
                            )
                        )
                    },
                    ClipOp.Difference
                ) {
                    drawRoundRect(
                        color = CustomTheme.colors.background.disabledBackground,
                        cornerRadius = CornerRadius(1f)
                    )
                }
            }
        }
        val alpha by animateFloatAsState(targetValue = if (messageWidth == 0) 0f else 1f, label = "")
        MessagePopupContent(
            tutorialMessage = tutorialMessage,
            modifier = Modifier
                .offset(
                    y = with(ld) {
                        messageYOffset
                            .toDp()
                            .plus(4.dp)
                    },
                    x = messageXOffset
                )
                .onSizeChanged { size -> messageWidth = size.width }
                .alpha(alpha)
        )
    }
}

@Preview
@Composable
fun HomeUiPreview() {
    AppTheme {
        HomeUi(FakeHomeComponent())
    }
}
