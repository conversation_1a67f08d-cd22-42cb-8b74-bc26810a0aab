package com.metacards.metacards.features.layout.ui.question_for_layout

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.calculateEndPadding
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.text_field.MetaTextField
import com.metacards.metacards.features.R
import com.metacards.metacards.features.tutorial.ui.MessagePopupContent
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun QuestionForLayoutUI(component: QuestionForLayoutComponent) {
    val currentSymbolsCount by component.currentSymbolsCount.collectAsState()
    val tutorialMessage by component.tutorialMessage.collectAsState()

    Column {
        tutorialMessage?.let {
            MessagePopupContent(
                tutorialMessage = it,
                modifier = Modifier.align(Alignment.CenterHorizontally)
            )
        }
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    CustomTheme.colors.background.modal,
                    RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)
                )
                .padding(horizontal = 16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 24.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    modifier = Modifier,
                    text = R.string.deck_layout_question_bottom_sheet_title.strResDesc()
                        .localizedByLocal(),
                    style = CustomTheme.typography.heading.secondary,
                    color = CustomTheme.colors.text.primary
                )

                Icon(
                    modifier = Modifier.clickable(
                        boundedRipple = false,
                        onClick = component::onCloseButtonClick
                    ),
                    painter = painterResource(id = R.drawable.ic_24_close),
                    contentDescription = null,
                    tint = CustomTheme.colors.icons.primary
                )
            }

            Text(
                modifier = Modifier.padding(bottom = 16.dp),
                text = R.string.deck_layout_question_bottom_sheet_subtitle.strResDesc()
                    .localizedByLocal(),
                style = CustomTheme.typography.body.primary,
                color = CustomTheme.colors.text.primary
            )

            MetaTextField(
                inputControl = component.questionInputControl,
                modifier = Modifier.padding(bottom = 24.dp),
                heightMin = 111.dp,
                heightMax = 111.dp,
                placeholder = R.string.deck_layout_question_bottom_sheet_hint.strResDesc()
                    .localizedByLocal(),
                decorationOverlay = { innerPaddings ->
                    val maxSymbolsCount = component.questionInputControl.maxLength
                    Box(
                        modifier = Modifier
                            .align(Alignment.BottomEnd)
                            .padding(
                                bottom = innerPaddings.calculateBottomPadding(),
                                end = innerPaddings.calculateEndPadding(LocalLayoutDirection.current)
                            )
                    ) {
                        SymbolCounter(currentCount = currentSymbolsCount, maxCount = maxSymbolsCount)
                    }
                }
            )

            MetaAccentButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 8.dp),
                text = R.string.deck_layout_question_bottom_sheet_next_button.strResDesc()
                    .localizedByLocal(),
                onClick = component::onNextButtonClick
            )
        }
    }
}

@Composable
fun SymbolCounter(
    currentCount: Int,
    maxCount: Int,
    modifier: Modifier = Modifier,
    shouldHighlightOverCount: Boolean = false,
) {
    val isOverCount = currentCount >= maxCount
    val color = if (shouldHighlightOverCount && isOverCount) {
        CustomTheme.colors.text.error
    } else {
        CustomTheme.colors.text.secondary
    }
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        Text(
            text = "$currentCount/$maxCount",
            style = CustomTheme.typography.caption.small,
            color = color
        )
    }
}

@Preview
@Composable
fun QuestionForLayoutUIPreview() {
    AppTheme {
        QuestionForLayoutUI(FakeQuestionForLayoutComponent())
    }
}
