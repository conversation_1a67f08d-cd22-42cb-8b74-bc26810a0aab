package com.metacards.metacards.features.user.domain.subscription.interactor

import com.metacards.metacards.core.user.domain.SubscriptionTariff
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.features.user.domain.subscription.repository.SubscriptionRepository
import kotlinx.coroutines.flow.Flow

class GetTariffsInteractor(private val repository: SubscriptionRepository) {
    fun execute(): Flow<LoadableState<List<SubscriptionTariff>>> = repository.getTariffs()
}