package com.metacards.metacards.features.analytics.delegates

import co.touchlab.kermit.Logger
import com.metacards.metacards.core.utils.isDebugBuild
import com.metacards.metacards.features.analytics.AnalyticsEvent

class LoggingDelegateImpl : LoggingDelegate {

    override fun logEvent(analyticsEvent: AnalyticsEvent) {
        if (isDebugBuild) {
            Logger.withTag("Analytics").d(formatEvent(analyticsEvent))
        }
    }

    private fun formatEvent(analyticsEvent: AnalyticsEvent): String {
        return if (analyticsEvent.parameters == null) {
            "Event: ${analyticsEvent.name}"
        } else {
            "Event: ${analyticsEvent.name}, params: ${formatParams(analyticsEvent.parameters)}"
        }
    }

    private fun formatParams(parameters: Map<String, String>): String {
        return parameters.entries.joinToString(
            transform = { (name, param) -> "$name: $param" },
            prefix = "\n{",
            separator = ", ",
            postfix = "}"
        )
    }
}