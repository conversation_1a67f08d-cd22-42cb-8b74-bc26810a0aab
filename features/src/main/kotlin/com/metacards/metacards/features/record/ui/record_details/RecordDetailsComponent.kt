package com.metacards.metacards.features.record.ui.record_details

import com.metacards.metacards.core.bottom_sheet.BottomSheetControl
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.record.domain.Comment
import com.metacards.metacards.features.record.domain.Record
import com.metacards.metacards.features.record.ui.model.FeelingItem
import com.metacards.metacards.features.record.ui.record_details.comment_details.CommentDetailsComponent
import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import kotlinx.coroutines.flow.StateFlow
import ru.mobileup.kmm_form_validation.control.InputControl

interface RecordDetailsComponent {
    val recordType: RecordType
    val isDailyCard: Boolean
    val record: StateFlow<LoadableState<Record>>
    val userSubscriptionState: StateFlow<User.SubscriptionState?>
    val commentInputControl: InputControl
    val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent>
    val bottomSheetControl: BottomSheetControl<CommentDetailsComponent.Config, CommentDetailsComponent>
    val moodLevel: StateFlow<FeelingItem<Int>?>
    val energyLevel: StateFlow<FeelingItem<Int>?>
    val isDataChanged: StateFlow<Boolean>
    val isLoading: StateFlow<Boolean>
    val buttonState: StateFlow<ButtonState>
    val isRecordInFavorites: StateFlow<Boolean>
    val courseName: StateFlow<LocalizableString?>
    val tutorialMessage: StateFlow<TutorialMessage?>

    fun onCardClick(cardIndex: Int, questionIndex: Int)
    fun onFavoriteClick()
    fun onBlockAreaClick()
    fun onAuthButtonClick()
    fun onSkipButtonClick()
    fun onCompleteButtonClick()
    fun onQuestionChanged(text: LocalizableString)
    fun onMoodLevelChange(newItem: FeelingItem<Int>?)
    fun onEnergyLevelChange(newItem: FeelingItem<Int>?)
    fun onCommentClick(comment: Comment)
    fun onCardCommentClick(cardImageUrl: String, commentText: String)

    sealed interface Output {
        data object CloseComponentRequested : Output
        data object AuthRequested : Output
        data object SubscriptionSuggestingRequested : Output
        data object AuthSuggestingRequested : Output
        data class FullScreenCardsRequested(
            val cards: List<Card>,
            val initialCardPosition: Int
        ) : Output
    }

    enum class RecordType {
        InCreation, Common, Archived
    }
}