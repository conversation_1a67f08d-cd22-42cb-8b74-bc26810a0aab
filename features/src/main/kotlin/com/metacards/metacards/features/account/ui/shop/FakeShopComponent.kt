package com.metacards.metacards.features.account.ui.shop

import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.domain.entity.DeckInfoWithCards
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow

class FakeShopComponent : ShopComponent {
    override val decksForPurchaseInfoWithCards: StateFlow<List<DeckInfoWithCards>> =
        MutableStateFlow(emptyList())

    override val currentDeckPosition = MutableStateFlow(0)
    override val scrollToPageCommand: SharedFlow<Int> = MutableSharedFlow()

    override fun onGoToWebSiteClick() = Unit
    override fun onDeckSwipe(newPosition: Int) = Unit
    override fun onDeckClick(deckId: DeckId) = Unit
}