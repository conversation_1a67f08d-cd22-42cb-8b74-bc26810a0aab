package com.metacards.metacards.features.deck.domain.interactor

import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.user.domain.UserRepository

class IsCardFavoriteInteractor(
    private val userRepository: UserRepository
) {

    fun execute(cardId: CardId): Bo<PERSON>an {
        return userRepository.user.value?.favoriteCards.orEmpty().any { it.id == cardId.value }
    }
}