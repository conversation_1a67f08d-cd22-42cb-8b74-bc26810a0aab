package com.metacards.metacards.features.account.data.dto

import com.google.firebase.Timestamp
import com.metacards.metacards.core.error_handling.DeserializationException
import com.metacards.metacards.core.utils.toKotlinInstant
import com.metacards.metacards.features.account.domain.entity.Promocode
import com.metacards.metacards.features.account.domain.entity.PromocodeInfo
import kotlin.time.DurationUnit
import kotlin.time.toDuration

data class PromocodeDto(
    val id: String? = null,
    val promocode: String? = null,
    val remains: Int? = null,
    val expirationDate: Timestamp? = null,
    val subDuration: Int? = null
)

fun PromocodeDto.toDomain(): PromocodeInfo {
    val expirationDate = expirationDate ?: throw DeserializationException(this::expirationDate)
    val subDuration = subDuration ?: throw DeserializationException(this::subDuration)
    val promocode = promocode ?: throw DeserializationException(this::promocode)
    val id = id ?: throw DeserializationException(this::id)

    return PromocodeInfo(
        id = PromocodeInfo.Id(id),
        promocode = Promocode(promocode),
        remains = remains ?: throw DeserializationException(this::remains),
        expirationDate = expirationDate.toKotlinInstant(),
        subDuration = subDuration.toDuration(DurationUnit.DAYS)
    )
}