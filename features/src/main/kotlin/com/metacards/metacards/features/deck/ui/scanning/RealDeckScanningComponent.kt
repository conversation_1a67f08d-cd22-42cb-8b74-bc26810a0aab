package com.metacards.metacards.features.deck.ui.scanning

import android.Manifest
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.doOnStart
import com.metacards.metacards.core.camera.QrCode
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.error_handling.safeRun
import com.metacards.metacards.core.external_apps.ExternalAppService
import com.metacards.metacards.core.localization.data.AppLanguageService
import com.metacards.metacards.core.message.data.MessageService
import com.metacards.metacards.core.message.domain.Message
import com.metacards.metacards.core.permissions.PermissionService
import com.metacards.metacards.core.permissions.SinglePermissionResult
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.features.R
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.domain.interactor.AddDeckToPurchasedInteractor
import com.metacards.metacards.features.deck.domain.interactor.AddDeckToPurchasedResult
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.datetime.Clock
import kotlin.time.Duration.Companion.milliseconds

class RealDeckScanningComponent(
    componentContext: ComponentContext,
    private val permissionService: PermissionService,
    private val messageService: MessageService,
    private val externalAppService: ExternalAppService,
    private val errorHandler: ErrorHandler,
    private val addDeckToPurchasedInteractor: AddDeckToPurchasedInteractor,
    private val analyticsService: AnalyticsService,
    private val appLanguageService: AppLanguageService,
    private val onOutput: (DeckScanningComponent.Output) -> Unit
) : ComponentContext by componentContext, DeckScanningComponent {

    companion object {
        private const val CAMERA_PERMISSION = Manifest.permission.CAMERA
    }

    override val state = MutableStateFlow<DeckScanningComponent.State>(
        DeckScanningComponent.State.RequestingPermission
    )

    init {
        lifecycle.doOnStart {
            checkPermission()
        }
    }

    override fun onQrCodeScanned(qrCode: QrCode) {
        try {
            componentScope.safeLaunch(
                errorHandler,
                onErrorHandled = {
                    state.value = DeckScanningComponent.State.Scanning
                }
            ) {
                state.value = DeckScanningComponent.State.DeckAddingInProgress
                val deckId = DeckId(qrCode.value)
                when (val result = addDeckToPurchasedInteractor.execute(deckId)) {
                    is AddDeckToPurchasedResult.Added -> {
                        analyticsService.logEvent(
                            AnalyticsEvent.AddDeckDoneEvent(
                                deckId = deckId.value,
                                deckName = result.deck.name.toString(appLanguageService.getLanguage())
                            )
                        )
                        messageService.showMessage(
                            Message(
                                text = R.string.deck_scanning_deck_added.strResDesc(),
                                isNotification = true
                            )
                        )
                        onOutput(DeckScanningComponent.Output.DeckAdded(deckId))
                    }

                    is AddDeckToPurchasedResult.AlreadyAdded -> {
                        analyticsService.logEvent(
                            AnalyticsEvent.AddDeckFailEvent(
                                result.message
                            )
                        )
                        messageService.showMessage(
                            Message(
                                text = R.string.deck_scanning_deck_added.strResDesc(),
                                isNotification = true
                            )
                        )
                        onOutput(DeckScanningComponent.Output.DeckAdded(deckId))
                    }

                    is AddDeckToPurchasedResult.InvalidDeckId -> {
                        analyticsService.logEvent(
                            AnalyticsEvent.AddDeckFailEvent(
                                result.message
                            )
                        )
                        state.value = DeckScanningComponent.State.DeckAddingFailed(
                            R.string.deck_scanning_invalid_deck_id_title.strResDesc(),
                            R.string.deck_scanning_invalid_deck_id_message.strResDesc()
                        )
                    }
                }
            }
        } catch (e: IllegalStateException) {
            // Sometimes onQrCodeScanned can be scanned multiple times
            // In such cases, the instance keeper has already been destroyed
            // So we have to handle IllegalStateException here
        }
    }

    override fun onGoToSettingsClick() {
        safeRun(errorHandler) {
            externalAppService.openAppSettings()
        }
    }

    override fun onDeckAddingErrorClosed() {
        state.value = DeckScanningComponent.State.Scanning
    }

    private fun checkPermission() {
        state.value = DeckScanningComponent.State.RequestingPermission
        componentScope.safeLaunch(errorHandler) {
            val startTime = Clock.System.now()
            when (permissionService.requestPermission(CAMERA_PERMISSION)) {
                SinglePermissionResult.Granted -> {
                    state.value = DeckScanningComponent.State.Scanning
                }

                is SinglePermissionResult.Denied -> {
                    val deniedAutomatically = Clock.System.now() - startTime < 500.milliseconds
                    if (deniedAutomatically) {
                        state.value = DeckScanningComponent.State.PermissionDenied
                    } else {
                        onOutput(DeckScanningComponent.Output.RejectedScanning)
                    }
                }
            }
        }
    }
}