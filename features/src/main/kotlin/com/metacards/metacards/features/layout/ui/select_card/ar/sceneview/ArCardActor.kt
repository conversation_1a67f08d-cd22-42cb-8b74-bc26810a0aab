package com.metacards.metacards.features.layout.ui.select_card.ar.sceneview

import com.metacards.metacards.features.layout.domain.ar.Position2D
import io.github.sceneview.math.Position
import io.github.sceneview.math.Rotation
import io.github.sceneview.math.Scale
import io.github.sceneview.math.size
import io.github.sceneview.node.CameraNode
import kotlin.math.tan

class ArCardActor(private val cameraNode: CameraNode) {

    companion object {
        const val CARD_Z_FACTOR = 0.0027f
    }

    private fun calculateDistanceForFullHeight(
        fovDegrees: Float,
        cardHeightWorldUnits: Float
    ): Float {
        val fovRadians = Math.toRadians(fovDegrees.toDouble() / 2)
        return (cardHeightWorldUnits / 2) / tan(fovRadians).toFloat()
    }

    fun layoutCard(deck: ArDeckNode, grid: CardGrid, position2D: Position2D) {
        val card = deck.popCard()
        val newPosition = grid.addCard(card, position2D)
        card.smooth(position = newPosition, card.rotation)
    }

    fun placeDeck(deck: ArDeckNode, planeNode: ArPlaneNode) {
        val position = planeNode.position
        deck.rotation = cameraNode.rotation.copy(x = 0f, z = 0f)
        deck.smooth(position, deck.rotation)
    }

    fun takeCard(
        grid: CardGrid,
        cardNode: ArCardNode,
        cardBackgroundNode: ArSelectedCardBackgroundNode
    ): Boolean {
        val isTakingSuccessful = grid.takeCard(cardNode)

        if (isTakingSuccessful) {
            cameraNode.addChildSaveTransform(cardNode)
            val z = -calculateDistanceForFullHeight(
                cameraNode.verticalFovDegrees,
                (cardNode.model?.boundingBox?.size?.y ?: 0.1f) * CARD_Z_FACTOR
            )

            cardNode.smooth(
                position = Position(
                    y = 0f,
                    z = z
                ),
                rotation = Rotation(x = 90f, y = 180f)
            )
            cardBackgroundNode.transform(
                position = cardNode.position.copy(z = z - 0.0001f),
                scale = cardBackgroundNode.scale * 100000f,
                rotation = cardBackgroundNode.rotation
            )
        }

        return isTakingSuccessful
    }

    fun stashCard(
        grid: CardGrid,
        cardNode: ArCardNode,
        cardBackgroundNode: ArSelectedCardBackgroundNode
    ) {
        val newPosition = grid.addToStash(cardNode)
        cardNode.smooth(newPosition, Rotation(z = 180f), scale = Scale(1f))
        cardNode.isStashed = true
        cardBackgroundNode.transform(
            position = cardBackgroundNode.position.copy(z = -100f),
            scale = cardBackgroundNode.scale / 100000f,
            rotation = cardBackgroundNode.rotation
        )
    }

    fun refillLayout(deck: ArDeckNode, grid: CardGrid) {
        val emptySlots = grid.getEmptySlots()
        emptySlots.forEach {
            layoutCard(deck, grid, it)
        }
    }
}