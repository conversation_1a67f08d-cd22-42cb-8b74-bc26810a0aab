package com.metacards.metacards.features.account.ui.lessons.categories

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.features.account.domain.entity.LessonCategory
import com.metacards.metacards.features.account.domain.entity.LessonCategoryId
import com.metacards.metacards.features.account.domain.interactor.GetAllLessonCategoriesInteractor
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import kotlinx.coroutines.flow.StateFlow

class RealLessonCategoriesComponent(
    componentContext: ComponentContext,
    getAllLessonCategoriesInteractor: GetAllLessonCategoriesInteractor,
    private val analyticsService: AnalyticsService,
    private val onOutput: (LessonCategoriesComponent.Output) -> Unit
) : ComponentContext by componentContext, LessonCategoriesComponent {

    override val categoriesState: StateFlow<List<LessonCategory>> =
        getAllLessonCategoriesInteractor.execute()

    override fun onCategoryClick(lessonCategoryId: LessonCategoryId) {
        onOutput(LessonCategoriesComponent.Output.CategorySelected(lessonCategoryId))
    }

    override fun onTutorialClick() {
        analyticsService.logEvent(AnalyticsEvent.EduTutorEvent)
        onOutput(LessonCategoriesComponent.Output.TutorialRequested)
    }
}