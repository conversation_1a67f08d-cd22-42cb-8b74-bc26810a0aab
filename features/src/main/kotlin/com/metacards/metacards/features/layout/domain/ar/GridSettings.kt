package com.metacards.metacards.features.layout.domain.ar

import io.github.sceneview.math.Position

data class GridSettings(
    val sizeX: Int,
    val sizeZ: Int,
    val stepX: Float = 0.07f,
    val stepZ: Float = -0.11f,
    val offset: Position = Position(x = 0.1f, z = 0.05f)
) {
    val size: Int = sizeZ * sizeX

    fun getPosition(position2D: Position2D): Position {
        val offsetX = position2D.x * stepX
        val offsetZ = position2D.z * stepZ
        return Position(offsetX, 0f, offsetZ) + offset
    }
}