package com.metacards.metacards.features.layout.domain.ar

import io.github.sceneview.math.Position

data class StashSettings(
    val sizeZ: Int,
    val stepZ: Float = 0.11f,
    val offset: Position
) {
    private fun getPosition(sizeZ: Int): Position {
        val offsetZ = (sizeZ - 1f) * stepZ
        return Position(0f, 0f, offsetZ)
    }

    fun getLastPosition(size: Int): Position = getPosition(size)
}