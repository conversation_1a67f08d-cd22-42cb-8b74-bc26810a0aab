package com.metacards.metacards.features.record.ui.create_record.add_card

import androidx.camera.core.ImageCapture
import com.metacards.metacards.features.record.domain.camera.CardPhoto
import com.metacards.metacards.features.record.ui.create_record.add_card.image_tracking.ImageTrackingComponent
import kotlinx.coroutines.flow.StateFlow

interface AddCardComponent {
    val currentState: StateFlow<State>
    val imageTrackingComponent: ImageTrackingComponent

    fun onCameraIconClick()
    fun onImageTrackingClick()
    fun takePhoto(imageCapture: ImageCapture)
    fun onContinueClick()

    sealed interface State {
        data object Camera : State
        data object ImageTracking : State
    }

    sealed interface Output {
        data class ConfirmPhotoRequested(val cardPhoto: CardPhoto) : Output
        data object PermissionCameraRequested : Output
    }
}