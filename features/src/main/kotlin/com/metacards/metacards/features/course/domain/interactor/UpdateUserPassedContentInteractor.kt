package com.metacards.metacards.features.course.domain.interactor

import com.metacards.metacards.features.course.domain.entity.CourseCompletedContent
import com.metacards.metacards.features.course.domain.entity.CourseContentId
import com.metacards.metacards.features.course.domain.entity.CourseContentType
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CourseThemeId
import com.metacards.metacards.features.course.domain.repository.CourseRepository
import java.util.Date

class UpdateUserPassedContentInteractor(
    private val courseRepository: CourseRepository,
) {
    suspend fun execute(
        themeId: CourseThemeId,
        courseId: CourseId,
        contentId: CourseContentId,
        type: CourseContentType,
        score: Int? = null
    ) {
        val passedContent = courseRepository.getUserPassedContent(courseId) ?: return
        val allContent = courseRepository.getCourseData(themeId, courseId)

        val updatedPassedContent = passedContent.completedContent.toMutableList().apply {
            add(
                CourseCompletedContent(
                    contentId = contentId,
                    type = type,
                    completeDate = Date(),
                    score = score
                )
            )
        }

        val isCompleted = allContent
            ?.content
            ?.map { it.contentId }
            ?.toSet()
            ?.let { it ->
                updatedPassedContent
                    .map { it.contentId }
                    .toSet()
                    .containsAll(it)
            }
            ?: false

        courseRepository.updateUserPassedContent(
            courseId = courseId,
            passedContent.copy(
                completedContent = updatedPassedContent,
                isCompleted = isCompleted
            )
        )
    }
}