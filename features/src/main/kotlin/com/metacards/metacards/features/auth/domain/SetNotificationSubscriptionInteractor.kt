package com.metacards.metacards.features.auth.domain

import com.metacards.metacards.core.notification.NotificationService
import com.metacards.metacards.core.preferences.PreferencesService

class SetNotificationSubscriptionInteractor(
    private val notificationService: NotificationService,
    private val preferencesService: PreferencesService
) {

    suspend fun execute(isSubscribed: <PERSON><PERSON>an) {
        preferencesService.setUserSubscriptionToNotifications(isSubscribed)
        notificationService.toggleNotificationSubscription(isSubscribed)
    }
}