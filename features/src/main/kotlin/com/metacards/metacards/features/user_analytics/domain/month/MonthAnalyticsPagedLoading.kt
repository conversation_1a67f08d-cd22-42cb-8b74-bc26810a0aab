package com.metacards.metacards.features.user_analytics.domain.month

import com.metacards.metacards.core.paged_loading.DuplicateRemovingDataMerger
import com.metacards.metacards.core.paged_loading.Page
import com.metacards.metacards.core.paged_loading.PagedLoader
import com.metacards.metacards.core.paged_loading.PagedLoading
import com.metacards.metacards.core.utils.APPLICATION_START_DATE
import com.metacards.metacards.core.utils.firstDayOfMonth
import com.metacards.metacards.core.utils.getTodayLocalDate
import com.metacards.metacards.core.utils.toYearMonth
import com.metacards.metacards.features.record.data.RecordRepository
import com.metacards.metacards.features.record.domain.RecordFilter
import kotlinx.coroutines.CoroutineScope
import kotlinx.datetime.DatePeriod
import kotlinx.datetime.minus

class MonthAnalyticsPagedLoading(
    coroutineScope: CoroutineScope,
    recordRepository: RecordRepository
) : PagedLoading<MonthAnalyticsInfo> by createPagedLoading(coroutineScope, recordRepository) {

    companion object {
        private fun createPagedLoading(
            coroutineScope: CoroutineScope,
            recordRepository: RecordRepository
        ): PagedLoading<MonthAnalyticsInfo> {
            return PagedLoading(
                scope = coroutineScope,
                loader = MonthAnalyticsPagedLoader(recordRepository),
                dataMerger = DuplicateRemovingDataMerger(keySelector = { it.month })
            )
        }
    }
}

private class MonthAnalyticsPagedLoader(
    private val recordRepository: RecordRepository
) : PagedLoader<MonthAnalyticsInfo> {

    companion object {
        private val FILTER: RecordFilter = RecordFilter(
            onlyFavourite = false,
            archived = false
        )
    }

    override suspend fun loadFirstPage(): Page<MonthAnalyticsInfo> {
        val endDate = getTodayLocalDate()
        val startDate = endDate.firstDayOfMonth()

        val records = recordRepository.getRecordsBetweenDates(startDate, endDate, FILTER)
        val monthAnalyticsInfo = MonthAnalyticsInfo(month = startDate.toYearMonth(), records)

        return Page(
            listOf(monthAnalyticsInfo),
            hasNextPage = true,
            hasPreviousPage = false
        )
    }

    override suspend fun loadNextPage(loadedData: List<MonthAnalyticsInfo>): Page<MonthAnalyticsInfo> {
        val endDate = loadedData.last().month.firstDayOfMonth() - DatePeriod(days = 1)
        val startDate = endDate.firstDayOfMonth()

        val records = recordRepository.getRecordsBetweenDates(startDate, endDate, FILTER)
        val monthAnalyticsInfo = MonthAnalyticsInfo(month = startDate.toYearMonth(), records)

        return Page(
            listOf(monthAnalyticsInfo),
            hasNextPage = startDate > APPLICATION_START_DATE,
            hasPreviousPage = true
        )
    }
}