package com.metacards.metacards.features.course.ui.list

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.features.course.domain.entity.CourseAvailability
import com.metacards.metacards.features.course.domain.entity.CourseData
import com.metacards.metacards.features.course.domain.entity.CourseShortData
import com.metacards.metacards.features.course.domain.entity.CourseStatus
import com.metacards.metacards.features.course.domain.entity.CourseTheme
import com.metacards.metacards.features.user.domain.UserRepository

class RealCourseListComponent(
    componentContext: ComponentContext,
    private val onOutput: (CourseListComponent.Output) -> Unit,
    override val theme: CourseTheme,
    userRepository: UserRepository,
) : ComponentContext by componentContext, CourseListComponent {

    private val user = userRepository.user
    override val isPremiumUser = computed(user) {
        it?.subscriptionState is User.SubscriptionState.Ongoing
    }

    override fun onCourseClick(courseShortData: CourseShortData) {
        if (user.value == null) {
            onOutput(CourseListComponent.Output.AuthSuggestingRequested)
            return
        }
        val needPremium = courseShortData.availability == CourseAvailability.SUB
        val output = if (!needPremium || isPremiumUser.value || courseShortData.status == CourseStatus.SOON) {
            val query = CourseData.Query(themeId = theme.themeId, courseId = courseShortData.courseId)
            CourseListComponent.Output.CoursePageRequested(query)
        } else {
            CourseListComponent.Output.SubscriptionBottomSheetRequested
        }
        onOutput(output)
    }
}