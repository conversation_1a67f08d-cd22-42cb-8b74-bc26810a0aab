package com.metacards.metacards.features.account.domain.interactor.promocodes

import com.metacards.metacards.core.error_handling.BusinessLogicException
import com.metacards.metacards.core.error_handling.ServerException
import com.metacards.metacards.core.error_handling.UnauthorizedException
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.features.R
import com.metacards.metacards.features.account.domain.entity.Promocode
import com.metacards.metacards.features.account.domain.entity.PromocodeInfo
import com.metacards.metacards.features.account.domain.repository.PromocodesRepository
import com.metacards.metacards.features.user.domain.UserRepository
import dev.icerock.moko.resources.StringResource
import dev.icerock.moko.resources.desc.desc

class ActivatePromocodeInteractor(
    private val userRepository: UserRepository,
    private val promocodesRepository: PromocodesRepository
) {
    suspend fun execute(promocode: Promocode) {
        checkUser()

        val promocodeInfo = getPromocodeInfo(promocode)
        if (promocodeInfo.remains == 0) {
            throw BusinessLogicException(null, StringResource(R.string.promocode_invalid).desc())
        } else {
            val result = promocodesRepository.activatePromocode(promocode)
            if (result.isError) {
                val errorData: Any? = result.data
                throw ServerException(null, errorData?.toString())
            }
        }
    }

    private suspend fun getPromocodeInfo(promocode: Promocode): PromocodeInfo {
        val promocodesInfo = promocodesRepository.getPromocodes()

        return promocodesInfo.find { it.promocode.value.equals(promocode.value, ignoreCase = true) }
            ?: throw BusinessLogicException(null, StringResource(R.string.promocode_invalid).desc())
    }

    private fun checkUser() {
        val user = userRepository.user.value
            ?: throw UnauthorizedException(NullPointerException("User is null"))

        if (user.subscriptionState is User.SubscriptionState.Ongoing) {
            throw BusinessLogicException(
                null,
                StringResource(R.string.promocode_already_has_subscription).desc()
            )
        }
    }
}