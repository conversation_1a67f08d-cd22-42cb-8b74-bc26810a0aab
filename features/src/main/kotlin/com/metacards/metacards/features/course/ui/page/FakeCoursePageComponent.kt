package com.metacards.metacards.features.course.ui.page

import com.metacards.metacards.features.course.domain.entity.CourseData
import com.metacards.metacards.features.course.domain.entity.CourseDataContent
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.desc
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakeCoursePageComponent : CoursePageComponent {
    override val courseQuery: CourseData.Query = CourseData.Query.mock
    override val courseData = MutableStateFlow<CourseData?>(CourseData.mock)
    override val bottomButtonText: StateFlow<StringDesc> = MutableStateFlow("Начать".desc())
    override val shouldBlur: StateFlow<Boolean> = MutableStateFlow(false)

    override fun onStartClick() = Unit
    override fun onContentClick(courseDataContent: CourseDataContent) = Unit
    override fun onShareClick() = Unit
    override fun onTestResultClick(courseDataContent: CourseDataContent) = Unit
    override fun onBlockContentClick() = Unit
    override fun onToOtherCoursesClick() = Unit
}