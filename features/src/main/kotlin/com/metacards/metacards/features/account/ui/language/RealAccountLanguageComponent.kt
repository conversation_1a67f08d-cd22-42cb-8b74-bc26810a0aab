package com.metacards.metacards.features.account.ui.language

import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.doOnCreate
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.localization.data.AppLanguageService
import com.metacards.metacards.core.localization.domain.AppLanguage
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class RealAccountLanguageComponent(
    componentContext: ComponentContext,
    private val appLanguageService: AppLanguageService,
    errorHandler: <PERSON>rrorHand<PERSON>,
    private val onOutput: (AccountLanguageComponent.Output) -> Unit
) : ComponentContext by componentContext, AccountLanguageComponent {

    private val debounce = Debounce()

    private val initLanguage = MutableStateFlow(AppLanguage.RUS)

    init {
        lifecycle.doOnCreate {
            componentScope.safeLaunch(errorHandler) {
                initLanguage.value = appLanguageService.getLanguage()
            }
        }
    }

    private val selectedLanguageFlow: MutableStateFlow<AppLanguage?> = MutableStateFlow(null)

    override val languageFlow: StateFlow<AppLanguage> =
        computed(initLanguage, selectedLanguageFlow) { init, selected ->
            selected ?: init
        }

    override fun onLanguageClick(language: AppLanguage) {
        selectedLanguageFlow.value = language
    }

    override fun onSaveClick() {
        DebounceClick(debounce, "onSaveClick") {
            appLanguageService.setLanguage(languageFlow.value, false)
            onOutput(AccountLanguageComponent.Output.DismissRequested)
        }
    }

    override fun onDismiss() {
        onOutput(AccountLanguageComponent.Output.DismissRequested)
    }
}