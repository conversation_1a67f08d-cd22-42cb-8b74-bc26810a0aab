package com.metacards.metacards.features.user.data.subscription

import com.google.firebase.firestore.toObject
import com.metacards.metacards.core.network.firestore.FirestoreService
import com.metacards.metacards.core.network.firestore.getFlowWithLoadable
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.mapLoadable
import com.metacards.metacards.features.user.data.subscription.dto.SubscriptionTariffDto
import kotlinx.coroutines.flow.Flow

class SubscriptionsDataSource(private val firestoreService: FirestoreService) {

    fun getSubscriptionTariffsFlow(): Flow<LoadableState<List<SubscriptionTariffDto>>> {
        val flow = firestoreService.db.collection(SUBSCRIPTIONS_COLLECTION_PATH)
            .getFlowWithLoadable(firestoreService)

        return flow.mapLoadable { querySnapshot ->
            querySnapshot?.documents?.mapNotNull {
                it.toObject<SubscriptionTariffDto>()?.copy(id = it.id)
            }
        }
    }

    companion object {
        private const val SUBSCRIPTIONS_COLLECTION_PATH = "subscriptions"
    }
}
