package com.metacards.metacards.features.layout.ui.select_card.ar.sceneview

import co.touchlab.kermit.Logger
import com.metacards.metacards.features.layout.domain.ar.Grid
import com.metacards.metacards.features.layout.domain.ar.GridSettings
import com.metacards.metacards.features.layout.domain.ar.Position2D
import com.metacards.metacards.features.layout.domain.ar.Stash
import com.metacards.metacards.features.layout.domain.ar.StashSettings
import io.github.sceneview.Filament
import io.github.sceneview.ar.node.ArModelNode
import io.github.sceneview.ar.node.ArNode
import io.github.sceneview.math.Position
import io.github.sceneview.math.Transform

data class CardGrid(
    val gridSettings: GridSettings,
    val stashSettings: StashSettings,
) {
    private val logger = Logger.withTag("CardGrid")
    private val grid: Grid<ArCardNode> = Grid(gridSettings.sizeX, gridSettings.sizeZ)
    private val stash: Stash<ArCardNode> = Stash(stashSettings.sizeZ)
    private var layoutNode: ArModelNode? = null
    private var stashNode: ArModelNode? = null

    fun setupGrid(transform: Transform): List<ArNode> {
        layoutNode = ArModelNode(Filament.engine).apply {
            isVisible = true
            followHitPosition = false
            this.transform = transform
            anchor()
        }

        stashNode = ArModelNode(Filament.engine).apply {
            isVisible = true
            followHitPosition = false
            this.transform = transform
            position += quaternion * stashSettings.offset
            anchor()
        }

        return listOf(layoutNode!!, stashNode!!)
    }

    fun addCard(cardNode: ArCardNode, position2D: Position2D): Position {
        val layoutNode = layoutNode ?: throw IllegalStateException("layout node is null")
        grid.put(cardNode, position2D)
        layoutNode.addChildSaveTransform(cardNode)
        layoutNode.logPosition(logger)
        return gridSettings.getPosition(position2D)
    }

    fun takeCard(cardNode: ArCardNode): Boolean {
        val isRemovingSuccessful = grid.remove(cardNode)

        if (isRemovingSuccessful) {
            val layoutNode = layoutNode ?: throw IllegalStateException("layout node is null")
            layoutNode.removeChildSaveTransform(cardNode)
        }

        return isRemovingSuccessful
    }

    fun addToStash(cardNode: ArCardNode): Position {
        val stashNode = stashNode ?: throw IllegalStateException("layout node is null")
        stash.add(cardNode)
        stashNode.addChildSaveTransform(cardNode)
        return stashSettings.getLastPosition(stash.size)
    }

    fun getEmptySlots() = grid.getEmptySlots()

    fun destroy() {
        runCatching { layoutNode?.destroy() }
        runCatching { stashNode?.destroy() }
        stashNode = null
        layoutNode = null
    }
}