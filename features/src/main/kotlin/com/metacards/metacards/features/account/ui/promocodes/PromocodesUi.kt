package com.metacards.metacards.features.account.ui.promocodes

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.message.ui.noOverlapByMessage
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.button.MetaButton
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.core.widget.text_field.MetaTextField
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun PromocodesUi(
    modifier: Modifier = Modifier,
    component: PromocodeComponent
) {
    val buttonState by component.buttonState.collectAsState()

    BoxWithFade(
        modifier.background(CustomTheme.colors.background.primary),
        listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .navigationBarsPadding()
                .imePadding()
        ) {
            TopNavigationBar(
                modifier = Modifier.fillMaxWidth(),
                title = R.string.promocode_title.strResDesc(),
                leadingIcon = { BackNavigationItem() }
            )

            Text(
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 24.dp),
                text = R.string.promocode_input_title.strResDesc().localizedByLocal(),
                style = CustomTheme.typography.caption.large,
                color = CustomTheme.colors.text.primary
            )

            MetaTextField(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                inputControl = component.promocodeInputControl,
                placeholder = R.string.promocode_input_placeholder.strResDesc().localizedByLocal(),
            )

            Spacer(modifier = Modifier.weight(1f))

            MetaButton(
                modifier = Modifier
                    .noOverlapByMessage()
                    .fillMaxWidth()
                    .padding(bottom = 24.dp, top = 16.dp)
                    .padding(horizontal = 16.dp),
                text = R.string.promocode_button.strResDesc().localizedByLocal(),
                state = buttonState,
                onClick = component::onActivateButtonClick
            )
        }
    }
}

@Preview
@Composable
fun PromocodesUiPreview() {
    AppTheme {
        PromocodesUi(component = FakePromocodeComponent())
    }
}