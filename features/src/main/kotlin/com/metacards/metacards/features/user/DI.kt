package com.metacards.metacards.features.user

import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.childContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.user.domain.UserProvider
import com.metacards.metacards.core.utils.KoinConsts
import com.metacards.metacards.features.user.data.UserDataSource
import com.metacards.metacards.features.user.data.UserRepositoryImpl
import com.metacards.metacards.features.user.data.subscription.SubscriptionRepositoryImpl
import com.metacards.metacards.features.user.data.subscription.SubscriptionsDataSource
import com.metacards.metacards.features.user.domain.GetUserSubscriptionStateInteractor
import com.metacards.metacards.features.user.domain.ToggleFavoriteCardInteractor
import com.metacards.metacards.features.user.domain.UpdateUserInteractor
import com.metacards.metacards.features.user.domain.UserProviderImpl
import com.metacards.metacards.features.user.domain.UserRepository
import com.metacards.metacards.features.user.domain.subscription.interactor.GetTariffsInteractor
import com.metacards.metacards.features.user.domain.subscription.repository.SubscriptionRepository
import com.metacards.metacards.features.user.ui.subscription.BaseSubscriptionComponent
import com.metacards.metacards.features.user.ui.subscription.RealBaseSubscriptionComponent
import com.metacards.metacards.features.user.ui.subscription.RealSubscriptionComponent
import com.metacards.metacards.features.user.ui.subscription.SubscriptionComponent
import org.koin.core.component.get
import org.koin.core.qualifier.named
import org.koin.dsl.bind
import org.koin.dsl.module

val userModule = module {
    single { UserDataSource(get(), get(), get()) }
    single {
        UserRepositoryImpl(
            get(),
            get(),
            get(named(KoinConsts.DefaultIoDispatcherName)),
            get(),
            get()
        )
    } bind UserRepository::class
    single { SubscriptionsDataSource(get()) }
    single<SubscriptionRepository> { SubscriptionRepositoryImpl(get(), get()) }
    factory { GetTariffsInteractor(get()) }
    factory { ToggleFavoriteCardInteractor(get()) }
    factory { UpdateUserInteractor(get()) }
    factory { GetUserSubscriptionStateInteractor(get()) }
    factory<UserProvider> { UserProviderImpl(get()) }
}

fun ComponentFactory.createSubscriptionComponent(
    componentContext: ComponentContext,
    onOutput: (SubscriptionComponent.Output) -> Unit
): SubscriptionComponent {
    return RealSubscriptionComponent(
        componentContext,
        get(),
        createBaseSubscriptionComponent(componentContext.childContext("BaseSub")),
        get(),
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createBaseSubscriptionComponent(
    componentContext: ComponentContext,
): BaseSubscriptionComponent {
    return RealBaseSubscriptionComponent(
        componentContext,
        get(),
        get(),
    )
}