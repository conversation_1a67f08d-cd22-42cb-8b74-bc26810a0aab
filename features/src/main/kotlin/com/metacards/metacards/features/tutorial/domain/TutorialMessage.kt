package com.metacards.metacards.features.tutorial.domain

import androidx.annotation.DrawableRes
import dev.icerock.moko.resources.desc.StringDesc

data class TutorialMessage(
    val content: MessageContent? = null,
    val alignment: MessageAlignment = MessageAlignment.CENTER,
    val arrow: TutorialMessageArrow = TutorialMessageArrow.NONE,
    val isOverlapEnabled: Boolean = false,
    val isDisableClicks: Boolean = false
)

data class MessageContent(
    val text: StringDesc,
    @DrawableRes val iconRes: Int? = null,
    val actionTitle: StringDesc? = null,
    val action: (() -> Unit)? = null,
)

enum class MessageAlignment {
    CENTER, TOP, BOTTOM, BOTTOM_START
}

enum class TutorialMessageArrow {
    TOP, TOP_LEFT, BOTTOM, BOTTOM_RIGHT, NONE
}