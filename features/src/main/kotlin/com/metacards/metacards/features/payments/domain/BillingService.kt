package com.metacards.metacards.features.payments.domain

import kotlinx.coroutines.flow.MutableStateFlow

interface BillingService {
    val currentPurchaseState: MutableStateFlow<PurchaseState>
    fun init()

    suspend fun processPurchases(purchaseType: PurchaseType)

    suspend fun fetchSubscriptions(storeId: String, promotionalOfferId: String?): List<SubscriptionPlan>

    suspend fun fetchDeck(storeId: String): DeckInApp
}