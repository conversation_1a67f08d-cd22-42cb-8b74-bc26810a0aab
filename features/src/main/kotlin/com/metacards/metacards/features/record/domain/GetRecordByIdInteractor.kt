package com.metacards.metacards.features.record.domain

import com.metacards.metacards.core.utils.ErrorMessage
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.features.record.data.RecordRepository
import com.metacards.metacards.features.user.domain.UserRepository
import dev.icerock.moko.resources.desc.desc
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.flatMapLatest

class GetRecordByIdInteractor(
    private val recordRepository: RecordRepository,
    private val userRepository: UserRepository
) {

    @OptIn(ExperimentalCoroutinesApi::class)
    fun execute(recordId: RecordId): Flow<LoadableState<Record>> {
        val userFlow = userRepository.user

        return userFlow.flatMapLatest { user ->
            recordRepository.getRecordById(
                user?.userId ?: return@flatMapLatest MutableStateFlow(
                    LoadableState(error = ErrorMessage("User ID was not found.".desc()))
                ),
                recordId
            )
        }
    }
}