package com.metacards.metacards.features.user.ui.subscription

import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.FakeDialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.FakeDefaultDialogComponent
import com.metacards.metacards.features.payments.ui.FakePaymentComponent
import com.metacards.metacards.features.payments.ui.PaymentComponent
import kotlinx.coroutines.flow.MutableStateFlow

class FakeSubscriptionComponent : SubscriptionComponent {
    override val baseSubscriptionComponent: BaseSubscriptionComponent =
        FakeBaseSubscriptionComponent()
    override val subscriptionScreenState = MutableStateFlow(SubscriptionScreenState.START)
    override val paymentComponent: PaymentComponent = FakePaymentComponent()
    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        FakeDialogControl(FakeDefaultDialogComponent())

    override fun onCompleteClick() = Unit
    override fun onGoToMainClick() = Unit
    override fun onSuccessBackClick() = Unit
    override fun onCancelClick() = Unit
}