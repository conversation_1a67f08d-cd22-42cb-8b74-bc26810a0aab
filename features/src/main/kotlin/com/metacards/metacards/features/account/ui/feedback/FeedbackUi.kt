package com.metacards.metacards.features.account.ui.feedback

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.core.widget.text_field.MetaTextField
import com.metacards.metacards.features.R
import com.metacards.metacards.features.account.domain.entity.QuestionSubject
import com.metacards.metacards.features.layout.ui.question_for_layout.SymbolCounter
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.launch

@Composable
fun FeedbackUi(
    component: FeedbackComponent,
    modifier: Modifier = Modifier,
) {
    val email by component.email.collectAsState()
    val emailError by component.emailError.collectAsState()
    val question by component.question.collectAsState()
    val questionError by component.questionError.collectAsState()
    val selectedQuestionSubject by component.selectedQuestionSubject.collectAsState()
    val questionSubjects =
        stringArrayResource(id = R.array.feedback_question_topics).map(::QuestionSubject)
    val loading by component.loading.collectAsState()

    val focusManager = LocalFocusManager.current
    val scroll = rememberScrollState()
    val lazyRowState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()
    val index = remember(selectedQuestionSubject, questionSubjects) {
        questionSubjects.indexOf(selectedQuestionSubject)
    }
    val buttonState = remember(email, question, loading) {
        when {
            loading -> ButtonState.Loading
            email.isNotEmpty() && question.isNotEmpty() && !questionError -> ButtonState.Enabled
            else -> ButtonState.Disabled
        }
    }

    LaunchedEffect(key1 = index) {
        if (index != -1) {
            coroutineScope.launch { lazyRowState.animateScrollToItem(index) }
        }
    }

    LaunchedEffect(key1 = question) {
        scroll.animateScrollTo(Int.MAX_VALUE)
    }

    BoxWithFade(
        modifier = modifier.imePadding(),
        listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        Column {
            TopNavigationBar(
                title = R.string.feedback_title.strResDesc(),
                leadingIcon = { BackNavigationItem() }
            )

            Column(
                modifier = Modifier
                    .weight(1f)
                    .verticalScroll(scroll)
            ) {
                Text(
                    text = R.string.feedback_question_subject.strResDesc().localizedByLocal(),
                    color = CustomTheme.colors.text.secondary,
                    style = CustomTheme.typography.caption.medium,
                    modifier = Modifier.padding(top = 16.dp, start = 32.dp, end = 32.dp)
                )

                LazyRow(
                    modifier = Modifier.padding(top = 8.dp, bottom = 40.dp),
                    state = lazyRowState
                ) {
                    item { Spacer(modifier = Modifier.size(16.dp)) }
                    items(questionSubjects) { questionSubject ->
                        QuestionSubjectCard(
                            questionSubject = questionSubject,
                            onClick = component::selectQuestionSubject,
                            selected = questionSubject == selectedQuestionSubject
                        )
                        Spacer(modifier = Modifier.size(4.dp))
                    }
                    item { Spacer(modifier = Modifier.size(16.dp)) }
                }

                Text(
                    text = R.string.feedback_email.strResDesc().localizedByLocal(),
                    color = CustomTheme.colors.text.secondary,
                    style = CustomTheme.typography.caption.medium,
                    modifier = Modifier.padding(top = 16.dp, start = 32.dp, end = 32.dp)
                )

                MetaTextField(
                    modifier = Modifier.padding(top = 8.dp, start = 16.dp, end = 16.dp),
                    value = email,
                    onValueChanged = component::changeEmail,
                    error = if (emailError) {
                        R.string.feedback_email_error.strResDesc()
                    } else {
                        null
                    },
                    placeholder = R.string.feedback_email_placeholder.strResDesc()
                        .localizedByLocal(),
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next),
                    keyboardActions = KeyboardActions(
                        onNext = { focusManager.moveFocus(FocusDirection.Down) }
                    )
                )

                Spacer(modifier = Modifier.size(20.dp))
                Text(
                    text = R.string.feedback_question.strResDesc().localizedByLocal(),
                    color = CustomTheme.colors.text.secondary,
                    style = CustomTheme.typography.caption.medium,
                    modifier = Modifier.padding(top = 16.dp, start = 32.dp, end = 32.dp)
                )
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp, start = 16.dp, end = 16.dp)
                ) {
                    MetaTextField(
                        value = question,
                        onValueChanged = component::changeQuestion,
                        singleLine = false,
                        placeholder = R.string.feedback_question_placeholder.strResDesc()
                            .localizedByLocal(),
                        minLines = 5,
                        shouldBeCarriedDown = true,
                        innerFooterContent = {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth(),
                                contentAlignment = Alignment.CenterEnd
                            ) {
                                SymbolCounter(
                                    currentCount = question.length,
                                    maxCount = FeedbackComponent.MAX_QUESTION_CHARS,
                                    shouldHighlightOverCount = true,
                                )
                            }
                        }
                    )
                }
            }

            MetaAccentButton(
                text = R.string.feedback_button_title.strResDesc().localizedByLocal(),
                onClick = component::onSendClick,
                modifier = Modifier
                    .fillMaxWidth()
                    .navigationBarsPadding()
                    .padding(horizontal = 16.dp)
                    .padding(bottom = 24.dp),
                state = buttonState
            )
        }
    }
}

@Composable
private fun QuestionSubjectCard(
    questionSubject: QuestionSubject,
    onClick: (QuestionSubject) -> Unit,
    selected: Boolean,
) {
    val cheapColor = if (selected) {
        CustomTheme.colors.icons.primary
    } else {
        CustomTheme.colors.button.secondary
    }
    val textColor = if (selected) {
        CustomTheme.colors.text.inverted
    } else {
        CustomTheme.colors.text.primary
    }

    Box(
        modifier = Modifier
            .clickable { onClick(questionSubject) }
            .background(color = cheapColor, shape = RoundedCornerShape(30.dp))
            .clip(shape = RoundedCornerShape(30.dp))
            .padding(vertical = 8.dp, horizontal = 16.dp)
    ) {
        Text(
            text = questionSubject.value,
            color = textColor,
            style = CustomTheme.typography.caption.medium,
        )
    }
}

@Preview
@Composable
private fun FeedbackUiPreview() {
    AppTheme {
        FeedbackUi(component = FakeFeedbackComponent())
    }
}
