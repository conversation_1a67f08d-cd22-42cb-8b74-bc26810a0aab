package com.metacards.metacards.features.favorite_cards.ui.details

import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.doOnStart
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.error_handling.safeRun
import com.metacards.metacards.core.message.data.MessageService
import com.metacards.metacards.core.message.domain.Message
import com.metacards.metacards.core.sharing.SharingManager
import com.metacards.metacards.core.utils.Activable
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.stateIn
import com.metacards.metacards.core.utils.withProgress
import com.metacards.metacards.features.R
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.deck.domain.interactor.IsCardFavoriteInteractor
import com.metacards.metacards.features.favorite_cards.domain.FavoriteCardInfo
import com.metacards.metacards.features.favorite_cards.domain.GetFavoriteCardInfoInteractor
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.user.domain.ToggleFavoriteCardInteractor
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow

class RealFavoriteCardDetailsComponent(
    componentContext: ComponentContext,
    cardId: CardId,
    private val errorHandler: ErrorHandler,
    private val messageService: MessageService,
    getFavoriteCardInfoInteractor: GetFavoriteCardInfoInteractor,
    private val toggleFavoriteCardInteractor: ToggleFavoriteCardInteractor,
    private val isCardFavoriteInteractor: IsCardFavoriteInteractor,
    private val analyticsService: AnalyticsService,
    private val sharingManager: SharingManager,
    private val onOutput: (FavoriteCardDetailsComponent.Output) -> Unit
) : ComponentContext by componentContext, FavoriteCardDetailsComponent {

    private val removingFromFavoriteInProgress = MutableStateFlow(false)

    // Во время удаления карты из избранного приостанавливаем обновления cardInfoState, иначе промелькнет состояние ошибки, что карта не найдена.
    private val cardInfoCollectingActive = computed(removingFromFavoriteInProgress) { !it }

    override val cardInfoState: StateFlow<LoadableState<FavoriteCardInfo>> =
        getFavoriteCardInfoInteractor.execute(cardId).stateIn(
            this,
            LoadableState(),
            SharingStarted.Activable(cardInfoCollectingActive)
        )
    override val isShareLoading = MutableStateFlow(false)

    private val debounce = Debounce()

    init {
        lifecycle.doOnStart {
            safeRun(errorHandler) {
                if (!isCardFavoriteInteractor.execute(cardId)) {
                    onCardRemovedFromFavorite()
                }
            }
        }
    }

    override fun onFavoriteClick() {
        val card = cardInfoState.value.data?.card ?: return
        DebounceClick(debounce, "removeFromFavorite", errorHandler) {
            withProgress(removingFromFavoriteInProgress) {
                toggleFavoriteCardInteractor.execute(isFavorite = false, card)
                onCardRemovedFromFavorite()
            }
        }
    }

    override fun onToggleShare() {
        val card = cardInfoState.value.data?.card ?: return
        componentScope.safeLaunch(
            errorHandler = errorHandler,
            onErrorHandled = {
                isShareLoading.value = false
            }
        ) {
            isShareLoading.value = true
            sharingManager.shareCard(
                cardUrl = card.imageUrl,
                isDailyCard = false
            )
            isShareLoading.value = false
        }
    }

    override fun onRecordClick(recordId: RecordId) {
        analyticsService.logEvent(AnalyticsEvent.FavCardsCardRecordEvent)
        onOutput(FavoriteCardDetailsComponent.Output.RecordDetailsRequested(recordId))
    }

    private fun onCardRemovedFromFavorite() {
        analyticsService.logEvent(AnalyticsEvent.FavCardsCardDeleteEvent)
        messageService.showMessage(
            Message(
                text = R.string.favorite_card_removed_from_favorite.strResDesc(),
                isNotification = true
            )
        )
        onOutput(FavoriteCardDetailsComponent.Output.RemovedFromFavorite)
    }
}