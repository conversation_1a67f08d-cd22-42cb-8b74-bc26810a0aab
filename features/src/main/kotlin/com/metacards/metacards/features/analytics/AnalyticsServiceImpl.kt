package com.metacards.metacards.features.analytics

import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.user.domain.UserProvider
import com.metacards.metacards.features.analytics.delegates.FirebaseAnalyticsDelegate
import com.metacards.metacards.features.analytics.delegates.LoggingDelegate
import kotlinx.coroutines.flow.StateFlow

class AnalyticsServiceImpl(
    private val firebaseAnalyticsDelegate: FirebaseAnalyticsDelegate,
    private val loggingDelegate: LoggingDelegate,
    userProvider: UserProvider
) : AnalyticsService {

    private val userFlow: StateFlow<User?> = userProvider.getUser()

    override fun logEvent(event: AnalyticsEvent) {
        val user = userFlow.value
        firebaseAnalyticsDelegate.sendEvent(event, user)
        loggingDelegate.logEvent(event)
    }
}