package com.metacards.metacards.features.course.ui.test

import android.os.CountDownTimer
import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.createDefaultDialogComponent
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.model.DialogData
import com.metacards.metacards.core.dialog.dialogControl
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.features.R
import com.metacards.metacards.features.course.domain.entity.CoursePassedTest
import com.metacards.metacards.features.course.domain.entity.CoursePassedTestResult
import com.metacards.metacards.features.course.domain.entity.CourseQuestion
import com.metacards.metacards.features.course.domain.entity.CourseTest
import com.metacards.metacards.features.course.domain.interactor.GetCourseTestDetailsInteractor
import com.metacards.metacards.features.course.domain.repository.CourseRepository
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import java.util.Date

class RealCourseTestComponent(
    componentContext: ComponentContext,
    componentFactory: ComponentFactory,
    private val courseTestQuery: CourseTest.Query,
    private val order: Int,
    override val courseName: LocalizableString?,
    private val onOutput: (CourseTestComponent.Output) -> Unit,
    private val getCourseTestDetailsInteractor: GetCourseTestDetailsInteractor,
    private val courseRepository: CourseRepository,
    private val errorHandler: ErrorHandler,
) : ComponentContext by componentContext, CourseTestComponent {

    private companion object {
        const val USER_INTERACTION_TIMEOUT = 60_000L
        const val TIMER_RESTART_DELAY = 500L
    }

    override val test: MutableStateFlow<CourseTest?> = MutableStateFlow(null)

    override val shouldShowTutorial = MutableStateFlow(false)

    private val userInteractionTimer =
        object : CountDownTimer(USER_INTERACTION_TIMEOUT, USER_INTERACTION_TIMEOUT) {
            override fun onTick(millisUntilFinished: Long) = Unit
            override fun onFinish() { shouldShowTutorial.value = true }
        }

    override val closeDialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        dialogControl(dialogComponentFactory = { config, componentContext, dialogControl ->
            componentFactory.createDefaultDialogComponent(
                componentContext,
                config.data,
                dialogControl::dismiss
            )
        })

    private val scoreCounter = MutableStateFlow<Map<Int, Int>>(emptyMap())

    private val closeDialogConfig by lazy {
        DefaultDialogComponent.Config(
            DialogData(
                title = StringDesc.Resource(R.string.course_close_dialog_title),
                message = StringDesc.Resource(R.string.course_test_close_dialog_text),
                buttons = listOf(
                    DialogData.Button(
                        title = StringDesc.Resource(R.string.course_lesson_close_dialog_cancel_button),
                        action = closeDialogControl::dismiss
                    ),
                    DialogData.Button(
                        title = StringDesc.Resource(R.string.course_lesson_close_dialog_close_button),
                        action = {
                            closeDialogControl.dismiss()
                            onOutput(CourseTestComponent.Output.CloseRequested)
                        }
                    )
                )
            )
        )
    }

    private var lastTimeWhenRestartTimerCalled = 0L
    init {
        componentScope.safeLaunch(errorHandler) {
            shouldShowTutorial.value = !courseRepository.getIsTestTutorialShown()
            test.value = getCourseTestDetailsInteractor.execute(courseTestQuery)
        }

        userInteractionTimer.start()

        scoreCounter.onEach { onUserInteracted() }.launchIn(componentScope)
    }

    override fun onQuestionAnswered(dragAnchor: CourseTestDragAnchors, question: CourseQuestion) {
        val score = when (dragAnchor) {
            CourseTestDragAnchors.Left -> question.noScore
            CourseTestDragAnchors.Center -> 0
            CourseTestDragAnchors.Right -> question.yesScore
        }
        scoreCounter.update { it.toMutableMap().apply { put(question.hashCode(), score) } }
    }

    override fun onRevertClick(previousQuestion: CourseQuestion) {
        scoreCounter.update { it.toMutableMap().apply { remove(previousQuestion.hashCode()) } }
    }

    override fun onLastQuestionAnswered() {
        val testCopy = test.value ?: return
        val totalScore = scoreCounter.value.values.sum()
        val result = testCopy.results.find { r ->
            val range = r.minScore..r.maxScore
            totalScore in range
        }
        if (result == null) {
            onOutput(CourseTestComponent.Output.CourseResultNotFound)
        } else {
            val dataToSave = CoursePassedTest(
                id = null,
                name = testCopy.name,
                testId = testCopy.testId,
                themeId = courseTestQuery.themeId,
                courseId = courseTestQuery.courseId,
                score = totalScore,
                resultDate = Date(),
                results = CoursePassedTestResult(
                    title = result.title,
                    subtitle = result.subtitle,
                    description = result.description,
                    coverUrl = result.cover,
                    videoUrl = result.video,
                    linkedLayouts = result.linkedLayouts,
                    linkedVideos = result.linkedVideos
                )
            )

            onOutput(
                CourseTestComponent.Output.CourseResultRequested(
                    result = result,
                    dataToSave = dataToSave,
                    order = order
                )
            )
        }
    }

    override fun onCloseTutorialClick() {
        componentScope.safeLaunch(errorHandler) {
            onUserInteracted()
            courseRepository.setTestTutorialShown()
        }
    }

    override fun onCloseClick() {
        closeDialogControl.show(closeDialogConfig)
    }

    override fun onUserInteracted() {
        if ((System.currentTimeMillis() - lastTimeWhenRestartTimerCalled) < TIMER_RESTART_DELAY) return
        restartTimer()
    }

    private fun restartTimer() {
        shouldShowTutorial.value = false
        userInteractionTimer.cancel()
        userInteractionTimer.start()
        lastTimeWhenRestartTimerCalled = System.currentTimeMillis()
    }
}