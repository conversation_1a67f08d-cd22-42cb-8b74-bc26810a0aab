package com.metacards.metacards.features.deck.ui.card_preview

import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardWithComment
import kotlinx.coroutines.flow.StateFlow

interface CardsPreviewComponent {
    val cards: List<Card>
    val currentCardIndex: StateFlow<Int>
    val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent>
    val isShareLoading: StateFlow<Boolean>
    val isToggleShareEnable: StateFlow<Boolean>
    val shouldShowGif: Boolean

    fun onCloseButtonClick()
    fun onPageSelected(index: Int)
    fun onToggleShare()

    sealed interface Output {
        data class CloseRequested(val currentCardPosition: Int) : Output
        data object SubscriptionSuggestingRequested : Output
        data object AuthSuggestingRequested : Output
        data class CreateRecordRequested(
            val cardsWithComment: List<CardWithComment>,
            val isDailyCard: <PERSON>olean,
        ) : Output
    }
}
