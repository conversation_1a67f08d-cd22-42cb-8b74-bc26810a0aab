package com.metacards.metacards.features.deck.domain.entity

import android.os.Parcelable
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.localization.ui.LocalizableStringWithFallback
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.parcelize.Parcelize

@Parcelize
data class CardHint(
    val text: LocalizableString
) : Parcelable {
    companion object {
        val MOCK = CardHint(text = LocalizableString.createNonLocalizable("Подсказка"))
    }
}

fun CardHint.withExpanded(isExpanded: Boolean = true) = CardHintWithExpanded(
    hint = this,
    isExpanded = isExpanded
)

data class CardHintWithExpanded(
    val hint: CardHint,
    val isExpanded: Boolean
)

fun Card?.getCartHintTitle(isDailyCard: Boolean): StringDesc {
    val defaultValue = if (isDailyCard) {
        R.string.record_card_list_daily_card_hint_label.strResDesc()
    } else {
        R.string.record_card_list_card_hint_label.strResDesc()
    }

    if (this?.name == null) return defaultValue

    return LocalizableStringWithFallback(name, defaultValue)
}

fun Card?.getCartHintText(defaultHint: CardHint): StringDesc {
    val defaultValue = defaultHint.text
    if (this?.help == null) return defaultValue
    return LocalizableStringWithFallback(help, defaultValue)
}