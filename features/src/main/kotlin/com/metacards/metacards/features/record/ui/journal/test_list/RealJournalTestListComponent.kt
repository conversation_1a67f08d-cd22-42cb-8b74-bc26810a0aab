package com.metacards.metacards.features.record.ui.journal.test_list

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.features.course.domain.entity.CoursePassedTest
import com.metacards.metacards.features.record.domain.GetUserPassedTestsInteractor
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn

class RealJournalTestListComponent(
    componentContext: ComponentContext,
    getUserPassedTestsInteractor: GetUserPassedTestsInteractor,
    private val onOutput: (JournalTestListComponent.Output) -> Unit
) : ComponentContext by componentContext, JournalTestListComponent {
    override val tests: StateFlow<List<CoursePassedTest>> =
        getUserPassedTestsInteractor.execute()
            .stateIn(componentScope, SharingStarted.Eagerly, emptyList())

    override fun onTestClick(coursePassedTest: CoursePassedTest) {
        onOutput(JournalTestListComponent.Output.TestDetailsRequested(coursePassedTest))
    }
}