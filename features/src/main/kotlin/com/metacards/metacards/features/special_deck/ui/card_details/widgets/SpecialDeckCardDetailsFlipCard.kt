package com.metacards.metacards.features.special_deck.ui.card_details.widgets

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.ui.MetaCardDefaults
import dev.icerock.moko.resources.desc.strResDesc

private const val FLIP_CARD_MAX_ROTATION = 180f
private const val FLIP_CARD_MIN_ROTATION = 0f
private const val CARD_CHANGE_DEGREE = 90f
private const val FLIP_ANIMATION_DURATION = 1000

@Composable
fun SpecialDeckCardDetailsFlipCard(
    mirrorImage: ImageBitmap?,
    quote: String,
    isFlipCardRotated: Boolean,
    onClick: () -> Unit
) {

    val rotationFront by animateFloatAsState(
        targetValue = if (isFlipCardRotated) FLIP_CARD_MAX_ROTATION else FLIP_CARD_MIN_ROTATION,
        animationSpec = tween(FLIP_ANIMATION_DURATION), label = ""
    )

    val rotationBack by animateFloatAsState(
        targetValue = if (isFlipCardRotated) -FLIP_CARD_MAX_ROTATION else FLIP_CARD_MIN_ROTATION,
        animationSpec = tween(FLIP_ANIMATION_DURATION), label = ""
    )

    val isFlipAnimationFinished by remember {
        derivedStateOf {
            (rotationFront == FLIP_CARD_MAX_ROTATION && rotationBack == -FLIP_CARD_MAX_ROTATION) ||
                (rotationFront == FLIP_CARD_MIN_ROTATION && rotationBack == FLIP_CARD_MIN_ROTATION)
        }
    }

    val frontVisible by remember { derivedStateOf { rotationFront < CARD_CHANGE_DEGREE } }
    val backVisible by remember { derivedStateOf { rotationBack < -CARD_CHANGE_DEGREE } }

    SpecialDeckCardContainer(
        cardContent = {
            if (backVisible) {
                mirrorImage?.let {
                    Image(
                        bitmap = it,
                        contentDescription = null,
                        contentScale = ContentScale.FillBounds,
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(MetaCardDefaults.shape)
                    )
                }
            }

            if (frontVisible) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            Brush.linearGradient(
                                colors = CustomTheme.colors.gradient.backgroundList,
                                start = Offset.Zero,
                                end = Offset(0f, Float.POSITIVE_INFINITY)
                            ),
                            MetaCardDefaults.shape
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    QuoteText(quote)
                }
            }
        },
        hintContent = {
            val hintAlpha by animateFloatAsState(
                targetValue = if (isFlipAnimationFinished) 1f else 0f,
                label = ""
            )

            Text(
                text = R.string.special_deck_details_button_rotate.strResDesc().localizedByLocal(),
                style = CustomTheme.typography.body.primary,
                color = CustomTheme.colors.text.primary,
                modifier = Modifier
                    .padding(top = 16.dp)
                    .alpha(hintAlpha)
            )
        },
        isLoading = mirrorImage == null,
        cardModifier = Modifier
            .graphicsLayer {
                cameraDistance = 8 * density
                rotationY = if (isFlipCardRotated) rotationFront else rotationBack
            }
            .clip(MetaCardDefaults.shape)
            .border(0.5.dp, CustomTheme.colors.stroke.secondary, MetaCardDefaults.shape)
            .clickable(
                onClick = onClick,
                enabled = isFlipAnimationFinished
            )
    )
}

@Composable
private fun QuoteText(quote: String) {
    var fontSize by remember { mutableIntStateOf(INITIAL_QUOTE_TEXT_SIZE) }
    var lineHeight by remember { mutableIntStateOf(INITIAL_QUOTE_LINE_HEIGHT) }
    var alpha by remember { mutableFloatStateOf(0f) }

    Text(
        text = quote,
        color = CustomTheme.colors.text.caption,
        style = CustomTheme.typography.quote.primary.copy(
            lineHeight = lineHeight.sp,
            fontSize = fontSize.sp
        ),
        textAlign = TextAlign.Center,
        modifier = Modifier
            .padding(20.dp)
            .alpha(alpha),
        overflow = TextOverflow.Ellipsis,
        onTextLayout = { result ->
            if (result.hasVisualOverflow && fontSize > MIN_QUOTE_TEXT_SIZE) {
                fontSize -= QUOTE_TEXT_SIZE_GAP
                lineHeight -= QUOTE_TEXT_SIZE_GAP
            }
            alpha = if (!result.hasVisualOverflow || fontSize <= MIN_QUOTE_TEXT_SIZE) 1f else 0f
        }
    )
}

private const val INITIAL_QUOTE_TEXT_SIZE = 24
private const val INITIAL_QUOTE_LINE_HEIGHT = 29
private const val MIN_QUOTE_TEXT_SIZE = 14
private const val QUOTE_TEXT_SIZE_GAP = 2
