package com.metacards.metacards.features.layout.domain

import com.metacards.metacards.core.user.domain.UserId
import com.metacards.metacards.features.record.data.RecordRepository
import com.metacards.metacards.features.record.domain.RecordId

class GetDailyCardRecordIdInteractor(
    private val recordRepository: RecordRepository
) {

    suspend fun execute(userId: UserId): RecordId? {
        return recordRepository.getDailyCardRecordId(userId)
    }
}
