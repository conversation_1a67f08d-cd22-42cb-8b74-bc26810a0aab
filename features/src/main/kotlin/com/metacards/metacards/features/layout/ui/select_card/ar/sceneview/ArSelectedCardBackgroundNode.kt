package com.metacards.metacards.features.layout.ui.select_card.ar.sceneview

import com.google.android.filament.Engine
import com.google.android.filament.Material
import com.google.android.filament.MaterialInstance
import com.google.android.filament.Texture
import com.google.android.filament.TextureSampler
import dev.romainguy.kotlin.math.times
import io.github.sceneview.ar.arcore.ArFrame
import io.github.sceneview.ar.node.ArCameraNode
import io.github.sceneview.ar.node.ArModelNode
import io.github.sceneview.math.Position
import io.github.sceneview.math.Scale
import io.github.sceneview.math.Transform
import io.github.sceneview.math.quaternion
import io.github.sceneview.model.ModelInstance

class ArSelectedCardBackgroundNode(
    engine: Engine,
    private val cameraNode: ArCameraNode
) : ArModelNode(
    engine,
    modelGlbFileLocation = "models/card.glb",
    scaleToUnits = 0.1f
) {

    private var texture: Texture? = null
    private var material: Material? = null
    private var materialInstance: MaterialInstance? = null

    init {
        position = Position(z = -100f)
        scale = Scale(0.001f)
    }

    fun addMaterial(material: Material?, texture: Texture?) {
        this.material = material
        this.texture = texture
    }

    override fun onArFrame(arFrame: ArFrame, isCameraTracking: Boolean) {
        super.onArFrame(arFrame, isCameraTracking)

        moveCardBackgroundInFrontOfCamera()
    }

    override fun onModelLoaded(modelInstance: ModelInstance) {
        super.onModelLoaded(modelInstance)

        setupMaterial(material, texture)
    }

    override fun destroy() {
        materialInstance?.let { engine.destroyMaterialInstance(it) }
        materialInstance = null
        super.destroy()
    }

    private fun moveCardBackgroundInFrontOfCamera() {
        val cameraTransform = cameraNode.transform
        val cameraPosition = cameraTransform.position
        val cameraForwardVector = cameraTransform.forward * -1f
        val newPosition = cameraPosition + (0.4f * cameraForwardVector)

        transform = Transform(newPosition.copy(z = newPosition.z - 0.00001f), cameraTransform.quaternion, scale)
    }

    private fun setupMaterial(material: Material?, texture: Texture?) {
        materialInstance = material?.createInstance() ?: return
        texture?.let {
            materialInstance!!.setParameter("texture", texture, TextureSampler())
        }

        setMaterialInstance(materialInstance!!)
    }
}
