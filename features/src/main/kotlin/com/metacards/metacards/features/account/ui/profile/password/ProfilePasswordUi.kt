package com.metacards.metacards.features.account.ui.profile.password

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.arkivanov.decompose.extensions.compose.jetpack.stack.Children
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.features.account.ui.profile.password.confirm.ProfileConfirmPasswordUi
import com.metacards.metacards.features.account.ui.profile.password.create_new.ProfileNewPasswordUi

@Composable
fun ProfilePasswordUi(
    component: ProfilePasswordComponent,
    modifier: Modifier = Modifier
) {
    val childStack by component.childStack.collectAsState()

    Box(modifier = modifier.navigationBarsPadding()) {
        Children(stack = childStack, modifier = Modifier.fillMaxSize()) { child ->
            when (val instance = child.instance) {
                is ProfilePasswordComponent.Child.ConfirmPassword -> ProfileConfirmPasswordUi(
                    instance.component,
                    Modifier.fillMaxSize()
                )
                is ProfilePasswordComponent.Child.NewPassword -> ProfileNewPasswordUi(
                    instance.component,
                    Modifier.fillMaxSize()
                )
            }
        }
    }
}

@Preview
@Composable
fun ProfilePasswordUiPreview() {
    AppTheme {
        ProfilePasswordUi(component = FakeProfilePasswordComponent())
    }
}