package com.metacards.metacards.features.account.ui.promocodes

import com.metacards.metacards.core.button.ButtonState
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.StateFlow
import ru.mobileup.kmm_form_validation.control.InputControl

interface PromocodeComponent {
    val promocodeInputControl: InputControl
    val buttonState: StateFlow<ButtonState>

    fun onActivateButtonClick()
    fun onCloseButtonClick()

    sealed interface Output {
        data class CloseScreenRequested(val message: StringDesc? = null) : Output
    }
}
