package com.metacards.metacards.features.record.ui.record_list

import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.PagedData
import com.metacards.metacards.features.record.domain.Record
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.record.domain.RecordListType
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.datetime.Instant

class FakeRecordListComponent : RecordListComponent {

    override val listType = RecordListType.All

    override val hasAddButton: Boolean = true

    override val recordsState = MutableStateFlow(
        LoadableState(loading = true, PagedData(Record.LIST_MOCK))
    )
    override val visibleRecordsState = MutableStateFlow(
        LoadableState(loading = true, PagedData(Record.LIST_MOCK))
    )

    override val pendingScrollCommand = MutableStateFlow<ScrollCommand?>(null)

    override fun onRefresh() = Unit

    override fun onRetryClick() = Unit

    override fun onAddClick() = Unit

    override fun onLoadNext() = Unit

    override fun onLoadPrevious() = Unit

    override fun onScrollToTopClick() = Unit

    override fun onStartTimeChosen(time: Instant) = Unit

    override fun onRecordClick(recordId: RecordId) = Unit

    override fun onArchiveClick(recordId: RecordId) = Unit

    override fun onFavouriteClick(recordId: RecordId) = Unit

    override fun onScrollCommandExecuted() = Unit
}