package com.metacards.metacards.features.record.ui.journal

import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CoursePassedTest
import com.metacards.metacards.features.course.domain.entity.CourseThemeId
import com.metacards.metacards.features.record.domain.RecordSource
import com.metacards.metacards.features.record.ui.journal.test_list.JournalTestListComponent
import com.metacards.metacards.features.record.ui.journal.test_list.JournalTestListComponent.Output
import com.metacards.metacards.features.record.ui.record_details.RecordDetailsComponent
import com.metacards.metacards.features.record.ui.record_list.RecordListComponent
import kotlinx.coroutines.flow.StateFlow
import kotlinx.datetime.Instant

interface JournalComponent {

    val tabsState: StateFlow<TabsState>

    val childStack: StateFlow<ChildStack<*, Child>>

    fun onCalendarClick()

    fun onArchiveClick()

    fun onTabSelected(tab: Tab)

    fun onScrollToTopClick()

    fun showJournalRecordsForDate(date: Instant)

    enum class Tab {
        Layouts, Tests, Favourite
    }

    sealed interface Output {
        data object CalendarRequested : Output
        data object ArchiveRequested : Output

        data class RecordDetailsRequested(
            val recordSource: RecordSource,
            val recordType: RecordDetailsComponent.RecordType,
            val isDailyCardLayout: Boolean,
            val courseId: CourseId?,
            val courseThemeId: CourseThemeId?
        ) : Output

        data object CreateRecordFlowRequested : Output
        data object SubscriptionSuggestingRequested : Output
        data class TestDetailsRequested(val coursePassedTest: CoursePassedTest) : Output
    }

    sealed interface Child {
        class RecordList(val component: RecordListComponent, val tab: Tab) : Child
        class TestsList(val component: JournalTestListComponent) : Child
    }
}