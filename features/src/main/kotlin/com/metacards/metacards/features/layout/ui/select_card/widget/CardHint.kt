package com.metacards.metacards.features.layout.ui.select_card.widget

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardHint
import com.metacards.metacards.features.deck.domain.entity.getCartHintText
import com.metacards.metacards.features.deck.domain.entity.getCartHintTitle

private const val EXPANSION_ANIMATION_DURATION_MILLIS = 200

@Composable
fun HintBlock(
    card: Card?,
    defaultCardHint: CardHint,
    isExpanded: Boolean,
    onExpansion: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    isAr: Boolean = false,
    isDailyCard: Boolean = false,
) {
    val iconRotation by animateFloatAsState(
        targetValue = if (isExpanded) 0f else 180f,
        label = "iconRotation"
    )

    val enterTransition = remember {
        expandVertically(
            expandFrom = Alignment.Top,
            animationSpec = tween(EXPANSION_ANIMATION_DURATION_MILLIS)
        )
    }

    val exitTransition = remember {
        shrinkVertically(
            shrinkTowards = Alignment.Top,
            animationSpec = tween(EXPANSION_ANIMATION_DURATION_MILLIS)
        )
    }

    Column(
        modifier = modifier.padding(vertical = 8.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = null,
                    onClick = {
                        onExpansion(!isExpanded)
                    }
                )
                .padding(
                    bottom = if (isExpanded) 4.dp else 0.dp
                )
        ) {
            Text(
                text = card.getCartHintTitle(isDailyCard).localizedByLocal(),
                style = CustomTheme.typography.body.primary,
                color = if (!isAr) {
                    CustomTheme.colors.text.secondary
                } else {
                    CustomTheme.colors.text.primary
                },
                modifier = Modifier.padding(end = 2.dp)
            )

            Icon(
                painter = painterResource(id = R.drawable.ic_24_chevron_up),
                tint = if (isAr) {
                    CustomTheme.colors.icons.primary
                } else {
                    CustomTheme.colors.icons.tertiary
                },
                contentDescription = null,
                modifier = Modifier.rotate(iconRotation)
            )
        }

        AnimatedVisibility(
            visible = isExpanded,
            enter = enterTransition,
            exit = exitTransition
        ) {
            Text(
                text = card.getCartHintText(defaultCardHint).localizedByLocal(),
                style = CustomTheme.typography.body.primary,
                color = if (!isAr) {
                    CustomTheme.colors.text.secondary
                } else {
                    CustomTheme.colors.text.primary
                },
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}