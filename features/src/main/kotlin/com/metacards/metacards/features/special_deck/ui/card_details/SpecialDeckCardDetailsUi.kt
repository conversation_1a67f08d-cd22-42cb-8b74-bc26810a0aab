@file:OptIn(ExperimentalComposeUiApi::class)

package com.metacards.metacards.features.special_deck.ui.card_details

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.dialog.default_component.DefaultDialog
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.widget.button.MetaPrimaryButton
import com.metacards.metacards.core.widget.navigation_bar.IconNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.ui.MetaCardDefaults
import com.metacards.metacards.features.special_deck.ui.card_details.widgets.GLASS_DAMAGE_LEVEL_1_START
import com.metacards.metacards.features.special_deck.ui.card_details.widgets.GLASS_DAMAGE_LEVEL_2_END
import com.metacards.metacards.features.special_deck.ui.card_details.widgets.GLASS_DAMAGE_LEVEL_2_START
import com.metacards.metacards.features.special_deck.ui.card_details.widgets.SpecialDeckCardDetailsFlipCard
import com.metacards.metacards.features.special_deck.ui.card_details.widgets.SpecialDeckCardDetailsGlassCard
import com.metacards.metacards.features.special_deck.ui.card_details.widgets.SpecialDeckCardDetailsScratchCard
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.delay
import com.metacards.metacards.core.R as CoreR

private const val CARD_SIZE_COEFFICIENT = 0.99f // helps to regulate how much need to scratch
private const val SCRATCH_CHECK_DELAY = 500L
private const val FLIP_CARD_INITIAL_FLIP_DELAY = 500L

@Composable
fun SpecialDeckCardDetailsUi(
    component: SpecialDeckCardDetailsComponent,
    modifier: Modifier = Modifier
) {

    val haptic = LocalHapticFeedback.current

    val blurredImage by component.blurredImage.collectAsState()
    val normalImage by component.normalImage.collectAsState()
    val mirrorImage by component.mirrorImage.collectAsState()

    val currentPath by remember { mutableStateOf(Path()) }
    var movedOffset by remember { mutableStateOf<Offset?>(null) }
    var scratchImageSize by remember { mutableStateOf(0f to 0f) }
    var isScratched by remember { mutableStateOf(false) }
    var glassDamageLevel by remember { mutableIntStateOf(0) }
    val isCardReleased by remember {
        derivedStateOf {
            isScratched ||
                glassDamageLevel >= GLASS_DAMAGE_LEVEL_2_END ||
                component.isCardAlreadyReceived
        }
    }
    var isFlipCardRotated by remember { mutableStateOf(true) }

    LaunchedEffect(Unit) {
        while (!isCardReleased) {
            delay(SCRATCH_CHECK_DELAY)
            val size = currentPath.getBounds().size
            isScratched =
                size.width > scratchImageSize.first && size.height > scratchImageSize.second
        }
    }

    LaunchedEffect(isCardReleased) {
        if (isCardReleased && !component.isCardAlreadyReceived) {
            component.onCardReleased()
            delay(FLIP_CARD_INITIAL_FLIP_DELAY)
            isFlipCardRotated = false
        }
    }

    Content(
        normalImage = normalImage,
        blurredImage = blurredImage,
        mirrorImage = mirrorImage,
        quote = component.card.quoteLocalized.localizedByLocal(),
        cardEffectType = component.cardEffectType,
        glassDamageLevel = glassDamageLevel,
        movedOffset = movedOffset,
        onMovedOffset = { movedOffset = it },
        currentPath = currentPath,
        isCardReleased = isCardReleased,
        isFlipCardRotated = isFlipCardRotated,
        isCardAlreadyReceived = component.isCardAlreadyReceived,
        onScratchCardSizeChanged = {
            scratchImageSize = it.width.times(CARD_SIZE_COEFFICIENT) to
                it.height.times(CARD_SIZE_COEFFICIENT)
        },
        onClickFlipCard = { isFlipCardRotated = !isFlipCardRotated },
        onReadyClick = component::onReadyClick,
        onGlassClick = {
            glassDamageLevel++
            val hapticFeedBackType = if (
                glassDamageLevel == GLASS_DAMAGE_LEVEL_1_START ||
                glassDamageLevel == GLASS_DAMAGE_LEVEL_2_START ||
                glassDamageLevel == GLASS_DAMAGE_LEVEL_2_END
            ) {
                HapticFeedbackType.LongPress
            } else {
                HapticFeedbackType.TextHandleMove
            }
            haptic.performHapticFeedback(hapticFeedBackType)
        },
        onCloseClick = { component.onCloseClick(isCardReleased) },
        modifier = modifier
    )

    DefaultDialog(dialogControl = component.dialogControl)
}

@Composable
private fun Content(
    normalImage: ImageBitmap?,
    blurredImage: ImageBitmap?,
    mirrorImage: ImageBitmap?,
    quote: String,
    cardEffectType: SpecialDeckCardDetailsComponent.CardEffectType,
    movedOffset: Offset?,
    glassDamageLevel: Int,
    onMovedOffset: (Offset) -> Unit,
    currentPath: Path,
    isCardReleased: Boolean,
    isFlipCardRotated: Boolean,
    isCardAlreadyReceived: Boolean,
    onScratchCardSizeChanged: (IntSize) -> Unit,
    onClickFlipCard: () -> Unit,
    onReadyClick: () -> Unit,
    onGlassClick: () -> Unit,
    onCloseClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .paint(
                painter = painterResource(CoreR.drawable.star_bg),
                contentScale = ContentScale.FillBounds
            )
    ) {

        TopNavigationBar(
            leadingIcon = {
                IconNavigationItem(
                    iconRes = R.drawable.ic_24_close,
                    onClick = onCloseClick
                )
            },
            title = if (isCardAlreadyReceived) {
                R.string.special_deck_details_title_card_opened
            } else {
                R.string.special_deck_details_title_card_new
            }.strResDesc(),
            modifier = Modifier
                .statusBarsPadding()
        )

        Box(
            modifier = Modifier
                .weight(1f),
        ) {
            if (!isCardReleased) {
                when (cardEffectType) {
                    SpecialDeckCardDetailsComponent.CardEffectType.Scratch -> SpecialDeckCardDetailsScratchCard(
                        normalImage = normalImage,
                        blurredImage = blurredImage,
                        movedOffset = movedOffset,
                        onMovedOffset = onMovedOffset,
                        currentPath = currentPath,
                        modifier = Modifier.onSizeChanged(onScratchCardSizeChanged)
                    )
                    SpecialDeckCardDetailsComponent.CardEffectType.Glass -> SpecialDeckCardDetailsGlassCard(
                        normalImage = normalImage,
                        glassDamageLevel = glassDamageLevel,
                        onGlassClick = onGlassClick
                    )
                }
            } else {
                SpecialDeckCardDetailsFlipCard(
                    mirrorImage = mirrorImage,
                    quote = quote,
                    isFlipCardRotated = isFlipCardRotated,
                    onClick = onClickFlipCard
                )
            }
        }

        val greatButtonState = if (isCardReleased && !isFlipCardRotated && !isCardAlreadyReceived) {
            ButtonState.Enabled
        } else {
            ButtonState.Disabled
        }
        val greatButtonAlpha by animateFloatAsState(
            targetValue = if (greatButtonState == ButtonState.Enabled) 1f else 0f,
            label = "greatButtonAlpha"
        )
        MetaPrimaryButton(
            text = R.string.special_deck_details_button_great.strResDesc().localizedByLocal(),
            onClick = onReadyClick,
            state = greatButtonState,
            modifier = Modifier
                .fillMaxWidth()
                .navigationBarsPadding()
                .padding(16.dp)
                .alpha(greatButtonAlpha)
        )
    }
}

@Composable
fun ColumnScope.StarDetailsCardLoadingStub() {
    Image(
        painter = painterResource(R.drawable.ic_card_loading_stub),
        contentDescription = null,
        contentScale = ContentScale.FillBounds,
        modifier = Modifier
            .weight(1f, fill = false)
            .aspectRatio(MetaCardDefaults.aspectRatio)
            .clip(MetaCardDefaults.shape)
    )
}

@Preview
@Composable
private fun Preview() {
    SpecialDeckCardDetailsUi(component = FakeSpecialDeckCardDetailsComponent())
}