package com.metacards.metacards.features.favorite_cards.ui.list

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.user.domain.FavoriteCard
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.favorite_cards.domain.GetFavoriteCardsInteractor
import kotlinx.coroutines.flow.StateFlow

class RealFavoriteCardListComponent(
    componentContext: ComponentContext,
    private val onOutput: (FavoriteCardListComponent.Output) -> Unit,
    getFavoriteCardsInteractor: GetFavoriteCardsInteractor
) : ComponentContext by componentContext, FavoriteCardListComponent {

    override val cards: StateFlow<List<FavoriteCard>> =
        getFavoriteCardsInteractor.execute()

    override fun onCardClick(cardId: CardId) {
        onOutput(FavoriteCardListComponent.Output.CardDetailsRequested(cardId))
    }

    override fun onGoToMainClick() {
        onOutput(FavoriteCardListComponent.Output.GoToMainRequested)
    }
}