package com.metacards.metacards.features.record.ui.record_details.comment_details

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

interface CommentDetailsComponent {
    val commentText: String
    val commentCreationDate: String?
    val cardImageUrl: String?
    val onCardClick: (() -> Unit)?

    fun onDismiss()

    @Parcelize
    data class Config(
        val commentText: String,
        val cardImageUrl: String? = null,
        val commentCreationDate: String? = null,
        val onCardClick: (() -> Unit)? = null
    ) : Parcelable

    sealed interface Output {
        data object DismissRequested : Output
    }
}