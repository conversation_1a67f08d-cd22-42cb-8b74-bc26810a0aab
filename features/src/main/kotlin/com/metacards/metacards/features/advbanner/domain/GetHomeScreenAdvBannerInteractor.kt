package com.metacards.metacards.features.advbanner.domain

import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.features.advbanner.data.BannerDataSource
import com.metacards.metacards.features.advbanner.domain.entity.HomeAdvBanner
import com.metacards.metacards.features.advbanner.domain.entity.HomeBannerType
import com.metacards.metacards.features.user.domain.GetUserSubscriptionStateInteractor
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine

class GetHomeScreenAdvBannerInteractor(
    private val getUserSubscriptionStateInteractor: GetUserSubscriptionStateInteractor,
    private val bannerDataSource: BannerDataSource
) {

    fun execute(): Flow<HomeAdvBanner?> = combine(
        getUserSubscriptionStateInteractor.execute(),
        bannerDataSource.getHomeBanners(),
        bannerDataSource.getHiddenBannerIdsFlow()
    ) { subscriptionState, bannersDto, hiddenIds ->
        val banners = bannersDto.map { it.toDomain() }
        val result = banners.find { it.priority == BannerDataSource.HIGHEST_PRIORITY } ?: run {
            val requiredType = if (subscriptionState is User.SubscriptionState.Ongoing) {
                HomeBannerType.DECK
            } else {
                HomeBannerType.SUB
            }
            banners.filter { it.type == requiredType }
                .minByOrNull { it.priority }
        }
        result?.takeIf { it.id !in hiddenIds }
    }
}
