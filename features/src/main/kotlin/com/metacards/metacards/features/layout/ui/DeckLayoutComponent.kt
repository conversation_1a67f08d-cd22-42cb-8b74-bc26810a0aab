package com.metacards.metacards.features.layout.ui

import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CourseThemeId
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardWithComment
import com.metacards.metacards.features.layout.domain.CardSource
import com.metacards.metacards.features.layout.ui.card_list.CardListComponent
import com.metacards.metacards.features.layout.ui.select_card.CardSelectComponent
import com.metacards.metacards.features.record.domain.RecordData
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.record.domain.RecordSource
import kotlinx.coroutines.flow.StateFlow

interface DeckLayoutComponent {
    val childStack: StateFlow<ChildStack<*, Child>>
    val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent>

    sealed interface Child {
        class SelectCard(val component: CardSelectComponent) : Child
        class CardList(val component: CardListComponent) : Child
    }

    sealed interface Output {
        data object OnCloseRequested : Output

        data class OnFinished(
            val cards: List<CardWithComment>,
            val recordData: RecordData
        ) : Output

        data class OnFinishedDaily(
            val cardWithComment: CardWithComment,
            val question: LocalizableString
        ) : Output

        data class OnPredefinedFinished(
            val recordSource: RecordSource,
            val recordData: RecordData,
            val courseId: CourseId?,
            val courseThemeId: CourseThemeId?
        ) : Output

        data class FullScreenCardRequested(val card: Card, val cardSource: CardSource) : Output
        data class SubscriptionSuggestingRequested(val withAdv: Boolean = false) : Output
        data object AuthSuggestingRequested : Output
        data class DailyRecordRequested(val recordId: RecordId) : Output
    }
}