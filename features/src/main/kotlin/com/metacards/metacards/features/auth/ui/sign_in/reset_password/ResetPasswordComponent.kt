package com.metacards.metacards.features.auth.ui.sign_in.reset_password

import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.features.auth.ui.auth_type.email.EmailEnterComponent
import com.metacards.metacards.features.auth.ui.auth_type.email.message.SentMessageToEmailComponent
import com.metacards.metacards.features.auth.ui.auth_type.pass.create_pass.CreatePasswordComponent
import kotlinx.coroutines.flow.StateFlow

interface ResetPasswordComponent {

    val childStack: StateFlow<ChildStack<*, Child>>

    fun onCloseButtonClick()

    sealed interface Child {
        class EmailEnter(val component: EmailEnterComponent) : Child
        class SentMessageToEmail(val component: SentMessageToEmailComponent) : Child
        class CreatePassword(val component: CreatePasswordComponent) : Child
    }

    sealed interface Output {
        data object PasswordWasReset : Output
        data object CloseScreenRequested : Output
        data object SignUpRequested : Output
        data object MainScreenRequested : Output
        data class WebViewRequested(val url: String) : Output
        data object GoBackRequested : Output
    }
}