package com.metacards.metacards.features.showcase.ui.widgets

import androidx.compose.animation.core.tween
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.navigation_bar.IconNavigationItem
import com.metacards.metacards.core.widget.placeholder.PlaceholderHighlight
import com.metacards.metacards.core.widget.placeholder.placeholder
import com.metacards.metacards.core.widget.placeholder.shimmer
import com.metacards.metacards.features.R
import com.metacards.metacards.features.home.ui.widget.ActionButton
import com.metacards.metacards.features.layout.domain.LayoutId
import com.metacards.metacards.features.layout.domain.PredefinedLayoutWithAvailable
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun ColumnScope.ShowcasePredefinedLayouts(
    predefinedLayouts: List<PredefinedLayoutWithAvailable>,
    onLayoutClick: (PredefinedLayoutWithAvailable) -> Unit,
    onBlockContentClick: () -> Unit,
) {
    ShowcaseItemContainer(
        title = R.string.showcase_prepared_layouts_title.strResDesc().localizedByLocal(),
        subtitle = R.string.showcase_prepared_layouts_subtitle.strResDesc().localizedByLocal()
    ) {
        val itemModifier = Modifier
            .size(width = 312.dp, height = 180.dp)
            .clip(RoundedCornerShape(16.dp))
        LazyRow(
            modifier = Modifier
                .fillMaxWidth(),
            contentPadding = PaddingValues(horizontal = 16.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(predefinedLayouts) { layout ->
                LayoutItem(
                    layoutWithAvailable = layout,
                    onLayoutClick = { onLayoutClick(layout) },
                    onBlockContentClick = onBlockContentClick,
                    modifier = itemModifier
                )
            }
        }
    }
}

@Composable
private fun LayoutItem(
    layoutWithAvailable: PredefinedLayoutWithAvailable,
    onLayoutClick: (LayoutId) -> Unit,
    onBlockContentClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    if (!layoutWithAvailable.isAvailable) {
        BoxWithFade(
            modifier = modifier,
            listOfColors = CustomTheme.colors.gradient.backgroundList.map {
                it.copy(alpha = 0.75f)
            },
            behindContent = {
                PredefinedLayoutItemContent(
                    predefinedLayout = layoutWithAvailable,
                    onLayoutClick = onLayoutClick
                )
            }
        ) {
            Box(
                modifier = Modifier
                    .matchParentSize()
                    .clickable(onClick = onBlockContentClick)
                    .align(Alignment.TopCenter)
            ) {
                ActionButton(
                    modifier = Modifier
                        .align(Alignment.TopCenter)
                        .padding(top = 64.dp),
                    text = StringDesc.Resource(R.string.add_record_premium_action_button),
                    borderStroke = BorderStroke(
                        0.5.dp,
                        CustomTheme.colors.stroke.secondary
                    ),
                    leadingIcon = {
                        IconNavigationItem(iconRes = R.drawable.ic_24_locked)
                    }
                )
            }
        }
    } else {
        PredefinedLayoutItemContent(
            predefinedLayout = layoutWithAvailable,
            onLayoutClick = onLayoutClick,
            modifier = modifier
        )
    }
}

@Composable
private fun PredefinedLayoutItemContent(
    predefinedLayout: PredefinedLayoutWithAvailable,
    onLayoutClick: (LayoutId) -> Unit,
    modifier: Modifier = Modifier,
) {
    var isSkeleton by remember { mutableStateOf(true) }
    val shape = RoundedCornerShape(16.dp)

    Box(
        modifier = modifier
            .border(
                border = BorderStroke(width = 0.5.dp, color = CustomTheme.colors.stroke.secondary),
                shape = shape
            )
            .clickable { onLayoutClick(predefinedLayout.layout.id) }
            .placeholder(
                visible = isSkeleton,
                color = CustomTheme.colors.background.placeholder,
                shape = shape,
                highlight = PlaceholderHighlight.shimmer(
                    highlightColor = CustomTheme.colors.system.shimmer
                ),
                placeholderFadeTransitionSpec = { tween() },
                contentFadeTransitionSpec = { tween() }
            )
    ) {
        AsyncImage(
            model = predefinedLayout.layout.coverUrl,
            contentDescription = null,
            contentScale = ContentScale.Crop,
            onSuccess = { isSkeleton = false },
            modifier = Modifier
                .fillMaxSize()
                .placeholder(
                    visible = isSkeleton,
                    color = CustomTheme.colors.background.placeholder,
                    shape = shape,
                    highlight = PlaceholderHighlight.shimmer(
                        highlightColor = CustomTheme.colors.system.shimmer
                    ),
                    placeholderFadeTransitionSpec = { tween() },
                    contentFadeTransitionSpec = { tween() }
                )
        )

        Text(
            modifier = Modifier
                .padding(16.dp)
                .clip(CircleShape)
                .background(CustomTheme.colors.button.small, CircleShape)
                .padding(horizontal = 24.dp, vertical = 8.dp),
            text = predefinedLayout.layout.name.localizedByLocal(),
            style = CustomTheme.typography.button.small,
            color = CustomTheme.colors.text.caption,
        )
    }
}