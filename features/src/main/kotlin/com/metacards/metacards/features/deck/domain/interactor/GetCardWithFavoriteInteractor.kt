package com.metacards.metacards.features.deck.domain.interactor

import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardWithFavoriteAndComment
import com.metacards.metacards.features.user.domain.UserRepository

class GetCardWithFavoriteInteractor(
    private val userRepository: UserRepository
) {
    fun execute(cards: List<Card>): List<CardWithFavoriteAndComment> {
        return cards.map { execute(it) }
    }

    fun execute(card: Card): CardWithFavoriteAndComment {
        val user = userRepository.user.value ?: return CardWithFavoriteAndComment.default(card)
        val isFavorite = user.favoriteCards.find { it.id == card.id.value } != null
        return CardWithFavoriteAndComment(card, isFavorite)
    }
}