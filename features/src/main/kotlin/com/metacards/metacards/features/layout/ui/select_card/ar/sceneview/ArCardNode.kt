package com.metacards.metacards.features.layout.ui.select_card.ar.sceneview

import android.view.MotionEvent
import com.google.android.filament.Engine
import com.google.android.filament.Material
import com.google.android.filament.MaterialInstance
import com.google.android.filament.Texture
import com.google.android.filament.TextureSampler
import com.metacards.metacards.features.deck.domain.entity.Card
import io.github.sceneview.ar.node.ArModelNode
import io.github.sceneview.math.Rotation
import io.github.sceneview.model.ModelInstance
import io.github.sceneview.renderable.Renderable

class ArCardNode(engine: Engine, val card: Card) :
    ArModelNode(
        engine,
        modelGlbFileLocation = "models/card.glb",
        scaleToUnits = 0.1f
    ) {
    private var material: Material? = null
    private var texture: Texture? = null
    private var materialInstance: MaterialInstance? = null

    var isStashed: Boolean = false

    init {
        modelRotation = Rotation(x = 90f)
        isPositionEditable = false
        isRotationEditable = false
        isScaleEditable = false
    }

    override fun onTap(motionEvent: MotionEvent, renderable: Renderable?) {
        if (!isStashed) super.onTap(motionEvent, renderable)
    }

    override val isVisibleInHierarchy: Boolean
        get() = parentNode?.isVisibleInHierarchy != false

    override fun onModelLoaded(modelInstance: ModelInstance) {
        super.onModelLoaded(modelInstance)
        setupMaterial(material, texture)
    }

    override fun destroy() {
        materialInstance?.let { engine.destroyMaterialInstance(it) }
        materialInstance = null
        super.destroy()
    }

    fun addMaterial(material: Material?, texture: Texture?) {
        this.material = material
        this.texture = texture
    }

    private fun setupMaterial(material: Material?, texture: Texture?) {
        materialInstance = material?.createInstance() ?: return
        texture?.let {
            materialInstance!!.setParameter("texture", texture, TextureSampler())
        }

        setMaterialInstance(materialInstance!!)
    }
}