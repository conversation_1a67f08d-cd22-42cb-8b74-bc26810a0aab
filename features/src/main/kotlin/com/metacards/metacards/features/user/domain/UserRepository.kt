package com.metacards.metacards.features.user.domain

import com.metacards.metacards.core.localization.domain.AppLanguage
import com.metacards.metacards.core.user.data.UserDto
import com.metacards.metacards.core.user.domain.AuthUserInfo
import com.metacards.metacards.core.user.domain.FavoriteCard
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.user.domain.UserId
import com.metacards.metacards.core.utils.LoadableState
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow

interface UserRepository {
    val user: StateFlow<User?>

    suspend fun fetchUser(authUserInfo: AuthUserInfo?)
    fun getPurchasedDecksIdFlow(): Flow<LoadableState<List<String>>>
    suspend fun getPurchasedDecksId(): List<String?>
    suspend fun toggleFavoriteCard(isFavorite: <PERSON>olean, card: FavoriteCard)
    suspend fun deleteUser(userId: UserId)
    suspend fun updateUser(userId: UserId, updatedUser: User)
    suspend fun addDeckToPurchased(deckId: String)
    suspend fun updateYearOfBirth(userId: UserId, yearOfBirth: Int)
    suspend fun updateGender(userId: UserId, gender: UserDto.Gender)
    suspend fun updateName(userId: UserId, name: String)
    suspend fun updateLanguage(userId: UserId, language: AppLanguage)
    suspend fun updateUserInfoOnAppLaunch(userId: UserId)
}