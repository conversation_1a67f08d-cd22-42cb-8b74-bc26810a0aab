package com.metacards.metacards.features.account.ui.shop

import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.domain.entity.DeckInfoWithCards
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow

interface ShopComponent {
    val decksForPurchaseInfoWithCards: StateFlow<List<DeckInfoWithCards>>
    val currentDeckPosition: StateFlow<Int>
    val scrollToPageCommand: SharedFlow<Int>

    fun onGoToWebSiteClick()
    fun onDeckSwipe(newPosition: Int)
    fun onDeckClick(deckId: DeckId)

    sealed interface Output {
        data class DeckDetailsRequested(val deckId: DeckId) : Output
    }
}