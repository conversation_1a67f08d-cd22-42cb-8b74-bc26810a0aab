package com.metacards.metacards.features.account.ui.profile.password.create_new

import com.metacards.metacards.core.button.ButtonState
import kotlinx.coroutines.flow.StateFlow
import ru.mobileup.kmm_form_validation.control.InputControl

interface ProfileNewPasswordComponent {
    val newPasswordInputControl: InputControl
    val confirmNewPasswordInputControl: InputControl
    val saveButtonState: StateFlow<ButtonState>

    fun onSaveClick()

    sealed interface Output {
        object ProfileScreenRequested : Output
    }
}