package com.metacards.metacards.features.deck.data.dto

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.price.Price
import com.metacards.metacards.core.utils.removeSeparators
import com.metacards.metacards.features.deck.domain.entity.Deck
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.domain.entity.DeckInfo
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.net.URI

@Serializable
class DeckDto(
    @SerialName("id")
    val id: String = "",
    @SerialName("nameLocalized")
    val nameLocalized: Map<String, String?> = emptyMap(),
    @SerialName("descriptionLocalized")
    val descriptionLocalized: Map<String, String?> = emptyMap(),
    @SerialName("cover")
    val cover: String = "",
    @SerialName("price")
    val price: Double = 0.0,
    @SerialName("availability")
    val availability: Deck.Availability = Deck.Availability.FREE,
    @SerialName("hasAR")
    val hasAR: Boolean = false,
    @SerialName("tagsLocalized")
    val tagsLocalized: List<Map<String, String?>> = emptyList(),
    @SerialName("videoLessons")
    val videoLessons: List<LessonDto> = emptyList(),
    @SerialName("shopUrl")
    val shopUrl: String? = null,
    @SerialName("storeId")
    val storeId: String? = null,
) {

    fun copy(
        id: String = this.id,
        name: Map<String, String?> = this.nameLocalized,
        description: Map<String, String?> = this.descriptionLocalized,
        cover: String = this.cover,
        price: Double = this.price,
        availability: Deck.Availability = this.availability,
        hasAR: Boolean = this.hasAR,
        tags: List<Map<String, String?>> = this.tagsLocalized,
        videoLessons: List<LessonDto> = this.videoLessons,
        shopUrl: String? = this.shopUrl,
        storeId: String? = this.storeId
    ) = DeckDto(
        id,
        name,
        description,
        cover,
        price,
        availability,
        hasAR,
        tags,
        videoLessons,
        shopUrl,
        storeId
    )
}

fun DeckDto.toDomain(): Deck {
    return Deck(
        id = DeckId(id),
        name = LocalizableString(nameLocalized),
        availability = availability,
        coverUrl = cover
    )
}

fun DeckDto.toDeckInfoDomain(): DeckInfo {
    return DeckInfo(
        deckId = DeckId(id),
        name = LocalizableString(nameLocalized),
        description = LocalizableString(descriptionLocalized.mapValues {
            it.value?.removeSeparators() ?: ""
        }),
        price = Price(price),
        availability = availability,
        videoLessons = videoLessons.map { it.toDomain() },
        shopUrl = shopUrl?.let { URI(it) },
        coverUrl = cover,
        storeId = storeId ?: "",
        tags = tagsLocalized.map { LocalizableString(it) },
        hasAR = hasAR
    )
}