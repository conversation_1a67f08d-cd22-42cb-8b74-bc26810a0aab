package com.metacards.metacards.features.account.ui.profile.name

import com.metacards.metacards.core.button.ButtonState
import kotlinx.coroutines.flow.StateFlow
import ru.mobileup.kmm_form_validation.control.InputControl

interface ProfileNameComponent {
    val initName: String
    val inputControl: InputControl
    val saveButtonState: StateFlow<ButtonState>

    fun onSaveClick()

    sealed interface Output {
        object DismissRequested : Output
    }
}