package com.metacards.metacards.features.tutorial.data

import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow

interface TutorialMessageService {

    val tutorialMessageFlow: Flow<TutorialMessage?>

    val tutorialStepFlow: StateFlow<TutorialStep>

    val tutorialCloseRequestFlow: SharedFlow<Unit>

    /**
     * Handle tutorial state and show the corresponding message
     */
    fun handleMainComponentTutorial()

    fun handleHomeComponentTutorial()

    fun handleQuestionForLayoutComponentTutorial(componentScope: CoroutineScope)

    fun handleCardSelectComponentTutorial(componentScope: CoroutineScope)

    fun handleCardListComponentTutorial(componentScope: CoroutineScope)

    fun handleRecordDetailsComponentTutorial(componentScope: CoroutineScope)

    /**
     * Show corresponding message without handling state
     * Used in component close cases
     */
    fun processCloseDeckLayoutComponentTutorial()

    fun processCloseQuestionForLayoutComponentTutorial()
}