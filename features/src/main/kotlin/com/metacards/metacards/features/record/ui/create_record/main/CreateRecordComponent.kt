package com.metacards.metacards.features.record.ui.create_record.main

import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.features.record.domain.camera.CardPhoto
import com.metacards.metacards.features.record.ui.model.FeelingItem
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow

interface CreateRecordComponent {
    val inputSource: SharedFlow<CardPhoto?>
    val userSubscriptionState: StateFlow<User.SubscriptionState?>
    val isFavourite: StateFlow<Boolean>
    val cardPhoto: StateFlow<CardPhoto?>
    val question: StateFlow<String>
    val comment: StateFlow<String>
    val energyLevel: StateFlow<FeelingItem<Int>?>
    val moodLevel: StateFlow<FeelingItem<Int>?>
    val loadingSaveRecord: StateFlow<Boolean>

    fun onFavoriteClick()
    fun onAddCardClick()
    fun onQuestionChange(value: String)
    fun onCommentChange(value: String)
    fun onMoodLevelChange(value: FeelingItem<Int>?)
    fun onEnergyLevelChange(value: FeelingItem<Int>?)
    fun onSaveClick(value: ByteArray?)
    fun onBlockAreaClick()

    sealed interface Output {
        object PermissionCameraRequested : Output
        object CameraRequested : Output
        object JournalRequested : Output
        object SubscriptionSuggestingRequested : Output
        object AuthSuggestingRequested : Output
    }
}
