@file:OptIn(ExperimentalFoundationApi::class)

package com.metacards.metacards.features.course.ui.lesson

import android.content.Context
import androidx.activity.compose.BackHandler
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.AnchoredDraggableState
import androidx.compose.foundation.gestures.DraggableAnchors
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.anchoredDraggable
import androidx.compose.foundation.gestures.animateTo
import androidx.compose.foundation.gestures.snapTo
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsBottomHeight
import androidx.compose.foundation.layout.windowInsetsTopHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.dialog.default_component.DefaultDialog
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.userGesturesEnabled
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.button.MetaSecondaryButton
import com.metacards.metacards.core.widget.navigation_bar.IconNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.course.domain.entity.CourseLessonContent
import com.metacards.metacards.features.course.ui.lesson.DragAnchors.Bottom
import com.metacards.metacards.features.course.ui.lesson.DragAnchors.Center
import com.metacards.metacards.features.course.ui.lesson.DragAnchors.Top
import com.metacards.metacards.features.course.ui.widgets.CardVideoWrapper
import com.metacards.metacards.features.course.ui.widgets.CourseLoaderSkeleton
import com.metacards.metacards.features.deck.ui.MetaCardDefaults
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlin.math.abs
import kotlin.math.roundToInt

@Composable
fun CourseLessonUi(
    component: CourseLessonComponent,
    modifier: Modifier = Modifier,
) {
    val lesson by component.cards.collectAsState()

    lesson?.let {
        Content(
            courseLesson = it,
            courseName = component.courseName,
            lastCardButtonText = component.lastCardButtonText,
            onToNextContentClick = component::onToNextContentClick,
            onVideoFullScreenClick = component::onVideoFullScreenClick,
            onCloseClick = component::onCloseClick,
            modifier = modifier
        )
    } ?: CourseLoaderSkeleton()

    DefaultDialog(component.closeDialogControl)

    BackHandler(onBack = component::onCloseClick)
}

private const val DRAG_ANIMATION_DURATION = 500
private const val CARD_HORIZONTAL_GAP = 16
private const val CARD_VERTICAL_GAP = 12
private const val CURRENT_CARD_BOTTOM_DRAG_EXTREME_POSITION_COEFFICIENT = 5
private const val PREVIOUS_CARD_BOTTOM_DRAG_EXTREME_POSITION_COEFFICIENT = 20

@Composable
private fun Content(
    courseLesson: CourseLessonCards,
    courseName: LocalizableString?,
    lastCardButtonText: String,
    onToNextContentClick: () -> Unit,
    onVideoFullScreenClick: (Long, String) -> Unit,
    onCloseClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val localDensity = LocalDensity.current
    val localConfig = LocalConfiguration.current
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val dragExtremePosition = remember { localConfig.screenHeightDp * localDensity.density }

    var currentIndex by remember { mutableIntStateOf(0) }
    val stack by remember { derivedStateOf { Stack.create(courseLesson.cards, currentIndex) } }
    val lastIndex = courseLesson.cards.lastIndex

    val currentCardDragState = DragAnchors.createState(
        initialValue = Center,
        density = localDensity.density,
        extremePosition = dragExtremePosition,
        bottomCoefficient = CURRENT_CARD_BOTTOM_DRAG_EXTREME_POSITION_COEFFICIENT,
    )

    val previousCardDragState = DragAnchors.createState(
        initialValue = Top,
        density = localDensity.density,
        extremePosition = dragExtremePosition,
        bottomCoefficient = PREVIOUS_CARD_BOTTOM_DRAG_EXTREME_POSITION_COEFFICIENT,
    )

    var topContentHeight by remember { mutableStateOf(0.dp) }
    var bottomContentHeight by remember { mutableStateOf(0.dp) }
    var dragAnimationInProgress by remember { mutableStateOf(true) }

    LaunchedEffect(currentIndex == lastIndex) {
        val topPosition = if (currentIndex == lastIndex) -dragExtremePosition * 0.2f else -dragExtremePosition
        currentCardDragState.updateAnchors(
            DraggableAnchors {
                Top at topPosition
                Center at 0f
                Bottom at (dragExtremePosition / CURRENT_CARD_BOTTOM_DRAG_EXTREME_POSITION_COEFFICIENT)
            }
        )
    }

    LaunchedEffect(Unit) {
        snapshotFlow { currentCardDragState.currentValue }.onEach { value ->
            if (value == Bottom) {
                if (currentIndex > 0) {
                    currentIndex--
                    previousCardDragState.animateTo(Bottom)
                    previousCardDragState.animateTo(Center)
                    previousCardDragState.snapTo(Top)
                }
                currentCardDragState.snapTo(Center)
                dragAnimationInProgress = true
            }

            if (value == Top) {
                if (currentIndex < lastIndex) {
                    currentIndex++
                    currentCardDragState.snapTo(Center)
                } else {
                    currentCardDragState.animateTo(Center)
                }
                dragAnimationInProgress = true
            }
        }.launchIn(this)
    }

    Box(
        modifier = modifier
            .fillMaxSize()
    ) {
        TopContent(
            currentIndex = currentIndex,
            cardsCount = courseLesson.cards.size,
            title = courseName ?: courseLesson.name,
            onCloseClick = onCloseClick,
            modifier = Modifier
                .onSizeChanged { with(localDensity) { topContentHeight = it.height.toDp() } }
        )

        BottomContent(
            lastCardButtonText = lastCardButtonText,
            onBackClick = {
                scope.launch {
                    dragAnimationInProgress = false
                    currentCardDragState.snapTo(Bottom)
                }
            },
            onForwardClick = {
                scope.launch {
                    dragAnimationInProgress = false
                    currentCardDragState.animateTo(Top)
                }
            },
            onToNextContentClick = onToNextContentClick,
            buttonsEnable = dragAnimationInProgress,
            currentIndex = currentIndex,
            lastIndex = lastIndex,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .onSizeChanged { with(localDensity) { bottomContentHeight = it.height.toDp() } }
        )

        Cards(
            dragState = currentCardDragState,
            prevDragState = previousCardDragState,
            currentCardStack = stack,
            context = context,
            dragExtremePosition = dragExtremePosition,
            onVideoFullScreenClick = onVideoFullScreenClick,
            modifier = Modifier
                .fillMaxSize()
                .padding(top = topContentHeight, bottom = bottomContentHeight)
        )
    }
}

@Composable
private fun TopContent(
    currentIndex: Int,
    cardsCount: Int,
    title: LocalizableString,
    onCloseClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
    ) {

        Spacer(Modifier.windowInsetsTopHeight(WindowInsets.statusBars))

        TopNavigationBar(
            modifier = Modifier.fillMaxWidth(),
            title = title,
            leadingIcon = {
                IconNavigationItem(
                    iconRes = R.drawable.ic_24_close,
                    onClick = onCloseClick
                )
            },
        )

        Spacer(modifier = Modifier.height(8.dp))

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            horizontalArrangement = Arrangement.spacedBy(if (cardsCount > 6) 1.5.dp else 4.dp)
        ) {
            repeat(cardsCount) { index ->
                val bgColor by animateColorAsState(
                    targetValue = if (index > currentIndex) {
                        CustomTheme.colors.button.secondary
                    } else {
                        CustomTheme.colors.stroke.primary
                    },
                    label = "color"
                )
                Spacer(
                    modifier = Modifier
                        .height(8.dp)
                        .weight(1f)
                        .background(bgColor, RoundedCornerShape(16.dp))
                )
            }
        }

        Spacer(modifier = Modifier.height(20.dp))
    }
}

@Composable
private fun BottomContent(
    lastCardButtonText: String,
    onBackClick: () -> Unit,
    onForwardClick: () -> Unit,
    onToNextContentClick: () -> Unit,
    buttonsEnable: Boolean,
    currentIndex: Int,
    lastIndex: Int,
    modifier: Modifier = Modifier,
) {

    Column(
        modifier = modifier
            .fillMaxWidth()
    ) {
        Spacer(modifier = Modifier.height(54.dp))
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .userGesturesEnabled(buttonsEnable),
        ) {

            LessonButton(
                onClick = onBackClick,
                enable = currentIndex > 0,
                text = R.string.course_lesson_back_button.strResDesc().localizedByLocal()
            )

            Spacer(modifier = Modifier.width(8.dp))

            val isLastCard = currentIndex == lastIndex

            if (isLastCard) {
                MetaAccentButton(
                    text = lastCardButtonText,
                    onClick = onToNextContentClick,
                    modifier = Modifier.weight(1f)
                )
            } else {
                LessonButton(
                    onClick = onForwardClick,
                    enable = true,
                    text = R.string.course_lesson_forward_button.strResDesc().localizedByLocal()
                )
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        Spacer(Modifier.windowInsetsBottomHeight(WindowInsets.navigationBars))
    }
}

@Composable
private fun RowScope.LessonButton(
    onClick: () -> Unit,
    enable: Boolean,
    text: String,
) {
    MetaSecondaryButton(
        text = text,
        state = if (enable) ButtonState.Enabled else ButtonState.Disabled,
        onClick = onClick,
        modifier = Modifier.weight(1f)
    )
}

@Composable
private fun Cards(
    dragState: AnchoredDraggableState<DragAnchors>,
    prevDragState: AnchoredDraggableState<DragAnchors>,
    currentCardStack: Stack,
    context: Context,
    dragExtremePosition: Float,
    onVideoFullScreenClick: (Long, String) -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        val dragPaddingCoefficient = remember { dragExtremePosition / CARD_HORIZONTAL_GAP }

        val dragExtraPadding by remember {
            derivedStateOf {
                dragState.offset.div(dragPaddingCoefficient)
                    .let { if (it < 0f) abs(it) else 0f }
            }
        }

        val afterNextHorizontalPadding by remember {
            derivedStateOf {
                ((CARD_HORIZONTAL_GAP * 2) - dragExtraPadding).coerceIn(
                    CARD_HORIZONTAL_GAP.toFloat(),
                    CARD_HORIZONTAL_GAP * 2f
                )
            }
        }

        val afterNextOffsetY by remember {
            derivedStateOf {
                ((CARD_VERTICAL_GAP * 2) - dragExtraPadding).coerceIn(
                    CARD_VERTICAL_GAP.toFloat(),
                    CARD_VERTICAL_GAP * 2f
                )
            }
        }

        currentCardStack.afterNext?.let { card ->
            NextCard(
                card = card,
                context = context,
                offsetY = afterNextOffsetY.dp,
                paddingHorizontal = afterNextHorizontalPadding.dp,
                onVideoFullScreenClick = onVideoFullScreenClick
            )
        }

        val nextHorizontalPadding by remember {
            derivedStateOf {
                (CARD_HORIZONTAL_GAP - dragExtraPadding).coerceIn(
                    0f,
                    CARD_HORIZONTAL_GAP.toFloat()
                )
            }
        }

        val nextOffsetY by remember {
            derivedStateOf {
                (CARD_VERTICAL_GAP - dragExtraPadding).coerceIn(
                    0f,
                    CARD_VERTICAL_GAP.toFloat()
                )
            }
        }

        currentCardStack.next?.let { card ->
            NextCard(
                card = card,
                context = context,
                offsetY = nextOffsetY.dp,
                paddingHorizontal = nextHorizontalPadding.dp,
                onVideoFullScreenClick = onVideoFullScreenClick
            )
        }

        val dragEnable by remember {
            derivedStateOf { !(dragState.isAnimationRunning || prevDragState.isAnimationRunning) }
        }

        currentCardStack.current?.let { card ->
            CurrentCard(
                state = dragState,
                dragEnable = dragEnable,
                card = card,
                context = context,
                onVideoFullScreenClick = onVideoFullScreenClick,
                modifier = Modifier.graphicsLayer {
                    alpha = if (prevDragState.isAnimationRunning) 0f else 1f
                }
            )
        }

        // previous
        currentCardStack.current?.let { card ->
            PreviousCard(
                state = prevDragState,
                dragEnable = dragEnable,
                card = card,
                context = context,
                onVideoFullScreenClick = onVideoFullScreenClick
            )
        }
    }
}

@Composable
private fun PreviousCard(
    state: AnchoredDraggableState<DragAnchors>,
    dragEnable: Boolean,
    card: CourseLessonContent,
    context: Context,
    onVideoFullScreenClick: (Long, String) -> Unit,
    modifier: Modifier = Modifier,
    offsetY: Dp = 0.dp,
    paddingHorizontal: Dp = 0.dp,
) {
    val draggableModifier = Modifier
        .offset {
            IntOffset(
                x = 0,
                y = state
                    .requireOffset()
                    .roundToInt(),
            )
        }
        .anchoredDraggable(state, Orientation.Vertical, dragEnable)
    Box(
        modifier = modifier
            .fillMaxHeight()
            .aspectRatio(MetaCardDefaults.aspectRatio)
            .offset(y = offsetY)
            .padding(horizontal = paddingHorizontal)
            .then(draggableModifier),
        contentAlignment = Alignment.Center
    ) {
        when (card) {
            is CourseLessonContent.Image -> CardImage(context, card.url)
            is CourseLessonContent.Video -> CardVideoWrapper(
                context = context,
                videoUrl = card.url,
                videoCoverUrl = card.videoCoverUrl,
                onFullScreenClick = { duration -> onVideoFullScreenClick(duration, card.url) },
            )
        }
    }
}

@Composable
private fun CurrentCard(
    state: AnchoredDraggableState<DragAnchors>,
    dragEnable: Boolean,
    card: CourseLessonContent,
    context: Context,
    onVideoFullScreenClick: (Long, String) -> Unit,
    modifier: Modifier = Modifier,
) {
    val yOffset by remember {
        derivedStateOf {
            state
                .requireOffset()
                .roundToInt().takeIf { it < 0 } ?: 0
        }
    }

    val maxAnchor = remember { state.anchors.maxAnchor() }
    val maxHorizontalPadding = remember { CARD_HORIZONTAL_GAP.minus(1) } // 1 == border 0.5 * 2
    val horizontalPaddingCoefficient = remember(maxAnchor) { maxHorizontalPadding.div(maxAnchor) }
    val horizontalPaddingValue by remember {
        derivedStateOf {
            state.requireOffset().roundToInt().times(horizontalPaddingCoefficient)
                .coerceIn(0f, CARD_HORIZONTAL_GAP.toFloat()).dp
        }
    }
    val verticalPaddingCoefficient = remember {
        CARD_VERTICAL_GAP.toFloat() / CARD_HORIZONTAL_GAP.toFloat()
    }
    val draggableModifier = Modifier
        .offset { IntOffset(x = 0, y = yOffset) }
        .anchoredDraggable(state, Orientation.Vertical, dragEnable)
    Box(
        modifier = modifier
            .fillMaxHeight()
            .aspectRatio(MetaCardDefaults.aspectRatio)
            .padding(
                horizontal = horizontalPaddingValue,
                vertical = horizontalPaddingValue.times(verticalPaddingCoefficient)
            )
            .then(draggableModifier),
        contentAlignment = Alignment.Center
    ) {
        when (card) {
            is CourseLessonContent.Image -> CardImage(context, card.url)
            is CourseLessonContent.Video -> CardVideoWrapper(
                context = context,
                videoUrl = card.url,
                videoCoverUrl = card.videoCoverUrl,
                onFullScreenClick = { duration -> onVideoFullScreenClick(duration, card.url) },
            )
        }
    }
}

@Composable
private fun NextCard(
    card: CourseLessonContent,
    context: Context,
    onVideoFullScreenClick: (Long, String) -> Unit,
    modifier: Modifier = Modifier,
    offsetY: Dp = 0.dp,
    paddingHorizontal: Dp = 0.dp,
) {
    Box(
        modifier = modifier
            .offset {
                IntOffset(
                    x = 0,
                    y = offsetY
                        .toPx()
                        .roundToInt()
                )
            }
            .fillMaxHeight()
            .aspectRatio(MetaCardDefaults.aspectRatio)
            .padding(horizontal = paddingHorizontal),
        contentAlignment = Alignment.Center
    ) {
        when (card) {
            is CourseLessonContent.Image -> CardImage(context, card.url)
            is CourseLessonContent.Video -> CardVideoWrapper(
                context = context,
                videoUrl = card.url,
                videoCoverUrl = card.videoCoverUrl,
                onFullScreenClick = { duration -> onVideoFullScreenClick(duration, card.url) },
            )
        }
    }
}

@Composable
private fun CardImage(
    context: Context,
    imageUrl: String,
) {
    AsyncImage(
        model = ImageRequest.Builder(context)
            .data(imageUrl)
            .crossfade(MetaCardDefaults.CROSSFADE_DURATION_MILLIS)
            .build(),
        contentScale = ContentScale.FillBounds,
        contentDescription = null,
        modifier = Modifier
            .fillMaxSize()
            .clip(RoundedCornerShape(30.dp))
            .border(
                BorderStroke(0.5.dp, CustomTheme.colors.stroke.secondary),
                RoundedCornerShape(30.dp)
            )
    )
}

private enum class DragAnchors {
    Top, Center, Bottom;

    companion object {
        @Composable
        fun createState(
            initialValue: DragAnchors,
            density: Float,
            extremePosition: Float,
            bottomCoefficient: Int,
        ) = remember {
            AnchoredDraggableState(
                initialValue = initialValue,
                positionalThreshold = { distance: Float -> distance * 0.7f },
                velocityThreshold = { 500f * density },
                animationSpec = tween(durationMillis = DRAG_ANIMATION_DURATION),
            ).apply {
                updateAnchors(
                    DraggableAnchors {
                        Top at -extremePosition
                        Center at 0f
                        Bottom at (extremePosition / bottomCoefficient)
                    }
                )
            }
        }
    }
}

private data class Stack(
    val current: CourseLessonContent?,
    val next: CourseLessonContent?,
    val afterNext: CourseLessonContent?,
) {
    companion object {
        fun create(data: List<CourseLessonContent>, currentIndex: Int) = Stack(
            current = data.getOrNull(currentIndex),
            next = data.getOrNull(currentIndex + 1),
            afterNext = data.getOrNull(currentIndex + 2)
        )
    }
}

@Preview(showSystemUi = true)
@Composable
private fun Preview() {
    AppTheme {
        CourseLessonUi(FakeCourseLessonComponent())
    }
}