package com.metacards.metacards.features.tutorial.data

import com.metacards.metacards.core.R
import com.metacards.metacards.core.preferences.models.TutorialState
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.tutorial.domain.MessageAlignment
import com.metacards.metacards.features.tutorial.domain.MessageContent
import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import com.metacards.metacards.features.tutorial.domain.TutorialMessageArrow
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import com.metacards.metacards.features.R as CoreR

private const val TUTORIAL_DELAY_MILLIS = 100L

class TutorialMessageServiceImpl(
    private val coroutineScope: CoroutineScope,
    private val tutorialRepository: TutorialRepository,
    private val analyticsService: AnalyticsService
) : TutorialMessageService {

    private val tutorialState: StateFlow<TutorialState> =
        tutorialRepository.tutorialStateFlow

    override val tutorialMessageFlow = MutableStateFlow<TutorialMessage?>(null)

    override val tutorialStepFlow: MutableStateFlow<TutorialStep> = MutableStateFlow(TutorialStep.START)

    override val tutorialCloseRequestFlow = MutableSharedFlow<Unit>(extraBufferCapacity = 1)

    override fun handleMainComponentTutorial() {
        coroutineScope.launch {
            tutorialState.collect {
                when (it) {
                    TutorialState.JOURNAL -> {
                        showTutorialJournal()
                    }

                    TutorialState.FINISH -> {
                        showTutorialFinish()
                    }

                    TutorialState.FORCE_FINISH -> {
                        showTutorialForceFinish()
                    }

                    else -> {
                        // Do nothing
                    }
                }
            }
        }
    }

    override fun handleHomeComponentTutorial() {
        coroutineScope.launch {
            tutorialState.collect {
                when (it) {
                    TutorialState.START -> {
                        showTutorialStart()
                    }

                    TutorialState.SELECT_CARD -> {
                        showTutorialDecks()
                    }

                    TutorialState.PREDEFINED -> {
                        showTutorialPredefined()
                    }

                    else -> {
                        // Do nothing
                    }
                }
            }
        }
    }

    override fun handleQuestionForLayoutComponentTutorial(componentScope: CoroutineScope) {
        componentScope.launch {
            tutorialState.collect {
                if (tutorialState.value == TutorialState.SELECT_CARD) {
                    showTutorialQuestion()
                }
            }
        }
    }

    override fun handleCardSelectComponentTutorial(
        componentScope: CoroutineScope,
    ) {
        componentScope.launch {
            tutorialState.collect {
                if (it == TutorialState.SELECT_CARD) {
                    showTutorialCardSelectTabs {
                        showTutorialCardSelect()
                    }
                }
            }
        }
    }

    override fun handleCardListComponentTutorial(componentScope: CoroutineScope) {
        componentScope.launch {
            tutorialState.collect {
                if (it == TutorialState.SELECT_CARD) {
                    showTutorialCardSelectFinish()
                }
            }
        }
    }

    override fun handleRecordDetailsComponentTutorial(componentScope: CoroutineScope) {
        componentScope.launch {
            tutorialState.collect {
                if (it == TutorialState.SELECT_CARD) {
                    showTutorialRecord()
                }
            }
        }
    }

    override fun processCloseDeckLayoutComponentTutorial() {
        if (tutorialState.value == TutorialState.SELECT_CARD) {
            showTutorialDecks()
        }
    }

    override fun processCloseQuestionForLayoutComponentTutorial() {
        if (tutorialState.value == TutorialState.SELECT_CARD) {
            showTutorialDecks()
            tutorialCloseRequestFlow.tryEmit(Unit)
        }
    }

    private fun showMessage(tutorialMessage: TutorialMessage) {
        tutorialMessageFlow.value = tutorialMessage
    }

    private fun dismissMessage() {
        tutorialMessageFlow.value = null
    }

    private fun postMessage(tutorialMessage: TutorialMessage) {
        coroutineScope.launch {
            showMessage(tutorialMessage.copy(content = null))
            delay(TUTORIAL_DELAY_MILLIS)
            showMessage(tutorialMessage)
        }
    }

    private fun showTutorialStart() {
        tutorialStepFlow.value = TutorialStep.START
        postMessage(
            TutorialMessage(
                content = MessageContent(
                    text = R.string.tutorial_start.strResDesc(),
                    iconRes = CoreR.drawable.logo_tutorial,
                    actionTitle = R.string.common_great.strResDesc(),
                    action = {
                        analyticsService.logEvent(AnalyticsEvent.TutorOkEvent)
                        tutorialRepository.updateTutorialState(TutorialState.SELECT_CARD)
                    }
                ),
                isOverlapEnabled = true,
                isDisableClicks = true
            )
        )
    }

    private fun showTutorialDecks() {
        tutorialStepFlow.value = TutorialStep.DECKS
        postMessage(
            TutorialMessage(
                content = MessageContent(
                    text = R.string.tutorial_decks.strResDesc(),
                ),
                alignment = MessageAlignment.BOTTOM,
                arrow = TutorialMessageArrow.BOTTOM,
                isOverlapEnabled = true
            )
        )
    }

    private fun showTutorialQuestion() {
        tutorialStepFlow.value = TutorialStep.QUESTION
        postMessage(
            TutorialMessage(
                content = MessageContent(
                    text = R.string.tutorial_question.strResDesc()
                ),
                alignment = MessageAlignment.BOTTOM,
                arrow = TutorialMessageArrow.BOTTOM,
                isOverlapEnabled = true
            )
        )
    }

    private fun showTutorialCardSelectTabs(onAction: () -> Unit) {
        tutorialStepFlow.value = TutorialStep.CARD_SELECT_TABS
        postMessage(
            TutorialMessage(
                content = MessageContent(
                    text = R.string.tutorial_card_select_tabs.strResDesc(),
                    actionTitle = R.string.common_great.strResDesc(),
                    action = onAction
                ),
                alignment = MessageAlignment.TOP,
                arrow = TutorialMessageArrow.TOP,
                isOverlapEnabled = true,
                isDisableClicks = true
            )
        )
    }

    private fun showTutorialCardSelect() {
        tutorialStepFlow.value = TutorialStep.CARD_SELECT
        postMessage(
            TutorialMessage(
                content = MessageContent(
                    text = R.string.tutorial_card_select.strResDesc()
                ),
                alignment = MessageAlignment.BOTTOM,
                arrow = TutorialMessageArrow.BOTTOM,
                isOverlapEnabled = true
            )
        )
    }

    private fun showTutorialCardSelectFinish() {
        tutorialStepFlow.value = TutorialStep.CARD_SELECT_FINISH
        postMessage(
            TutorialMessage(
                content = MessageContent(
                    text = R.string.tutorial_card_select_finish.strResDesc(),
                    actionTitle = R.string.common_great.strResDesc(),
                    action = {
                        dismissMessage()
                    }
                ),
                alignment = MessageAlignment.CENTER
            )
        )
    }

    private fun showTutorialRecord() {
        tutorialStepFlow.value = TutorialStep.CARD_SELECT_RECORD
        postMessage(
            TutorialMessage(
                content = MessageContent(
                    text = R.string.tutorial_record.strResDesc(),
                    actionTitle = R.string.common_great.strResDesc(),
                    action = {
                        dismissMessage()
                    }
                ),
                alignment = MessageAlignment.CENTER
            )
        )
    }

    private fun showTutorialJournal() {
        tutorialStepFlow.value = TutorialStep.JOURNAL
        postMessage(
            TutorialMessage(
                content = MessageContent(
                    text = R.string.tutorial_journal.strResDesc(),
                    actionTitle = R.string.common_great.strResDesc(),
                    action = {
                        tutorialRepository.updateTutorialState(TutorialState.PREDEFINED)
                    }
                ),
                alignment = MessageAlignment.BOTTOM_START,
                arrow = TutorialMessageArrow.BOTTOM,
                isOverlapEnabled = true,
                isDisableClicks = true
            )
        )
    }

    private fun showTutorialPredefined() {
        tutorialStepFlow.value = TutorialStep.PREDEFINED
        postMessage(
            TutorialMessage(
                content = MessageContent(
                    text = R.string.tutorial_predefined.strResDesc(),
                    actionTitle = R.string.common_great.strResDesc(),
                    action = {
                        tutorialRepository.updateTutorialState(TutorialState.FINISH)
                    }
                ),
                alignment = MessageAlignment.TOP,
                arrow = TutorialMessageArrow.TOP_LEFT,
                isOverlapEnabled = true,
                isDisableClicks = true
            )
        )
    }

    private fun showTutorialFinish() {
        tutorialStepFlow.value = TutorialStep.FINISH
        postMessage(
            TutorialMessage(
                content = MessageContent(
                    text = R.string.tutorial_finish.strResDesc(),
                    actionTitle = R.string.common_great.strResDesc(),
                    action = {
                        dismissMessage()
                        analyticsService.logEvent(AnalyticsEvent.TutorFinishEvent)
                        tutorialRepository.updateTutorialState(TutorialState.COMPLETED)
                    }
                ),
                alignment = MessageAlignment.BOTTOM_START,
                arrow = TutorialMessageArrow.BOTTOM_RIGHT,
                isOverlapEnabled = true,
                isDisableClicks = true
            )
        )
    }

    private fun showTutorialForceFinish() {
        tutorialStepFlow.value = TutorialStep.FINISH
        postMessage(
            TutorialMessage(
                content = MessageContent(
                    text = R.string.tutorial_force_finish.strResDesc(),
                    actionTitle = R.string.common_great.strResDesc(),
                    action = {
                        dismissMessage()
                        analyticsService.logEvent(AnalyticsEvent.TutorAfterFormOkEvent)
                        tutorialRepository.updateTutorialState(TutorialState.COMPLETED)
                    }
                ),
                alignment = MessageAlignment.BOTTOM_START,
                arrow = TutorialMessageArrow.BOTTOM_RIGHT,
                isOverlapEnabled = true,
                isDisableClicks = true
            )
        )
    }
}