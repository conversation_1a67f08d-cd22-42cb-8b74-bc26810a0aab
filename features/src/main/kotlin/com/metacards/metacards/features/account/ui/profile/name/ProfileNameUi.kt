package com.metacards.metacards.features.account.ui.profile.name

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.core.widget.text_field.MetaTextField
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.strResDesc
import com.metacards.metacards.core.R as CoreR

@Composable
fun ProfileNameUi(
    component: ProfileNameComponent,
    modifier: Modifier = Modifier,
) {
    val saveButtonState by component.saveButtonState.collectAsState()

    BoxWithFade(
        modifier = modifier.navigationBarsPadding(),
        listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        Column(modifier = Modifier.fillMaxWidth()) {
            TopNavigationBar(
                title = R.string.account_profile_edit_title.strResDesc(),
                leadingIcon = { BackNavigationItem() },
                modifier = Modifier.padding(bottom = 44.dp)
            )

            MetaTextField(
                inputControl = component.inputControl,
                headerMessage = {
                    Text(
                        text = R.string.account_profile_edit_name_title.strResDesc()
                            .localizedByLocal(),
                        color = CustomTheme.colors.text.primary,
                        style = CustomTheme.typography.caption.large,
                        modifier = Modifier.padding(bottom = 24.dp)
                    )
                },
                footerMessage = {
                    Text(
                        text = R.string.account_profile_edit_name_footer.strResDesc()
                            .localizedByLocal(),
                        color = CustomTheme.colors.text.secondary,
                        style = CustomTheme.typography.caption.small,
                        modifier = Modifier
                            .padding(top = 8.dp)
                            .padding(horizontal = 16.dp)
                    )
                },
                placeholder = R.string.account_profile_edit_name_placeholder.strResDesc()
                    .localizedByLocal(),
                modifier = Modifier.padding(horizontal = 16.dp)
            )
        }

        MetaAccentButton(
            state = saveButtonState,
            text = CoreR.string.common_save.strResDesc().localizedByLocal(),
            onClick = component::onSaveClick,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .padding(bottom = 24.dp)
                .align(Alignment.BottomCenter)
        )
    }
}

@Preview
@Composable
fun ProfileNameUiPreview() {
    AppTheme {
        ProfileNameUi(component = FakeProfileNameComponent())
    }
}