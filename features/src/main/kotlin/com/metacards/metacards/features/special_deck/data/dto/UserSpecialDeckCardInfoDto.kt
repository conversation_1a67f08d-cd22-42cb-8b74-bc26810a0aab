package com.metacards.metacards.features.special_deck.data.dto

import com.google.firebase.Timestamp
import com.google.firebase.firestore.ServerTimestamp
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.special_deck.domain.entity.UserSpecialDeckCardInfo

data class UserSpecialDeckCardInfoDto(
    val cardId: String = "",
    @ServerTimestamp
    val gettingDate: Timestamp = Timestamp.now()
) {
    fun toDomain() = UserSpecialDeckCardInfo(
        cardId = CardId(cardId),
        gettingDate = gettingDate.toDate()
    )

    companion object {
        fun fromDomain(entity: UserSpecialDeckCardInfo) = UserSpecialDeckCardInfoDto(
            cardId = entity.cardId.value,
            gettingDate = Timestamp(entity.gettingDate)
        )
    }
}