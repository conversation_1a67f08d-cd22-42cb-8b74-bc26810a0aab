package com.metacards.metacards.features.special_deck.ui.swipe_tutorial

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun SpecialDeckSwipeTutorialUi(
    component: SpecialDeckSwipeTutorialComponent,
    modifier: Modifier = Modifier
) {

    Surface(
        shape = RoundedCornerShape(16.dp),
        color = CustomTheme.colors.background.modal,
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
            .padding(bottom = 8.dp)
    ) {
        Column(
            modifier = modifier
                .padding(
                    horizontal = 24.dp,
                    vertical = 40.dp
                ),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_special_deck_swipe_tutorial_phone),
                contentDescription = null,
                modifier = Modifier
                    .height(230.dp)
                    .padding(bottom = 24.dp)
            )

            Text(
                text = R.string.special_deck_swipe_tutorial_title.strResDesc().localizedByLocal(),
                style = CustomTheme.typography.heading.medium,
                color = CustomTheme.colors.text.primary,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            Text(
                text = R.string.special_deck_swipe_tutorial_description.strResDesc()
                    .localizedByLocal(),
                style = CustomTheme.typography.body.primary,
                color = CustomTheme.colors.text.primary,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            MetaAccentButton(
                text = R.string.special_deck_swipe_tutorial_button_wow
                    .strResDesc()
                    .localizedByLocal(),
                onClick = component::onWowClick,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Preview
@Composable
private fun SpecialDeckSwipeTutorialPreview() {
    AppTheme {
        SpecialDeckSwipeTutorialUi(FakeSpecialDeckSwipeTutorialComponent())
    }
}