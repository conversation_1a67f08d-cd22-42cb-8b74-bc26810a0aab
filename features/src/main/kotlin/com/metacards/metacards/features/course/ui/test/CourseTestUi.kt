@file:OptIn(ExperimentalFoundationApi::class, ExperimentalFoundationApi::class)

package com.metacards.metacards.features.course.ui.test

import androidx.activity.compose.BackHandler
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.AnchoredDraggableState
import androidx.compose.foundation.gestures.DraggableAnchors
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.anchoredDraggable
import androidx.compose.foundation.gestures.animateTo
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.gestures.snapTo
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsBottomHeight
import androidx.compose.foundation.layout.windowInsetsTopHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.metacards.metacards.core.dialog.default_component.DefaultDialog
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.navigation_bar.IconNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.course.domain.entity.CourseQuestion
import com.metacards.metacards.features.course.domain.entity.CourseTest
import com.metacards.metacards.features.course.ui.widgets.CourseLoaderSkeleton
import com.metacards.metacards.features.deck.ui.MetaCardDefaults
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlin.math.abs
import kotlin.math.roundToInt

@Composable
fun CourseTestUi(
    component: CourseTestComponent,
) {
    val test by component.test.collectAsState()
    val shouldShowTutorial by component.shouldShowTutorial.collectAsState()
    test?.let {
        Content(
            test = it,
            courseName = component.courseName,
            onQuestionAnswered = component::onQuestionAnswered,
            onRevertClick = component::onRevertClick,
            onLastQuestionAnswered = component::onLastQuestionAnswered,
            onCloseClick = component::onCloseClick,
            onUserInteracted = component::onUserInteracted,
        )
        if (shouldShowTutorial) {
            TutorialPopUp(component::onCloseTutorialClick)
        }
    } ?: CourseLoaderSkeleton()

    DefaultDialog(component.closeDialogControl)

    BackHandler(onBack = component::onCloseClick)
}

@Composable
private fun TutorialPopUp(
    onCloseClick: () -> Unit,
) {
    val localConfiguration = LocalConfiguration.current
    val paddingTop = remember { localConfiguration.screenHeightDp * 0.54 }
    Popup(
        properties = PopupProperties(
            focusable = true,
            dismissOnBackPress = false,
            dismissOnClickOutside = false,
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    brush = Brush.linearGradient(
                        CustomTheme.colors.gradient.backgroundList.map { it.copy(alpha = 0.7f) }
                    )
                )
                .clickable(onClick = onCloseClick)
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_course_test_tutorial),
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(top = paddingTop.dp)
            )

            Box(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(16.dp)
                    .size(32.dp)
                    .clip(CircleShape)
                    .background(CustomTheme.colors.button.secondary)
                    .clickable(onClick = onCloseClick),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_24_close),
                    contentDescription = "close",
                    tint = CustomTheme.colors.icons.primary,
                    modifier = Modifier
                        .size(18.dp)
                )
            }
        }
    }
}

private const val DRAG_ANIMATION_DURATION = 500
private const val HORIZONTAL_DRUG_EXTREME_POSITION = 1500f
private const val VERTICAL_DRUG_EXTREME_POSITION = -2500f
private const val CARD_HORIZONTAL_GAP = 16
private const val CARD_VERTICAL_GAP = 12

@Composable
private fun Content(
    test: CourseTest,
    courseName: LocalizableString?,
    onQuestionAnswered: (CourseTestDragAnchors, CourseQuestion) -> Unit,
    onRevertClick: (CourseQuestion) -> Unit,
    onLastQuestionAnswered: () -> Unit,
    onCloseClick: () -> Unit,
    onUserInteracted: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val localDensity = LocalDensity.current
    val scope = rememberCoroutineScope()

    val dragState = remember {
        AnchoredDraggableState(
            initialValue = CourseTestDragAnchors.Center,
            positionalThreshold = { distance: Float -> distance * 0.7f },
            velocityThreshold = { with(localDensity) { 500.dp.toPx() } },
            animationSpec = tween(DRAG_ANIMATION_DURATION)
        ).apply {
            updateAnchors(
                DraggableAnchors {
                    CourseTestDragAnchors.Left at -HORIZONTAL_DRUG_EXTREME_POSITION
                    CourseTestDragAnchors.Center at 0f
                    CourseTestDragAnchors.Right at HORIZONTAL_DRUG_EXTREME_POSITION
                }
            )
        }
    }

    val prevDragState = remember {
        AnchoredDraggableState(
            initialValue = CourseTestPrevDragAnchors.Top,
            positionalThreshold = { distance: Float -> distance * 0.7f },
            velocityThreshold = { with(localDensity) { 500.dp.toPx() } },
            animationSpec = tween(durationMillis = DRAG_ANIMATION_DURATION)
        ).apply {
            updateAnchors(
                DraggableAnchors {
                    CourseTestPrevDragAnchors.Top at VERTICAL_DRUG_EXTREME_POSITION
                    CourseTestPrevDragAnchors.Center at 0f
                }
            )
        }
    }
    LaunchedEffect(Unit) {
        snapshotFlow { dragState.offset }.onEach { onUserInteracted() }.launchIn(this)
    }

    var currentIndex by remember { mutableIntStateOf(0) }
    val stack by remember { derivedStateOf { Stack.create(test.questions, currentIndex) } }
    var topContentHeight by remember { mutableStateOf(0.dp) }
    var bottomContentHeight by remember { mutableStateOf(0.dp) }
    var bottomButtonsEnable by remember { mutableStateOf(true) }

    LaunchedEffect(Unit) {
        snapshotFlow { dragState.currentValue }.onEach { value ->
            if (value == CourseTestDragAnchors.Left || value == CourseTestDragAnchors.Right) {
                var lastAnswered = false
                stack.current?.let { onQuestionAnswered(value, it) }
                if (currentIndex < test.questions.lastIndex) currentIndex++ else lastAnswered = true
                if (lastAnswered) {
                    onLastQuestionAnswered()
                } else {
                    dragState.snapTo(CourseTestDragAnchors.Center)
                    bottomButtonsEnable = true
                }
            }
        }.launchIn(this)
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .pointerInput(Unit) { detectTapGestures(onPress = { onUserInteracted() }) }
    ) {
        TopContent(
            currentIndex = currentIndex,
            cardsCount = test.questions.size,
            title = courseName ?: test.name,
            onCloseClick = onCloseClick,
            modifier = Modifier
                .onSizeChanged { with(localDensity) { topContentHeight = it.height.toDp() } }
        )

        BottomContent(
            onNoClick = {
                scope.launch {
                    bottomButtonsEnable = false
                    dragState.animateTo(CourseTestDragAnchors.Left)
                }
            },
            onYesClick = {
                scope.launch {
                    bottomButtonsEnable = false
                    dragState.animateTo(CourseTestDragAnchors.Right)
                }
            },
            onPreviousClick = {
                scope.launch {
                    if (currentIndex < 0) return@launch
                    bottomButtonsEnable = false
                    prevDragState.animateTo(CourseTestPrevDragAnchors.Center)
                    stack.previous?.let(onRevertClick)
                    currentIndex--
                    prevDragState.snapTo(CourseTestPrevDragAnchors.Top)
                    bottomButtonsEnable = true
                }
            },
            buttonsEnable = bottomButtonsEnable,
            shouldShowReturnButton = currentIndex > 0,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .onSizeChanged { with(localDensity) { bottomContentHeight = it.height.toDp() } }
        )

        Cards(
            dragState = dragState,
            prevDragState = prevDragState,
            currentCardStack = stack,
            modifier = Modifier
                .fillMaxSize()
                .padding(top = topContentHeight, bottom = bottomContentHeight)
        )
    }
}

@Composable
private fun BottomContent(
    onNoClick: () -> Unit,
    onYesClick: () -> Unit,
    onPreviousClick: () -> Unit,
    buttonsEnable: Boolean,
    shouldShowReturnButton: Boolean,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
    ) {
        Spacer(modifier = Modifier.height(54.dp))
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
        ) {

            TestButton(
                onClick = onNoClick,
                enable = buttonsEnable,
                text = R.string.course_test_button_no.strResDesc().localizedByLocal()
            )

            Spacer(modifier = Modifier.width(16.dp))

            IconButton(
                onClick = onPreviousClick,
                enabled = shouldShowReturnButton && buttonsEnable,
                modifier = Modifier
                    .alpha(if (shouldShowReturnButton) 1f else 0f)
            ) {
                Icon(
                    painter = painterResource(R.drawable.ic_32_repeat),
                    contentDescription = "repeat",
                    tint = CustomTheme.colors.icons.primary,
                    modifier = Modifier.clip(CircleShape)
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            TestButton(
                onClick = onYesClick,
                enable = buttonsEnable,
                text = R.string.course_test_button_yes.strResDesc().localizedByLocal(),
                bgColor = CustomTheme.colors.button.secondary
            )
        }

        Spacer(modifier = Modifier.height(24.dp))

        Spacer(Modifier.windowInsetsBottomHeight(WindowInsets.navigationBars))
    }
}

@Composable
private fun RowScope.TestButton(
    onClick: () -> Unit,
    enable: Boolean,
    text: String,
    bgColor: Color = CustomTheme.colors.button.secondary,
) {
    Box(
        modifier = Modifier
            .weight(1f)
            .clip(RoundedCornerShape(16.dp))
            .background(bgColor)
            .clickable(enabled = enable, onClick = onClick),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            style = CustomTheme.typography.button.primary,
            color = CustomTheme.colors.text.primary,
            modifier = Modifier
                .padding(vertical = 16.dp)
        )
    }
}

@Composable
private fun TopContent(
    currentIndex: Int,
    cardsCount: Int,
    title: LocalizableString,
    onCloseClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
    ) {

        Spacer(Modifier.windowInsetsTopHeight(WindowInsets.statusBars))

        TopNavigationBar(
            modifier = Modifier.fillMaxWidth(),
            title = title,
            leadingIcon = {
                IconNavigationItem(
                    iconRes = R.drawable.ic_24_close,
                    onClick = onCloseClick
                )
            },
        )

        Spacer(modifier = Modifier.height(8.dp))

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            horizontalArrangement = Arrangement.spacedBy(if (cardsCount > 6) 1.5.dp else 4.dp)
        ) {
            repeat(cardsCount) { index ->
                val bgColor by animateColorAsState(
                    targetValue = if (index > currentIndex) {
                        CustomTheme.colors.button.secondary
                    } else {
                        CustomTheme.colors.stroke.primary
                    },
                    label = "color"
                )
                Spacer(
                    modifier = Modifier
                        .height(8.dp)
                        .weight(1f)
                        .background(bgColor, RoundedCornerShape(16.dp))
                )
            }
        }

        Spacer(modifier = Modifier.height(20.dp))
    }
}

@Composable
private fun Cards(
    dragState: AnchoredDraggableState<CourseTestDragAnchors>,
    prevDragState: AnchoredDraggableState<CourseTestPrevDragAnchors>,
    currentCardStack: Stack,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center
    ) {
        val dragPaddingCoefficient =
            remember { HORIZONTAL_DRUG_EXTREME_POSITION / CARD_HORIZONTAL_GAP }

        val dragExtraPadding by remember {
            derivedStateOf { abs(dragState.offset.div(dragPaddingCoefficient)) }
        }

        val afterNextOffsetY by remember {
            derivedStateOf {
                ((CARD_VERTICAL_GAP * 2) - dragExtraPadding).coerceIn(
                    CARD_VERTICAL_GAP.toFloat(),
                    CARD_VERTICAL_GAP * 2f
                )
            }
        }

        val afterNextPadding by remember {
            derivedStateOf {
                ((CARD_HORIZONTAL_GAP * 2) - dragExtraPadding).takeIf { it > 0 } ?: 0f
            }
        }

        val afterNextPaddingAnim by animateDpAsState(
            targetValue = afterNextPadding.dp,
            label = "afterNextPaddingAnim"
        )

        currentCardStack.afterNext?.let { card ->
            Card(
                state = dragState,
                isDraggable = false,
                card = card,
                offsetY = afterNextOffsetY.dp,
                paddingHorizontal = afterNextPaddingAnim,
            )
        }

        val nextCardHorizontalPadding by remember {
            derivedStateOf { (CARD_HORIZONTAL_GAP - dragExtraPadding).takeIf { it > 0 } ?: 0f }
        }

        val nextOffsetY by remember {
            derivedStateOf {
                (CARD_VERTICAL_GAP - dragExtraPadding).coerceIn(
                    0f,
                    CARD_VERTICAL_GAP.toFloat()
                )
            }
        }

        val nextPaddingAnim by animateDpAsState(
            targetValue = nextCardHorizontalPadding.dp,
            label = "afterNextPaddingAnim"
        )

        currentCardStack.next?.let { card ->
            Card(
                state = dragState,
                isDraggable = false,
                card = card,
                offsetY = nextOffsetY.dp,
                paddingHorizontal = nextPaddingAnim,
            )
        }

        currentCardStack.current?.let { card ->
            Card(
                state = dragState,
                isDraggable = true,
                card = card,
            )
        }

        currentCardStack.previous?.let { card ->
            PrevCard(
                state = prevDragState,
                card = card,
            )
        }
    }
}

@Composable
private fun Card(
    state: AnchoredDraggableState<CourseTestDragAnchors>,
    isDraggable: Boolean,
    card: CourseQuestion,
    modifier: Modifier = Modifier,
    offsetY: Dp = 0.dp,
    paddingHorizontal: Dp = 0.dp,
) {
    val draggableModifier = if (isDraggable) {
        Modifier
            .offset {
                IntOffset(
                    y = 0,
                    x = state
                        .requireOffset()
                        .roundToInt(),
                )
            }
            .anchoredDraggable(state, Orientation.Horizontal)
    } else {
        Modifier
    }
    Box(
        modifier = modifier
            .then(draggableModifier)
            .offset {
                IntOffset(
                    x = 0,
                    y = offsetY
                        .toPx()
                        .roundToInt()
                )
            }
            .fillMaxHeight()
            .aspectRatio(MetaCardDefaults.aspectRatio)
            .padding(horizontal = paddingHorizontal)
            .background(CustomTheme.colors.background.placeholder, RoundedCornerShape(16.dp))
            .border(
                BorderStroke(0.5.dp, CustomTheme.colors.stroke.secondary),
                RoundedCornerShape(16.dp)
            ),
        contentAlignment = Alignment.Center
    ) {
        card.image?.let {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(it)
                    .crossfade(MetaCardDefaults.CROSSFADE_DURATION_MILLIS)
                    .build(),
                contentScale = ContentScale.Crop,
                contentDescription = null,
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(16.dp))
            )
        }
        Text(
            text = card.name.localizedByLocal(),
            style = CustomTheme.typography.heading.additional,
            color = CustomTheme.colors.text.primary,
            textAlign = TextAlign.Center,
        )
    }
}

@Composable
private fun PrevCard(
    state: AnchoredDraggableState<CourseTestPrevDragAnchors>,
    card: CourseQuestion,
    modifier: Modifier = Modifier,
    offsetY: Dp = 0.dp,
    paddingHorizontal: Dp = 0.dp,
) {
    Box(
        modifier = modifier
            .offset {
                IntOffset(
                    x = 0,
                    y = state
                        .requireOffset()
                        .roundToInt(),
                )
            }
            .anchoredDraggable(state, Orientation.Vertical)
            .fillMaxHeight()
            .aspectRatio(MetaCardDefaults.aspectRatio)
            .offset(y = offsetY)
            .padding(horizontal = paddingHorizontal)
            .background(CustomTheme.colors.background.placeholder, RoundedCornerShape(16.dp))
            .border(
                BorderStroke(0.5.dp, CustomTheme.colors.stroke.secondary),
                RoundedCornerShape(16.dp)
            ),
        contentAlignment = Alignment.Center
    ) {
        card.image?.let {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(it)
                    .crossfade(MetaCardDefaults.CROSSFADE_DURATION_MILLIS)
                    .build(),
                contentScale = ContentScale.Crop,
                contentDescription = null,
                modifier = Modifier
                    .fillMaxSize()
                    .clip(RoundedCornerShape(16.dp))
            )
        }
        Text(
            text = card.name.localizedByLocal(),
            style = CustomTheme.typography.heading.additional,
            color = CustomTheme.colors.text.primary,
            textAlign = TextAlign.Center,
        )
    }
}

private data class Stack(
    val previous: CourseQuestion?,
    val current: CourseQuestion?,
    val next: CourseQuestion?,
    val afterNext: CourseQuestion?,
) {
    companion object {
        fun create(data: List<CourseQuestion>, currentIndex: Int) = Stack(
            previous = data.getOrNull(currentIndex - 1),
            current = data.getOrNull(currentIndex),
            next = data.getOrNull(currentIndex + 1),
            afterNext = data.getOrNull(currentIndex + 2)
        )
    }
}

@Preview(showSystemUi = true)
@Composable
private fun Preview() {
    AppTheme {
        CourseTestUi(FakeCourseTestComponent())
    }
}