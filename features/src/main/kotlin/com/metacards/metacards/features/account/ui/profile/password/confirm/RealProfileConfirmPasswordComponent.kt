package com.metacards.metacards.features.account.ui.profile.password.confirm

import com.ark<PERSON><PERSON>.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.doOnResume
import com.google.firebase.auth.FirebaseAuthException
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.utils.PASSWORD_MIN_LENGTH
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.passwordInput
import com.metacards.metacards.features.R
import com.metacards.metacards.features.auth.domain.TryPasswordInteractor
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.MutableStateFlow
import ru.mobileup.kmm_form_validation.control.InputControl
import ru.mobileup.kmm_form_validation.options.ImeAction
import ru.mobileup.kmm_form_validation.validation.control.isNotBlank
import ru.mobileup.kmm_form_validation.validation.control.minLength
import ru.mobileup.kmm_form_validation.validation.form.RevalidateOnValueChanged
import ru.mobileup.kmm_form_validation.validation.form.SetFocusOnFirstInvalidControlAfterValidation
import ru.mobileup.kmm_form_validation.validation.form.ValidateOnFocusLost
import ru.mobileup.kmm_form_validation.validation.form.dynamicValidationResult
import ru.mobileup.kmm_form_validation.validation.form.formValidator

class RealProfileConfirmPasswordComponent(
    componentContext: ComponentContext,
    private val errorHandler: ErrorHandler,
    override val toolbarText: StringDesc,
    override val textFieldHeader: StringDesc,
    override val textFieldCaption: StringDesc?,
    override val bottomButtonText: StringDesc,
    private val tryPasswordInteractor: TryPasswordInteractor,
    private val onOutput: (ProfileConfirmPasswordComponent.Output) -> Unit
) : ComponentContext by componentContext, ProfileConfirmPasswordComponent {

    private val debounce = Debounce()

    init {
        lifecycle.doOnResume {
            componentScope.safeLaunch(errorHandler) {
                inputControl.text.collect {
                    proceedResultButtonState.value = null
                }
            }
        }
    }

    override val inputControl: InputControl = passwordInput(componentScope, ImeAction.Done)

    private val formValidator = componentScope.formValidator {
        features = listOf(
            ValidateOnFocusLost,
            RevalidateOnValueChanged,
            SetFocusOnFirstInvalidControlAfterValidation
        )

        input(inputControl) {
            isNotBlank(StringDesc.Resource(R.string.password_enter_error))
            minLength(PASSWORD_MIN_LENGTH, StringDesc.Resource(R.string.password_enter_error))
        }
    }

    private val dynamicResult = componentScope.dynamicValidationResult(formValidator)

    private val proceedResultButtonState: MutableStateFlow<ButtonState?> = MutableStateFlow(null)

    override val proceedButtonState =
        computed(dynamicResult, proceedResultButtonState) { validationResult, proceedResult ->
            proceedResult ?: if (validationResult.isValid) {
                ButtonState.Enabled
            } else {
                ButtonState.Disabled
            }
        }

    override fun onProceedClick() {
        DebounceClick(debounce, "onProfileEditPasswordProceedClick", errorHandler) {
            try {
                proceedResultButtonState.value = ButtonState.Loading
                tryPasswordInteractor.execute(
                    password = inputControl.text.value,
                )

                onOutput(ProfileConfirmPasswordComponent.Output.PasswordConfirmed(inputControl.text.value))
            } catch (_: FirebaseAuthException) {
                inputControl.error.value =
                    StringDesc.Resource(R.string.account_profile_edit_password_error_wrong_password)
                proceedResultButtonState.value = ButtonState.Disabled
            }
        }
    }
}