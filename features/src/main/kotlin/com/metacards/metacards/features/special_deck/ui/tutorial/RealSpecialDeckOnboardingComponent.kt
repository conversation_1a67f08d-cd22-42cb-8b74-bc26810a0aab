package com.metacards.metacards.features.special_deck.ui.tutorial

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.localization.ui.LocalizableString

class RealSpecialDeckOnboardingComponent(
    componentContext: ComponentContext,
    override val deckTitle: LocalizableString,
    private val onOutput: (SpecialDeckOnboardingComponent.Output) -> Unit
) : ComponentContext by componentContext, SpecialDeckOnboardingComponent {

    override fun onStartClick() {
        onOutput(SpecialDeckOnboardingComponent.Output.OnboardingCompleted)
    }
}