package com.metacards.metacards.features.auth.ui.auth_type.email.message

import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.FakeDialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.FakeDefaultDialogComponent
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.desc
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakeSentMessageToEmailComponent : SentMessageToEmailComponent {
    override val title: StateFlow<StringDesc> = MutableStateFlow("Title".desc())
    override val text: StateFlow<StringDesc> = MutableStateFlow("Some long text".desc())
    override val dismissDialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        FakeDialogControl(FakeDefaultDialogComponent())

    override fun onCloseButtonClick() = Unit
}