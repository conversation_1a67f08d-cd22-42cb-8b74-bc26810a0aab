package com.metacards.metacards.features.payments.data

import com.metacards.metacards.core.functions.CloudFunctionsService
import com.metacards.metacards.core.functions.FunctionResult
import com.metacards.metacards.core.utils.zonedDateTimeMoscow
import com.metacards.metacards.features.payments.domain.PaymentRepository
import firebase.com.protolitewrapper.BuildConfig

class PaymentRepositoryImpl(
    private val cloudFunctionsService: CloudFunctionsService
) : PaymentRepository {

    override suspend fun makePaymentForDeck(deckId: String): FunctionResult<String> {
        val data = mapOf("deckId" to deckId)
        return makePayment(data)
    }

    override suspend fun makePaymentForSubscription(subscriptionId: String): FunctionResult<String> {
        val data = mapOf("subscriptionId" to subscriptionId)
        return makePayment(data)
    }

    private suspend fun makePayment(params: Map<String, String>): FunctionResult<String> {
        val data = params + mapOf(
            "appVersion" to BuildConfig.VERSION_NAME,
            "buildNumber" to BuildConfig.VERSION_CODE.toString(),
            "timestamb" to zonedDateTimeMoscow()
        )

        return cloudFunctionsService.call("makePayment", data)
    }
}