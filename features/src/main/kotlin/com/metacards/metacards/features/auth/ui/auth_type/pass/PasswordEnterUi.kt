package com.metacards.metacards.features.auth.ui.auth_type.pass

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.text_field.MetaTextField
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.delay
import ru.mobileup.kmm_form_validation.toCompose

@Composable
fun PasswordEnterUi(component: PasswordEnterComponent) {
    val enterButtonState by component.enterButtonState.collectAsState()

    // Workaround to fix a weird bug on Samsung devices where pasting text via the keyboard
    // makes it impossible to delete the pasted content without this delay
    LaunchedEffect(Unit) {
        delay(350)
        component.onFocusRequest()
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(bottom = 24.dp, top = 8.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceBetween
    ) {

        Column {
            var showPass by remember { mutableStateOf(false) }

            Text(
                text = R.string.password_enter_title.strResDesc().localizedByLocal(),
                style = CustomTheme.typography.caption.large,
                color = CustomTheme.colors.text.primary,
                modifier = Modifier.padding(top = 60.dp)
            )

            MetaTextField(
                modifier = Modifier.padding(top = 24.dp, bottom = 8.dp),
                inputControl = component.passwordInputControl,
                placeholder = R.string.password_create_placeholder.strResDesc().localizedByLocal(),
                trailingIcon = { TextVisibilityIcon(showPass) { showPass = !showPass } },
                visualTransformation = if (!showPass) component.passwordInputControl.visualTransformation.toCompose() else VisualTransformation.None,
            )

            Text(
                modifier = Modifier
                    .padding(start = 16.dp)
                    .clickable(onClick = component::onResetPasswordButtonClick),
                text = R.string.password_enter_forget_password.strResDesc().localizedByLocal(),
                color = CustomTheme.colors.text.caption,
                style = CustomTheme.typography.caption.small,
            )
        }

        MetaAccentButton(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 4.dp),
            text = R.string.password_enter_signin.strResDesc().localizedByLocal(),
            onClick = component::onEnterButtonClick,
            state = enterButtonState
        )
    }
}

@Composable
fun TextVisibilityIcon(showPass: Boolean, onClick: () -> Unit) {
    val icon = if (showPass) R.drawable.ic_24_visible else R.drawable.ic_24_invsible

    Icon(
        modifier = Modifier
            .padding(start = 8.dp)
            .clickable(false, onClick = onClick),
        tint = CustomTheme.colors.icons.primary,
        painter = painterResource(id = icon),
        contentDescription = null
    )
}

@Preview
@Composable
fun PasswordEnterUiPreview() {
    val scope = rememberCoroutineScope()

    AppTheme {
        PasswordEnterUi(component = FakePasswordEnterComponent(scope))
    }
}