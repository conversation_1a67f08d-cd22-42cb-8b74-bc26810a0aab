package com.metacards.metacards.features.course.ui.test_result

import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.FakeDialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.FakeDefaultDialogComponent
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.course.domain.entity.CourseResult
import com.metacards.metacards.features.layout.domain.LayoutId
import com.metacards.metacards.features.layout.domain.PredefinedLayoutWithAvailable
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakeCourseTestResultComponent : CourseTestResultComponent {

    override val courseName = null
    override val isCourseResultSaved = MutableStateFlow(false)
    override val courseResult: CourseResult = CourseResult.mock
    override val predefinedLayouts = MutableStateFlow<List<PredefinedLayoutWithAvailable>>(emptyList())
    override val bottomButtonText: String = ""
    override val isShareInProgress: StateFlow<Boolean> = MutableStateFlow(false)
    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        FakeDialogControl(FakeDefaultDialogComponent())
    override val title: LocalizableString = LocalizableString.createNonLocalizable("")

    override fun onGoForwardClick() = Unit
    override fun onCloseClick() = Unit
    override fun onShareClick() = Unit
    override fun onVideoFullScreenClick(position: Long, videoUrl: String) = Unit
    override fun onLayoutClick(layoutId: LayoutId) = Unit
    override fun onBlockContentClick() = Unit
}