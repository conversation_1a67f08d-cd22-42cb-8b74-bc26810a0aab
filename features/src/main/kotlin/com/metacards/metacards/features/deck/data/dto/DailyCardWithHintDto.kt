package com.metacards.metacards.features.deck.data.dto

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.deck.domain.entity.CardHint
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.deck.domain.entity.DailyCard
import kotlinx.serialization.Serializable

@Serializable
data class DailyCardWithHintDto(
    val cardId: String,
    val imageUrl: String,
    val hint: Map<String, String?>,
    val gifUrl: String?,
)

fun DailyCardWithHintDto.toDomain(): DailyCard {
    return DailyCard(
        cardId = CardId(cardId),
        imageUrl = imageUrl,
        hint = CardHint(LocalizableString(hint)),
        gifUrl = gifUrl
    )
}
