package com.metacards.metacards.features.splash.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.animateLottieCompositionAsState
import com.airbnb.lottie.compose.rememberLottieComposition
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.features.R

@Composable
fun SplashUi(
    component: SplashComponent,
    modifier: Modifier = Modifier
) {
    BoxWithFade(
        modifier = modifier
            .fillMaxSize()
            .background(CustomTheme.colors.background.primary),
        contentAlignment = Alignment.Center,
        listOfColors = CustomTheme.colors.gradient.backgroundList,
    ) {
        val composition by rememberLottieComposition(LottieCompositionSpec.RawRes(R.raw.anim_logo_step1))
        val progress by animateLottieCompositionAsState(composition)
        val isFinished by remember { derivedStateOf { progress == 1f } }
        LaunchedEffect(isFinished) {
            if (isFinished) component.onAnimationEnd()
        }

        LottieAnimation(
            composition = composition,
            progress = { progress },
            alignment = Alignment.Center,
            contentScale = ContentScale.None,
            modifier = Modifier
                .offset(y = (-120).dp)
        )
    }
}

@Preview
@Composable
fun SplashUiPreview() {
    AppTheme {
        SplashUi(component = FakeSplashComponent())
    }
}