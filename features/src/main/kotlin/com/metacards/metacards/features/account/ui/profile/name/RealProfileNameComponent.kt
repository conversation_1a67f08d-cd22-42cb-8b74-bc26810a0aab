package com.metacards.metacards.features.account.ui.profile.name

import com.ark<PERSON>nov.decompose.ComponentContext
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.user.domain.UserProvider
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.features.user.domain.UpdateUserInteractor
import kotlinx.coroutines.flow.MutableStateFlow
import ru.mobileup.kmm_form_validation.control.InputControl
import ru.mobileup.kmm_form_validation.options.KeyboardCapitalization
import ru.mobileup.kmm_form_validation.options.KeyboardOptions

class RealProfileNameComponent(
    componentContext: ComponentContext,
    override val initName: String,
    private val userProvider: UserProvider,
    private val updateUserInteractor: UpdateUserInteractor,
    private val onOutput: (ProfileNameComponent.Output) -> Unit
) : ComponentContext by componentContext, ProfileNameComponent {

    companion object {
        private const val NAME_SYMBOL_COUNT_MIN = 2
        private const val NAME_SYMBOL_COUNT_MAX = 30
    }

    private val debounce = Debounce()

    override val inputControl: InputControl = InputControl(
        coroutineScope = componentScope,
        singleLine = true,
        keyboardOptions = KeyboardOptions(KeyboardCapitalization.Words),
        initialText = initName
    ).apply { requestFocus() }

    override val saveButtonState: MutableStateFlow<ButtonState> = computed(inputControl.text) { text ->
        if (
            text.trim().length in NAME_SYMBOL_COUNT_MIN..NAME_SYMBOL_COUNT_MAX &&
                    text.isNotBlank()
        ) {
            ButtonState.Enabled
        } else {
            ButtonState.Disabled
        }
    } as MutableStateFlow<ButtonState>

    override fun onSaveClick() {
        DebounceClick(debounce, "onSaveNameClick") {
            saveButtonState.value = ButtonState.Loading
            val user = userProvider.getUser().value
            val name = inputControl.text.value.trim()
            user?.let {
                updateUserInteractor.updateName(
                    it.userId,
                    name
                )
            }

            onOutput(ProfileNameComponent.Output.DismissRequested)
        }
    }
}