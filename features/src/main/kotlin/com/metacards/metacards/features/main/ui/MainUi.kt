package com.metacards.metacards.features.main.ui

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.RoundRect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.ClipOp
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.clipPath
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.dp
import com.arkivanov.decompose.extensions.compose.jetpack.stack.Children
import com.metacards.metacards.core.message.ui.noOverlapByMessage
import com.metacards.metacards.core.preferences.models.TutorialState
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.WidgetPosition
import com.metacards.metacards.core.widget.navigation_bar.BottomNavigationBar
import com.metacards.metacards.core.widget.navigation_bar.NavigationItem
import com.metacards.metacards.core.widget.navigation_bar.NavigationPage
import com.metacards.metacards.features.account.ui.AccountUi
import com.metacards.metacards.features.home.ui.HomeUi
import com.metacards.metacards.features.main.ui.widget.ShakerPopup
import com.metacards.metacards.features.record.ui.journal.JournalUi
import com.metacards.metacards.features.showcase.ui.ShowcaseUi
import com.metacards.metacards.features.tutorial.data.TutorialStep
import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import com.metacards.metacards.features.tutorial.ui.MessagePopupContent

@Composable
fun MainUi(
    component: MainComponent,
    modifier: Modifier = Modifier,
) {
    val childStack by component.childStack.collectAsState()
    val navigationItems by component.navigationItems.collectAsState()
    val tutorialState by component.tutorialState.collectAsState()
    val tutorialStep by component.tutorialStep.collectAsState()
    val tutorialMessage by component.tutorialMessage.collectAsState()
    val isShakerPopupVisible by component.isShakerPopupVisible.collectAsState()
    var navBarHeight by remember { mutableStateOf(0.dp) }
    val ld = LocalDensity.current

    Box(
        modifier = modifier
            .statusBarsPadding()
    ) {
        Children(stack = childStack, modifier = Modifier.fillMaxSize()) { child ->
            when (val instance = child.instance) {
                is MainComponent.Child.Home -> HomeUi(
                    component = instance.component,
                    modifier = Modifier.fillMaxSize()
                )

                is MainComponent.Child.Journal -> JournalUi(
                    component = instance.component,
                    navBarHeight = navBarHeight,
                    modifier = Modifier.fillMaxSize()
                )

                is MainComponent.Child.Account -> AccountUi(
                    component = instance.component,
                    modifier = Modifier.fillMaxSize(),
                    navBarHeight = navBarHeight
                )

                is MainComponent.Child.Showcase -> ShowcaseUi(
                    component = instance.component,
                    modifier = Modifier.fillMaxSize(),
                    navBarHeight = navBarHeight
                )
            }
        }
        if (tutorialState != TutorialState.SELECT_CARD) {
            BottomNavigation(
                tutorialState = tutorialState,
                tutorialStep = tutorialStep,
                tutorialMessage = tutorialMessage,
                navigationItems = navigationItems,
                onPageChange = component::onNavigationPageChange,
                modifier = Modifier
                    .noOverlapByMessage()
                    .align(Alignment.BottomCenter)
                    .onSizeChanged { size -> with(ld) { navBarHeight = size.height.toDp() } }
                    .navigationBarsPadding()
            )
        }

        if (isShakerPopupVisible) {
            ShakerPopup(onDismissButtonClick = component::onShakerPopupDismiss)
        }
    }
}

@Composable
private fun BoxScope.BottomNavigation(
    tutorialState: TutorialState,
    tutorialStep: TutorialStep,
    tutorialMessage: TutorialMessage?,
    navigationItems: List<NavigationItem>,
    onPageChange: (NavigationPage) -> Unit,
    modifier: Modifier = Modifier,
) {
    val ld = LocalDensity.current
    var firstIconSize by remember { mutableStateOf(WidgetPosition.initial) }
    BottomNavigationBar(
        modifier = modifier,
        items = when (tutorialStep) {
            TutorialStep.JOURNAL -> NavigationItem.SELECTED_JOURNAL
            TutorialStep.PREDEFINED -> NavigationItem.ALL
            TutorialStep.FINISH -> if (tutorialState != TutorialState.COMPLETED) {
                NavigationItem.SELECTED_ACCOUNT
            } else {
                navigationItems
            }
            else -> navigationItems
        },
        onItemSelected = {
            if (tutorialState == TutorialState.COMPLETED) {
                onPageChange(it)
            }
        },
        onFirstIconMeasured = { firstIconSize = it }
    )

    tutorialMessage?.let {
        when (tutorialStep) {
            TutorialStep.JOURNAL -> TutorialStepJournalAndFinish(it, firstIconSize, ld, false)
            TutorialStep.FINISH -> TutorialStepJournalAndFinish(it, firstIconSize, ld, true)
            else -> Unit
        }
    }
}

@Composable
private fun TutorialStepJournalAndFinish(
    tutorialMessage: TutorialMessage,
    firstIconSize: WidgetPosition,
    ld: Density,
    isFinish: Boolean,
) {
    val iconPaddings = 16
    val iconGap = 28
    val statusBarInsetDp = with(ld) { WindowInsets.statusBars.getTop(this).toDp() }
    val statusBarInset = WindowInsets.statusBars.getTop(ld)

    val journalIconXOffsetDp = with(ld) {
        firstIconSize.xOffset.toDp() + firstIconSize.size.width.toDp() + (iconPaddings * 2).dp
    }

    val journalIconXOffsetForCanvas = if (isFinish) {
        (firstIconSize.xOffset + (firstIconSize.size.width * 3)) + (iconGap * ld.density * 3)
    } else {
        (firstIconSize.xOffset + firstIconSize.size.width) + (iconGap * ld.density)
    }
    val journalIconYOffsetForCanvas = firstIconSize.yOffset - statusBarInset

    var messageSize by remember { mutableStateOf(0.dp to 0.dp) } // height to width
    val messageOffsetY = with(ld) { firstIconSize.yOffset.toDp() }
    val messageOffsetXArrow = if (isFinish) 0.dp else 4.dp
    val messageOffsetX = with(ld) {
        journalIconXOffsetDp - messageSize.second.div(2) +
                firstIconSize.size.width.toDp().div(2) - messageOffsetXArrow
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
    ) {
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .clickable(enabled = false) { }
        ) {
            drawIntoCanvas {
                clipPath(
                    Path().apply {
                        addRoundRect(
                            RoundRect(
                                Rect(
                                    offset = Offset(
                                        y = journalIconYOffsetForCanvas,
                                        x = journalIconXOffsetForCanvas
                                    ),
                                    size = Size(
                                        width = firstIconSize.size.width.toFloat(),
                                        height = firstIconSize.size.height.toFloat()
                                    )
                                ),
                                cornerRadius = CornerRadius(8f * ld.density)
                            )
                        )
                    },
                    ClipOp.Difference
                ) {
                    drawRoundRect(
                        color = CustomTheme.colors.background.disabledBackground,
                        cornerRadius = CornerRadius(1f)
                    )
                }
            }
        }
        val alpha by animateFloatAsState(
            targetValue = if (messageSize == 0.dp to 0.dp) 0f else 1f,
            label = ""
        )
        MessagePopupContent(
            tutorialMessage = tutorialMessage,
            modifier = Modifier
                .offset(
                    y = messageOffsetY - messageSize.first - statusBarInsetDp - iconPaddings.dp,
                    x = messageOffsetX
                )
                .onSizeChanged { size ->
                    with((ld)) {
                        messageSize = size.height.toDp() to size.width.toDp()
                    }
                }
                .alpha(alpha)
        )
    }
}

@Preview
@Composable
fun MainUiPreview() {
    AppTheme {
        MainUi(FakeMainComponent())
    }
}