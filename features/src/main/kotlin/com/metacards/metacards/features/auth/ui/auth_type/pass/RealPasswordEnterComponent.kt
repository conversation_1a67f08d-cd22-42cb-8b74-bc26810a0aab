package com.metacards.metacards.features.auth.ui.auth_type.pass

import com.arkivanov.decompose.ComponentContext
import com.google.firebase.auth.FirebaseAuthInvalidCredentialsException
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.user.domain.Email
import com.metacards.metacards.core.utils.PASSWORD_MIN_LENGTH
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.passwordInput
import com.metacards.metacards.features.R
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.auth.domain.AuthUserInteractor
import com.metacards.metacards.features.auth.domain.GetAuthUserInteractor
import com.metacards.metacards.features.auth.domain.LoginType
import com.metacards.metacards.features.auth.domain.Password
import com.metacards.metacards.features.user.domain.UpdateUserInteractor
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import ru.mobileup.kmm_form_validation.control.InputControl
import ru.mobileup.kmm_form_validation.options.ImeAction
import ru.mobileup.kmm_form_validation.validation.control.isNotBlank
import ru.mobileup.kmm_form_validation.validation.control.minLength
import ru.mobileup.kmm_form_validation.validation.form.RevalidateOnValueChanged
import ru.mobileup.kmm_form_validation.validation.form.SetFocusOnFirstInvalidControlAfterValidation
import ru.mobileup.kmm_form_validation.validation.form.ValidateOnFocusLost
import ru.mobileup.kmm_form_validation.validation.form.dynamicValidationResult
import ru.mobileup.kmm_form_validation.validation.form.formValidator
import com.metacards.metacards.core.R as CoreR

class RealPasswordEnterComponent(
    componentContext: ComponentContext,
    private val email: Email,
    private val onOutput: (PasswordEnterComponent.Output) -> Unit,
    private val authUserInteractor: AuthUserInteractor,
    private val errorHandler: ErrorHandler,
    private val analyticsService: AnalyticsService,
    private val getAuthUserInteractor: GetAuthUserInteractor,
    private val updateUserInteractor: UpdateUserInteractor
) : ComponentContext by componentContext, PasswordEnterComponent {

    private val debounce = Debounce()

    override val passwordInputControl: InputControl = passwordInput(componentScope, ImeAction.Next)

    private val formValidator = componentScope.formValidator {
        features = listOf(
            ValidateOnFocusLost,
            RevalidateOnValueChanged,
            SetFocusOnFirstInvalidControlAfterValidation
        )

        input(passwordInputControl) {
            isNotBlank(StringDesc.Resource(R.string.password_enter_error))
            minLength(PASSWORD_MIN_LENGTH, StringDesc.Resource(R.string.password_enter_error))
        }
    }

    private val dynamicResult = componentScope.dynamicValidationResult(formValidator)
    private val isLoading = MutableStateFlow(false)
    override val enterButtonState: StateFlow<ButtonState> =
        computed(dynamicResult, passwordInputControl.error, isLoading) { validationResult, error, loading ->
            val valid = validationResult.isValid && error == null
            ButtonState.create(disabled = !valid, loading = loading)
        }

    init {
        passwordInputControl.text
            .onEach { passwordInputControl.error.value = null }
            .launchIn(componentScope)
    }

    override fun onFocusRequest() = passwordInputControl.requestFocus()

    override fun onEnterButtonClick() {
        DebounceClick(
            debounce = debounce,
            id = "onEnterButtonClick",
            errorHandler = null,
            onClick = ::enterButtonAction
        )
    }

    override fun onResetPasswordButtonClick() {
        analyticsService.logEvent(AnalyticsEvent.AuthMailForgetPassword)
        onOutput(PasswordEnterComponent.Output.ResetPasswordRequested)
    }

    private fun enterButtonAction() {
        componentScope.safeLaunch(
            errorHandler = errorHandler,
            showError = false,
            onErrorHandled = {
                val messageRes = when (it) {
                    is FirebaseAuthInvalidCredentialsException -> R.string.password_enter_wrong_password_error
                    else -> CoreR.string.error_unexpected
                }
                passwordInputControl.error.value = StringDesc.Resource(messageRes)
                isLoading.value = false
            }
        ) {
            analyticsService.logEvent(AnalyticsEvent.AuthMailPasswordNextEvent)
            val password = Password(passwordInputControl.text.value)
            isLoading.value = true
            authUserInteractor.execute(email, password)
            analyticsService.logEvent(AnalyticsEvent.AuthMailDoneEvent)
            val authUser = getAuthUserInteractor.execute().value
            if (authUser != null && !authUser.isEmailVerified) {
                val userEmail = authUser.getLoginByType<LoginType.Email>()
                check(userEmail != null)

                onOutput(PasswordEnterComponent.Output.EmailVerificationRequested(Email(userEmail.value)))
            } else {
                onOutput(PasswordEnterComponent.Output.EnterButtonPressed)
            }
        }
    }
}