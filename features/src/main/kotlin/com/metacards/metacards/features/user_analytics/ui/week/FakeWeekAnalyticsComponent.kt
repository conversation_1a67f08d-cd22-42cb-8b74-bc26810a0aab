package com.metacards.metacards.features.user_analytics.ui.week

import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.PagedData
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.user_analytics.domain.week.WeekAnalyticsInfo
import kotlinx.coroutines.flow.MutableStateFlow

class FakeWeekAnalyticsComponent : WeekAnalyticsComponent {

    override val analyticsState = MutableStateFlow(
        LoadableState(loading = true, PagedData(listOf(WeekAnalyticsInfo.MOCK)))
    )
    override val isPremiumUser = MutableStateFlow(false)

    override fun onLoadMore() = Unit

    override fun onWeekPrev() = Unit

    override fun onWeekNext() = Unit

    override fun onRecordClick(recordId: RecordId) = Unit
}