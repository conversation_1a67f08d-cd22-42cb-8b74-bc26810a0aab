package com.metacards.metacards.features.user_analytics.domain

import com.metacards.metacards.features.record.data.RecordsUpdateListener
import com.metacards.metacards.features.record.domain.Record
import com.metacards.metacards.features.record.domain.RecordsPagedLoading
import com.metacards.metacards.features.user_analytics.domain.month.MonthAnalyticsInfo
import com.metacards.metacards.features.user_analytics.domain.month.MonthAnalyticsPagedLoading
import com.metacards.metacards.features.user_analytics.domain.week.WeekAnalyticsInfo
import com.metacards.metacards.features.user_analytics.domain.week.WeekAnalyticsPagedLoading

class UpdateUserAnalyticsRecordInteractor(
    private val weekAnalyticsPagedLoading: WeekAnalyticsPagedLoading,
    private val monthAnalyticsPagedLoading: MonthAnalyticsPagedLoading,
    private val layoutAnalyticsPagedLoading: RecordsPagedLoading
) : RecordsUpdateListener {

    override suspend fun updateAnalyticsRecord(updatedRecord: Record) {
        weekAnalyticsPagedLoading.mutateData { infos ->
            infos.map { it.withUpdatedRecord(updatedRecord) }
        }

        monthAnalyticsPagedLoading.mutateData { infos ->
            infos.map {
                it.withUpdatedRecord(updatedRecord)
            }
        }

        layoutAnalyticsPagedLoading.mutateData { infos ->
            infos.withUpdatedRecord(updatedRecord)
        }
    }
}

private fun WeekAnalyticsInfo.withUpdatedRecord(updatedRecord: Record): WeekAnalyticsInfo {
    return this.copy(records = records.mapToUpdatedRecords(updatedRecord))
}

private fun MonthAnalyticsInfo.withUpdatedRecord(updatedRecord: Record): MonthAnalyticsInfo {
    return this.copy(records = records.mapToUpdatedRecords(updatedRecord))
}

private fun List<Record>.withUpdatedRecord(updatedRecord: Record): List<Record> {
    return this.mapToUpdatedRecords(updatedRecord)
}

private fun List<Record>.mapToUpdatedRecords(updatedRecord: Record): List<Record> {
    return this.map { record ->
        if (record.id == updatedRecord.id) {
            updatedRecord
        } else {
            record
        }
    }
}