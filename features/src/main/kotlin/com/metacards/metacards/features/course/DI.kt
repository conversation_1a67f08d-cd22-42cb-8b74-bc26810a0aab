package com.metacards.metacards.features.course

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.course.data.data_source.CourseDataSource
import com.metacards.metacards.features.course.data.repository.CourseRepositoryImpl
import com.metacards.metacards.features.course.domain.entity.CourseContentId
import com.metacards.metacards.features.course.domain.entity.CourseData
import com.metacards.metacards.features.course.domain.entity.CourseLesson
import com.metacards.metacards.features.course.domain.entity.CoursePassedTest
import com.metacards.metacards.features.course.domain.entity.CourseResult
import com.metacards.metacards.features.course.domain.entity.CourseTest
import com.metacards.metacards.features.course.domain.entity.CourseTestDocumentId
import com.metacards.metacards.features.course.domain.entity.CourseTheme
import com.metacards.metacards.features.course.domain.interactor.GetCourseAvailabilityInteractor
import com.metacards.metacards.features.course.domain.interactor.GetCourseDataInteractor
import com.metacards.metacards.features.course.domain.interactor.GetCourseLessonDetailsInteractor
import com.metacards.metacards.features.course.domain.interactor.GetCourseTestDetailsInteractor
import com.metacards.metacards.features.course.domain.interactor.GetCourseThemesInteractor
import com.metacards.metacards.features.course.domain.interactor.UpdateUserPassedContentInteractor
import com.metacards.metacards.features.course.domain.interactor.UpdateUserPassedTestInteractor
import com.metacards.metacards.features.course.domain.repository.CourseRepository
import com.metacards.metacards.features.course.ui.CourseComponent
import com.metacards.metacards.features.course.ui.RealCourseComponent
import com.metacards.metacards.features.course.ui.details.CourseDetailsComponent
import com.metacards.metacards.features.course.ui.details.RealCourseDetailsComponent
import com.metacards.metacards.features.course.ui.lesson.CourseLessonComponent
import com.metacards.metacards.features.course.ui.lesson.RealCourseLessonComponent
import com.metacards.metacards.features.course.ui.list.CourseListComponent
import com.metacards.metacards.features.course.ui.list.RealCourseListComponent
import com.metacards.metacards.features.course.ui.page.CoursePageComponent
import com.metacards.metacards.features.course.ui.page.RealCoursePageComponent
import com.metacards.metacards.features.course.ui.passed_test_result.CoursePassedTestResultComponent
import com.metacards.metacards.features.course.ui.passed_test_result.RealCoursePassedTestResultComponent
import com.metacards.metacards.features.course.ui.test.CourseTestComponent
import com.metacards.metacards.features.course.ui.test.RealCourseTestComponent
import com.metacards.metacards.features.course.ui.test_result.CourseTestResultComponent
import com.metacards.metacards.features.course.ui.test_result.RealCourseTestResultComponent
import org.koin.core.component.get
import org.koin.core.qualifier.named
import org.koin.dsl.module

val courseModule = module {
    val userPrefsName = "user_preferences_name"
    single { CourseDataSource(get()) }
    single<CourseRepository> { CourseRepositoryImpl(get(), get(), get(named(userPrefsName))) }
    single { GetCourseThemesInteractor(get(), get(), get()) }
    single { GetCourseLessonDetailsInteractor(get(), get()) }
    single { UpdateUserPassedContentInteractor(get()) }
    single { UpdateUserPassedTestInteractor(get(), get()) }
    factory { GetCourseDataInteractor(get()) }
    single { GetCourseTestDetailsInteractor(get()) }
    single { GetCourseAvailabilityInteractor(get(), get()) }
}

fun ComponentFactory.createCourseComponent(
    componentContext: ComponentContext,
    output: (CourseComponent.Output) -> Unit,
    screen: CourseComponent.Screen
): CourseComponent = RealCourseComponent(
    componentContext,
    get(),
    output,
    screen,
)

fun ComponentFactory.createCourseDetails(
    componentContext: ComponentContext,
    output: (CourseDetailsComponent.Output) -> Unit,
    screen: CourseDetailsComponent.Screen
): CourseDetailsComponent = RealCourseDetailsComponent(
    componentContext,
    get(),
    output,
    screen,
    get(),
    get(),
    get(),
)

fun ComponentFactory.createCoursePageComponent(
    componentContext: ComponentContext,
    output: (CoursePageComponent.Output) -> Unit,
    courseQuery: CourseData.Query
): CoursePageComponent = RealCoursePageComponent(
    componentContext,
    output,
    courseQuery,
    get(),
    get(),
    get(),
    get(),
    get(),
    get()
)

fun ComponentFactory.createCourseLessonComponent(
    componentContext: ComponentContext,
    output: (CourseLessonComponent.Output) -> Unit,
    query: CourseLesson.Query,
    lastCardButtonText: String,
    order: Int,
    courseName: LocalizableString?
): CourseLessonComponent = RealCourseLessonComponent(
    componentContext,
    get(),
    output,
    query,
    lastCardButtonText,
    order,
    courseName,
    get(),
    get(),
    get(),
    get(),
)

fun ComponentFactory.createCourseTestComponent(
    componentContext: ComponentContext,
    query: CourseTest.Query,
    order: Int,
    courseName: LocalizableString?,
    output: (CourseTestComponent.Output) -> Unit
): CourseTestComponent = RealCourseTestComponent(
    componentContext,
    get(),
    query,
    order,
    courseName,
    output,
    get(),
    get(),
    get()
)

fun ComponentFactory.createCourseTestResultComponent(
    componentContext: ComponentContext,
    output: (CourseTestResultComponent.Output) -> Unit,
    courseResult: CourseResult,
    dataToSave: CoursePassedTest,
    bottomButtonText: String,
    order: Int,
    courseName: LocalizableString?,
): CourseTestResultComponent = RealCourseTestResultComponent(
    componentContext,
    get(),
    output,
    courseResult,
    dataToSave,
    order,
    bottomButtonText,
    courseName,
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    get()
)

fun ComponentFactory.createCourseListComponent(
    componentContext: ComponentContext,
    output: (CourseListComponent.Output) -> Unit,
    theme: CourseTheme
): CourseListComponent = RealCourseListComponent(
    componentContext,
    output,
    theme,
    get()
)

fun ComponentFactory.createCoursePassedTestResultComponent(
    componentContext: ComponentContext,
    testId: CourseContentId,
    testResultId: CourseTestDocumentId?,
    courseName: LocalizableString?,
    output: (CoursePassedTestResultComponent.Output) -> Unit,
): CoursePassedTestResultComponent = RealCoursePassedTestResultComponent(
    componentContext,
    get(),
    testId,
    testResultId,
    courseName,
    output,
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    get()
)