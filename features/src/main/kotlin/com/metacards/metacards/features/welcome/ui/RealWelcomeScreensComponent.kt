package com.metacards.metacards.features.welcome.ui

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.features.R
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow

class RealWelcomeScreensComponent(
    componentContext: ComponentContext,
    private val analyticsService: AnalyticsService,
    private val onOutput: (WelcomeScreensComponent.Output) -> Unit
) : ComponentContext by componentContext, WelcomeScreensComponent {

    override val pages: List<WelcomeScreenPage> = listOf(
        WelcomeScreenPage(
            title = StringDesc.Resource(R.string.welcome_screen_page_1_title),
            caption = StringDesc.Resource(R.string.welcome_screen_page_1_text)
        ),
        WelcomeScreenPage(
            title = StringDesc.Resource(R.string.welcome_screen_page_2_title),
            caption = StringDesc.Resource(R.string.welcome_screen_page_2_text)
        ),
        WelcomeScreenPage(
            title = StringDesc.Resource(R.string.welcome_screen_page_3_title),
            caption = StringDesc.Resource(R.string.welcome_screen_page_3_text)
        ),
        WelcomeScreenPage(
            title = StringDesc.Resource(R.string.welcome_screen_page_4_title),
            caption = StringDesc.Resource(R.string.welcome_screen_page_4_text)
        )
    )

    override val pageToOpen = MutableSharedFlow<Int>(
        extraBufferCapacity = 1,
        onBufferOverflow = BufferOverflow.DROP_LATEST
    )

    override fun onPageChanged(page: Int) {
        when (page) {
            1 -> {
                analyticsService.logEvent(AnalyticsEvent.WelcomeFirstNextEvent)
            }

            2 -> {
                analyticsService.logEvent(AnalyticsEvent.WelcomeSecondNextEvent)
            }
            3 -> {
                analyticsService.logEvent(AnalyticsEvent.WelcomeThirdNextEvent)
            }
        }
    }

    override fun onProceedClick(pageToOpen: Int) {
        this.pageToOpen.tryEmit(pageToOpen)
    }

    override fun onAuthClick() {
        analyticsService.logEvent(AnalyticsEvent.WelcomeRegEvent)
        onOutput(WelcomeScreensComponent.Output.AuthRequested)
    }

    override fun onAuthLaterClick() {
        analyticsService.logEvent(AnalyticsEvent.WelcomeRegSkipEvent)
        onOutput(WelcomeScreensComponent.Output.ContinueWithoutAuthRequested)
    }
}