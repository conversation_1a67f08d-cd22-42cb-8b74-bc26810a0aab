package com.metacards.metacards.features.record.ui.utils

import androidx.compose.ui.graphics.Color
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.features.record.domain.LevelType

fun feelingLevelToColor(levelType: LevelType, level: Int): Color {
    return when (levelType) {
        LevelType.Mood -> moodLevelToColor(level)
        LevelType.Energy -> energyLevelToColor(level)
    }
}

fun moodLevelToColor(level: Int): Color {
    return when {
        level <= 1 -> CustomTheme.colors.moon.brightness20
        level == 2 -> CustomTheme.colors.moon.brightness40
        level == 3 -> CustomTheme.colors.moon.brightness60
        level == 4 -> CustomTheme.colors.moon.brightness80
        else -> CustomTheme.colors.moon.brightness100
    }
}

fun energyLevelToColor(level: Int): Color {
    return when {
        level <= 1 -> CustomTheme.colors.mars.brightness20
        level == 2 -> CustomTheme.colors.mars.brightness40
        level == 3 -> CustomTheme.colors.mars.brightness60
        level == 4 -> CustomTheme.colors.mars.brightness80
        else -> CustomTheme.colors.mars.brightness100
    }
}