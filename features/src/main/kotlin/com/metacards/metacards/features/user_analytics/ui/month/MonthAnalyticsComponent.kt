package com.metacards.metacards.features.user_analytics.ui.month

import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.PagedData
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.user_analytics.domain.month.MonthAnalyticsInfo
import kotlinx.coroutines.flow.StateFlow

interface MonthAnalyticsComponent {

    val analyticsState: StateFlow<LoadableState<PagedData<MonthAnalyticsInfo>>>
    val isPremiumUser: StateFlow<Boolean>

    fun onLoadMore()

    fun onMonthPrev()

    fun onMonthNext()

    fun onRecordClick(recordId: RecordId)

    sealed interface Output {
        data class RecordDetailsRequested(val recordId: RecordId) : Output
    }
}