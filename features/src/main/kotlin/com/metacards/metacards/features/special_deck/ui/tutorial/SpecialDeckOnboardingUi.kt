package com.metacards.metacards.features.special_deck.ui.tutorial

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Icon
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.button.MetaPrimaryButton
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.StringResource
import dev.icerock.moko.resources.desc.ResourceFormattedStringDesc
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun SpecialDeckOnboardingUi(
    component: SpecialDeckOnboardingComponent,
    modifier: Modifier = Modifier
) {
    Scaffold(
        modifier = modifier
            .paint(
                painter = painterResource(id = R.drawable.bg_star_deck),
                contentScale = ContentScale.FillBounds
            ),
        topBar = {
            TopNavigationBar(
                title = component.deckTitle,
                leadingIcon = {
                    BackNavigationItem()
                },
                modifier = Modifier
                    .statusBarsPadding()
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(paddingValues)
        ) {
            Text(
                text = R.string.special_deck_onboarding_description_1.strResDesc().localizedByLocal(),
                style = CustomTheme.typography.heading.additional,
                color = CustomTheme.colors.text.caption,
                modifier = Modifier
                    .padding(
                        top = 8.dp,
                        start = 16.dp,
                        end = 16.dp,
                    )
            )
            Text(
                text = ResourceFormattedStringDesc(
                    StringResource(R.string.special_deck_onboarding_description_2),
                    listOf(component.deckTitle.localizedByLocal())
                ).localizedByLocal(),
                style = CustomTheme.typography.heading.additional,
                color = CustomTheme.colors.text.caption,
                modifier = Modifier
                    .padding(
                        top = 16.dp,
                        start = 16.dp,
                        end = 16.dp,
                        bottom = 40.dp
                    )
            )

            OnboardingExplanationBlock(deckTitle = component.deckTitle.localizedByLocal())

            Spacer(modifier = Modifier.weight(1f))

            MetaPrimaryButton(
                text = R.string.special_deck_onboarding_button_start.strResDesc()
                    .localizedByLocal(),
                onClick = component::onStartClick,
                modifier = Modifier
                    .navigationBarsPadding()
                    .fillMaxWidth()
                    .padding(16.dp)
            )
        }
    }
}

@Composable
private fun OnboardingExplanationBlock(
    deckTitle: String,
    modifier: Modifier = Modifier
) {

    val starsLightenAnimatable = remember {
        Animatable(0f)
    }

    LaunchedEffect(key1 = Unit) {
        starsLightenAnimatable.animateTo(
            targetValue = 3f,
            animationSpec = tween(
                durationMillis = 3000,
                easing = LinearEasing
            )
        )
    }

    val starsLightenProgress = starsLightenAnimatable.value

    val star1Color by animateColorAsState(
        label = "star1Color",
        targetValue = if (starsLightenProgress >= 1) {
            CustomTheme.colors.icons.secondary
        } else {
            CustomTheme.colors.icons.secondary.copy(alpha = 0.6f)
        }
    )

    val star2Color by animateColorAsState(
        label = "star2Color",
        targetValue = if (starsLightenProgress >= 2) {
            CustomTheme.colors.icons.secondary
        } else {
            CustomTheme.colors.icons.secondary.copy(alpha = 0.6f)
        }
    )

    val star3Color by animateColorAsState(
        label = "star3Color",
        targetValue = if (starsLightenProgress >= 3) {
            CustomTheme.colors.icons.secondary
        } else {
            CustomTheme.colors.icons.secondary.copy(alpha = 0.6f)
        }
    )

    ConstraintLayout(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
            .background(
                color = CustomTheme.colors.background.card,
                shape = RoundedCornerShape(16.dp)
            )
            .border(
                width = 1.dp,
                color = CustomTheme.colors.stroke.secondary,
                shape = RoundedCornerShape(16.dp)
            )
            .padding(16.dp)
    ) {
        val (star1, line1, star2, line2, star3, text1, text2, text3) = createRefs()

        Icon(
            painter = painterResource(id = R.drawable.star_6),
            contentDescription = null,
            tint = star1Color,
            modifier = Modifier.constrainAs(star1) {
                top.linkTo(text1.top)
                start.linkTo(parent.start)
            }
        )

        Box(
            modifier = Modifier
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            star1Color,
                            star2Color
                        ),
                    ),
                    shape = CircleShape
                )
                .constrainAs(line1) {
                    top.linkTo(star1.bottom, margin = 4.dp)
                    start.linkTo(star1.start)
                    end.linkTo(star1.end)
                    width = Dimension.value(2.dp)
                    height = Dimension.value(46.dp)
                }
        )

        Icon(
            painter = painterResource(id = R.drawable.star_6),
            contentDescription = null,
            tint = star2Color,
            modifier = Modifier.constrainAs(star2) {
                top.linkTo(line1.bottom, margin = 4.dp)
                start.linkTo(parent.start)
            }
        )

        Box(
            modifier = Modifier
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            star2Color,
                            star3Color
                        ),
                    ),
                    shape = CircleShape
                )
                .constrainAs(line2) {
                    top.linkTo(star2.bottom, margin = 4.dp)
                    start.linkTo(star2.start)
                    end.linkTo(star2.end)
                    width = Dimension.value(2.dp)
                    height = Dimension.value(46.dp)
                }
        )

        Icon(
            painter = painterResource(id = R.drawable.star_6),
            contentDescription = null,
            tint = star3Color,
            modifier = Modifier.constrainAs(star3) {
                top.linkTo(line2.bottom, margin = 4.dp)
                start.linkTo(parent.start)
            }
        )

        Text(
            text = R.string.special_deck_onboarding_steps_block_text_1
                .strResDesc()
                .localizedByLocal(),
            style = CustomTheme.typography.body.primary,
            color = CustomTheme.colors.text.caption,
            modifier = Modifier.constrainAs(text1) {
                top.linkTo(parent.top)
                start.linkTo(star1.end, margin = 8.dp)
                end.linkTo(parent.end, margin = 16.dp)
                width = Dimension.fillToConstraints
            }
        )

        Text(
            text = ResourceFormattedStringDesc(
                StringResource(R.string.special_deck_onboarding_steps_block_text_2),
                listOf(deckTitle)
            ).localizedByLocal(),
            style = CustomTheme.typography.body.primary,
            color = CustomTheme.colors.text.caption,
            modifier = Modifier.constrainAs(text2) {
                top.linkTo(star2.top)
                start.linkTo(star2.end, margin = 8.dp)
                end.linkTo(parent.end, margin = 16.dp)
                width = Dimension.fillToConstraints
            }
        )

        Text(
            text = R.string.special_deck_onboarding_steps_block_text_3
                .strResDesc()
                .localizedByLocal(),
            style = CustomTheme.typography.body.primary,
            color = CustomTheme.colors.text.caption,
            modifier = Modifier.constrainAs(text3) {
                top.linkTo(star3.top)
                start.linkTo(star3.end, margin = 8.dp)
                end.linkTo(parent.end, margin = 16.dp)
                width = Dimension.fillToConstraints
            }
        )
    }
}

@Preview
@Composable
private fun StarDeckOnboardingUiPreview() {
    AppTheme {
        SpecialDeckOnboardingUi(FakeSpecialDeckOnboardingComponent())
    }
}