package com.metacards.metacards.features.root.ui

import android.os.Parcelable
import androidx.annotation.StringRes
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.childContext
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.router.stack.bringToFront
import com.arkivanov.decompose.router.stack.childStack
import com.arkivanov.decompose.router.stack.pop
import com.arkivanov.decompose.router.stack.popWhile
import com.arkivanov.decompose.router.stack.push
import com.arkivanov.decompose.router.stack.replaceAll
import com.arkivanov.decompose.router.stack.replaceCurrent
import com.arkivanov.essenty.lifecycle.doOnPause
import com.arkivanov.essenty.lifecycle.doOnResume
import com.arkivanov.essenty.lifecycle.doOnStart
import com.arkivanov.essenty.lifecycle.doOnStop
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.adv.data.AdvUserConsentRepository
import com.metacards.metacards.core.adv.domain.AdvUserConsentState
import com.metacards.metacards.core.adv.domain.YandexAdvHelper
import com.metacards.metacards.core.adv.ui.AdvUserConsentComponent
import com.metacards.metacards.core.bottom_sheet.BottomSheetControl
import com.metacards.metacards.core.bottom_sheet.bottomSheetControl
import com.metacards.metacards.core.createAdvUserConsentComponent
import com.metacards.metacards.core.createDefaultDialogComponent
import com.metacards.metacards.core.createMessageComponent
import com.metacards.metacards.core.createWebViewComponent
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.model.DialogData
import com.metacards.metacards.core.dialog.dialogControl
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.localization.data.AppLanguageService
import com.metacards.metacards.core.localization.data.AppLanguageUpdateListener
import com.metacards.metacards.core.localization.domain.AppLanguage
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.message.data.MessageService
import com.metacards.metacards.core.message.domain.Message
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.toStateFlow
import com.metacards.metacards.core.widget.navigation_bar.NavigationPage
import com.metacards.metacards.features.R
import com.metacards.metacards.features.account.createAccountFlowComponent
import com.metacards.metacards.features.account.ui.flow.AccountFlowComponent
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.auth.createAuthComponent
import com.metacards.metacards.features.auth.domain.AuthUserProvider
import com.metacards.metacards.features.auth.domain.AwaitAuthUserInteractor
import com.metacards.metacards.features.auth.ui.AuthComponent
import com.metacards.metacards.features.course.createCourseComponent
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CourseThemeId
import com.metacards.metacards.features.course.ui.CourseComponent
import com.metacards.metacards.features.course.ui.details.CourseDetailsComponent
import com.metacards.metacards.features.deck.createDeckInfoComponent
import com.metacards.metacards.features.deck.createDeckScanningComponent
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardHint
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.domain.repository.DecksRepository
import com.metacards.metacards.features.deck.ui.card_preview.CardsPreviewComponent
import com.metacards.metacards.features.deck.ui.deck_info.DeckInfoComponent
import com.metacards.metacards.features.deck.ui.scanning.DeckScanningComponent
import com.metacards.metacards.features.deeplink.createDeeplinkComponent
import com.metacards.metacards.features.deeplink.ui.DeeplinkNavigationComponent
import com.metacards.metacards.features.favorite_cards.createFavoriteCardsComponent
import com.metacards.metacards.features.favorite_cards.ui.FavoriteCardsComponent
import com.metacards.metacards.features.layout.createCardPreviewComponent
import com.metacards.metacards.features.layout.createDeckLayoutComponent
import com.metacards.metacards.features.layout.createPredefinedLayoutComponent
import com.metacards.metacards.features.layout.createQuestionForLayoutComponent
import com.metacards.metacards.features.layout.domain.CardSource
import com.metacards.metacards.features.layout.ui.DeckLayoutComponent
import com.metacards.metacards.features.layout.ui.predefined_layout.PredefinedLayoutComponent
import com.metacards.metacards.features.layout.ui.question_for_layout.QuestionForLayoutComponent
import com.metacards.metacards.features.main.createMainComponent
import com.metacards.metacards.features.main.ui.MainComponent
import com.metacards.metacards.features.payments.createPaymentComponent
import com.metacards.metacards.features.payments.domain.BillingService
import com.metacards.metacards.features.payments.ui.PaymentComponent
import com.metacards.metacards.features.record.createArchiveComponent
import com.metacards.metacards.features.record.createCalendarComponent
import com.metacards.metacards.features.record.createCreateRecordFlowComponent
import com.metacards.metacards.features.record.createRecordDetailsComponent
import com.metacards.metacards.features.record.createSubscriptionBottomSheetComponent
import com.metacards.metacards.features.record.domain.Question
import com.metacards.metacards.features.record.domain.RecordData
import com.metacards.metacards.features.record.domain.RecordSource
import com.metacards.metacards.features.record.ui.archive.ArchiveComponent
import com.metacards.metacards.features.record.ui.calendar.CalendarComponent
import com.metacards.metacards.features.record.ui.create_record.CreateRecordFlowComponent
import com.metacards.metacards.features.record.ui.record_details.RecordDetailsComponent
import com.metacards.metacards.features.special_deck.createStarDeckComponent
import com.metacards.metacards.features.special_deck.ui.SpecialDeckComponent
import com.metacards.metacards.features.splash.createSplashComponent
import com.metacards.metacards.features.splash.ui.SplashComponent
import com.metacards.metacards.features.tutorial.createTutorialMessageComponent
import com.metacards.metacards.features.tutorial.ui.TutorialMessageComponent
import com.metacards.metacards.features.user.domain.UpdateUserInteractor
import com.metacards.metacards.features.user.ui.subscription.subscription_bottom_sheet.SubscriptionBottomSheetComponent
import com.metacards.metacards.features.user_analytics.createAnalyticsComponent
import com.metacards.metacards.features.user_analytics.ui.UserAnalyticsComponent
import com.metacards.metacards.features.welcome.createWelcomeScreensComponent
import com.metacards.metacards.features.welcome.ui.WelcomeScreensComponent
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue
import androidx.lifecycle.Lifecycle.Event as LifeCycleEvent

class RealRootComponent(
    componentContext: ComponentContext,
    isActivityRecreated: Boolean,
    private val componentFactory: ComponentFactory,
    private val messageService: MessageService,
    private val errorHandler: ErrorHandler,
    private val appLanguageService: AppLanguageService,
    private val analyticsService: AnalyticsService,
    private val billingServiceImpl: BillingService,
    private val awaitAuthUserInteractor: AwaitAuthUserInteractor,
    private val updateUserInteractor: UpdateUserInteractor,
    private val authUserProvider: AuthUserProvider,
    private val advUserConsentRepository: AdvUserConsentRepository,
    appLanguageUpdateListener: AppLanguageUpdateListener,
    decksRepository: DecksRepository,
    yandexAdvHelper: YandexAdvHelper,
) : ComponentContext by componentContext, RootComponent {

    // TODO: вынести в отдельный компонент все, что относится к раскладам
    private var question: Question = Question.createEmptyQuestion()
    private var popToConfigAfterAuth: ChildConfig? = null
    private val navigation = StackNavigation<ChildConfig>()

    override val appLanguage: StateFlow<AppLanguage> = appLanguageService.currentAppLanguage

    private val lastLifecycleEvent = MutableStateFlow(LifeCycleEvent.ON_CREATE)
    override val isShouldBecomeInvisible: StateFlow<Boolean> = combine(
        yandexAdvHelper.isAdvInProgress,
        lastLifecycleEvent
    ) { advInProgress, event -> advInProgress && event != LifeCycleEvent.ON_RESUME }
        .stateIn(componentScope, SharingStarted.Eagerly, false)

    private var cardHints = listOf<CardHint>()

    companion object {
        private const val DEFAULT_USER_AWAIT_TIMEOUT = 2000L
    }

    init {
        lifecycle.doOnStart {
            billingServiceImpl.init()
            componentScope.safeLaunch(errorHandler) {
                cardHints = decksRepository.getCardHints().orEmpty()
            }
        }

        if (isActivityRecreated) {
            componentScope.launch {
                // Re-authorize if the activity has been recreated
                // In such cases SplashComponent will not be called
                awaitAuthUserInteractor.execute(DEFAULT_USER_AWAIT_TIMEOUT)
            }
        }

        lifecycle.doOnStart {
            lastLifecycleEvent.update { LifeCycleEvent.ON_START }
            componentScope.safeLaunch(errorHandler) {
                appLanguageService.addListener(appLanguageUpdateListener)
                appLanguageService.onAppStart()
            }
        }

        lifecycle.doOnResume {
            lastLifecycleEvent.update { LifeCycleEvent.ON_RESUME }
        }

        lifecycle.doOnPause {
            lastLifecycleEvent.update { LifeCycleEvent.ON_PAUSE }
        }

        lifecycle.doOnStop {
            lastLifecycleEvent.update { LifeCycleEvent.ON_STOP }
            appLanguageService.removeListener(appLanguageUpdateListener)
        }

        componentScope.safeLaunch(errorHandler) {
            authUserProvider.authUser.firstOrNull { it != null }?.let { user ->
                updateUserInteractor.updateUserInfoOnAppLaunch(user.userId)
            }
        }
    }

    private val deeplinkNavigationComponent: DeeplinkNavigationComponent =
        componentFactory.createDeeplinkComponent(
            childContext(key = "deeplink"),
            ::onDeeplinkOutput
        )

    private val authDialogConfig by lazy {
        DefaultDialogComponent.Config(
            DialogData(
                title = StringDesc.Resource(R.string.add_record_auth_dialog_title),
                message = StringDesc.Resource(R.string.add_record_auth_dialog_message),
                buttons = listOf(
                    DialogData.Button(
                        title = StringDesc.Resource(R.string.add_record_auth_dialog_cancel_button),
                        action = {
                            analyticsService.logEvent(AnalyticsEvent.AuthFormCancelEvent)
                            dialogControl.dismiss()
                        }
                    ),
                    DialogData.Button(
                        title = StringDesc.Resource(R.string.add_record_auth_dialog_auth_button),
                        action = {
                            dialogControl.dismiss()
                            analyticsService.logEvent(AnalyticsEvent.AuthFormOkEvent)
                            navigation.replaceAll(ChildConfig.MainAuth())
                        }
                    )
                )
            )
        )
    }

    override val childStack = childStack(
        source = navigation,
        initialConfiguration = ChildConfig.Splash,
        handleBackButton = true,
        childFactory = ::createChild
    ).toStateFlow(lifecycle)

    override val paymentComponent: PaymentComponent by lazy {
        componentFactory.createPaymentComponent(
            componentContext.childContext("PaymentComponent"),
            ::onPaymentOutput
        )
    }

    override val messageComponent = componentFactory.createMessageComponent(
        childContext(key = "message")
    )

    override val tutorialMessageComponent = componentFactory.createTutorialMessageComponent(
        childContext(key = "tutorialMessage"),
        componentFactory,
        ::onTutorialOutput
    )

    override val bottomSheetControl: BottomSheetControl<QuestionForLayoutComponent.Config, QuestionForLayoutComponent> =
        bottomSheetControl(
            key = "questionForLayout",
            bottomSheetComponentFactory = { config, context, _ ->
                componentFactory.createQuestionForLayoutComponent(
                    context,
                    config.deckId,
                    ::onQuestionForLayoutOutput
                )
            },
            halfExpandingSupported = false,
            hidingSupported = true,
            handleBackButton = true
        )

    override val subscriptionBottomSheetControl:
            BottomSheetControl<SubscriptionBottomSheetComponent.Config, SubscriptionBottomSheetComponent> =
        bottomSheetControl(
            key = "subscription",
            bottomSheetComponentFactory = {
                    config: SubscriptionBottomSheetComponent.Config,
                    context: ComponentContext, _,
                ->
                componentFactory.createSubscriptionBottomSheetComponent(
                    context,
                    isSpecialDeckCardRequested = config.isSpecialDeckCardRequested,
                    isWithAdv = config.isWithAdv,
                    ::onSubscriptionBottomSheetOutput
                )
            },
            halfExpandingSupported = false,
            hidingSupported = true,
            handleBackButton = true
        )

    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        dialogControl(dialogComponentFactory = { config, componentContext, dialogControl ->
            componentFactory.createDefaultDialogComponent(
                componentContext,
                config.data,
                dialogControl::dismiss
            )
        })

    private fun onPaymentOutput(output: PaymentComponent.Output) {
        when (output) {
            is PaymentComponent.Output.Canceled -> {}

            is PaymentComponent.Output.Succeed -> {
                messageService.showMessage(
                    Message(
                        text = R.string.deck_info_payment_completed.strResDesc(),
                    )
                )
            }

            is PaymentComponent.Output.WebStoreRequested -> {
                navigation.push(ChildConfig.WebView(output.url.toString(), output.title))
            }
        }
    }

    private fun onSubscriptionBottomSheetOutput(output: SubscriptionBottomSheetComponent.Output) {
        when (output) {
            is SubscriptionBottomSheetComponent.Output.DismissRequested -> Unit

            is SubscriptionBottomSheetComponent.Output.TariffsRequested ->
                navigation.bringToFront(ChildConfig.AccountFlow(AccountFlowComponent.Screen.Subscription))
        }
        subscriptionBottomSheetControl.dismiss()
    }

    private fun createChild(
        config: ChildConfig,
        componentContext: ComponentContext,
    ): RootComponent.Child = when (config) {
        is ChildConfig.MainAuth -> {
            RootComponent.Child.MainAuth(
                componentFactory.createAuthComponent(
                    componentContext,
                    config.startCommand,
                    ::onMainAuthOutput
                )
            )
        }

        is ChildConfig.Main -> {
            RootComponent.Child.Main(
                componentFactory.createMainComponent(
                    componentContext,
                    ::onMainOutput
                )
            )
        }

        ChildConfig.Splash -> {
            RootComponent.Child.Splash(
                componentFactory.createSplashComponent(componentContext, ::onSplashOutput)
            )
        }

        is ChildConfig.DeckLayout -> RootComponent.Child.DeckLayout(
            componentFactory.createDeckLayoutComponent(
                componentContext,
                componentFactory,
                config.cardSource,
                config.questionText,
                config.courseId,
                config.courseThemeId,
                ::onDeckLayoutOutput
            )
        )

        is ChildConfig.PredefinedLayouts -> RootComponent.Child.PredefinedLayouts(
            componentFactory.createPredefinedLayoutComponent(
                componentContext,
                ::onPredefinedLayoutOutput
            )
        )

        is ChildConfig.RecordDetails -> RootComponent.Child.JournalRecordDetails(
            componentFactory.createRecordDetailsComponent(
                componentContext,
                componentFactory,
                config.recordType,
                config.isDailyCard,
                config.recordSource,
                config.recordData,
                config.courseId,
                config.courseThemeId,
                ::onRecordDetailsOutput,
            )
        )

        is ChildConfig.CardPreview -> {
            RootComponent.Child.CardPreview(
                componentFactory.createCardPreviewComponent(
                    componentContext,
                    config.cards,
                    config.initialCardIndex,
                    config.shouldShowGif,
                    ::onCardPreviewOutput
                )
            )
        }

        is ChildConfig.Calendar -> {
            RootComponent.Child.Calendar(
                componentFactory.createCalendarComponent(
                    componentContext,
                    ::onCalendarOutput
                )
            )
        }

        is ChildConfig.JournalArchive -> {
            RootComponent.Child.JournalArchive(
                componentFactory.createArchiveComponent(
                    componentContext,
                    componentFactory,
                    ::onArchiveOutput
                )
            )
        }

        is ChildConfig.DeckInfo -> {
            RootComponent.Child.DeckInfo(
                componentFactory.createDeckInfoComponent(
                    componentContext,
                    config.deckId,
                    config.isPaymentCompleted,
                    ::onDeckInfoOutput
                )
            )
        }

        is ChildConfig.DeckScanning -> {
            RootComponent.Child.DeckScanning(
                componentFactory.createDeckScanningComponent(
                    componentContext,
                    ::onDeckScanningOutput
                )
            )
        }

        is ChildConfig.AccountFlow -> {
            RootComponent.Child.AccountFlow(
                componentFactory.createAccountFlowComponent(
                    componentContext,
                    config.screenToShow,
                    ::onAccountFlowOutput
                )
            )
        }

        is ChildConfig.FavoriteCards -> {
            RootComponent.Child.FavoriteCards(
                componentFactory.createFavoriteCardsComponent(
                    componentContext,
                    ::onFavoriteCardsOutput,
                    config.screen
                )
            )
        }

        is ChildConfig.WebView -> {
            RootComponent.Child.WebView(
                componentFactory.createWebViewComponent(
                    componentContext,
                    config.url,
                    config.title
                )
            )
        }

        is ChildConfig.CreateRecordFlow -> RootComponent.Child.CreateRecordFlow(
            componentFactory.createCreateRecordFlowComponent(
                componentContext,
                config.screenToShow,
                ::onCreateRecordFlowScreensOutput
            )
        )

        is ChildConfig.WelcomeScreens -> {
            RootComponent.Child.WelcomeScreens(
                componentFactory.createWelcomeScreensComponent(
                    componentContext,
                    ::onWelcomeScreensOutput
                )
            )
        }

        is ChildConfig.WebViewResource -> {
            RootComponent.Child.WebView(
                componentFactory.createWebViewComponent(
                    componentContext,
                    config.url,
                    config.title
                )
            )
        }

        is ChildConfig.AdvUserConsent -> RootComponent.Child.AdvUserConsent(
            componentFactory.createAdvUserConsentComponent(
                componentContext,
                ::onAdvUserConsentOutput
            )
        )

        is ChildConfig.SpecialDeck -> RootComponent.Child.SpecialDeck(
            componentFactory.createStarDeckComponent(
                componentContext,
                ::onStarDeckOutput,
                config.deckId,
                config.forceOnboarding
            )
        )

        ChildConfig.UserAnalytics -> RootComponent.Child.UserAnalytics(
            componentFactory.createAnalyticsComponent(
                componentContext = componentContext,
                onOutput = ::onAnalyticsOutput
            )
        )

        is ChildConfig.Course -> RootComponent.Child.Course(
            componentFactory.createCourseComponent(
                componentContext,
                ::onCourseOutput,
                config.screen
            )
        )
    }

    private fun onCourseOutput(output: CourseComponent.Output) {
        when (output) {
            CourseComponent.Output.CloseRequested -> navigation.popWhile { it != ChildConfig.Main }
            is CourseComponent.Output.SubscriptionBottomSheetRequested ->
                subscriptionBottomSheetControl.show(SubscriptionBottomSheetComponent.Config(isWithAdv = output.withAdv))

            CourseComponent.Output.AuthScreenRequested -> navigation.replaceAll(ChildConfig.MainAuth())
            is CourseComponent.Output.LayoutDetailsRequested ->
                predefinedLayoutDetailsRequested(
                    cardSource = output.cardSource,
                    courseId = output.courseId,
                    courseThemeId = output.themeId
                )

            CourseComponent.Output.AuthSuggestingRequested -> dialogControl.show(authDialogConfig)
        }
    }

    private fun onAnalyticsOutput(output: UserAnalyticsComponent.Output) {
        when (output) {
            is UserAnalyticsComponent.Output.RecordDetailsRequested -> navigation.push(
                ChildConfig.RecordDetails(
                    RecordSource.Viewing(output.recordId),
                    RecordDetailsComponent.RecordType.Common
                )
            )

            UserAnalyticsComponent.Output.PremiumSuggestingRequested ->
                subscriptionBottomSheetControl.show(
                    SubscriptionBottomSheetComponent.Config(isWithAdv = false)
                )

            UserAnalyticsComponent.Output.AuthSuggestingRequested ->
                dialogControl.show(authDialogConfig)
        }
    }

    private fun onStarDeckOutput(output: SpecialDeckComponent.Output) {
        when (output) {
            is SpecialDeckComponent.Output.CloseRequested -> {
                navigation.popWhile { it != ChildConfig.Main }
            }

            is SpecialDeckComponent.Output.LayoutRequested -> {
                bottomSheetControl.show(
                    QuestionForLayoutComponent.Config(output.deckId)
                )
            }

            is SpecialDeckComponent.Output.SubscriptionBottomSheetRequested ->
                subscriptionBottomSheetControl.show(
                    SubscriptionBottomSheetComponent.Config(
                        isWithAdv = true,
                        isSpecialDeckCardRequested = output.isSpecialDeckCardRequested
                    )
                )
        }
    }

    private fun onAdvUserConsentOutput(output: AdvUserConsentComponent.Output) {
        when (output) {
            is AdvUserConsentComponent.Output.UserConsentDenied,
            is AdvUserConsentComponent.Output.UserConsentGranted -> {
                navigateToMain()
            }
        }
    }

    private fun onWelcomeScreensOutput(output: WelcomeScreensComponent.Output) {
        when (output) {
            is WelcomeScreensComponent.Output.AuthRequested -> {
                navigation.replaceAll(ChildConfig.MainAuth(AuthComponent.StartCommand.OpenWithAnimation))
            }

            is WelcomeScreensComponent.Output.ContinueWithoutAuthRequested -> {
                componentScope.safeLaunch(errorHandler) {
                    if (advUserConsentRepository.getUserConsentState() == AdvUserConsentState.Unknown) {
                        navigation.push(ChildConfig.AdvUserConsent)
                    } else {
                        navigateToMain()
                    }
                }
            }
        }
    }

    private fun onAccountFlowOutput(output: AccountFlowComponent.Output) {
        when (output) {
            is AccountFlowComponent.Output.MainScreenRequested -> {
                navigateToMain(output.navigationPage)
            }

            is AccountFlowComponent.Output.WebViewRequested -> {
                navigation.push(ChildConfig.WebView(output.url, output.title))
            }

            is AccountFlowComponent.Output.WebViewRequestedResource -> {
                navigation.push(ChildConfig.WebViewResource(output.url, output.title))
            }

            is AccountFlowComponent.Output.DeckDetailsRequested -> {
                navigation.push(ChildConfig.DeckInfo(output.deckId))
            }

            is AccountFlowComponent.Output.AuthScreenRequested -> {
                navigation.replaceAll(ChildConfig.MainAuth())
            }

            is AccountFlowComponent.Output.ShowMessageRequested -> {
                messageService.showMessage(message = output.message)
            }

            is AccountFlowComponent.Output.SignInViaWebViewRequested -> {
                navigation.bringToFront(ChildConfig.WebView(output.url, output.title))
            }

            is AccountFlowComponent.Output.WebViewDismissRequested -> {
                if (childStack.value.active.instance is RootComponent.Child.WebView) {
                    navigation.pop()
                }
            }
        }
    }

    private fun onArchiveOutput(output: ArchiveComponent.Output) {
        when (output) {
            is ArchiveComponent.Output.RecordDetailsRequested -> {
                navigation.push(
                    ChildConfig.RecordDetails(
                        recordSource = output.recordSource,
                        recordType = output.recordType,
                        courseId = output.courseId,
                        courseThemeId = output.courseThemeId
                    )
                )
            }

            ArchiveComponent.Output.CreateRecordFlowRequested -> {
                navigation.push(
                    ChildConfig.CreateRecordFlow(
                        CreateRecordFlowComponent.Screen.CreateRecord()
                    )
                )
            }

            ArchiveComponent.Output.SubscriptionSuggestingRequested -> {
                subscriptionBottomSheetControl.show(SubscriptionBottomSheetComponent.Config())
            }
        }
    }

    private fun onCalendarOutput(output: CalendarComponent.Output) {
        when (output) {
            is CalendarComponent.Output.RecordsViewRequested -> {
                navigation.pop()
                (childStack.value.active.instance as? RootComponent.Child.Main)
                    ?.component // TODO: исправить после выноса компонентов из RootComponent
                    ?.showJournalRecordsForDate(output.date)
            }
        }
    }

    private fun onDeckInfoOutput(output: DeckInfoComponent.Output) {
        when (output) {
            is DeckInfoComponent.Output.MaterialDeckBuyRequested -> Unit // TODO()
            is DeckInfoComponent.Output.DeckLayoutRequested -> {
                bottomSheetControl.show(QuestionForLayoutComponent.Config(output.deckId))
            }

            is DeckInfoComponent.Output.FullScreenPreviewRequested -> {
                navigation.push(
                    ChildConfig.CardPreview(
                        cards = output.cards.cards,
                        initialCardIndex = output.initialCardPosition,
                        shouldShowGif = true,
                    )
                )
            }

            is DeckInfoComponent.Output.WebViewRequested -> {
                navigation.push(ChildConfig.WebView(output.url.toString(), output.title))
            }

            DeckInfoComponent.Output.AuthSuggestingRequested -> dialogControl.show(authDialogConfig)

            DeckInfoComponent.Output.PremiumSuggestingRequested -> {
                subscriptionBottomSheetControl.show(
                    SubscriptionBottomSheetComponent.Config(isWithAdv = true)
                )
            }

            DeckInfoComponent.Output.DecksListFallback -> {
                navigation.push(ChildConfig.AccountFlow(AccountFlowComponent.Screen.Shop))
            }
        }
    }

    private fun onDeckScanningOutput(output: DeckScanningComponent.Output) {
        when (output) {
            is DeckScanningComponent.Output.DeckAdded -> {
                navigation.replaceCurrent(ChildConfig.DeckInfo(output.deckId))
            }

            is DeckScanningComponent.Output.RejectedScanning -> {
                navigation.pop()
            }
        }
    }

    private fun onPredefinedLayoutOutput(output: PredefinedLayoutComponent.Output) {
        when (output) {
            is PredefinedLayoutComponent.Output.LayoutDetailsRequested ->
                predefinedLayoutDetailsRequested(output.cardSource)

            is PredefinedLayoutComponent.Output.SubscriptionBottomsheetRequested -> {
                subscriptionBottomSheetControl.show(
                    SubscriptionBottomSheetComponent.Config(isWithAdv = output.withAdv)
                )
            }

            PredefinedLayoutComponent.Output.AuthScreenRequested -> {
                navigation.replaceAll(ChildConfig.MainAuth())
            }
        }
    }

    private fun onCardPreviewOutput(output: CardsPreviewComponent.Output) {
        when (output) {
            is CardsPreviewComponent.Output.CloseRequested -> {
                navigation.pop()
                (childStack.value.active.instance as? RootComponent.Child.DeckInfo)
                    ?.component
                    ?.scrollToPage(output.currentCardPosition)
            }

            is CardsPreviewComponent.Output.CreateRecordRequested -> {
                navigation.replaceCurrent(
                    ChildConfig.RecordDetails(
                        RecordSource.Creation(
                            listOf(
                                question.copy(
                                    cardsWithComment = output.cardsWithComment
                                )
                            )
                        ),
                        RecordDetailsComponent.RecordType.InCreation,
                        recordData = RecordData(
                            setupType = "",
                            cardsNumber = 1,
                            deckId = null,
                        ),
                        isDailyCard = output.isDailyCard
                    )
                )
            }

            CardsPreviewComponent.Output.AuthSuggestingRequested -> dialogControl.show(
                authDialogConfig
            )

            CardsPreviewComponent.Output.SubscriptionSuggestingRequested -> {
                subscriptionBottomSheetControl.show(SubscriptionBottomSheetComponent.Config())
            }
        }
    }

    private fun onRecordDetailsOutput(output: RecordDetailsComponent.Output) {
        when (output) {
            RecordDetailsComponent.Output.AuthRequested -> {
                popToConfigAfterAuth = childStack.value.active.configuration
                navigation.push(ChildConfig.MainAuth())
            }

            RecordDetailsComponent.Output.CloseComponentRequested -> navigation.popWhile {
                it != ChildConfig.Main
            }

            is RecordDetailsComponent.Output.FullScreenCardsRequested -> navigation.push(
                ChildConfig.CardPreview(
                    cards = output.cards,
                    initialCardIndex = output.initialCardPosition,
                    shouldShowGif = true,
                )
            )

            RecordDetailsComponent.Output.SubscriptionSuggestingRequested -> {
                subscriptionBottomSheetControl.show(SubscriptionBottomSheetComponent.Config())
            }

            RecordDetailsComponent.Output.AuthSuggestingRequested -> dialogControl.show(
                authDialogConfig
            )
        }
    }

    private fun onDeckLayoutOutput(output: DeckLayoutComponent.Output) {
        when (output) {
            DeckLayoutComponent.Output.OnCloseRequested -> navigation.pop()
            is DeckLayoutComponent.Output.OnFinished -> navigation.replaceCurrent(
                ChildConfig.RecordDetails(
                    RecordSource.Creation(
                        listOf(
                            question.copy(
                                cardsWithComment = output.cards
                            )
                        )
                    ),
                    RecordDetailsComponent.RecordType.InCreation,
                    recordData = output.recordData
                )
            )

            is DeckLayoutComponent.Output.OnFinishedDaily -> navigation.replaceCurrent(
                ChildConfig.RecordDetails(
                    recordSource = RecordSource.Creation(
                        listOf(
                            Question.createEmptyQuestion().copy(
                                text = output.question,
                                cardsWithComment = listOf(output.cardWithComment)
                            )
                        )
                    ),
                    recordType = RecordDetailsComponent.RecordType.InCreation,
                    isDailyCard = true
                )
            )

            is DeckLayoutComponent.Output.OnPredefinedFinished -> navigation.replaceCurrent(
                ChildConfig.RecordDetails(
                    output.recordSource,
                    RecordDetailsComponent.RecordType.InCreation,
                    recordData = output.recordData,
                    courseId = output.courseId,
                    courseThemeId = output.courseThemeId
                )
            )

            is DeckLayoutComponent.Output.FullScreenCardRequested -> navigation.push(
                ChildConfig.CardPreview(
                    cards = listOf(output.card),
                    initialCardIndex = 0,
                    shouldShowGif = when (output.cardSource) {
                        is CardSource.DailyCardSource -> true
                        is CardSource.Deck -> false
                        is CardSource.Questions -> false
                    },
                )
            )

            DeckLayoutComponent.Output.AuthSuggestingRequested -> dialogControl.show(
                authDialogConfig
            )

            is DeckLayoutComponent.Output.SubscriptionSuggestingRequested -> {
                subscriptionBottomSheetControl.show(
                    SubscriptionBottomSheetComponent.Config(isWithAdv = output.withAdv)
                )
            }

            is DeckLayoutComponent.Output.DailyRecordRequested -> {
                navigation.push(
                    ChildConfig.RecordDetails(
                        recordSource = RecordSource.Viewing(output.recordId),
                        recordType = RecordDetailsComponent.RecordType.Common,
                        isDailyCard = true
                    )
                )
            }
        }
    }

    private fun onMainAuthOutput(output: AuthComponent.Output) {
        when (output) {
            AuthComponent.Output.MainPageRequested -> {
                if (popToConfigAfterAuth == null) {
                    componentScope.safeLaunch(errorHandler) {
                        if (advUserConsentRepository.getUserConsentState() == AdvUserConsentState.Unknown) {
                            navigation.push(ChildConfig.AdvUserConsent)
                        } else {
                            navigateToMain()
                        }
                    }
                } else {
                    navigation.popWhile { it != popToConfigAfterAuth }
                    popToConfigAfterAuth = null
                }
            }

            is AuthComponent.Output.WebViewRequested -> {
                navigation.push(ChildConfig.WebView(output.url, output.title))
            }
        }
    }

    private fun onSplashOutput(output: SplashComponent.Output) {
        when (output) {
            is SplashComponent.Output.AuthScreenRequested ->
                navigation.replaceAll(ChildConfig.MainAuth(AuthComponent.StartCommand.OpenWithAnimation))

            is SplashComponent.Output.WelcomeScreenRequested ->
                navigation.replaceAll(ChildConfig.WelcomeScreens)

            is SplashComponent.Output.MainScreenRequested -> navigateToMain()

            is SplashComponent.Output.AdvUserConsentScreenRequested -> {
                navigation.push(ChildConfig.AdvUserConsent)
            }
        }

        deeplinkNavigationComponent.readyToReceiveEvents()
    }

    private fun onMainOutput(output: MainComponent.Output) {
        when (output) {
            is MainComponent.Output.NewDeckLayoutRequested -> bottomSheetControl.show(
                QuestionForLayoutComponent.Config(output.deckId)
            )

            is MainComponent.Output.PredefinedLayoutsRequested -> {
                navigation.push(ChildConfig.PredefinedLayouts)
            }

            is MainComponent.Output.CalendarRequested -> {
                navigation.push(ChildConfig.Calendar)
            }

            is MainComponent.Output.ArchiveRequested -> {
                navigation.push(ChildConfig.JournalArchive)
            }

            is MainComponent.Output.RecordDetailsRequested -> {
                navigation.push(
                    ChildConfig.RecordDetails(
                        recordSource = output.recordSource,
                        recordType = output.recordType,
                        courseId = output.courseId,
                        courseThemeId = output.courseThemeId
                    )
                )
            }

            is MainComponent.Output.DeckScreenRequested -> {
                navigation.push(ChildConfig.DeckInfo(output.deckId))
            }

            is MainComponent.Output.AccountFlowScreenRequested -> {
                navigation.bringToFront(ChildConfig.AccountFlow(output.screen))
            }

            is MainComponent.Output.FavoriteCardsRequested -> {
                navigation.push(ChildConfig.FavoriteCards(output.screen))
            }

            is MainComponent.Output.AuthScreenRequested -> {
                navigation.replaceAll(ChildConfig.MainAuth())
            }

            is MainComponent.Output.AddPhysicalDeckRequested -> {
                navigation.push(ChildConfig.DeckScanning)
            }

            is MainComponent.Output.DailyCardRequested -> {
                navigation.push(ChildConfig.DeckLayout(output.cardSource))
            }

            is MainComponent.Output.CreateRecordFlowScreenRequested -> {
                navigation.bringToFront(
                    ChildConfig.CreateRecordFlow(
                        CreateRecordFlowComponent.Screen.CreateRecord(isDailyCard = output.isDailyCard)
                    )
                )
            }

            MainComponent.Output.AuthSuggestingScreenRequested -> dialogControl.show(
                authDialogConfig
            )

            is MainComponent.Output.SubscriptionSuggestingScreenRequested -> {
                subscriptionBottomSheetControl.show(
                    SubscriptionBottomSheetComponent.Config(isWithAdv = output.withAdv)
                )
            }

            MainComponent.Output.SubscriptionScreenRequested -> {
                navigation.bringToFront(ChildConfig.AccountFlow(AccountFlowComponent.Screen.Subscription))
            }

            is MainComponent.Output.SpecialDeckRequested -> {
                navigation.push(ChildConfig.SpecialDeck(output.deckId, output.forceOnboarding))
            }

            is MainComponent.Output.PredefinedLayoutDetailsRequested ->
                predefinedLayoutDetailsRequested(output.cardSource)

            MainComponent.Output.AnalyticsRequested -> navigation.push(ChildConfig.UserAnalytics)
            is MainComponent.Output.CourseRequested -> {
                navigation.push(ChildConfig.Course(output.screen))
            }
        }
    }

    private fun onQuestionForLayoutOutput(output: QuestionForLayoutComponent.Output) {
        when (output) {
            QuestionForLayoutComponent.Output.OnDismiss -> bottomSheetControl.dismiss()
            is QuestionForLayoutComponent.Output.LayoutRequested -> {
                question = question.copy(
                    text = if (output.question.isBlank()) {
                        null
                    } else {
                        LocalizableString.createNonLocalizable(output.question)
                    }
                )
                navigation.push(ChildConfig.DeckLayout(output.cardSource, output.question))
                bottomSheetControl.dismiss()
            }
        }
    }

    private fun onFavoriteCardsOutput(output: FavoriteCardsComponent.Output) {
        when (output) {
            is FavoriteCardsComponent.Output.GoToMainRequested -> {
                navigateToMain(NavigationPage.Home)
            }

            is FavoriteCardsComponent.Output.RecordDetailsRequested -> {
                navigation.push(
                    ChildConfig.RecordDetails(
                        recordSource = RecordSource.Viewing(output.recordId),
                        recordType = RecordDetailsComponent.RecordType.Common,
                    )
                )
            }

            FavoriteCardsComponent.Output.GoBackRequested -> navigation.pop()
        }
    }

    private fun onCreateRecordFlowScreensOutput(output: CreateRecordFlowComponent.Output) {
        when (output) {
            CreateRecordFlowComponent.Output.JournalRequested -> {
                navigation.pop()
            }

            CreateRecordFlowComponent.Output.AuthSuggestingRequested -> dialogControl.show(
                authDialogConfig
            )

            CreateRecordFlowComponent.Output.SubscriptionSuggestingRequested -> subscriptionBottomSheetControl.show(
                SubscriptionBottomSheetComponent.Config()
            )
        }
    }

    override fun onShakeDetected() {
        if (bottomSheetControl.sheetOverlay.value.child != null) return
        if (dialogControl.dialogOverlay.value.child != null) return
        if (subscriptionBottomSheetControl.sheetOverlay.value.child != null) return
        (childStack.value.active.instance as? RootComponent.Child.Main)
            ?.component?.onShakeDetected()
    }

    private fun onDeeplinkOutput(output: DeeplinkNavigationComponent.Output) {
        when (output) {
            is DeeplinkNavigationComponent.Output.DeckInfoPageRequested -> {
                navigation.bringToFront(
                    ChildConfig.DeckInfo(
                        output.deckId,
                        isPaymentCompleted = output.isPaymentCompleted
                    )
                )
            }

            is DeeplinkNavigationComponent.Output.AnalyticsPageRequested -> {
                navigateToMain(NavigationPage.Showcase, UserAnalyticsComponent.Tab.Month)
            }

            DeeplinkNavigationComponent.Output.HomePageRequested -> {
                navigateToMain(NavigationPage.Home)
            }

            DeeplinkNavigationComponent.Output.CloseWebViewAuthRequested -> {
                if (childStack.value.active.instance is RootComponent.Child.WebView) {
                    navigation.pop()
                }
            }

            is DeeplinkNavigationComponent.Output.SubscriptionPageRequested -> {
                navigation.bringToFront(ChildConfig.AccountFlow(AccountFlowComponent.Screen.Subscription))
            }

            DeeplinkNavigationComponent.Output.SubscriptionPaymentCompleted -> {
                navigation.pop()
                messageService.showMessage(Message(R.string.account_subscription_success_message.strResDesc()))
            }

            is DeeplinkNavigationComponent.Output.CourseRequested -> {
                val screen = CourseComponent.Screen.Details(CourseDetailsComponent.Screen.Course(output.courseQuery))
                navigation.bringToFront(ChildConfig.Course(screen))
            }
        }
    }

    private fun onTutorialOutput(output: TutorialMessageComponent.Output) {
        when (output) {
            TutorialMessageComponent.Output.CancelTutorialRequested -> {
                navigateToMain(NavigationPage.Home)
            }
        }
    }

    /**
     * @param navigationPage what page have to be active. Null means leave a page unchanged.
     * @param analyticsInitialTab pass it to open the analytics page with a specific tab
     */
    private fun navigateToMain(
        navigationPage: NavigationPage? = null,
        analyticsInitialTab: UserAnalyticsComponent.Tab = UserAnalyticsComponent.Tab.Layouts,
    ) {
        navigation.replaceAll(ChildConfig.Main)
        if (navigationPage != null) {
            (childStack.value.active.instance as? RootComponent.Child.Main)
                ?.component?.onNavigationPageChange(navigationPage, analyticsInitialTab)
        }
    }

    private fun predefinedLayoutDetailsRequested(
        cardSource: CardSource,
        courseId: CourseId? = null,
        courseThemeId: CourseThemeId? = null
    ) {
        val questions = cardSource as? CardSource.Questions ?: error("predefinedLayoutDetailsRequested cast error")
        navigation.push(
            ChildConfig.DeckLayout(
                questions.copy(
                    list = questions.list.map {
                        it.copy(
                            text = LocalizableString.createNonLocalizable(it.text.toString(appLanguage.value))
                        )
                    }
                ),
                courseId = courseId,
                courseThemeId = courseThemeId
            )
        )
    }

    sealed interface ChildConfig : Parcelable {

        @Parcelize
        data class MainAuth(val startCommand: AuthComponent.StartCommand? = null) : ChildConfig

        @Parcelize
        data object Main : ChildConfig

        @Parcelize
        data object Splash : ChildConfig

        @Parcelize
        data class DeckLayout(
            val cardSource: CardSource,
            val questionText: String? = null,
            val courseId: CourseId? = null,
            val courseThemeId: CourseThemeId? = null
        ) : ChildConfig

        @Parcelize
        data class DeckInfo(val deckId: DeckId, val isPaymentCompleted: Boolean = false) :
            ChildConfig

        @Parcelize
        data object DeckScanning : ChildConfig

        @Parcelize
        data object PredefinedLayouts : ChildConfig

        @Parcelize
        data class RecordDetails(
            val recordSource: RecordSource,
            val recordType: RecordDetailsComponent.RecordType,
            val recordData: RecordData? = null,
            val isDailyCard: Boolean = false,
            val courseId: CourseId? = null,
            val courseThemeId: CourseThemeId? = null
        ) : ChildConfig

        @Parcelize
        data class CardPreview(
            val cards: List<Card>,
            val initialCardIndex: Int,
            val shouldShowGif: Boolean,
        ) : ChildConfig

        @Parcelize
        data object Calendar : ChildConfig

        @Parcelize
        data object JournalArchive : ChildConfig

        @Parcelize
        data class AccountFlow(val screenToShow: AccountFlowComponent.Screen) : ChildConfig

        @Parcelize
        data class FavoriteCards(val screen: FavoriteCardsComponent.Screen) : ChildConfig

        @Parcelize
        data class WebView(val url: String, @StringRes val title: Int) : ChildConfig

        @Parcelize
        data class WebViewResource(val url: @RawValue StringDesc, @StringRes val title: Int) :
            ChildConfig

        @Parcelize
        data object WelcomeScreens : ChildConfig

        @Parcelize
        data class CreateRecordFlow(val screenToShow: CreateRecordFlowComponent.Screen) :
            ChildConfig

        @Parcelize
        data object AdvUserConsent : ChildConfig

        @Parcelize
        data class SpecialDeck(val deckId: DeckId, val forceOnboarding: Boolean) : ChildConfig

        @Parcelize
        data object UserAnalytics : ChildConfig

        @Parcelize
        data class Course(val screen: CourseComponent.Screen) : ChildConfig
    }
}
