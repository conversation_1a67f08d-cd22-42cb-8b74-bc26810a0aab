package com.metacards.metacards.features.course.domain.interactor

import com.metacards.metacards.features.course.domain.entity.CourseData
import com.metacards.metacards.features.course.domain.repository.CourseRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine

class GetCourseDataInteractor(
    private val courseRepository: CourseRepository,
) {
    fun execute(query: CourseData.Query): Flow<CourseData?> {
        val courseData = courseRepository.getCourseDataFlow(query.themeId, query.courseId)
        val userPassedCourses = courseRepository.getUserPassedContentFlow(query.courseId)
        return combine(courseData, userPassedCourses) { data, courses ->
            val checkedContent = data?.content?.toMutableList()
            val completedCoursesIds = courses?.completedContent?.map { it.contentId } ?: emptyList()
            checkedContent?.toList()?.forEach { content ->
                if (content.contentId in completedCoursesIds) {
                    val copy = content.copy(isCompleted = true)
                    checkedContent.remove(content)
                    checkedContent.add(copy)
                }
            }
            data?.copy(
                content = checkedContent?.toList()?.sortedBy { it.order } ?: emptyList()
            )
        }
    }
}