package com.metacards.metacards.features.layout.ui.select_card.ar.sceneview

import com.google.android.filament.Engine
import com.google.android.filament.Material
import com.google.android.filament.MaterialInstance
import com.google.android.filament.Texture
import com.google.android.filament.TextureSampler
import io.github.sceneview.ar.node.ArModelNode
import io.github.sceneview.math.Position
import io.github.sceneview.math.Rotation
import io.github.sceneview.model.ModelInstance

class ArDeckNode(engine: Engine, cards: Collection<ArCardNode>) : ArModelNode(
    engine,
    modelGlbFileLocation = "models/card.glb",
    scaleToUnits = 0.1f
) {
    private val setOfCards: ArrayDeque<ArCardNode> = ArrayDeque(cards)

    private var material: Material? = null
    private var texture: Texture? = null
    private var materialInstance: MaterialInstance? = null

    init {
        modelRotation = Rotation(x = 90f)
        initChildren()
        isPositionEditable = false
        isScaleEditable = false
        isRotationEditable = false
        followHitPosition = false
    }

    override val isVisibleInHierarchy: Boolean
        get() = parentNode?.isVisibleInHierarchy != false

    override fun onModelLoaded(modelInstance: ModelInstance) {
        super.onModelLoaded(modelInstance)
        setupMaterial(material, texture)
    }

    override fun destroy() {
        materialInstance?.let { engine.destroyMaterialInstance(it) }
        materialInstance = null
        super.destroy()
    }

    fun addMaterial(material: Material?, texture: Texture?) {
        this.material = material
        this.texture = texture
    }

    fun popCard(): ArCardNode {
        val card = setOfCards.removeLast()
        removeChildSaveTransform(card)
        return card
    }

    fun hasCard(): Boolean = setOfCards.isNotEmpty()

    private fun initChildren() {
        setOfCards.forEachIndexed { index, node ->
            addChild(node)
            node.position = Position(y = 0.001f) * index.toFloat()
        }
    }

    private fun setupMaterial(material: Material?, texture: Texture?) {
        materialInstance = material?.createInstance() ?: return
        texture?.let {
            materialInstance!!.setParameter("texture", texture, TextureSampler())
        }

        setMaterialInstance(materialInstance!!)
    }
}