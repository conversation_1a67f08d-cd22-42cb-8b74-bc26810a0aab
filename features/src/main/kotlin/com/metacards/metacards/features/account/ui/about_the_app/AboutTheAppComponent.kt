package com.metacards.metacards.features.account.ui.about_the_app

import androidx.annotation.StringRes
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.strResDesc

interface AboutTheAppComponent {

    fun onTermsOfUseClick()
    fun onPrivacyPolicyClick()
    fun onAboutUsClick()

    enum class AboutAppSection(
        val title: StringDesc,
        val onClick: (AboutTheAppComponent) -> Unit,
    ) {
        TermsOfUse(
            R.string.about_the_app_terms_of_use.strResDesc(),
            AboutTheAppComponent::onTermsOfUseClick
        ),
        PrivacyPolicy(
            R.string.about_the_app_privacy_policy.strResDesc(),
            AboutTheAppComponent::onPrivacyPolicyClick
        ),
        AboutCreators(
            R.string.about_the_app_about_creators.strResDesc(),
            AboutTheAppComponent::onAboutUsClick
        )
    }

    sealed interface Output {
        data class WebViewResourceRequested(val url: StringDesc, @StringRes val titleRes: Int) : Output
        data class WebViewRequested(val url: String, @StringRes val titleRes: Int) : Output
        data object AboutUsRequested : Output
    }
}