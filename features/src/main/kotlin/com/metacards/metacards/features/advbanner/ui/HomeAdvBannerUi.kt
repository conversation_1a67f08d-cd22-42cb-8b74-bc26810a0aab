package com.metacards.metacards.features.advbanner.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.features.R
import com.metacards.metacards.features.advbanner.domain.entity.HomeAdvBanner
import com.metacards.metacards.features.advbanner.domain.entity.HomeBannerStyle

@Composable
fun HomeAdvBannerUi(
    banner: HomeAdvBanner?,
    modifier: Modifier = Modifier,
    onClick: (HomeAdvBanner) -> Unit = {},
    onDismiss: () -> Unit
) {
    banner ?: return
    val (bgColor, subtitleColor) = if (banner.style == HomeBannerStyle.PROMO) {
        CustomTheme.colors.accent.primary to CustomTheme.colors.text.primary
    } else {
        CustomTheme.colors.background.primary to CustomTheme.colors.text.secondary
    }

    Box(
        modifier = modifier
            .clip(RoundedCornerShape(16.dp))
            .background(bgColor)
            .clickable { onClick(banner) }
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Box(
                modifier = Modifier
                    .padding(start = 16.dp, top = 16.dp, bottom = 16.dp)
            ) {
                AsyncImage(
                    model = banner.coverUrl,
                    contentDescription = null,
                    modifier = Modifier.size(width = 60.dp, height = 40.dp)
                )
            }

            Column(
                modifier = Modifier
                    .padding(top = 16.dp, bottom = 16.dp, start = 12.dp)
            ) {
                Text(
                    text = banner.title.localizedByLocal(),
                    style = CustomTheme.typography.heading.medium,
                    color = CustomTheme.colors.text.primary,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier
                        .padding(end = 48.dp)
                )

                banner.description?.let {
                    Text(
                        text = it.localizedByLocal(),
                        style = CustomTheme.typography.caption.medium,
                        color = subtitleColor
                    )
                }
            }
        }
        if (banner.priority != 0) {
            Box(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .requiredSize(36.dp)
                    .clickable(boundedRipple = false, onClick = onDismiss)
            ) {
                Icon(
                    modifier = Modifier
                        .align(Alignment.Center)
                        .requiredSize(20.dp),
                    painter = painterResource(id = R.drawable.ic_20_close),
                    contentDescription = null,
                    tint = CustomTheme.colors.system.unspecified
                )
            }
        }
    }
}

@Preview
@Composable
fun AdvertisingBannerPreview() {
    AppTheme {
        HomeAdvBannerUi(banner = HomeAdvBanner.MOCK, onClick = {}, onDismiss = {})
    }
}
