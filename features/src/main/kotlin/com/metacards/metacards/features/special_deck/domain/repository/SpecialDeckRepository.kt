package com.metacards.metacards.features.special_deck.domain.repository

import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeck
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCard
import com.metacards.metacards.features.special_deck.domain.entity.UserSpecialDeckInfo
import kotlinx.coroutines.flow.Flow

interface SpecialDeckRepository {

    suspend fun getSpecialDecks(): List<SpecialDeck>
    suspend fun getSpecialDeckById(deckId: DeckId): SpecialDeck?
    suspend fun updateUserSpecialDeckInfo(info: UserSpecialDeckInfo)
    suspend fun getUserSpecialDeckInfoById(specialDeckId: DeckId): UserSpecialDeckInfo?

    fun getSpecialDecksFlow(): Flow<List<SpecialDeck>>
    fun getSpecialDeckByIdFlow(deckId: DeckId): Flow<SpecialDeck?>
    fun getSpecialDeckCardsByIdFlow(deckId: DeckId): Flow<List<SpecialDeckCard>>
    fun getUserSpecialDecksFlow(): Flow<List<UserSpecialDeckInfo>>
    fun getUserSpecialDeckInfoByIdFlow(specialDeckId: DeckId): Flow<UserSpecialDeckInfo>
}