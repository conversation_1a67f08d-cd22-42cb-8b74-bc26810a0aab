package com.metacards.metacards.features.record.ui.create_record.add_card.image_tracking.widgets

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import co.touchlab.kermit.Logger
import com.google.ar.core.AugmentedImageDatabase
import com.metacards.metacards.core.utils.e
import dev.romainguy.kotlin.math.Float3
import dev.romainguy.kotlin.math.Quaternion
import dev.romainguy.kotlin.math.cross
import dev.romainguy.kotlin.math.dot
import dev.romainguy.kotlin.math.length2
import dev.romainguy.kotlin.math.normalize
import io.github.sceneview.ar.ArSceneView
import io.github.sceneview.ar.arcore.transform
import io.github.sceneview.ar.node.AugmentedImageNode
import io.github.sceneview.node.ModelNode
import io.github.sceneview.node.Node
import kotlinx.coroutines.delay
import java.io.IOException
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.sqrt

private val logger = Logger.withTag("ImageTrackingUi")
private const val imageName = "512.png"
private const val modelPath = "models/scene.gltf"
private const val imageDbPath = "models/myimages.imgdb"
private const val augmentedImageNodeName = "AugmentedImage"
private const val modelNodeName = "Model"
private const val rotationSpeed = 70.0
private const val rotationRadius = 0.07
private const val rotationDuration = 60_000L

@Composable
fun ArSceneImageTracking(modifier: Modifier = Modifier, onStartTracking: () -> Unit) {
    val nodes = remember { mutableStateOf(listOf<Node>()) }
    var sceneViewNodes = remember { listOf<Node>() }
    val isTracking = remember { mutableStateOf(false) }

    AndroidView(
        modifier = modifier,
        factory = { context ->
            ArSceneView(context).apply {
                configureImageTrackingSession()

                val augmentedNode = createAugmentedImageNode(context, 0.1f) {
                    val imageNode = (nodes.value.firstOrNull() as? AugmentedImageNode)
                    val isTrack = imageNode?.isTracking ?: false
                    if (isTrack) {
                        onStartTracking()
                        isTracking.value = true
                    }
                }

                nodes.value += listOf(augmentedNode)
            }
        },
        update = { sceneView ->
            sceneViewNodes.filter { it !in nodes.value }.forEach {
                sceneView.removeChild(it)
            }
            nodes.value.filter { it !in sceneViewNodes }.forEach {
                sceneView.addChild(it)
            }
            sceneViewNodes = nodes.value.toList()

            sceneView.planeRenderer.isEnabled = false
        }
    )

    LaunchedEffect(key1 = isTracking.value, key2 = nodes.value) {
        if (!isTracking.value) return@LaunchedEffect

        val augmentedNode = nodes.value
            .filterIsInstance<AugmentedImageNode>()
            .firstOrNull { it.name == augmentedImageNodeName }
            ?: return@LaunchedEffect
        val arNode = augmentedNode.children
            .filterIsInstance<ModelNode>()
            .firstOrNull { it.name == modelNodeName }
            ?: return@LaunchedEffect
        val augmentedImage = augmentedNode.augmentedImage ?: return@LaunchedEffect

        rotateObjectAtFixedDistance(
            arNode,
            augmentedImage.centerPose.transform.up,
            Float3(),
            rotationSpeed,
            rotationRadius,
            rotationDuration
        )
    }
}

private fun ArSceneView.createAugmentedImageNode(
    context: Context,
    widthInMeters: Float,
    onUpdate: () -> Unit
): AugmentedImageNode {
    val node = AugmentedImageNode(
        engine,
        imageName = imageName,
        widthInMeters = widthInMeters,
        onError = {
            logger.e(it)
        },
        onUpdate = { _, _ ->
            onUpdate()
        }
    ).apply {
        name = augmentedImageNodeName
    }

    val modelNode = ModelNode(engine).apply {
        name = modelNodeName
        loadModelGltfAsync(
            context = context,
            modelPath,
            scaleToUnits = 0.05f,
            onLoaded = { logger.d("model loaded") },
            onError = { logger.e(it) }
        )
    }

    node.addChild(modelNode)
    return node
}

private fun ArSceneView.configureImageTrackingSession() {
    configureSession { arSession, config ->
        try {
            resources.assets.open(imageDbPath).use { inStream ->
                config.augmentedImageDatabase =
                    AugmentedImageDatabase.deserialize(arSession, inStream)
            }
        } catch (e: IOException) {
            logger.e(
                "IO exception loading augmented image database.",
                e
            )
        }
    }
}

private fun alignToNormal(normal: Float3): Quaternion {
    val up = Float3(0f, 1f, 0f)

    val w = sqrt(length2(up) * length2(normal) + dot(up, normal))
    val xyz = cross(up, normal)

    val rotationQuaternion = Quaternion(xyz.x, xyz.y, xyz.z, w)

    return normalize(rotationQuaternion)
}

private suspend fun rotateObjectAtFixedDistance(
    arNode: ModelNode,
    planeNormal: Float3,
    center: Float3,
    rotationSpeed: Double,
    radius: Double,
    durationMillis: Long,
) {
    val alignmentQuaternion = alignToNormal(planeNormal)
    arNode.quaternion = alignmentQuaternion

    val arbitraryVector =
        if (planeNormal.x != 0f || planeNormal.y != 0f) Float3(0f, 0f, 1f) else Float3(0f, 1f, 0f)
    val u = normalize(cross(planeNormal, arbitraryVector))

    val v = normalize(cross(planeNormal, u))

    val startTime = System.currentTimeMillis()
    while (System.currentTimeMillis() - startTime < durationMillis) {
        val elapsedTime =
            (System.currentTimeMillis() - startTime) / 1000.0
        val angle = rotationSpeed * elapsedTime
        val radianAngle = Math.toRadians(angle)

        val displacement = Float3(
            (u.x * cos(radianAngle) + v.x * sin(radianAngle)).toFloat(),
            (u.y * cos(radianAngle) + v.y * sin(radianAngle)).toFloat(),
            (u.z * cos(radianAngle) + v.z * sin(radianAngle)).toFloat()
        ) * radius.toFloat()

        val newPosition = center + displacement

        arNode.position = newPosition

        delay(10)
    }
}