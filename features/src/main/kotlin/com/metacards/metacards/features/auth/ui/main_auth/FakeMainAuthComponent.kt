package com.metacards.metacards.features.auth.ui.main_auth

import com.metacards.metacards.features.auth.domain.SSOType
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakeMainAuthComponent : MainAuthComponent {
    override val ssoAuthVariants: StateFlow<List<SSOType>> =
        MutableStateFlow(listOf(SSOType.Google, SSOType.Google, SSOType.Google))
    override val shouldAnimate = MutableStateFlow(true)

    override fun onSignInButtonClick() = Unit
    override fun onSignUpButtonClick() = Unit
    override fun onCloseButtonClick() = Unit
    override fun onSSOClick(ssoType: SSOType) = Unit
    override fun onAnimationEnd() = Unit
}