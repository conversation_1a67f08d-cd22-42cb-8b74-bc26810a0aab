package com.metacards.metacards.features.account.ui.profile.password.create_new

import com.metacards.metacards.core.button.ButtonState
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import ru.mobileup.kmm_form_validation.control.InputControl

class FakeProfileNewPasswordComponent : ProfileNewPasswordComponent {
    @OptIn(DelicateCoroutinesApi::class)
    override val newPasswordInputControl: InputControl = InputControl(GlobalScope)
    @OptIn(DelicateCoroutinesApi::class)
    override val confirmNewPasswordInputControl: InputControl = InputControl(GlobalScope)
    override val saveButtonState: StateFlow<ButtonState> = MutableStateFlow(ButtonState.Enabled)

    override fun onSaveClick() = Unit
}
