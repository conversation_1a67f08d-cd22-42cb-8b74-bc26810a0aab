package com.metacards.metacards.features.record.domain

import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CourseThemeId

data class NewRecord(
    val questions: List<Question>,
    val comments: List<Comment>,
    val userFeelings: UserFeelings,
    val isFavourite: <PERSON><PERSON><PERSON>,
    val isDailyCardLayout: <PERSON><PERSON><PERSON>,
    val isViewedAds: <PERSON><PERSON><PERSON>,
    val courseId: CourseId?,
    val courseThemeId: CourseThemeId?
)