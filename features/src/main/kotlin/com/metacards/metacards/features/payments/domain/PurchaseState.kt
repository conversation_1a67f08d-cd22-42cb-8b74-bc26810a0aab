package com.metacards.metacards.features.payments.domain

sealed interface PurchaseState {
    data object NotStarted : PurchaseState
    data class Started(val purchaseType: PurchaseType) : PurchaseState
    data class InfoReceived(val purchaseType: PurchaseType) : PurchaseState
    data class PurchaseFlowStarted(val purchaseType: PurchaseType) : PurchaseState
    data class Purchased(val purchaseType: PurchaseType) : PurchaseState
    data class Error(val error: com.metacards.metacards.features.payments.domain.Error) :
        PurchaseState

    val isInProgress: <PERSON>olean
        get() = this is Started || this is InfoReceived || this is PurchaseFlowStarted

    fun tryGetPurchaseType(): PurchaseType? {
        return when (this) {
            is Started -> purchaseType
            is InfoReceived -> purchaseType
            is PurchaseFlowStarted -> purchaseType
            is Purchased -> purchaseType
            else -> null
        }
    }
}