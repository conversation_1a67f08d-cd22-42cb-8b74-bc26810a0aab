package com.metacards.metacards.features.special_deck.data.data_source

import com.google.firebase.firestore.toObject
import com.metacards.metacards.core.network.firestore.FirestoreService
import com.metacards.metacards.core.network.firestore.getFlow
import com.metacards.metacards.core.user.domain.UserId
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.special_deck.data.dto.SpecialDeckCardDto
import com.metacards.metacards.features.special_deck.data.dto.SpecialDeckDto
import com.metacards.metacards.features.special_deck.data.dto.UserSpecialDeckInfoDto
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.tasks.await

class SpecialDeckDataSource(
    private val firestoreService: FirestoreService,
) {

    suspend fun getSpecialDecks(): List<SpecialDeckDto> =
        firestoreService.db.collection(SPECIAL_DECK_COLLECTION_PATH).get().await().documents
            .mapNotNull { it.toObject(SpecialDeckDto::class.java)?.copy(id = it.id) }

    fun getSpecialDecksFlow(): Flow<List<SpecialDeckDto>> {
        val flow = firestoreService.db.collection(SPECIAL_DECK_COLLECTION_PATH).getFlow(firestoreService)
        return flow.map { snapshot ->
            snapshot.documents.mapNotNull {
                it.toObject(SpecialDeckDto::class.java)?.copy(id = it.id)
            }
        }
    }

    suspend fun getSpecialDeckById(deckId: DeckId): SpecialDeckDto? {
        return firestoreService.db
            .collection(SPECIAL_DECK_COLLECTION_PATH)
            .document(deckId.value)
            .get()
            .await()
            .let { it.toObject<SpecialDeckDto?>()?.copy(id = it.id) }
    }

    fun getSpecialDeckByIdFlow(deckId: DeckId): Flow<SpecialDeckDto?> {
        val flow = firestoreService.db.collection(SPECIAL_DECK_COLLECTION_PATH).document(deckId.value)
            .getFlow(firestoreService)
        return flow.map { snapShot ->
            snapShot.toObject(SpecialDeckDto::class.java)?.copy(id = deckId.value)
        }
    }

    fun getSpecialDeckCardsByIdFlow(deckId: DeckId): Flow<List<SpecialDeckCardDto>> {
        val flow = firestoreService.db.collection(SPECIAL_DECK_COLLECTION_PATH).document(deckId.value)
            .collection(SPECIAL_DECK_COLLECTION_CARDS_PATH)
            .getFlow(firestoreService)
        return flow.map { snapShot ->
            snapShot.documents.mapNotNull {
                it.toObject(SpecialDeckCardDto::class.java)?.copy(id = it.id)
            }
        }
    }

    suspend fun updateUserSpecialDeckInfo(userId: UserId, info: UserSpecialDeckInfoDto) {
        firestoreService.db.collection(getUserSpecialDeckInfoCollectionPath(userId))
            .document(info.specialDeckId)
            .set(info)
            .await()
    }

    suspend fun getUserSpecialDeckInfoById(
        userId: UserId,
        specialDeckId: DeckId
    ): UserSpecialDeckInfoDto? {
        return firestoreService.db.collection(getUserSpecialDeckInfoCollectionPath(userId))
            .document(specialDeckId.value)
            .get()
            .await()
            .toObject<UserSpecialDeckInfoDto>()
    }

    fun getUserSpecialDeckInfoByIdFlow(
        userId: UserId,
        specialDeckId: DeckId
    ): Flow<UserSpecialDeckInfoDto?> {
        val flow = firestoreService.db.collection(getUserSpecialDeckInfoCollectionPath(userId))
            .document(specialDeckId.value)
            .getFlow(firestoreService)
        return flow.map { snapShot ->
            snapShot.toObject(UserSpecialDeckInfoDto::class.java)
        }
    }

    fun getUserSpecialDecksInfoFlow(userId: UserId): Flow<List<UserSpecialDeckInfoDto>> {
        val flow = firestoreService.db.collection(getUserSpecialDeckInfoCollectionPath(userId))
            .getFlow(firestoreService)
        return flow.map { snapShot ->
            snapShot.documents.mapNotNull { document ->
                document.toObject(UserSpecialDeckInfoDto::class.java)
            }
        }
    }

    private fun getUserSpecialDeckInfoCollectionPath(
        userId: UserId,
    ): String {
        return USERS_COLLECTION_PATH + SLASH + userId.value + SLASH +
            SPECIAL_DECK_COLLECTION_PATH
    }

    companion object {
        private const val SPECIAL_DECK_COLLECTION_PATH = "specialDecks"
        private const val SPECIAL_DECK_COLLECTION_CARDS_PATH = "cards"
        private const val USERS_COLLECTION_PATH = "users"
        private const val SLASH = "/"
    }
}