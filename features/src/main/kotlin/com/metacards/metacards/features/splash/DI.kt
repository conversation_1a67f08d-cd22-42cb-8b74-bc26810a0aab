package com.metacards.metacards.features.splash

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.features.splash.ui.RealSplashComponent
import com.metacards.metacards.features.splash.ui.SplashComponent
import org.koin.core.component.get
import org.koin.dsl.module

val splashModule = module {
}

fun ComponentFactory.createSplashComponent(
    componentContext: ComponentContext,
    onOutput: (SplashComponent.Output) -> Unit,
): SplashComponent {
    return RealSplashComponent(
        componentContext,
        onOutput,
        get(),
        get()
    )
}