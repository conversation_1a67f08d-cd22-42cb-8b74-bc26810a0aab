package com.metacards.metacards.features.tutorial.ui

import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import kotlinx.coroutines.flow.StateFlow

/**
 * A component for centralized tutorial message showing. There should be only one instance
 * of this component in the app connected to the root component.
 */
interface TutorialMessageComponent {

    val tutorialInProgress: StateFlow<Boolean>
    val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent>

    fun onTutorialCancel()

    sealed interface Output {
        data object CancelTutorialRequested : Output
    }
}