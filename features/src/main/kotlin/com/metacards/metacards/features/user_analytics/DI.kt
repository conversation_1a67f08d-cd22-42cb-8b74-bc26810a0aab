package com.metacards.metacards.features.user_analytics

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.features.record.data.RecordsUpdateListener
import com.metacards.metacards.features.record.domain.RecordListType
import com.metacards.metacards.features.user_analytics.domain.ClearUserAnalyticsInteractor
import com.metacards.metacards.features.user_analytics.domain.UpdateUserAnalyticsRecordInteractor
import com.metacards.metacards.features.user_analytics.domain.month.MonthAnalyticsPagedLoading
import com.metacards.metacards.features.user_analytics.domain.week.WeekAnalyticsPagedLoading
import com.metacards.metacards.features.user_analytics.ui.RealUserAnalyticsComponent
import com.metacards.metacards.features.user_analytics.ui.UserAnalyticsComponent
import com.metacards.metacards.features.user_analytics.ui.layouts.LayoutsAnalyticsComponent
import com.metacards.metacards.features.user_analytics.ui.layouts.RealLayoutsAnalyticsComponent
import com.metacards.metacards.features.user_analytics.ui.month.MonthAnalyticsComponent
import com.metacards.metacards.features.user_analytics.ui.month.RealMonthAnalyticsComponent
import com.metacards.metacards.features.user_analytics.ui.week.RealWeekAnalyticsComponent
import com.metacards.metacards.features.user_analytics.ui.week.WeekAnalyticsComponent
import org.koin.core.component.get
import org.koin.core.qualifier.qualifier
import org.koin.dsl.module

val userAnalyticsModule = module {
    factory<RecordsUpdateListener> {
        UpdateUserAnalyticsRecordInteractor(
            get(), get(),
            get(
                qualifier(
                    RecordListType.Analytics
                )
            )
        )
    }
    factory { ClearUserAnalyticsInteractor(get(), get(), get(qualifier(RecordListType.Analytics))) }
    single { WeekAnalyticsPagedLoading(get(), get()) }
    single { MonthAnalyticsPagedLoading(get(), get()) }
}

fun ComponentFactory.createAnalyticsComponent(
    componentContext: ComponentContext,
    onOutput: (UserAnalyticsComponent.Output) -> Unit
): UserAnalyticsComponent {
    return RealUserAnalyticsComponent(
        componentContext,
        get(),
        get(),
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createWeekAnalyticsComponent(
    componentContext: ComponentContext,
    onOutput: (WeekAnalyticsComponent.Output) -> Unit,
): WeekAnalyticsComponent {
    return RealWeekAnalyticsComponent(
        componentContext,
        get(),
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createLayoutsAnalyticsComponent(
    componentContext: ComponentContext,
    onOutput: (LayoutsAnalyticsComponent.Output) -> Unit,
): LayoutsAnalyticsComponent {
    return RealLayoutsAnalyticsComponent(
        componentContext,
        get(),
        get(qualifier(RecordListType.Analytics)),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createMonthAnalyticsComponent(
    componentContext: ComponentContext,
    onOutput: (MonthAnalyticsComponent.Output) -> Unit
): MonthAnalyticsComponent {
    return RealMonthAnalyticsComponent(componentContext, get(), get(), get(), get(), onOutput)
}