package com.metacards.metacards.features.user_analytics.ui

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.arkivanov.decompose.extensions.compose.jetpack.stack.Children
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.IconNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.core.widget.tab.MetaTabs
import com.metacards.metacards.features.R
import com.metacards.metacards.features.home.ui.widget.ActionButton
import com.metacards.metacards.features.user_analytics.ui.layouts.LayoutsAnalyticsUi
import com.metacards.metacards.features.user_analytics.ui.month.MonthAnalyticsUi
import com.metacards.metacards.features.user_analytics.ui.week.WeekAnalyticsUi
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun UserAnalyticsUi(component: UserAnalyticsComponent, modifier: Modifier = Modifier) {
    val childStack by component.childStack.collectAsState()
    val selectedTab by component.selectedTab.collectAsState()
    val userSubscriptionState by component.userSubscriptionState.collectAsState()

    BoxWithFade(
        modifier = modifier
            .statusBarsPadding(),
        listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            TopNavigationBar(
                title = R.string.analytics_title.strResDesc(),
                leadingIcon = { BackNavigationItem() }
            )

            AnalyticsTabs(
                selectedTab = selectedTab,
                onTabSelected = component::onTabSelected
            )

            if (userSubscriptionState !is User.SubscriptionState.Ongoing) {
                BoxWithFade(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(16.dp))
                        .clickable { component.onBlockContentClick() },
                    listOfColors = CustomTheme.colors.gradient.backgroundList.map {
                        it.copy(alpha = 0.75f)
                    },
                    behindContent = {
                        Children(stack = childStack) { child ->
                            when (val instance = child.instance) {
                                is UserAnalyticsComponent.Child.Layouts -> LayoutsAnalyticsUi(instance.component)
                                is UserAnalyticsComponent.Child.Week -> WeekAnalyticsUi(instance.component)
                                is UserAnalyticsComponent.Child.Month -> MonthAnalyticsUi(instance.component)
                            }
                        }
                    }
                ) {}
            } else {
                Children(stack = childStack) { child ->
                    when (val instance = child.instance) {
                        is UserAnalyticsComponent.Child.Layouts -> LayoutsAnalyticsUi(instance.component)
                        is UserAnalyticsComponent.Child.Week -> WeekAnalyticsUi(instance.component)
                        is UserAnalyticsComponent.Child.Month -> MonthAnalyticsUi(instance.component)
                    }
                }
            }
        }

        if (userSubscriptionState !is User.SubscriptionState.Ongoing) {
            Box(modifier = Modifier.matchParentSize()) {
                ActionButton(
                    modifier = Modifier
                        .align(Alignment.Center)
                        .clickable { component.onBlockContentClick() },
                    text = StringDesc.Resource(R.string.add_record_premium_action_button),
                    borderStroke = BorderStroke(
                        0.5.dp,
                        CustomTheme.colors.stroke.secondary
                    ),
                    leadingIcon = {
                        IconNavigationItem(iconRes = R.drawable.ic_24_locked)
                    }
                )
            }
        }
    }
}

@Composable
private fun AnalyticsTabs(
    selectedTab: UserAnalyticsComponent.Tab,
    onTabSelected: (UserAnalyticsComponent.Tab) -> Unit,
    modifier: Modifier = Modifier
) {
    MetaTabs(
        modifier = modifier.padding(start = 10.dp, end = 10.dp, top = 4.dp, bottom = 12.dp),
        selectedTabIndex = selectedTab.toIndex(),
        tabItems = listOf(
            R.string.analytics_tab_layouts.strResDesc(),
            R.string.analytics_tab_week.strResDesc(),
            R.string.analytics_tab_month.strResDesc()
        ),
        onTabClicked = {
            onTabSelected(it.toTab())
        }
    )
}

private fun UserAnalyticsComponent.Tab.toIndex(): Int = when (this) {
    UserAnalyticsComponent.Tab.Layouts -> 0
    UserAnalyticsComponent.Tab.Week -> 1
    UserAnalyticsComponent.Tab.Month -> 2
}

private fun Int.toTab(): UserAnalyticsComponent.Tab = when (this) {
    0 -> UserAnalyticsComponent.Tab.Layouts
    1 -> UserAnalyticsComponent.Tab.Week
    else -> UserAnalyticsComponent.Tab.Month
}

@Composable
@Preview(showSystemUi = true)
fun UserAnalyticsUiPreview() {
    AppTheme {
        UserAnalyticsUi(FakeUserAnalyticsComponent())
    }
}