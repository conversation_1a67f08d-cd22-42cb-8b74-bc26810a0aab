package com.metacards.metacards.features.account.ui.profile.name

import com.metacards.metacards.core.button.ButtonState
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import ru.mobileup.kmm_form_validation.control.InputControl

class FakeProfileNameComponent : ProfileNameComponent {
    override val initName: String = "Алиса"
    @OptIn(DelicateCoroutinesApi::class)
    override val inputControl: InputControl = InputControl(GlobalScope)
    override val saveButtonState: StateFlow<ButtonState> = MutableStateFlow(ButtonState.Enabled)

    override fun onSaveClick() = Unit
}