package com.metacards.metacards.features.favorite_cards.ui

import android.os.Parcelable
import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.favorite_cards.ui.details.FavoriteCardDetailsComponent
import com.metacards.metacards.features.favorite_cards.ui.list.FavoriteCardListComponent
import com.metacards.metacards.features.record.domain.RecordId
import kotlinx.coroutines.flow.StateFlow
import kotlinx.parcelize.Parcelize

interface FavoriteCardsComponent {

    val childStack: StateFlow<ChildStack<*, Child>>

    sealed interface Child {
        data class List(val component: FavoriteCardListComponent) : Child
        data class Details(val component: FavoriteCardDetailsComponent) : Child
    }

    sealed interface Screen : Parcelable {
        @Parcelize
        data object List : Screen

        @Parcelize
        data class Details(val cardId: CardId) : Screen
    }

    sealed interface Output {
        data object GoToMainRequested : Output
        data object GoBackRequested : Output
        data class RecordDetailsRequested(val recordId: RecordId) : Output
    }
}