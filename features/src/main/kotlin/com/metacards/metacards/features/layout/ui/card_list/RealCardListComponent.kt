package com.metacards.metacards.features.layout.ui.card_list

import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.backhandler.BackCallback
import com.arkivanov.essenty.lifecycle.doOnStart
import com.metacards.metacards.core.adv.domain.YandexAdvHelper
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.error_handling.safeRun
import com.metacards.metacards.core.localization.data.AppLanguageService
import com.metacards.metacards.core.localization.domain.AppLanguage
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.sharing.SharingManager
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.UserInputTransformation
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.withProgress
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardWithFavoriteAndComment
import com.metacards.metacards.features.deck.domain.entity.toFavorite
import com.metacards.metacards.features.deck.domain.entity.withExpanded
import com.metacards.metacards.features.deck.domain.interactor.GetCardWithFavoriteInteractor
import com.metacards.metacards.features.deck.domain.interactor.IsCardFavoriteInteractor
import com.metacards.metacards.features.layout.domain.CardListViewData
import com.metacards.metacards.features.tutorial.data.TutorialMessageService
import com.metacards.metacards.features.tutorial.data.TutorialStep
import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import com.metacards.metacards.features.user.domain.GetUserSubscriptionStateInteractor
import com.metacards.metacards.features.user.domain.ToggleFavoriteCardInteractor
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update

class RealCardListComponent(
    componentContext: ComponentContext,
    override val cardListViewData: CardListViewData,
    private val tutorialMessageService: TutorialMessageService,
    private val sharingManager: SharingManager,
    getCardWithFavoriteInteractor: GetCardWithFavoriteInteractor,
    getUserSubscriptionStateInteractor: GetUserSubscriptionStateInteractor,
    private val toggleFavoriteCardInteractor: ToggleFavoriteCardInteractor,
    private val isCardFavoriteInteractor: IsCardFavoriteInteractor,
    private val errorHandler: ErrorHandler,
    private val analyticsService: AnalyticsService,
    appLanguageService: AppLanguageService,
    private val yandexAdvHelper: YandexAdvHelper,
    private val onOutput: (CardListComponent.Output) -> Unit,
) : ComponentContext by componentContext, CardListComponent {

    companion object {
        private const val MAX_CARD_COUNT = 2
        private const val MAX_CARD_COMMENT_TEXT_LENGTH = 3000
    }

    private val appLanguage: StateFlow<AppLanguage> = appLanguageService.currentAppLanguage

    private val debounce = Debounce()

    private val subscriptionState = getUserSubscriptionStateInteractor.execute()
        .stateIn(componentScope, SharingStarted.Eagerly, null)

    private val availableCardsWithFavorite = safeRun(errorHandler) {
        getCardWithFavoriteInteractor.execute(cardListViewData.availableCards).toMutableList()
    } ?: mutableListOf()

    private val backCallback = BackCallback { onOutput(CardListComponent.Output.CloseRequested) }

    override val tutorialMessage: StateFlow<TutorialMessage?> = combine(
        tutorialMessageService.tutorialStepFlow, tutorialMessageService.tutorialMessageFlow
    ) { step, message ->
        if (step == TutorialStep.CARD_SELECT_FINISH) message else null
    }.stateIn(componentScope, SharingStarted.Lazily, null)

    init {
        backHandler.register(backCallback)
        lifecycle.doOnStart {
            updateFavoriteFlagsOfSelectedCards()
            tutorialMessageService.handleCardListComponentTutorial(componentScope)
        }
    }

    override val canSelectMoreCard = MutableStateFlow(CardListComponent.CanSelectCard.Can)

    override val selectedCards = MutableStateFlow(
        listOf(
            safeRun(errorHandler) {
                getCardWithFavoriteInteractor.execute(cardListViewData.selectedCard)
            } ?: CardWithFavoriteAndComment.default(cardListViewData.selectedCard)
        )
    )

    override val pageCount = computed(selectedCards) { it.size }

    override val cardHints = MutableStateFlow(
        cardListViewData.cardHintList?.let {
            it.shuffled() + it.shuffled() + it.shuffled()
        }?.map { it.withExpanded() }
    )

    override val isShareLoading: MutableStateFlow<Boolean> = MutableStateFlow(false)

    override fun onToggleShare(cardUrl: String, cardIndex: Int) {
        componentScope.safeLaunch(errorHandler) {
            withProgress(isShareLoading) {
                val text = cardListViewData.questionText
                    ?: cardHints.value?.get(cardIndex)?.hint?.text
                    ?: error("onToggleShare text not found")

                sharingManager.shareCard(
                    cardUrl = cardUrl,
                    isDailyCard = cardListViewData.isDailyCard,
                    text = text.toString(appLanguage.value)
                )
            }
        }
    }

    override fun onToggleFavorite(cardIndex: Int, isOn: Boolean) {
        DebounceClick(debounce, "toggleFavorite", errorHandler) {
            when (subscriptionState.value) {
                is User.SubscriptionState.None -> onOutput(
                    CardListComponent.Output.PremiumSuggestingScreenRequested()
                )

                is User.SubscriptionState.Ongoing -> {
                    if (isOn) {
                        run {
                            when {
                                cardListViewData.isDailyCard -> return@run
                                cardListViewData.isPredefined -> AnalyticsEvent.LayoutAddFavCardEvent
                                else -> AnalyticsEvent.SetupManualCardFavAddEvent
                            }.let(analyticsService::logEvent)
                        }
                    }
                    toggleFavorite(cardIndex, isOn)
                }

                null -> onOutput(CardListComponent.Output.AuthSuggestingScreenRequested)
            }
        }
    }

    override fun onMoreCardClick() {
        if (cardListViewData.isPredefined) {
            AnalyticsEvent.LayoutOneMoreEvent(cardListViewData.questionIndex)
        } else {
            AnalyticsEvent.SetupManualCardOneMoreEvent
        }.let(analyticsService::logEvent)

        selectedCards.value += availableCardsWithFavorite.removeFirst()

        canSelectMoreCard.value = when {
            selectedCards.value.size >= MAX_CARD_COUNT -> CardListComponent.CanSelectCard.Cant
            selectedCards.value.size == MAX_CARD_COUNT - 1 -> CardListComponent.CanSelectCard.CanLast
            else -> CardListComponent.CanSelectCard.Can
        }
    }

    override fun onHintExpand(page: Int, isExpanded: Boolean) {
        cardHints.update { hintsWithExpanded ->
            hintsWithExpanded?.let {
                val mutableHintList = it.toMutableList()
                mutableHintList[page] = mutableHintList[page].copy(isExpanded = isExpanded)
                mutableHintList
            }
        }
    }

    override fun onFinishClick() {
        cardListViewData.run {
            if (isPredefined) {
                if (isLastPredefined) {
                    AnalyticsEvent.LayoutFinishEvent
                } else {
                    AnalyticsEvent.LayoutNextQuestionEvent(questionIndex)
                }.let(analyticsService::logEvent)

                onOutput(
                    CardListComponent.Output.OnPredefinedFinished(
                        selectedCards.value.map { it.toCardWithComment() },
                        isLastPredefined
                    )
                )
            } else {
                analyticsService.logEvent(AnalyticsEvent.SetupManualFinishEvent)
                onOutput(
                    CardListComponent.Output.OnFinished(
                        selectedCards.value.map { it.toCardWithComment() }
                    )
                )
            }
        }
    }

    override fun onAddEntryClick() {
        when {
            cardListViewData.recordId != null -> onOutput(
                CardListComponent.Output.DailyCardRecordRequested(cardListViewData.recordId)
            )

            subscriptionState.value?.canLayout == true -> finishDaily()

            subscriptionState.value is User.SubscriptionState.None -> {
                onOutput(CardListComponent.Output.PremiumSuggestingScreenRequested(withAdv = true))
                yandexAdvHelper.subscribeForReward(::finishDaily)
            }

            else -> onOutput(CardListComponent.Output.AuthSuggestingScreenRequested)
        }
    }

    override fun onCardClick(card: Card) {
        onOutput(CardListComponent.Output.FullScreenCardRequested(card))
    }

    override fun onCardCommentValueChanged(cardIndex: Int, value: String) {
        val transformedValue = UserInputTransformation
            .transform(value)
            .take(MAX_CARD_COMMENT_TEXT_LENGTH)

        selectedCards.update {
            it.toMutableList().apply {
                this[cardIndex] = this[cardIndex].copy(comment = transformedValue)
            }
        }
    }

    override fun onCloseClick() {
        onOutput(CardListComponent.Output.CloseRequested)
    }

    private suspend fun toggleFavorite(cardIndex: Int, isOn: Boolean) {
        val cardWithFavorite = selectedCards.value[cardIndex]
        toggleFavoriteCardInteractor.execute(isOn, cardWithFavorite.card.toFavorite())
        selectedCards.value =
            selectedCards.value.map { if (it.card == cardWithFavorite.card) cardWithFavorite.toggleFavorite() else it }
    }

    private fun updateFavoriteFlagsOfSelectedCards() {
        safeRun(errorHandler) {
            selectedCards.value = selectedCards.value.map {
                it.copy(isFavorite = isCardFavoriteInteractor.execute(it.card.id))
            }
        }
    }

    private fun finishDaily() {
        cardHints.value?.let { hintList ->
            onOutput(
                CardListComponent.Output.OnFinishedDaily(
                    cardWithComment = selectedCards.value.first().toCardWithComment(),
                    question = LocalizableString.createNonLocalizable(
                        hintList.first().hint.text.toString(appLanguage.value)
                    )
                )
            )
        }
    }
}
