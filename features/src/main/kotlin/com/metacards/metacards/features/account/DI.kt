package com.metacards.metacards.features.account

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.user.data.UserDto
import com.metacards.metacards.features.account.data.FeedbackRepositoryImpl
import com.metacards.metacards.features.account.data.LessonRepositoryImpl
import com.metacards.metacards.features.account.data.PromocodesRepositoryImpl
import com.metacards.metacards.features.account.data.data_source.LessonsDataSource
import com.metacards.metacards.features.account.data.data_source.PromocodesDataSource
import com.metacards.metacards.features.account.domain.entity.LessonCategoryId
import com.metacards.metacards.features.account.domain.interactor.DeleteUserInteractor
import com.metacards.metacards.features.account.domain.interactor.GetAllLessonCategoriesInteractor
import com.metacards.metacards.features.account.domain.interactor.GetCategoryLessonsInteractor
import com.metacards.metacards.features.account.domain.interactor.GetDecksForPurchaseWithCardsInteractor
import com.metacards.metacards.features.account.domain.interactor.GetUserSubscriptionToNotificationsInteractor
import com.metacards.metacards.features.account.domain.interactor.SendFeedbackInteractor
import com.metacards.metacards.features.account.domain.interactor.promocodes.ActivatePromocodeInteractor
import com.metacards.metacards.features.account.domain.repository.FeedbackRepository
import com.metacards.metacards.features.account.domain.repository.LessonRepository
import com.metacards.metacards.features.account.domain.repository.PromocodesRepository
import com.metacards.metacards.features.account.ui.AccountComponent
import com.metacards.metacards.features.account.ui.RealAccountComponent
import com.metacards.metacards.features.account.ui.about_the_app.AboutTheAppComponent
import com.metacards.metacards.features.account.ui.about_the_app.RealAboutTheAppComponent
import com.metacards.metacards.features.account.ui.about_the_app.about_us.AboutUsComponent
import com.metacards.metacards.features.account.ui.about_the_app.about_us.RealAboutUsComponent
import com.metacards.metacards.features.account.ui.feedback.FeedbackComponent
import com.metacards.metacards.features.account.ui.feedback.RealFeedbackComponent
import com.metacards.metacards.features.account.ui.flow.AccountFlowComponent
import com.metacards.metacards.features.account.ui.flow.RealAccountFlowComponent
import com.metacards.metacards.features.account.ui.language.AccountLanguageComponent
import com.metacards.metacards.features.account.ui.language.RealAccountLanguageComponent
import com.metacards.metacards.features.account.ui.lessons.LessonsComponent
import com.metacards.metacards.features.account.ui.lessons.RealLessonsComponent
import com.metacards.metacards.features.account.ui.lessons.categories.LessonCategoriesComponent
import com.metacards.metacards.features.account.ui.lessons.categories.RealLessonCategoriesComponent
import com.metacards.metacards.features.account.ui.lessons.details.LessonDetailsComponent
import com.metacards.metacards.features.account.ui.lessons.details.RealLessonDetailsComponent
import com.metacards.metacards.features.account.ui.lessons.list.LessonListComponent
import com.metacards.metacards.features.account.ui.lessons.list.RealLessonListComponent
import com.metacards.metacards.features.account.ui.profile.ProfileFlowComponent
import com.metacards.metacards.features.account.ui.profile.RealProfileFlowComponent
import com.metacards.metacards.features.account.ui.profile.birth_year.ProfileBirthYearComponent
import com.metacards.metacards.features.account.ui.profile.birth_year.RealProfileBirthYearComponent
import com.metacards.metacards.features.account.ui.profile.delete.ProfileDeleteAccountComponent
import com.metacards.metacards.features.account.ui.profile.delete.RealProfileDeleteAccountComponent
import com.metacards.metacards.features.account.ui.profile.email.ProfileEmailComponent
import com.metacards.metacards.features.account.ui.profile.email.RealProfileEmailComponent
import com.metacards.metacards.features.account.ui.profile.email.new_email.ProfileNewEmailComponent
import com.metacards.metacards.features.account.ui.profile.email.new_email.RealProfileNewEmailComponent
import com.metacards.metacards.features.account.ui.profile.gender.ProfileGenderComponent
import com.metacards.metacards.features.account.ui.profile.gender.RealProfileGenderComponent
import com.metacards.metacards.features.account.ui.profile.main.ProfileMainComponent
import com.metacards.metacards.features.account.ui.profile.main.RealProfileMainComponent
import com.metacards.metacards.features.account.ui.profile.name.ProfileNameComponent
import com.metacards.metacards.features.account.ui.profile.name.RealProfileNameComponent
import com.metacards.metacards.features.account.ui.profile.password.ProfilePasswordComponent
import com.metacards.metacards.features.account.ui.profile.password.RealProfilePasswordComponent
import com.metacards.metacards.features.account.ui.profile.password.confirm.ProfileConfirmPasswordComponent
import com.metacards.metacards.features.account.ui.profile.password.confirm.RealProfileConfirmPasswordComponent
import com.metacards.metacards.features.account.ui.profile.password.create_new.ProfileNewPasswordComponent
import com.metacards.metacards.features.account.ui.profile.password.create_new.RealProfileNewPasswordComponent
import com.metacards.metacards.features.account.ui.promocodes.PromocodeComponent
import com.metacards.metacards.features.account.ui.promocodes.RealPromocodeComponent
import com.metacards.metacards.features.account.ui.shop.RealShopComponent
import com.metacards.metacards.features.account.ui.shop.ShopComponent
import com.metacards.metacards.features.deck.domain.entity.Lesson
import dev.icerock.moko.resources.desc.StringDesc
import org.koin.core.component.get
import org.koin.dsl.module

val accountModule = module {
    single { LessonsDataSource(get()) }
    single<LessonRepository> { LessonRepositoryImpl(get()) }
    single<FeedbackRepository> { FeedbackRepositoryImpl(get()) }
    single { PromocodesDataSource(get(), get()) }
    single<PromocodesRepository> { PromocodesRepositoryImpl(get()) }
    single { GetAllLessonCategoriesInteractor(get(), get(), get()) }
    factory { GetCategoryLessonsInteractor(get(), get()) }
    factory { SendFeedbackInteractor(get()) }
    single { GetDecksForPurchaseWithCardsInteractor(get(), get(), get(), get()) }
    factory { GetUserSubscriptionToNotificationsInteractor(get()) }
    factory { DeleteUserInteractor(get()) }
    factory { ActivatePromocodeInteractor(get(), get()) }
}

fun ComponentFactory.createAccountComponent(
    componentContext: ComponentContext,
    onOutput: (AccountComponent.Output) -> Unit
): AccountComponent {
    return RealAccountComponent(
        componentContext,
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createAccountFlowComponent(
    componentContext: ComponentContext,
    screenToShow: AccountFlowComponent.Screen,
    onOutput: (AccountFlowComponent.Output) -> Unit
): AccountFlowComponent {
    return RealAccountFlowComponent(
        componentContext,
        get(),
        get(),
        get(),
        screenToShow,
        onOutput,
    )
}

fun ComponentFactory.createLessonsComponent(
    componentContext: ComponentContext,
    onOutput: (LessonsComponent.Output) -> Unit,
    screen: LessonsComponent.Screen
): LessonsComponent {
    return RealLessonsComponent(
        componentContext,
        get(),
        onOutput,
        screen = screen
    )
}

fun ComponentFactory.createLessonCategoriesComponent(
    componentContext: ComponentContext,
    onOutput: (LessonCategoriesComponent.Output) -> Unit
): LessonCategoriesComponent {
    return RealLessonCategoriesComponent(
        componentContext,
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createLessonListComponent(
    componentContext: ComponentContext,
    categoryId: LessonCategoryId,
    onOutput: (LessonListComponent.Output) -> Unit
): LessonListComponent {
    return RealLessonListComponent(
        componentContext,
        categoryId,
        get(),
        get(),
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createLessonDetailsComponent(
    componentContext: ComponentContext,
    lesson: Lesson
): LessonDetailsComponent {
    return RealLessonDetailsComponent(
        componentContext,
        lesson,
        get(),
        get(),
        get()
    )
}

fun ComponentFactory.createAboutTheAppComponent(
    componentContext: ComponentContext,
    onOutput: (AboutTheAppComponent.Output) -> Unit
): AboutTheAppComponent {
    return RealAboutTheAppComponent(
        componentContext,
        get(),
        onOutput
    )
}

fun ComponentFactory.createAboutUsComponent(
    componentContext: ComponentContext
): AboutUsComponent {
    return RealAboutUsComponent(componentContext, get())
}

fun ComponentFactory.createShopComponent(
    componentContext: ComponentContext,
    onOutput: (ShopComponent.Output) -> Unit
): ShopComponent {
    return RealShopComponent(
        componentContext,
        get(),
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createProfileFlowComponent(
    componentContext: ComponentContext,
    screen: ProfileFlowComponent.Screen,
    onOutput: (ProfileFlowComponent.Output) -> Unit
): ProfileFlowComponent {
    return RealProfileFlowComponent(
        componentContext,
        screen,
        get(),
        onOutput
    )
}

fun ComponentFactory.createProfileMainComponent(
    componentContext: ComponentContext,
    onOutput: (ProfileMainComponent.Output) -> Unit
): ProfileMainComponent {
    return RealProfileMainComponent(
        componentContext,
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createProfileNameComponent(
    componentContext: ComponentContext,
    initialUserName: String,
    onOutput: (ProfileNameComponent.Output) -> Unit
): ProfileNameComponent {
    return RealProfileNameComponent(
        componentContext,
        initialUserName,
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createProfileBirthYearComponent(
    componentContext: ComponentContext,
    initialYear: Int?,
    onOutput: (ProfileBirthYearComponent.Output) -> Unit
): ProfileBirthYearComponent {
    return RealProfileBirthYearComponent(
        componentContext,
        initialYear,
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createProfileGenderComponent(
    componentContext: ComponentContext,
    initialGender: UserDto.Gender?,
    onOutput: (ProfileGenderComponent.Output) -> Unit
): ProfileGenderComponent {
    return RealProfileGenderComponent(
        componentContext,
        initialGender,
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createFeedbackComponent(
    componentContext: ComponentContext,
    onOutput: (FeedbackComponent.Output) -> Unit
): FeedbackComponent {
    return RealFeedbackComponent(
        componentContext,
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createProfileEmailComponent(
    componentContext: ComponentContext,
    onOutput: (ProfileEmailComponent.Output) -> Unit
): ProfileEmailComponent {
    return RealProfileEmailComponent(
        componentContext,
        get(),
        onOutput
    )
}

fun ComponentFactory.createProfileConfirmPasswordComponent(
    componentContext: ComponentContext,
    toolbarText: StringDesc,
    textFieldHeader: StringDesc,
    textFieldCaption: StringDesc?,
    bottomButtonText: StringDesc,
    onOutput: (ProfileConfirmPasswordComponent.Output) -> Unit
): ProfileConfirmPasswordComponent {
    return RealProfileConfirmPasswordComponent(
        componentContext,
        get(),
        toolbarText,
        textFieldHeader,
        textFieldCaption,
        bottomButtonText,
        get(),
        onOutput
    )
}

fun ComponentFactory.createProfileNewEmailComponent(
    componentContext: ComponentContext,
    password: String,
    onOutput: (ProfileNewEmailComponent.Output) -> Unit
): ProfileNewEmailComponent {
    return RealProfileNewEmailComponent(
        componentContext,
        password,
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createProfilePasswordComponent(
    componentContext: ComponentContext,
    onOutput: (ProfilePasswordComponent.Output) -> Unit
): ProfilePasswordComponent {
    return RealProfilePasswordComponent(
        componentContext,
        get(),
        onOutput
    )
}

fun ComponentFactory.createProfileNewPasswordComponent(
    componentContext: ComponentContext,
    oldPassword: String,
    onOutput: (ProfileNewPasswordComponent.Output) -> Unit
): ProfileNewPasswordComponent {
    return RealProfileNewPasswordComponent(
        componentContext,
        oldPassword,
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createAccountLanguageComponent(
    componentContext: ComponentContext,
    onOutput: (AccountLanguageComponent.Output) -> Unit
): AccountLanguageComponent {
    return RealAccountLanguageComponent(
        componentContext,
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createProfileDeleteAccountComponent(
    componentContext: ComponentContext,
    onOutput: (ProfileDeleteAccountComponent.Output) -> Unit
): ProfileDeleteAccountComponent {
    return RealProfileDeleteAccountComponent(
        componentContext,
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createPromocodeComponent(
    componentContext: ComponentContext,
    onOutput: (PromocodeComponent.Output) -> Unit
): PromocodeComponent {
    return RealPromocodeComponent(
        componentContext,
        get(),
        get(),
        onOutput
    )
}