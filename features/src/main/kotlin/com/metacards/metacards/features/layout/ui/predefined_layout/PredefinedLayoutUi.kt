package com.metacards.metacards.features.layout.ui.predefined_layout

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.dialog.default_component.DefaultDialog
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.navigationBarsPaddingDp
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.LceWidget
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.layout.ui.predefined_layout.widgets.PredefinedLayoutGroupItem
import com.metacards.metacards.features.layout.ui.predefined_layout.widgets.PredefinedLayoutItem
import com.metacards.metacards.features.layout.ui.predefined_layout.widgets.PredefinedLayoutSkeleton
import dev.icerock.moko.resources.desc.strResDesc

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun PredefinedLayoutUi(
    component: PredefinedLayoutComponent,
    modifier: Modifier = Modifier,
) {
    val layoutGroups by component.filteredLayoutGroups.collectAsState()
    val groupNames by component.groupNames.collectAsState()
    val currentGroupIndex by component.currentGroupIndex.collectAsState()

    val lazyListState = rememberLazyListState()

    LaunchedEffect(currentGroupIndex) {
        lazyListState.animateScrollToItem(0)
    }

    BoxWithFade(
        modifier = modifier
            .fillMaxSize()
            .statusBarsPadding(),
        listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        Column {
            TopNavigationBar(
                title = R.string.predefined_layout_header.strResDesc(),
                leadingIcon = {
                    BackNavigationItem()
                }
            )

            LceWidget(
                state = layoutGroups,
                onRetryClick = { },
                loadingProgress = { PredefinedLayoutSkeleton() }
            ) { groups, _ ->
                LazyRow(
                    modifier = Modifier.fillMaxWidth(),
                    contentPadding = PaddingValues(
                        start = 16.dp,
                        end = 16.dp,
                        top = 16.dp,
                        bottom = 8.dp
                    ),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    itemsIndexed(items = groupNames) { index, groupName ->
                        PredefinedLayoutGroupItem(
                            text = groupName.localizedByLocal(),
                            isSelected = index == currentGroupIndex,
                            onClick = { component.onGroupClick(index) }
                        )
                    }
                }

                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    state = lazyListState,
                    verticalArrangement = Arrangement.spacedBy(12.dp),
                    contentPadding = PaddingValues(
                        start = 16.dp,
                        end = 16.dp,
                        top = 8.dp,
                        bottom = navigationBarsPaddingDp
                    )
                ) {
                    groups.forEach { group ->
                        item {
                            Text(
                                modifier = Modifier
                                    .padding(bottom = 4.dp, top = 8.dp)
                                    .animateItemPlacement(),
                                text = group.name.localizedByLocal(),
                                style = CustomTheme.typography.heading.medium,
                                color = CustomTheme.colors.text.primary,
                            )
                        }
                        items(group.layouts) { layoutWithAvailable ->
                            PredefinedLayoutItem(
                                layoutWithAvailable = layoutWithAvailable,
                                onLayoutClick = component::onLayoutClick,
                                onBlockContentClick = component::onBlockContentClick,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .aspectRatio(2f)
                                    .animateItemPlacement()
                            )
                        }
                        item {
                            Spacer(Modifier.height(12.dp))
                        }
                    }
                }
            }
        }
    }

    DefaultDialog(dialogControl = component.dialogControl)
}

@Preview
@Composable
private fun PredefinedLayoutPreview() {
    AppTheme {
        PredefinedLayoutUi(component = FakePredefinedLayoutComponent())
    }
}