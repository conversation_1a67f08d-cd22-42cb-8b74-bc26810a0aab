package com.metacards.metacards.features.special_deck.data.dto

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCard
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCardCoordinate
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCardSize

data class SpecialDeckCardDto(
    val id: String = "",
    val arObject: String = "",
    val connections: List<String> = emptyList(),
    val coordinates: Map<String, Int> = emptyMap(),
    val image: String = "",
    val quoteLocalized: Map<String, String?> = emptyMap(),
    val size: String = "",
    val texture: String = ""
) {
    fun toDomain() = SpecialDeckCard(
        id = CardId(id),
        arObject = arObject,
        connections = connections.map(::CardId),
        coordinates = coordinates.toSpecialDeckCardCoordinates(),
        imageUrl = image,
        quoteLocalized = LocalizableString(quoteLocalized),
        size = SpecialDeckCardSize.fromString(size),
        texture = texture
    )
}

private const val X_KEY = "axisX"
private const val Y_KEY = "axisY"

private fun Map<String, Int>.toSpecialDeckCardCoordinates(): SpecialDeckCardCoordinate {
    return SpecialDeckCardCoordinate(
        x = getOrDefault(X_KEY, 0),
        y = getOrDefault(Y_KEY, 0),
    )
}