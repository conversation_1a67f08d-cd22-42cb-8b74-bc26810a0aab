package com.metacards.metacards.features.course.data.repository

import android.content.SharedPreferences
import androidx.core.content.edit
import com.metacards.metacards.core.user.domain.UserProvider
import com.metacards.metacards.features.course.data.data_source.CourseDataSource
import com.metacards.metacards.features.course.data.dto.CourseLessonDto
import com.metacards.metacards.features.course.data.dto.CoursePassedCoursesDto
import com.metacards.metacards.features.course.data.dto.CoursePassedTestDto
import com.metacards.metacards.features.course.data.dto.CourseTestDto
import com.metacards.metacards.features.course.data.dto.toDomain
import com.metacards.metacards.features.course.domain.entity.CourseContentId
import com.metacards.metacards.features.course.domain.entity.CourseData
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CourseLesson
import com.metacards.metacards.features.course.domain.entity.CoursePassedCourse
import com.metacards.metacards.features.course.domain.entity.CoursePassedTest
import com.metacards.metacards.features.course.domain.entity.CourseTest
import com.metacards.metacards.features.course.domain.entity.CourseTestDocumentId
import com.metacards.metacards.features.course.domain.entity.CourseTheme
import com.metacards.metacards.features.course.domain.entity.CourseThemeId
import com.metacards.metacards.features.course.domain.repository.CourseRepository
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map

class CourseRepositoryImpl(
    private val courseDataSource: CourseDataSource,
    private val userProvider: UserProvider,
    private val userPrefs: SharedPreferences,
) : CourseRepository {

    private companion object {
        const val TEST_TUTORIAL_SHOWN_KEY = "TEST_TUTORIAL_SHOWN_KEY"
    }
    override fun getCourseThemesFlow(): Flow<List<CourseTheme>> = courseDataSource
        .getCoursesListFlow().map { list -> list.map { course -> course.toDomain() } }

    override fun getCourseDataFlow(themeId: CourseThemeId, courseId: CourseId): Flow<CourseData?> =
        courseDataSource.getCourseDataFlow(themeId.value, courseId.value).map { it?.toDomain() }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun getUserPassedContentFlow(courseId: CourseId): Flow<CoursePassedCourse?> =
        userProvider.getUser().flatMapLatest { user ->
            user?.userId?.value?.let { userId ->
                courseDataSource.getUserPassedContentFlow(userId, courseId.value)
                    .map { dto -> dto?.toDomain() ?: CoursePassedCourse.createEmpty(courseId) }
            } ?: flowOf(null)
        }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun getUserPassedTestsFlow(): Flow<List<CoursePassedTest>> =
        userProvider.getUser().flatMapLatest { user ->
            user?.userId?.value?.let { userId ->
                courseDataSource.getUserPassedTestsFlow(userId)
                    .map { list -> list.map { dto -> dto.toDomain() } }
            } ?: flowOf(emptyList())
        }

    override suspend fun getCourseLessonDetails(query: CourseLesson.Query): CourseLesson? {
        val requestDto = CourseLessonDto.Request.fromDomain(query)
        return courseDataSource.getCourseLessonDetails(requestDto)?.toDomain()
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun getUserPassedCoursesFlow(): Flow<List<CoursePassedCourse>> =
        userProvider.getUser().flatMapLatest { user ->
            user?.userId?.value?.let { userId ->
                courseDataSource
                    .getUserPassedCoursesFlow(userId)
                    .map { list -> list.map { it.toDomain() } }
            } ?: flowOf(emptyList())
        }

    override suspend fun getUserPassedContent(
        courseId: CourseId
    ): CoursePassedCourse? = getUserId()?.let {
        courseDataSource.getUserPassedContent(it, courseId.value)
            ?.toDomain() ?: CoursePassedCourse.createEmpty(courseId)
    }

    override suspend fun updateUserPassedContent(
        courseId: CourseId,
        info: CoursePassedCourse
    ) {
        getUserId()?.let {
            courseDataSource.updateUserPassedContent(
                userId = it,
                courseId = courseId.value,
                info = CoursePassedCoursesDto.fromDomain(info)
            )
        }
    }

    override suspend fun getCourseData(themeId: CourseThemeId, courseId: CourseId): CourseData? =
        courseDataSource.getCourseData(themeId.value, courseId.value)?.toDomain()

    override suspend fun getUserPassedTestById(
        testResultId: CourseTestDocumentId?,
        testId: CourseContentId
    ): CoursePassedTest? {
        val userId = getUserId() ?: return null
        val dto = if (testResultId != null) {
            courseDataSource.getUserPassedTestById(userId, testResultId.value)
        } else {
            courseDataSource.getUserPassedTests(userId).filter { it.testId == testId.value }
                .maxBy { it.resultDate.seconds }
        }
        return dto?.toDomain()
    }

    override suspend fun addUserPassedTest(info: CoursePassedTest) {
        getUserId()?.let {
            courseDataSource.addUserPassedTest(it, CoursePassedTestDto.fromDomain(info))
        }
    }

    override suspend fun getCourseTestDetails(query: CourseTest.Query): CourseTest? {
        return courseDataSource
            .getCourseTestDetails(CourseTestDto.Request.fromDomain(query))
            ?.toDomain()
    }

    override suspend fun getIsTestTutorialShown(): Boolean =
        userPrefs.getBoolean(TEST_TUTORIAL_SHOWN_KEY, false)

    override suspend fun setTestTutorialShown() {
        userPrefs.edit { putBoolean(TEST_TUTORIAL_SHOWN_KEY, true) }
    }

    private fun getUserId() = userProvider.getUser().value?.userId?.value
}
