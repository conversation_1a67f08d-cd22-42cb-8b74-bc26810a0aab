package com.metacards.metacards.features.favorite_cards.ui

import android.os.Parcelable
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.router.stack.childStack
import com.arkivanov.decompose.router.stack.pop
import com.arkivanov.decompose.router.stack.push
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.utils.toStateFlow
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.favorite_cards.createFavoriteCardDetailsComponent
import com.metacards.metacards.features.favorite_cards.createFavoriteCardListComponent
import com.metacards.metacards.features.favorite_cards.ui.RealFavoriteCardsComponent.ChildConfig.List.toChildConfig
import com.metacards.metacards.features.favorite_cards.ui.details.FavoriteCardDetailsComponent
import com.metacards.metacards.features.favorite_cards.ui.list.FavoriteCardListComponent
import kotlinx.parcelize.Parcelize

class RealFavoriteCardsComponent(
    componentContext: ComponentContext,
    private val onOutput: (FavoriteCardsComponent.Output) -> Unit,
    private val componentFactory: ComponentFactory,
    private val screen: FavoriteCardsComponent.Screen
) : ComponentContext by componentContext, FavoriteCardsComponent {

    private val navigation = StackNavigation<ChildConfig>()

    override val childStack = childStack(
        source = navigation,
        initialConfiguration = screen.toChildConfig(),
        handleBackButton = true,
        childFactory = ::createChild
    ).toStateFlow(lifecycle)

    private fun createChild(
        config: ChildConfig,
        componentContext: ComponentContext
    ): FavoriteCardsComponent.Child = when (config) {

        ChildConfig.List -> {
            FavoriteCardsComponent.Child.List(
                componentFactory.createFavoriteCardListComponent(
                    componentContext,
                    ::onFavoriteCardListOutput
                )
            )
        }

        is ChildConfig.Details -> {
            FavoriteCardsComponent.Child.Details(
                componentFactory.createFavoriteCardDetailsComponent(
                    componentContext,
                    config.cardId,
                    ::onFavoriteCardDetailsOutput
                )
            )
        }
    }

    private fun onFavoriteCardListOutput(output: FavoriteCardListComponent.Output) {
        when (output) {
            is FavoriteCardListComponent.Output.CardDetailsRequested -> {
                navigation.push(ChildConfig.Details(output.cardId))
            }

            is FavoriteCardListComponent.Output.GoToMainRequested -> {
                onOutput(FavoriteCardsComponent.Output.GoToMainRequested)
            }
        }
    }

    private fun onFavoriteCardDetailsOutput(output: FavoriteCardDetailsComponent.Output) {
        when (output) {
            is FavoriteCardDetailsComponent.Output.RecordDetailsRequested -> {
                onOutput(
                    FavoriteCardsComponent.Output.RecordDetailsRequested(output.recordId)
                )
            }

            is FavoriteCardDetailsComponent.Output.RemovedFromFavorite -> {
                when (screen) {
                    is FavoriteCardsComponent.Screen.Details -> onOutput(FavoriteCardsComponent.Output.GoBackRequested)
                    FavoriteCardsComponent.Screen.List -> navigation.pop()
                }
            }
        }
    }

    sealed interface ChildConfig : Parcelable {
        @Parcelize
        data object List : ChildConfig

        @Parcelize
        data class Details(val cardId: CardId) : ChildConfig

        fun FavoriteCardsComponent.Screen.toChildConfig() = when (this) {
            is FavoriteCardsComponent.Screen.Details -> Details(cardId)
            FavoriteCardsComponent.Screen.List -> List
        }
    }
}