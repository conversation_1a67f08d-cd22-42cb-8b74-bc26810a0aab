package com.metacards.metacards.features.home.ui

import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.FakeDialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.FakeDefaultDialogComponent
import com.metacards.metacards.features.advbanner.domain.entity.HomeAdvBanner
import com.metacards.metacards.features.deck.domain.entity.DailyCard
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.domain.entity.DeckWithAvailable
import com.metacards.metacards.features.tutorial.data.TutorialStep
import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.util.LinkedList

class FakeHomeComponent : HomeComponent {
    override val deckList: MutableStateFlow<List<DeckWithAvailable>> =
        MutableStateFlow(LinkedList(DeckWithAvailable.LIST_MOCK))
    override val homeAdvBanner = MutableStateFlow(null)
    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        FakeDialogControl(FakeDefaultDialogComponent())
    override val dailyCardState: StateFlow<DailyCard?> = MutableStateFlow(null)
    override val tutorialStep: StateFlow<TutorialStep> = MutableStateFlow(TutorialStep.START)
    override val tutorialMessage: StateFlow<TutorialMessage?> = MutableStateFlow(null)

    override fun onDeckInfoButtonClick(deckId: DeckId) = Unit
    override fun onDeckClick(deckId: DeckId) = Unit
    override fun onAdvBannerClick(homeAdvBanner: HomeAdvBanner) = Unit
    override fun onAdvBannerDismiss() = Unit
    override fun onLayoutBannerClick() = Unit
    override fun onCardOfTheDayClick() = Unit
}
