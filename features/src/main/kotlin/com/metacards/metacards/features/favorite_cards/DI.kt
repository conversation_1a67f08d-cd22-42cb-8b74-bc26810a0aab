package com.metacards.metacards.features.favorite_cards

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.favorite_cards.domain.GetFavoriteCardInfoInteractor
import com.metacards.metacards.features.favorite_cards.domain.GetFavoriteCardsInteractor
import com.metacards.metacards.features.favorite_cards.ui.FavoriteCardsComponent
import com.metacards.metacards.features.favorite_cards.ui.RealFavoriteCardsComponent
import com.metacards.metacards.features.favorite_cards.ui.details.FavoriteCardDetailsComponent
import com.metacards.metacards.features.favorite_cards.ui.details.RealFavoriteCardDetailsComponent
import com.metacards.metacards.features.favorite_cards.ui.list.FavoriteCardListComponent
import com.metacards.metacards.features.favorite_cards.ui.list.RealFavoriteCardListComponent
import org.koin.core.component.get
import org.koin.dsl.module

val favoriteCardsModule = module {
    single { GetFavoriteCardsInteractor(get(), get()) }
    factory { GetFavoriteCardInfoInteractor(get(), get()) }
}

fun ComponentFactory.createFavoriteCardsComponent(
    componentContext: ComponentContext,
    onOutput: (FavoriteCardsComponent.Output) -> Unit,
    screen: FavoriteCardsComponent.Screen,
): FavoriteCardsComponent {
    return RealFavoriteCardsComponent(componentContext, onOutput, get(), screen)
}

fun ComponentFactory.createFavoriteCardListComponent(
    componentContext: ComponentContext,
    onOutput: (FavoriteCardListComponent.Output) -> Unit
): FavoriteCardListComponent {
    return RealFavoriteCardListComponent(componentContext, onOutput, get())
}

fun ComponentFactory.createFavoriteCardDetailsComponent(
    componentContext: ComponentContext,
    cardId: CardId,
    onOutput: (FavoriteCardDetailsComponent.Output) -> Unit
): FavoriteCardDetailsComponent {
    return RealFavoriteCardDetailsComponent(
        componentContext,
        cardId,
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        onOutput
    )
}