package com.metacards.metacards.features.deeplink.ui

import co.touchlab.kermit.Logger
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.doOnStart
import com.arkivanov.essenty.lifecycle.doOnStop
import com.metacards.metacards.core.deeplink.DeeplinkAction
import com.metacards.metacards.core.deeplink.DeeplinkService
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.user.domain.UserProvider
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.features.course.domain.entity.CourseData
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CourseThemeId
import com.metacards.metacards.features.course.domain.interactor.GetCourseAvailabilityInteractor
import com.metacards.metacards.features.deeplink.domain.DeeplinkData
import com.metacards.metacards.features.user_analytics.ui.UserAnalyticsComponent
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach

class RealDeeplinkNavigationComponent(
    componentContext: ComponentContext,
    private val deeplinkService: DeeplinkService,
    private val errorHandler: ErrorHandler,
    private val userProvider: UserProvider,
    private val getCourseAvailabilityInteractor: GetCourseAvailabilityInteractor,
    private val onOutput: (DeeplinkNavigationComponent.Output) -> Unit,
) : DeeplinkNavigationComponent, ComponentContext by componentContext {
    private val logger = Logger.withTag("DeeplinkComponent")
    private var deeplinkCollector: Job? = null
    private var isReadyToCollect = false
    private val innerDeeplinkActionFlow = MutableStateFlow<DeeplinkAction?>(null)

    init {
        lifecycle.doOnStart {
            deeplinkCollector = componentScope.safeLaunch(errorHandler) {
                deeplinkService.deeplinkActionFlow.collect {
                    innerDeeplinkActionFlow.tryEmit(it)
                }
            }
        }

        lifecycle.doOnStart {
            if (!isReadyToCollect) {
                return@doOnStart
            } else {
                startCollect()
            }
        }

        lifecycle.doOnStop {
            deeplinkCollector?.cancel()
        }
    }

    private fun startCollect() {
        deeplinkCollector = componentScope.safeLaunch(errorHandler) {
            innerDeeplinkActionFlow.collect { action ->
                if (action == null) return@collect

                innerDeeplinkActionFlow.tryEmit(null)

                if (action is DeeplinkData) {
                    navigateByDeeplink(action)
                } else {
                    logger.w("DeeplinkAction is not instance of DeeplinkData")
                }
            }
        }
    }

    private fun navigateByDeeplink(deeplinkData: DeeplinkData) {
        when (deeplinkData) {
            is DeeplinkData.Auth -> Unit
            DeeplinkData.GetSub, DeeplinkData.SubPurchaseSoon, DeeplinkData.SubPurchaseError -> {
                onOutput(DeeplinkNavigationComponent.Output.SubscriptionPageRequested())
            }

            DeeplinkData.LookAnalytics -> {
                onOutput(
                    DeeplinkNavigationComponent.Output.AnalyticsPageRequested(
                        UserAnalyticsComponent.Tab.Month
                    )
                )
            }

            DeeplinkData.MakeRecord -> {
                onOutput(DeeplinkNavigationComponent.Output.HomePageRequested)
            }

            is DeeplinkData.NewDeck -> {
                onOutput(DeeplinkNavigationComponent.Output.DeckInfoPageRequested(deeplinkData.deckId))
            }

            is DeeplinkData.Deck -> {
                onOutput(DeeplinkNavigationComponent.Output.DeckInfoPageRequested(deeplinkData.deckId))
            }

            DeeplinkData.CloseAuth -> {
                onOutput(DeeplinkNavigationComponent.Output.CloseWebViewAuthRequested)
            }

            is DeeplinkData.PaymentCompleted.Deck -> {
                onOutput(
                    DeeplinkNavigationComponent.Output.DeckInfoPageRequested(
                        deeplinkData.deckId,
                        isPaymentCompleted = true
                    )
                )
            }

            is DeeplinkData.PaymentCompleted.Subscription -> {
                onOutput(DeeplinkNavigationComponent.Output.SubscriptionPaymentCompleted)
            }

            DeeplinkData.DailyCard -> {
                onOutput(DeeplinkNavigationComponent.Output.HomePageRequested)
            }

            is DeeplinkData.Course -> {
                val isUserAuthorized = userProvider.getUser().value != null

                val query = CourseData.Query(
                    themeId = CourseThemeId(deeplinkData.themeId),
                    courseId = CourseId(deeplinkData.courseId)
                )

                getCourseAvailabilityInteractor.execute(query).onEach { isCourseAvailable ->
                    if (isCourseAvailable && isUserAuthorized) {
                        DeeplinkNavigationComponent.Output.CourseRequested(query)
                    } else {
                        DeeplinkNavigationComponent.Output.HomePageRequested
                    }.run(onOutput)
                }.launchIn(componentScope)
            }
        }
    }

    override fun readyToReceiveEvents() {
        isReadyToCollect = true
        startCollect()
    }
}