package com.metacards.metacards.features.record.ui.record_list

import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.PagedData
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CourseThemeId
import com.metacards.metacards.features.record.domain.Record
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.record.domain.RecordListType
import com.metacards.metacards.features.record.ui.record_details.RecordDetailsComponent
import kotlinx.coroutines.flow.StateFlow
import kotlinx.datetime.Instant

interface RecordListComponent {

    val listType: RecordListType

    val hasAddButton: Boolean

    val recordsState: StateFlow<LoadableState<PagedData<Record>>>

    val visibleRecordsState: StateFlow<LoadableState<PagedData<Record>>>

    val pendingScrollCommand: StateFlow<ScrollCommand?>

    fun onRefresh()

    fun onRetryClick()

    fun onAddClick()

    fun onLoadNext()

    fun onLoadPrevious()

    fun onScrollToTopClick()

    fun onStartTimeChosen(time: Instant)

    fun onRecordClick(recordId: RecordId)

    fun onArchiveClick(recordId: RecordId)

    fun onFavouriteClick(recordId: RecordId)

    fun onScrollCommandExecuted()

    sealed interface Output {
        data class RecordDetailsRequested(
            val recordId: RecordId,
            val recordType: RecordDetailsComponent.RecordType,
            val isDailyCardLayout: Boolean,
            val courseId: CourseId?,
            val courseThemeId: CourseThemeId?
        ) : Output

        data object CreateRecordFlowRequested : Output
        data object PremiumSuggestingRequested : Output
    }
}