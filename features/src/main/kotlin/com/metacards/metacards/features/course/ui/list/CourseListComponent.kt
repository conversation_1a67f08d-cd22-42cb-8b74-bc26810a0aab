package com.metacards.metacards.features.course.ui.list

import com.metacards.metacards.features.course.domain.entity.CourseData
import com.metacards.metacards.features.course.domain.entity.CourseShortData
import com.metacards.metacards.features.course.domain.entity.CourseTheme
import kotlinx.coroutines.flow.StateFlow

interface CourseListComponent {
    val theme: CourseTheme
    val isPremiumUser: StateFlow<Boolean>

    fun onCourseClick(courseShortData: CourseShortData)

    sealed interface Output {
        data class CoursePageRequested(val courseQuery: CourseData.Query) : Output
        data object SubscriptionBottomSheetRequested : Output
        data object AuthSuggestingRequested : Output
    }
}
