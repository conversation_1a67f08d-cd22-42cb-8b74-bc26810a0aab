package com.metacards.metacards.features.account.ui.about_the_app

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.features.R
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import dev.icerock.moko.resources.desc.strResDesc

class RealAboutTheAppComponent(
    componentContext: ComponentContext,
    private val analyticsService: AnalyticsService,
    private val onOutput: (AboutTheAppComponent.Output) -> Unit
) : ComponentContext by componentContext, AboutTheAppComponent {

    override fun onTermsOfUseClick() {
        analyticsService.logEvent(AnalyticsEvent.AboutAppTermsEvent)
        onOutput(
            AboutTheAppComponent.Output.WebViewResourceRequested(
                titleRes = R.string.about_the_app_terms_of_use,
                url = R.string.email_enter_terms_link_first.strResDesc()
            )
        )
    }

    override fun onPrivacyPolicyClick() {
        analyticsService.logEvent(AnalyticsEvent.AboutAppPolicyEvent)
        onOutput(
            AboutTheAppComponent.Output.WebViewResourceRequested(
                titleRes = R.string.about_the_app_privacy_policy,
                url = R.string.email_enter_terms_link_second.strResDesc()
            )
        )
    }

    override fun onAboutUsClick() {
        analyticsService.logEvent(AnalyticsEvent.AboutAppCreatorsEvent)
        onOutput(AboutTheAppComponent.Output.AboutUsRequested)
    }
}