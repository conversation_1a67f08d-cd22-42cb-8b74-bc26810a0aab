package com.metacards.metacards.features.deck.ui.deck_info

import android.content.Context
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.childContext
import com.arkivanov.essenty.lifecycle.doOnDestroy
import com.arkivanov.essenty.lifecycle.doOnStart
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.adv.domain.YandexAdvHelper
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.deeplink.DeckLinkInfo
import com.metacards.metacards.core.deeplink.LinkGenerator
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeRun
import com.metacards.metacards.core.external_apps.ExternalAppService
import com.metacards.metacards.core.localization.data.AppLanguageService
import com.metacards.metacards.core.message.data.MessageService
import com.metacards.metacards.core.message.domain.Message
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.user.domain.UserProvider
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.stateIn
import com.metacards.metacards.features.R
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.deck.domain.entity.Deck
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.domain.entity.DeckInfoWithCards
import com.metacards.metacards.features.deck.domain.interactor.GetDeckInfoWithCardsInteractor
import com.metacards.metacards.features.payments.createPaymentComponent
import com.metacards.metacards.features.payments.domain.BillingService
import com.metacards.metacards.features.payments.domain.PurchaseType
import com.metacards.metacards.features.payments.ui.PaymentComponent
import com.metacards.metacards.features.user.domain.GetUserSubscriptionStateInteractor
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.desc
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.stateIn

@OptIn(ExperimentalCoroutinesApi::class)
class RealDeckInfoComponent(
    componentContext: ComponentContext,
    private val deckId: DeckId,
    isPaymentCompleted: Boolean,
    userProvider: UserProvider,
    getUserSubscriptionStateInteractor: GetUserSubscriptionStateInteractor,
    getDeckInfoWithCardsInteractor: GetDeckInfoWithCardsInteractor,
    private val componentFactory: ComponentFactory,
    private val errorHandler: ErrorHandler,
    private val messageService: MessageService,
    private val analyticsService: AnalyticsService,
    private val appLanguageService: AppLanguageService,
    private val linkGenerator: LinkGenerator,
    private val externalAppService: ExternalAppService,
    billingService: BillingService,
    private val yandexAdvHelper: YandexAdvHelper,
    private val onOutput: (DeckInfoComponent.Output) -> Unit
) : ComponentContext by componentContext, DeckInfoComponent {

    init {
        lifecycle.doOnStart {
            if (isPaymentCompleted) {
                messageService.showMessage(
                    Message(
                        text = StringDesc.Resource(R.string.deck_info_payment_completed),
                        isNotification = true
                    )
                )
            }
        }

        lifecycle.doOnDestroy {
            analyticsService.logEvent(
                AnalyticsEvent.ShopDeckBackEvent(
                    deckId = deckId.value,
                    deckName = deckName
                )
            )
        }
    }

    private val user = userProvider.getUser()

    override val paymentComponent: PaymentComponent by lazy {
        componentFactory.createPaymentComponent(
            componentContext.childContext("PaymentComponent"),
            ::onPaymentOutput
        )
    }

    private val subscriptionState = getUserSubscriptionStateInteractor.execute()
        .stateIn(componentScope, SharingStarted.Eagerly, null)

    private val deckName: String
        get() = deckInfo.value.data?.deckInfo?.name?.toString(appLanguageService.getLanguage())
            ?: ""

    override val currentCardPosition: MutableStateFlow<Int> = MutableStateFlow(3)

    override val deckInfo: StateFlow<LoadableState<DeckInfoWithCards>> =
        getDeckInfoWithCardsInteractor.execute(deckId).onEach {
            if (!it.loading && it.data == null) {
                onOutput(DeckInfoComponent.Output.DecksListFallback)
            }
        }.stateIn(this, LoadableState())

    override val scrollToPageCommand: MutableSharedFlow<Int> =
        MutableSharedFlow(
            replay = 1,
            extraBufferCapacity = 1,
            onBufferOverflow = BufferOverflow.DROP_OLDEST
        )

    override val actionButtonState = computed(billingService.currentPurchaseState) {
        if (it.isInProgress) ButtonState.Loading else ButtonState.Enabled
    }

    override fun onActionButtonClick() {
        if (deckInfo.value.data?.deckAvailable == true) {
            analyticsService.logEvent(AnalyticsEvent.ShopDeckSetupEvent)
            onDeckLayoutClick()
        } else {
            onDeckBuyClick()
        }
    }

    private fun onDeckLayoutClick() {
        when (subscriptionState.value) {
            is User.SubscriptionState.None -> {
                if ((subscriptionState.value as User.SubscriptionState.None).canLayout) {
                    onOutput(DeckInfoComponent.Output.DeckLayoutRequested(deckId))
                } else {
                    onOutput(DeckInfoComponent.Output.PremiumSuggestingRequested)
                    yandexAdvHelper.subscribeForReward {
                        onOutput(DeckInfoComponent.Output.DeckLayoutRequested(deckId))
                    }
                }
            }

            is User.SubscriptionState.Ongoing ->
                onOutput(DeckInfoComponent.Output.DeckLayoutRequested(deckId))

            null -> {
                if (deckInfo.value.data?.deckInfo?.availability == Deck.Availability.FREE) {
                    onOutput(DeckInfoComponent.Output.DeckLayoutRequested(deckId))
                } else {
                    onOutput(DeckInfoComponent.Output.AuthSuggestingRequested)
                }
            }
        }
    }

    private fun onDeckBuyClick() {
        if (user.value == null) {
            onOutput(DeckInfoComponent.Output.AuthSuggestingRequested)
        } else {
            val deckInfo = deckInfo.value.data?.deckInfo ?: return
            val paymentType = PurchaseType.Deck(
                deckInfo.storeId,
                deckInfo.deckId.value,
                deckInfo.name.toString(appLanguageService.getLanguage())
            )

            paymentComponent.startPayment(paymentType)
        }
    }

    override fun onMaterialDeckBuyClick() {
        safeRun(errorHandler) {
            val url = deckInfo.value.data?.deckInfo?.shopUrl
            require(url != null)
            analyticsService.logEvent(
                AnalyticsEvent.ShopDeckPhysicalSiteEvent(
                    deckId = deckId.value,
                    deckName = deckName
                )
            )
            onOutput(DeckInfoComponent.Output.MaterialDeckBuyRequested(url))
        }
    }

    override fun onCardClick(cardPosition: Int) {
        deckInfo.value.data?.let {
            onOutput(
                DeckInfoComponent.Output.FullScreenPreviewRequested(it, cardPosition)
            )
        }
    }

    override fun onCardSwipe(newPosition: Int) {
        currentCardPosition.value = newPosition
    }

    override fun onVideoPlay() {
        analyticsService.logEvent(
            AnalyticsEvent.ShopDeckVideoEvent(
                deckId = deckId.value,
                deckName = deckName
            )
        )
    }

    override fun onShareClick(context: Context) {
        safeRun(errorHandler) {
            val deckInfo =
                deckInfo.value.data?.deckInfo ?: throw IllegalStateException("Deck info is null")
            val deckLinkInfo = DeckLinkInfo(
                deckId = deckInfo.deckId.value,
                title = deckInfo.name,
                coverUrl = deckInfo.coverUrl
            )
            linkGenerator.generateDeckLink(deckLinkInfo) { url: String?, error: String? ->
                when {
                    error != null -> {
                        messageService.showMessage(
                            Message(
                                text = error.desc(),
                                isNotification = false
                            )
                        )
                    }

                    url == null -> {
                        messageService.showMessage(
                            Message(
                                text = com.metacards.metacards.core.R.string.error_unexpected.strResDesc(),
                                isNotification = false
                            )
                        )
                    }

                    else -> {
                        externalAppService.openShareSheetForText(url)
                    }
                }
            }
        }
    }

    override fun scrollToPage(page: Int) {
        scrollToPageCommand.resetReplayCache()
        scrollToPageCommand.tryEmit(page)
    }

    private fun onPaymentOutput(output: PaymentComponent.Output) {
        when (output) {
            is PaymentComponent.Output.Canceled -> {
                messageService.showMessage(
                    Message(
                        text = "StringDesc.Resource(R.string.deck_info_payment_canceled)".desc(),
                    )
                )
            }

            is PaymentComponent.Output.Succeed -> {
                messageService.showMessage(
                    Message(
                        text = "StringDesc.Resource(R.string.deck_info_payment_completed)".desc(),
                    )
                )
            }

            is PaymentComponent.Output.WebStoreRequested -> {
                onOutput(DeckInfoComponent.Output.WebViewRequested(output.url, output.title))
            }
        }
    }
}
