package com.metacards.metacards.features.account.ui.profile.password.confirm

import com.metacards.metacards.core.button.ButtonState
import dev.icerock.moko.resources.desc.Raw
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import ru.mobileup.kmm_form_validation.control.InputControl

class FakeProfileConfirmPasswordComponent : ProfileConfirmPasswordComponent {
    @OptIn(DelicateCoroutinesApi::class)
    override val inputControl: InputControl = InputControl(GlobalScope)
    override val proceedButtonState: StateFlow<ButtonState> = MutableStateFlow(ButtonState.Enabled)
    override val toolbarText: StringDesc = StringDesc.Raw("Редактирование")
    override val textFieldHeader: StringDesc = StringDesc.Raw("Введите текущий пароль")
    override val textFieldCaption: StringDesc? = null
    override val bottomButtonText: StringDesc = StringDesc.Raw("Подтвердить")

    override fun onProceedClick() = Unit
}
