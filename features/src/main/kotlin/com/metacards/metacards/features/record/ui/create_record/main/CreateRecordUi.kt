@file:OptIn(ExperimentalComposeUiApi::class)

package com.metacards.metacards.features.record.ui.create_record.main

import android.content.Context
import android.net.Uri
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusDirection
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import coil.compose.rememberAsyncImagePainter
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.button.MetaButton
import com.metacards.metacards.core.widget.button.MetaButtonDefaults
import com.metacards.metacards.core.widget.button.MetaButtonIconWidget
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.IconNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.core.widget.text_field.MetaTextField
import com.metacards.metacards.features.R
import com.metacards.metacards.features.record.domain.camera.CardPhoto
import com.metacards.metacards.features.record.ui.record_details.widget.FeelingContent
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun CreateRecordUi(
    component: CreateRecordComponent,
    modifier: Modifier = Modifier
) {
    val userSubscriptionState by component.userSubscriptionState.collectAsState()
    val isFavourite by component.isFavourite.collectAsState()
    val cardPhoto by component.cardPhoto.collectAsState()
    val question by component.question.collectAsState()
    val comment by component.comment.collectAsState()
    val energyLevel by component.energyLevel.collectAsState()
    val moodLevel by component.moodLevel.collectAsState()
    val loadingSaveRecord by component.loadingSaveRecord.collectAsState()
    val context = LocalContext.current

    BoxWithFade(
        modifier = Modifier.fillMaxSize(),
        listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        Column {
            TopNavigationBar(
                title = R.string.create_record_top_bar_title.strResDesc(),
                leadingIcon = if (cardPhoto == null) {
                    { BackNavigationItem() }
                } else {
                    null
                },
                trailingContent = {
                    IconNavigationItem(
                        iconRes = if (isFavourite) {
                            R.drawable.ic_24_favourite
                        } else {
                            R.drawable.ic_24_no_favourite
                        },
                        onClick = { component.onFavoriteClick() }
                    )
                }
            )

            Column(
                modifier = modifier
                    .weight(1f)
                    .verticalScroll(rememberScrollState())
            ) {
                if (cardPhoto == null) {
                    Spacer(modifier = Modifier.size(24.dp))
                    MetaButton(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp),
                        text = R.string.create_record_add_card.strResDesc().localizedByLocal(),
                        colors = MetaButtonDefaults.bigButton.buttonSecondaryColors,
                        leadingWidget = {
                            MetaButtonIconWidget(
                                modifier = Modifier.padding(horizontal = 8.dp),
                                icon = R.drawable.ic_24_photo
                            )
                        },
                        onClick = component::onAddCardClick
                    )
                }

                QuestionContent(
                    modifier = Modifier.padding(16.dp),
                    cardPhoto = cardPhoto?.takeUri(),
                    question = question,
                    comment = comment,
                    loadingSaveRecord = loadingSaveRecord,
                    onQuestionChange = component::onQuestionChange,
                    onCommentChange = component::onCommentChange,
                )

                FeelingContent(
                    userSubscriptionState = userSubscriptionState,
                    moodLevel = moodLevel,
                    energyLevel = energyLevel,
                    isMoodInteractionEnabled = !loadingSaveRecord,
                    isEnergyInteractionEnabled = !loadingSaveRecord,
                    onMoodLevelChange = component::onMoodLevelChange,
                    onEnergyLevelChange = component::onEnergyLevelChange,
                    onBlockContentClick = component::onBlockAreaClick
                )
            }

            val buttonState = remember(cardPhoto, loadingSaveRecord) {
                when {
                    cardPhoto == null -> ButtonState.Disabled
                    loadingSaveRecord -> ButtonState.Loading
                    else -> ButtonState.Enabled
                }
            }

            MetaAccentButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                text = R.string.common_record_complete_button.strResDesc().localizedByLocal(),
                state = buttonState,
                onClick = {
                    component.onSaveClick(
                        cardPhoto?.convertToByteArray(context)
                    )
                }
            )
        }
    }
}

@OptIn(ExperimentalComposeUiApi::class)
@Composable
private fun QuestionContent(
    modifier: Modifier = Modifier,
    cardPhoto: Uri?,
    question: String,
    comment: String,
    loadingSaveRecord: Boolean,
    onQuestionChange: (String) -> Unit,
    onCommentChange: (String) -> Unit
) {
    val focusManager = LocalFocusManager.current
    val keyboard = LocalSoftwareKeyboardController.current

    Column(modifier = modifier) {
        MetaTextField(
            modifier = Modifier.fillMaxWidth(),
            singleLine = false,
            heightMax = Dp.Infinity,
            value = question,
            isEnabled = !loadingSaveRecord,
            onValueChanged = onQuestionChange,
            placeholder = R.string.add_record_question_hint.strResDesc().localizedByLocal(),
            headerMessage = {
                Text(
                    modifier = Modifier.padding(bottom = 8.dp, start = 16.dp),
                    text = R.string.add_record_question_label.strResDesc().localizedByLocal(),
                    style = CustomTheme.typography.caption.medium,
                    color = CustomTheme.colors.text.secondary
                )
            },
            footerMessage = {
                if (cardPhoto != null) {
                    Spacer(modifier = Modifier.size(16.dp))
                    Image(
                        modifier = Modifier
                            .padding(start = 16.dp)
                            .clip(RoundedCornerShape(8.dp))
                            .height(64.dp)
                            .width(39.dp),
                        contentScale = ContentScale.Crop,
                        painter = rememberAsyncImagePainter(cardPhoto),
                        contentDescription = null
                    )
                }
            },
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Next),
            keyboardActions = (
                KeyboardActions(onNext = { focusManager.moveFocus(FocusDirection.Down) })
                )
        )

        Spacer(modifier = Modifier.size(16.dp))
        MetaTextField(
            modifier = Modifier.fillMaxWidth(),
            singleLine = false,
            heightMax = Dp.Infinity,
            isEnabled = !loadingSaveRecord,
            value = comment,
            onValueChanged = onCommentChange,
            placeholder = R.string.add_record_new_comment_hint.strResDesc().localizedByLocal(),
            headerMessage = {
                Text(
                    modifier = Modifier.padding(bottom = 8.dp, start = 16.dp),
                    text = R.string.add_record_comment_label.strResDesc().localizedByLocal(),
                    style = CustomTheme.typography.caption.medium,
                    color = CustomTheme.colors.text.secondary
                )
            },
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
            keyboardActions = (KeyboardActions(onNext = { keyboard?.hide() }))
        )
    }
}

private fun CardPhoto.convertToByteArray(context: Context): ByteArray? {
    var byteArray: ByteArray? = null

    if (uri == null && assetsPath != null) {
        context.assets.open(assetsPath).use { inputStream ->
            val bytes = inputStream.readBytes()
            byteArray = bytes
        }
    } else if (uri != null) {
        context.contentResolver.openInputStream(uri).use { inputStream ->
            inputStream?.readBytes()?.let { byte ->
                byteArray = byte
            }
        }
    } else {
        throw IllegalStateException("CardPhoto must have uri or assetsPath")
    }

    return byteArray
}

@Preview
@Composable
private fun CreateRecordUiPreview() {
    AppTheme {
        CreateRecordUi(component = FakeCreateRecordComponent())
    }
}