package com.metacards.metacards.features.user.ui.subscription.widget

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.widget.FadeDefaults
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun SubscriptionOptionsBlock(modifier: Modifier = Modifier) {
    Text(
        text = R.string.account_subscription_options_block_header.strResDesc().localizedByLocal(),
        color = CustomTheme.colors.text.primary,
        style = CustomTheme.typography.heading.secondary,
        modifier = Modifier.padding(bottom = 16.dp)
    )
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp),
        modifier = modifier.fillMaxWidth()
    ) {
        SubscriptionOptionItem(
            iconRes = R.drawable.ic_24_card,
            iconTint = CustomTheme.colors.moon.brightness60,
            iconBackgroundColorsList = CustomTheme.colors.gradient.tailMoonList.reversed(),
            titleText = R.string.account_subscription_option_layout_title.strResDesc(),
            mainText = R.string.account_subscription_option_layout_text.strResDesc()
        )
        SubscriptionOptionItem(
            iconRes = R.drawable.ic_24_charts,
            iconTint = CustomTheme.colors.mars.brightness80,
            iconBackgroundColorsList = CustomTheme.colors.gradient.tailMarsList.reversed(),
            titleText = R.string.account_subscription_option_analytics_title.strResDesc(),
            mainText = R.string.account_subscription_option_analytics_text.strResDesc()
        )
        SubscriptionOptionItem(
            iconRes = R.drawable.ic_24_photo,
            iconTint = CustomTheme.colors.moon.brightness80,
            iconBackgroundColorsList = CustomTheme.colors.gradient.tailMoonList.reversed(),
            titleText = R.string.account_subscription_option_photo_title.strResDesc(),
            mainText = R.string.account_subscription_option_photo_text.strResDesc()
        )
        SubscriptionOptionItem(
            iconRes = R.drawable.ic_24_edit,
            iconTint = CustomTheme.colors.mars.darkest,
            iconBackgroundColorsList = CustomTheme.colors.gradient.scaleMarsList,
            titleText = R.string.account_subscription_option_comments_title.strResDesc(),
            mainText = R.string.account_subscription_option_comments_text.strResDesc()
        )
        SubscriptionOptionItem(
            iconRes = R.drawable.ic_24_no_favourite,
            iconTint = CustomTheme.colors.moon.darkest,
            iconBackgroundColorsList = CustomTheme.colors.gradient.scaleMoonList,
            titleText = R.string.account_subscription_option_favourite_title.strResDesc(),
            mainText = R.string.account_subscription_option_favourite_text.strResDesc()
        )
        SubscriptionOptionItem(
            iconRes = R.drawable.ic_24_archive,
            iconTint = CustomTheme.colors.button.accent,
            iconBackgroundColorsList = CustomTheme.colors.gradient.brandList.reversed(),
            iconBackgroundFadeDirection = FadeDefaults.Direction.Vertical,
            titleText = R.string.account_subscription_option_archive_title.strResDesc(),
            mainText = R.string.account_subscription_option_archive_text.strResDesc()
        )
    }
}