package com.metacards.metacards.features.course.ui.passed_test_result

import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.FakeDialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.FakeDefaultDialogComponent
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.course.domain.entity.CourseContentId
import com.metacards.metacards.features.course.domain.entity.CourseResult
import com.metacards.metacards.features.layout.domain.LayoutId
import com.metacards.metacards.features.layout.domain.PredefinedLayoutWithAvailable
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakePassedTestResultComponent : CoursePassedTestResultComponent {

    override val courseName = null
    override val isShareInProgress: StateFlow<Boolean> = MutableStateFlow(false)
    override val courseResult = MutableStateFlow<CourseResult?>(null)
    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        FakeDialogControl(FakeDefaultDialogComponent())
    override val predefinedLayouts = MutableStateFlow<List<PredefinedLayoutWithAvailable>>(emptyList())
    override val testId: CourseContentId = CourseContentId("")
    override val title: StateFlow<LocalizableString> = MutableStateFlow(LocalizableString.empty)

    override fun onCloseClick() = Unit
    override fun onShareClick() = Unit
    override fun onVideoFullScreenClick(position: Long, videoUrl: String) = Unit
    override fun onRepeatTestClick() = Unit
    override fun onLayoutClick(layoutId: LayoutId) = Unit
    override fun onBlockContentClick() = Unit
}