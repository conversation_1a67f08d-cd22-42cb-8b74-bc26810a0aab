package com.metacards.metacards.features.course.domain.entity

import android.os.Parcelable
import com.metacards.metacards.core.localization.ui.LocalizableString
import kotlinx.datetime.toKotlinLocalDate
import kotlinx.parcelize.Parcelize
import java.time.ZoneId
import java.util.Date

@Parcelize
data class CoursePassedTest(
    val id: CourseTestDocumentId?,
    val name: LocalizableString,
    val testId: CourseContentId,
    val themeId: CourseThemeId,
    val courseId: CourseId,
    val score: Int,
    val resultDate: Date,
    val results: CoursePassedTestResult
) : Parcelable {

    fun getLocalDate() = resultDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().toKotlinLocalDate()

    fun toCourseResult() = CourseResult(
        cover = results.coverUrl,
        video = results.videoUrl,
        title = results.title,
        subtitle = results.subtitle,
        description = results.description,
        minScore = 0,
        maxScore = 0,
        linkedLayouts = results.linkedLayouts,
        linkedVideos = results.linkedVideos
    )
}