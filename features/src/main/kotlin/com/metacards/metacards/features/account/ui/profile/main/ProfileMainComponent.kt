package com.metacards.metacards.features.account.ui.profile.main

import androidx.annotation.StringRes
import com.metacards.metacards.core.bottom_sheet.BottomSheetControl
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.features.account.ui.profile.ProfileFlowComponent
import com.metacards.metacards.features.account.ui.profile.birth_year.ProfileBirthYearComponent
import com.metacards.metacards.features.account.ui.profile.delete.ProfileDeleteAccountComponent
import com.metacards.metacards.features.account.ui.profile.gender.ProfileGenderComponent
import com.metacards.metacards.features.auth.domain.LoginType
import kotlinx.coroutines.flow.StateFlow

interface ProfileMainComponent {
    val user: StateFlow<User?>
    val userEmail: StateFlow<String?>
    val userLoginTypes: StateFlow<Set<LoginType>?>
    val operationInProgress: StateFlow<Boolean>
    val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent>
    val birthYearBottomSheetControl: BottomSheetControl<ProfileBirthYearComponent.Config, ProfileBirthYearComponent>
    val genderBottomSheetControl: BottomSheetControl<ProfileGenderComponent.Config, ProfileGenderComponent>
    val deleteAccountComponent: ProfileDeleteAccountComponent
    val deleteAccountButtonState: StateFlow<ButtonState>

    fun onNameClick()
    fun onGenderClick()
    fun onBirthYearClick()
    fun onEmailClick()
    fun onChangePasswordClick()
    fun onLogoutClick()
    fun onDeleteAccountClick()
    fun onPasswordConfirmed()
    fun onSubscriptionStatusClick()
    fun onSubscriptionStatusLongPress()

    sealed interface Output {
        data class ProfileFlowScreenRequested(val screen: ProfileFlowComponent.Screen) : Output
        data object AuthScreenRequested : Output
        data class SignInViaWebViewRequested(
            val url: String,
            @StringRes val title: Int
        ) : Output
        data object WebViewDismissRequested : Output
        data object SubscriptionRequested : Output
    }
}