package com.metacards.metacards.features.special_deck.ui.starry_sky

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.bottom_sheet.ModalBottomSheet
import com.metacards.metacards.core.dialog.popup.MetaPopup
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.widget.button.MetaPrimaryButton
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeck
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCard
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCardCoordinate
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCardSize
import com.metacards.metacards.features.special_deck.domain.entity.ids
import com.metacards.metacards.features.special_deck.ui.card_opening_cooldown.SpecialDeckCardOpeningCooldownUi
import com.metacards.metacards.features.special_deck.ui.starry_sky.widgets.SpecialDeckStarrySkyCards
import com.metacards.metacards.features.special_deck.ui.starry_sky.widgets.SpecialDeckStarrySkyConnectionLines
import com.metacards.metacards.features.special_deck.ui.starry_sky.widgets.SpecialDeckStarrySkyConstellationNames
import com.metacards.metacards.features.special_deck.ui.starry_sky.widgets.SpecialDeckStarrySkyOnboardingHand
import com.metacards.metacards.features.special_deck.ui.starry_sky.widgets.SpecialDeckStarrySkyTitle
import com.metacards.metacards.features.special_deck.ui.swipe_tutorial.SpecialDeckSwipeTutorialUi
import dev.icerock.moko.resources.desc.strResDesc

private const val WIDTH_COEFFICIENT = 1.43f
private const val SKY_LAYOUT_HEIGHT = 609f
private const val INITIAL_SCALE = 1f
private const val SKY_APPEARANCE_ANIMATION_DURATION = 500

@Composable
fun SpecialDeckStarrySkyUi(component: SpecialDeckStarrySkyComponent) {
    val config = LocalConfiguration.current
    val ld = LocalDensity.current
    var titleHeightDp by remember { mutableStateOf(0.dp) }
    val skyHeight by remember { derivedStateOf { config.screenHeightDp.dp - titleHeightDp } }
    val skyWidth by remember { derivedStateOf { skyHeight * WIDTH_COEFFICIENT } }
    var scaleCoefficient by remember { mutableFloatStateOf(INITIAL_SCALE) }
    val deckCards by component.deckCards.collectAsState()
    val deckInfo by component.deckInfo.collectAsState()
    val userSpecialDeckInfo by component.userSpecialDeckInfo.collectAsState()
    val receivedCards by remember { derivedStateOf { userSpecialDeckInfo.cards.ids().toSet() } }
    val isUserStoppedInteraction by component.isUserStoppedInteraction.collectAsState()
    val isLoading by remember { derivedStateOf { scaleCoefficient == INITIAL_SCALE } }

    val skyAlpha by animateFloatAsState(
        targetValue = if (isLoading) 0f else 1f,
        label = "",
        animationSpec = tween(SKY_APPEARANCE_ANIMATION_DURATION)
    )

    Column(
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painter = painterResource(R.drawable.ic_starry_sky_bg),
                contentScale = ContentScale.FillBounds
            )
            .pointerInput(Unit) { detectTapGestures(onPress = { component.onUserInteracted() }) }
    ) {
        SpecialDeckStarrySkyTitle(
            deckCards = deckCards,
            receivedCards = receivedCards,
            userSpecialDeckInfo = userSpecialDeckInfo,
            onCloseClick = component::onCloseClick,
            modifier = Modifier
                .statusBarsPadding()
                .onSizeChanged { titleHeightDp = with(ld) { it.height.toDp() } }
        )

        Sky(
            skyWidth = skyWidth,
            skyHeight = skyWidth,
            skyAlpha = skyAlpha,
            isLoading = isLoading,
            isCanSelectNewCard = userSpecialDeckInfo.isCanSelectNewCard(),
            isUserStoppedInteraction = isUserStoppedInteraction,
            scaleCoefficient = scaleCoefficient,
            deckCards = deckCards,
            receivedCards = receivedCards,
            deckInfo = deckInfo,
            onCardClick = component::onCardClick,
            onLayoutClick = component::onLayoutClick,
            isDeckCompleted = userSpecialDeckInfo.isCompleted,
            modifier = Modifier
                .onSizeChanged { scaleCoefficient = it.height.div(SKY_LAYOUT_HEIGHT) }
        )
    }
    ModalBottomSheet(
        control = component.cooldownBottomSheetControl,
        isTransparentScrim = true
    ) {
        SpecialDeckCardOpeningCooldownUi(component = it)
    }

    MetaPopup(dialogControl = component.swipeTutorialControl) {
        SpecialDeckSwipeTutorialUi(it)
    }
}

@Composable
private fun Sky(
    skyWidth: Dp,
    skyHeight: Dp,
    skyAlpha: Float,
    isLoading: Boolean,
    isCanSelectNewCard: Boolean,
    isUserStoppedInteraction: Boolean,
    scaleCoefficient: Float,
    deckCards: List<SpecialDeckCard>,
    receivedCards: Set<CardId>,
    deckInfo: SpecialDeck?,
    onCardClick: (SpecialDeckCard) -> Unit,
    onLayoutClick: () -> Unit,
    isDeckCompleted: Boolean,
    modifier: Modifier = Modifier
) {
    CompositionLocalProvider(LocalLayoutDirection provides LayoutDirection.Ltr) {
        Box(
            modifier = Modifier
                .navigationBarsPadding()
        ) {
            Box(
                modifier = modifier
                    .horizontalScroll(rememberScrollState())
                    .size(
                        width = skyWidth,
                        height = skyHeight
                    )
                    .alpha(skyAlpha)
            ) {
                SpecialDeckStarrySkyConnectionLines(
                    modifier = Modifier
                        .fillMaxHeight()
                        .width(skyWidth),
                    cards = deckCards,
                    receivedStars = receivedCards,
                    scaleCoefficient = scaleCoefficient
                )

                SpecialDeckStarrySkyCards(
                    cards = deckCards,
                    scaleCoefficient = scaleCoefficient,
                    receivedCards = receivedCards,
                    isCanSelectNewStar = isCanSelectNewCard,
                    onCardClick = onCardClick
                )

                SpecialDeckStarrySkyConstellationNames(
                    deckInfo = deckInfo,
                    receivedCards = receivedCards,
                    scaleCoefficient = scaleCoefficient
                )
            }

            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier
                        .align(Alignment.Center),
                    backgroundColor = CustomTheme.colors.icons.secondary,
                    color = CustomTheme.colors.button.accent,
                    strokeWidth = 5.dp,
                    strokeCap = StrokeCap.Round
                )
            }
            Column(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
            ) {
                SpecialDeckStarrySkyOnboardingHand(
                    isUserStoppedInteraction,
                    isLoading,
                    isDeckCompleted,
                    modifier = Modifier.fillMaxWidth()
                )

                if (isDeckCompleted) {
                    MetaPrimaryButton(
                        text = R.string.special_deck_starry_sky_button_layout
                            .strResDesc()
                            .localizedByLocal(),
                        onClick = onLayoutClick,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun SkyPreview() {
    Sky(
        skyWidth = 1500.dp,
        skyHeight = 1000.dp,
        skyAlpha = 1f,
        isLoading = false,
        isCanSelectNewCard = true,
        isUserStoppedInteraction = true,
        scaleCoefficient = 1.5f,
        deckCards = listOf(
            SpecialDeckCard(
                id = CardId("1"), arObject = "", connections = listOf(),
                coordinates = SpecialDeckCardCoordinate(
                    x = 100,
                    y = 100
                ),
                imageUrl = "",
                quoteLocalized = LocalizableString(
                    localizationMap = mapOf(),
                    isNonLocalizable = false
                ),
                size = SpecialDeckCardSize.MINI, texture = "",
            ),
            SpecialDeckCard(
                id = CardId("2"), arObject = "",
                connections = listOf(CardId("1")),
                coordinates = SpecialDeckCardCoordinate(
                    x = 150,
                    y = 200
                ),
                imageUrl = "",
                quoteLocalized = LocalizableString(
                    localizationMap = mapOf(),
                    isNonLocalizable = false
                ),
                size = SpecialDeckCardSize.MINI, texture = "",
            ),
            SpecialDeckCard(
                id = CardId("3"), arObject = "",
                connections = listOf(CardId("1"), CardId("2")),
                coordinates = SpecialDeckCardCoordinate(
                    x = 250,
                    y = 150
                ),
                imageUrl = "",
                quoteLocalized = LocalizableString(
                    localizationMap = mapOf(),
                    isNonLocalizable = false
                ),
                size = SpecialDeckCardSize.MINI, texture = "",
            )
        ),
        receivedCards = setOf(CardId("1"), CardId("2"), CardId("3")),
        deckInfo = SpecialDeck(
            id = DeckId(value = "123"), coverUrl = "", hasAR = false, constellations = listOf(),
            name = LocalizableString(
                localizationMap = mapOf(),
                isNonLocalizable = false
            ),
            description = LocalizableString(
                localizationMap = mapOf(),
                isNonLocalizable = false
            )
        ),
        onCardClick = {},
        onLayoutClick = {},
        isDeckCompleted = true
    )
}
