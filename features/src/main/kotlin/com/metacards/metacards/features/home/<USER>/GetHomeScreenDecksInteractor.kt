package com.metacards.metacards.features.home.domain

import com.metacards.metacards.core.preferences.models.TutorialState
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.mapData
import com.metacards.metacards.features.deck.domain.entity.Deck.Companion.asDeckWithAvailable
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.domain.entity.DeckWithAvailable
import com.metacards.metacards.features.deck.domain.entity.getForPurchaseDecks
import com.metacards.metacards.features.deck.domain.entity.getPurchasedDecks
import com.metacards.metacards.features.deck.domain.repository.DecksRepository
import com.metacards.metacards.features.special_deck.domain.repository.SpecialDeckRepository
import com.metacards.metacards.features.tutorial.data.TutorialRepository
import com.metacards.metacards.features.user.domain.UserRepository
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.mapLatest

class GetHomeScreenDecksInteractor(
    private val userRepository: UserRepository,
    private val decksRepository: DecksRepository,
    private val specialDeckRepository: SpecialDeckRepository,
    private val tutorialRepository: TutorialRepository,
) {
    @OptIn(ExperimentalCoroutinesApi::class)
    fun execute(): Flow<List<DeckWithAvailable>> {
        val allDecksFlow = userRepository.user.flatMapLatest { user ->
            userRepository.getPurchasedDecksIdFlow().mapLatest { purchasedDecks ->
                getDecks(user, purchasedDecks.mapData { deckId -> deckId?.map { DeckId(it) } })
            }
        }

        val userSpecialDecksFlow = specialDeckRepository.getUserSpecialDecksFlow()
        val allSpecialDecks = specialDeckRepository.getSpecialDecksFlow()
        val specialDecksWithAvailableFlow = combine(allSpecialDecks, userSpecialDecksFlow) { all, users ->
            val usersCompletedSpecialDecksIds: List<DeckId> = users
                .filter { it.isCompleted }
                .map { it.specialDeckId }
            all.map { specialDeck ->
                specialDeck.toDeckWithAvailable(specialDeck.id in usersCompletedSpecialDecksIds)
            }
        }
        return combine(
            tutorialRepository.tutorialStateFlow,
            allDecksFlow,
            specialDecksWithAvailableFlow
        ) { tutorState, decks, specialDecks ->
            val sortedDecks = decks.toMutableList()
            if (tutorState == TutorialState.COMPLETED) {
                sortedDecks.addAll(0, specialDecks)
            } else {
                sortedDecks.addAll(specialDecks)
            }
            sortedDecks
        }
    }

    private suspend fun getDecks(
        user: User?,
        purchasedDecks: LoadableState<List<DeckId>>
    ): List<DeckWithAvailable> {
        val hasSubscription = user?.subscriptionState is User.SubscriptionState.Ongoing
        val allForPurchaseDecks = decksRepository.getDecksForPurchase()
        val purchased =
            purchasedDecks.data?.let { getPurchasedDecks(it, allForPurchaseDecks + decksRepository.getDecksForSubscription()) }
                ?: emptyList()
        val availableForPurchase = getForPurchaseDecks(purchased, allForPurchaseDecks)
        val forSubscription = decksRepository.getDecksForSubscription()
        val free = decksRepository.getFreeDecks()

        val decks = if (hasSubscription) {
            purchased + forSubscription + free + availableForPurchase
        } else {
            purchased + free + forSubscription + availableForPurchase
        }

        return decks.asDeckWithAvailable(hasSubscription, purchased)
    }
}
