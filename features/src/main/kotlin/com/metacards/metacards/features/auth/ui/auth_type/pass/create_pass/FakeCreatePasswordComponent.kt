package com.metacards.metacards.features.auth.ui.auth_type.pass.create_pass

import com.metacards.metacards.core.button.ButtonState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import ru.mobileup.kmm_form_validation.control.InputControl

class FakeCreatePasswordComponent(coroutineScope: CoroutineScope) : CreatePasswordComponent {
    override val passwordInputControl: InputControl = InputControl(coroutineScope)
    override val confirmPasswordInputControl: InputControl = InputControl(coroutineScope)
    override val enterButtonState: StateFlow<ButtonState> = MutableStateFlow(ButtonState.Enabled)

    override fun onEnterButtonClick() = Unit
}