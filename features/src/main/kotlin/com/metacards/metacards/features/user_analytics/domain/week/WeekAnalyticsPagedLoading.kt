package com.metacards.metacards.features.user_analytics.domain.week

import com.metacards.metacards.core.paged_loading.DuplicateRemovingDataMerger
import com.metacards.metacards.core.paged_loading.Page
import com.metacards.metacards.core.paged_loading.PagedLoader
import com.metacards.metacards.core.paged_loading.PagedLoading
import com.metacards.metacards.core.utils.APPLICATION_START_DATE
import com.metacards.metacards.core.utils.firstDayOfWeek
import com.metacards.metacards.core.utils.getTodayLocalDate
import com.metacards.metacards.features.record.data.RecordRepository
import com.metacards.metacards.features.record.domain.RecordFilter
import kotlinx.coroutines.CoroutineScope
import kotlinx.datetime.DatePeriod
import kotlinx.datetime.minus

class WeekAnalyticsPagedLoading(
    coroutineScope: CoroutineScope,
    recordRepository: RecordRepository
) : PagedLoading<WeekAnalyticsInfo> by createPagedLoading(coroutineScope, recordRepository) {

    companion object {
        private fun createPagedLoading(
            coroutineScope: CoroutineScope,
            recordRepository: RecordRepository
        ): PagedLoading<WeekAnalyticsInfo> {
            return PagedLoading(
                scope = coroutineScope,
                loader = WeekAnalyticsPagedLoader(recordRepository),
                dataMerger = DuplicateRemovingDataMerger(keySelector = { it.firstDayOfWeek })
            )
        }
    }
}

private class WeekAnalyticsPagedLoader(
    private val recordRepository: RecordRepository
) : PagedLoader<WeekAnalyticsInfo> {

    companion object {
        private val FILTER: RecordFilter = RecordFilter(
            onlyFavourite = false,
            archived = false
        )
    }

    override suspend fun loadFirstPage(): Page<WeekAnalyticsInfo> {
        val endDate = getTodayLocalDate()
        val startDate = endDate.firstDayOfWeek()

        val records = recordRepository.getRecordsBetweenDates(startDate, endDate, FILTER)
        val weekAnalyticsInfo = WeekAnalyticsInfo(firstDayOfWeek = startDate, records)

        return Page(
            listOf(weekAnalyticsInfo),
            hasNextPage = true,
            hasPreviousPage = false
        )
    }

    override suspend fun loadNextPage(loadedData: List<WeekAnalyticsInfo>): Page<WeekAnalyticsInfo> {
        val endDate = loadedData.last().firstDayOfWeek - DatePeriod(days = 1)
        val startDate = endDate.firstDayOfWeek()

        val records = recordRepository.getRecordsBetweenDates(startDate, endDate, FILTER)
        val weekAnalyticsInfo = WeekAnalyticsInfo(firstDayOfWeek = startDate, records)

        return Page(
            listOf(weekAnalyticsInfo),
            hasNextPage = startDate > APPLICATION_START_DATE,
            hasPreviousPage = true
        )
    }
}