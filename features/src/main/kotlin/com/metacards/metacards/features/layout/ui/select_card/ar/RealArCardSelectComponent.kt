package com.metacards.metacards.features.layout.ui.select_card.ar

import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.doOnCreate
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.error_handling.safeRun
import com.metacards.metacards.core.sharing.SharingManager
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.UserInputTransformation
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardHint
import com.metacards.metacards.features.deck.domain.entity.CardWithFavoriteAndComment
import com.metacards.metacards.features.deck.domain.entity.toFavorite
import com.metacards.metacards.features.deck.domain.interactor.GetCardWithFavoriteInteractor
import com.metacards.metacards.features.deck.domain.repository.DecksRepository
import com.metacards.metacards.features.layout.domain.ar.GridSettings
import com.metacards.metacards.features.layout.domain.ar.StashSettings
import com.metacards.metacards.features.layout.ui.card_list.CardListComponent
import com.metacards.metacards.features.user.domain.GetUserSubscriptionStateInteractor
import com.metacards.metacards.features.user.domain.ToggleFavoriteCardInteractor
import io.github.sceneview.math.Position
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

class RealArCardSelectComponent(
    componentContext: ComponentContext,
    override val isPredefined: Boolean,
    private val getCardWithFavoriteInteractor: GetCardWithFavoriteInteractor,
    private val toggleFavoriteCardInteractor: ToggleFavoriteCardInteractor,
    private val sharingManager: SharingManager,
    getUserSubscriptionStateInteractor: GetUserSubscriptionStateInteractor,
    private val errorHandler: ErrorHandler,
    private val analyticsService: AnalyticsService,
    private val decksRepository: DecksRepository,
    private val onOutput: (ArCardSelectComponent.Output) -> Unit
) : ArCardSelectComponent, ComponentContext by componentContext {
    companion object {
        private const val MAX_CARD_COUNT = 2
        private const val GRID_VERTICAL_SIZE = 4
        private const val GRID_HORIZONTAL_SIZE = 2
        private const val MAX_CARD_COMMENT_TEXT_LENGTH = 3000
    }

    private val debounce = Debounce()

    override val gridSettings = GridSettings(GRID_VERTICAL_SIZE, GRID_HORIZONTAL_SIZE)
    override val stashSettings =
        StashSettings(MAX_CARD_COUNT - 1, offset = Position(x = 0.41f, z = -0.2f))

    private val subscriptionState = getUserSubscriptionStateInteractor.execute()
        .stateIn(componentScope, SharingStarted.Eagerly, null)

    override val stashCardEvent: MutableSharedFlow<StashAction> =
        MutableSharedFlow(extraBufferCapacity = 1, onBufferOverflow = BufferOverflow.DROP_OLDEST)

    override val reloadEvent: MutableSharedFlow<Unit> =
        MutableSharedFlow(extraBufferCapacity = 1, onBufferOverflow = BufferOverflow.DROP_OLDEST)

    override val canSelectMoreCard: MutableStateFlow<CardListComponent.CanSelectCard> =
        MutableStateFlow(CardListComponent.CanSelectCard.Can)

    private val selectedCards: MutableStateFlow<List<CardWithFavoriteAndComment>> =
        MutableStateFlow(listOf())

    override val selectedCard = computed(selectedCards) { it.lastOrNull() }

    private val cardHintList = MutableStateFlow<List<CardHint>?>(null)

    override val cardHint = computed(selectedCards, cardHintList) { cards, hints ->
        hints?.let { hintList ->
            val modResult = cards.size % hintList.size
            var hintsToTakeFrom = hintList

            if (modResult == 0) {
                hintsToTakeFrom = hintsToTakeFrom.shuffled()
            }

            hintsToTakeFrom[modResult]
        }
    }

    override val isShareLoading: MutableStateFlow<Boolean> = MutableStateFlow(false)

    init {
        lifecycle.doOnCreate {
            componentScope.safeLaunch(errorHandler) {
                cardHintList.value = decksRepository.getCardHints()?.shuffled()
            }
        }
    }

    override fun onToggleShare(cardUrl: String, questionText: String) {
        componentScope.safeLaunch(
            errorHandler = errorHandler,
            onErrorHandled = {
                isShareLoading.value = false
            }
        ) {
            isShareLoading.value = true
            sharingManager.shareCard(
                cardUrl = cardUrl,
                isDailyCard = false,
                text = "initialQuestionText"
            )
            isShareLoading.value = false
        }
    }

    override fun onToggleFavorite(isOn: Boolean) {
        DebounceClick(debounce, "toggleFavorite", errorHandler) {
            when (subscriptionState.value) {
                is User.SubscriptionState.None -> onOutput(ArCardSelectComponent.Output.SubscriptionSuggestingRequested)
                is User.SubscriptionState.Ongoing -> {
                    if (isOn) {
                        analyticsService.logEvent(
                            if (isPredefined) {
                                AnalyticsEvent.LayoutArAddFavCardEvent
                            } else {
                                AnalyticsEvent.SetupArCardFavAddEvent
                            }
                        )
                    }
                    val cardWithFavorite = selectedCard.value
                    cardWithFavorite?.let {
                        toggleFavoriteCardInteractor.execute(isOn, cardWithFavorite.card.toFavorite())
                        selectedCards.update { cards ->
                            cards.map {
                                if (it.card == cardWithFavorite.card) cardWithFavorite.toggleFavorite() else it
                            }
                        }
                    }
                }

                null -> onOutput(ArCardSelectComponent.Output.AuthSuggestingRequested)
            }
        }
    }

    override fun selectCard(card: Card) {
        analyticsService.logEvent(AnalyticsEvent.SetupArCardTapEvent)
        selectedCards.update {
            it.toMutableList() + listOf(
                safeRun(errorHandler) {
                    getCardWithFavoriteInteractor.execute(card)
                } ?: CardWithFavoriteAndComment.default(card)
            )
        }

        canSelectMoreCard.value = when {
            selectedCards.value.size >= MAX_CARD_COUNT -> CardListComponent.CanSelectCard.Cant
            selectedCards.value.size == MAX_CARD_COUNT - 1 -> CardListComponent.CanSelectCard.CanLast
            else -> CardListComponent.CanSelectCard.Can
        }
    }

    override fun onMoreCardClick() {
        analyticsService.logEvent(
            if (isPredefined) {
                AnalyticsEvent.LayoutArOneMoreEvent
            } else {
                AnalyticsEvent.SetupArOneMoreEvent
            }
        )

        stashCardEvent.tryEmit(StashAction.WithRefilling)
    }

    override fun onFinishClick() {
        componentScope.launch {
            stashCardEvent.tryEmit(StashAction.WithoutRefilling)

            delay(500) // Wait for stash animation

            if (isPredefined) {
                onOutput(
                    ArCardSelectComponent.Output.OnPredefinedFinished(
                        selectedCards.value.map { it.toCardWithComment() }
                    )
                )
                selectedCards.value = emptyList()
            } else {
                onOutput(
                    ArCardSelectComponent.Output.OnFinished(
                        selectedCards.value.map { it.toCardWithComment() }
                    )
                )
            }
        }
    }

    override fun onCardsLoaded() {
        analyticsService.logEvent(AnalyticsEvent.SetupArPrepEvent)
    }

    override fun reload() {
        reloadEvent.tryEmit(Unit)
    }

    override fun onCardCommentValueChanged(newComment: String) {
        val transformedValue = UserInputTransformation
            .transform(newComment)
            .take(MAX_CARD_COMMENT_TEXT_LENGTH)

        selectedCards.update {
            it.toMutableList().apply {
                this[indices.last] = this[indices.last].copy(comment = transformedValue)
            }
        }
    }
}

sealed interface StashAction {
    data object WithRefilling : StashAction
    data object WithoutRefilling : StashAction
}
