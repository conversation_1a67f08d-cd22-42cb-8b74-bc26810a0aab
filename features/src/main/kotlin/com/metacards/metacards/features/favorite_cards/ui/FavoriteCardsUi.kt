package com.metacards.metacards.features.favorite_cards.ui

import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.arkivanov.decompose.extensions.compose.jetpack.stack.Children
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.features.favorite_cards.ui.details.FavoriteCardDetailsUi
import com.metacards.metacards.features.favorite_cards.ui.list.FavoriteCardListUi

@Composable
fun FavoriteCardsUi(component: FavoriteCardsComponent, modifier: Modifier = Modifier) {
    val childStack by component.childStack.collectAsState()

    Children(
        stack = childStack,
        modifier = modifier.statusBarsPadding()
    ) { child ->
        when (val instance = child.instance) {
            is FavoriteCardsComponent.Child.List -> FavoriteCardListUi(component = instance.component)
            is FavoriteCardsComponent.Child.Details -> FavoriteCardDetailsUi(component = instance.component)
        }
    }
}

@Preview(showSystemUi = true)
@Composable
fun FavoriteCardsUiPreview() {
    AppTheme {
        FavoriteCardsUi(component = FakeFavoriteCardsComponent())
    }
}