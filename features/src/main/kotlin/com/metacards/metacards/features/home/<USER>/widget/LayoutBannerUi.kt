package com.metacards.metacards.features.home.ui.widget

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.ui.MetaCardDefaults
import com.metacards.metacards.features.home.ui.image.transformation.RenderScriptReplacementToolkitBlurTransformation
import dev.icerock.moko.resources.desc.desc

@Composable
fun LayoutBanner(
    modifier: Modifier = Modifier,
    headerText: String,
    descriptionText: String,
    actionButtonText: String,
    customBackground: Any? = R.drawable.bg_start_point,
    blurBackground: Boolean = false,
    onClick: () -> Unit = {},
) {
    Surface(
        modifier = modifier
            .clip(RoundedCornerShape(16.dp))
            .height(165.dp)
            .clickable(onClick = onClick),
        shape = RoundedCornerShape(16.dp),
        color = CustomTheme.colors.background.primary
    ) {
        Box {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(customBackground)
                    .crossfade(MetaCardDefaults.CROSSFADE_DURATION_MILLIS)
                    .transformations(if (blurBackground) listOf(RenderScriptReplacementToolkitBlurTransformation()) else emptyList())
                    .build(),
                contentScale = ContentScale.Crop,
                contentDescription = null,
                modifier = Modifier.matchParentSize()
            )
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 10.dp, vertical = 20.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = headerText,
                    style = CustomTheme.typography.heading.additional,
                    color = CustomTheme.colors.text.primary,
                    textAlign = TextAlign.Center
                )

                Text(
                    text = descriptionText,
                    textAlign = TextAlign.Center,
                    minLines = 3,
                    maxLines = 3,
                    style = CustomTheme.typography.caption.medium,
                    color = CustomTheme.colors.text.primary
                )
            }

            ActionButton(
                actionButtonText.desc(),
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 20.dp)
            )
        }
    }
}

@Preview
@Composable
fun LayoutBannerPreview() {
    AppTheme {
        LayoutBanner(
            headerText = "Header",
            descriptionText = "Some description text. Can be multiline. 3 lines max. That's it! More text for preview. On the far-away island of Sala-ma-Sond, Yertle the Trutle was the kign ",
            actionButtonText = "Смотреть"
        )
    }
}
