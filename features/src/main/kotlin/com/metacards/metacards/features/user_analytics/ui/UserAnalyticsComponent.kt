package com.metacards.metacards.features.user_analytics.ui

import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.user_analytics.ui.layouts.LayoutsAnalyticsComponent
import com.metacards.metacards.features.user_analytics.ui.month.MonthAnalyticsComponent
import com.metacards.metacards.features.user_analytics.ui.week.WeekAnalyticsComponent
import kotlinx.coroutines.flow.StateFlow

interface UserAnalyticsComponent {

    val childStack: StateFlow<ChildStack<*, Child>>

    val selectedTab: StateFlow<Tab>

    val userSubscriptionState: StateFlow<User.SubscriptionState?>

    fun onTabSelected(tab: Tab)

    fun onBlockContentClick()

    enum class Tab {
        Layouts, Week, Month
    }

    sealed interface Child {
        data class Layouts(val component: LayoutsAnalyticsComponent) : Child
        data class Week(val component: WeekAnalyticsComponent) : Child
        data class Month(val component: MonthAnalyticsComponent) : Child
    }

    sealed interface Output {
        data class RecordDetailsRequested(val recordId: RecordId) : Output
        data object PremiumSuggestingRequested : Output
        data object AuthSuggestingRequested : Output
    }
}