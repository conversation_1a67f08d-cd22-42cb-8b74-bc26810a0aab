package com.metacards.metacards.features.record.ui.journal.test_list

import com.metacards.metacards.features.course.domain.entity.CoursePassedTest
import kotlinx.coroutines.flow.StateFlow

interface JournalTestListComponent {
    fun onTestClick(coursePassedTest: CoursePassedTest)

    val tests: StateFlow<List<CoursePassedTest>>

    sealed interface Output {
        data class TestDetailsRequested(val coursePassedTest: CoursePassedTest) : Output
    }
}