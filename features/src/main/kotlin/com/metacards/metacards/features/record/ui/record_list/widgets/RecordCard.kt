package com.metacards.metacards.features.record.ui.record_list.widgets

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.DismissDirection
import androidx.compose.material.DismissState
import androidx.compose.material.DismissValue
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.prettyFormat
import com.metacards.metacards.core.widget.SwipeToReveal
import com.metacards.metacards.features.R
import com.metacards.metacards.features.record.domain.Record
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.record.ui.utils.energyLevelToColor
import com.metacards.metacards.features.record.ui.utils.moodLevelToColor
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.datetime.LocalDate

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun RecordCard(
    record: Record,
    showDate: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    onFavouriteClick: (RecordId) -> Unit = {},
    onArchiveClick: (RecordId) -> Unit = {},
    revealEnabled: Boolean = true,
    revealedRecordIdFlow: MutableSharedFlow<RecordId> = MutableSharedFlow(),
    resetEvent: Flow<Unit> = emptyFlow()
) {
    var isOnArchiveClick by remember { mutableStateOf(false) }
    var isOnFavouriteClick by remember { mutableStateOf(false) }
    val revealedRecordId by revealedRecordIdFlow.collectAsState(initial = record.id)

    Column(modifier = modifier) {
        if (showDate) {
            DateText(
                date = record.creationDate,
                modifier = Modifier.padding(start = 16.dp, bottom = 8.dp, top = 8.dp)
            )
        }

        val state = remember {
            DismissState(DismissValue.Default) { true }
        }

        SwipeToReveal(
            state = state,
            dismissMaxOffset = if (!record.isArchived) 128.dp else 76.dp,
            directions = setOf(DismissDirection.EndToStart),
            enabled = revealEnabled,
            background = {
                Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.CenterEnd) {
                    Row(
                        modifier = Modifier.matchParentSize(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.End
                    ) {
                        if (!record.isArchived) {
                            Surface(
                                modifier = Modifier
                                    .padding(start = 24.dp, end = 8.dp)
                                    .size(36.dp),
                                color = CustomTheme.colors.button.accent,
                                shape = CircleShape,
                                onClick = {
                                    onFavouriteClick(record.id)
                                    isOnFavouriteClick = true
                                }
                            ) {
                                Icon(
                                    modifier = Modifier.padding(6.dp),
                                    painter = painterResource(
                                        id = if (record.isFavourite) {
                                            R.drawable.ic_24_favourite
                                        } else {
                                            R.drawable.ic_24_no_favourite
                                        }
                                    ),
                                    contentDescription = null,
                                    tint = CustomTheme.colors.icons.primary
                                )
                            }
                        }

                        Surface(
                            modifier = Modifier
                                .padding(
                                    start = if (!record.isArchived) 8.dp else 24.dp,
                                    end = 12.dp
                                )
                                .size(36.dp),
                            color = CustomTheme.colors.button.accent,
                            shape = CircleShape,
                            onClick = {
                                onArchiveClick(record.id)
                                isOnArchiveClick = true
                            }
                        ) {
                            Icon(
                                modifier = Modifier.padding(6.dp),
                                painter = painterResource(
                                    id = if (!record.isArchived) {
                                        R.drawable.ic_24_archive
                                    } else {
                                        R.drawable.ic_24_out_archive
                                    }
                                ),
                                contentDescription = null,
                                tint = CustomTheme.colors.icons.primary
                            )
                        }
                    }
                }
            }
        ) {
            LaunchedEffect(key1 = Unit) {
                resetEvent.collect {
                    state.animateTo(DismissValue.Default)
                }
            }

            LaunchedEffect(
                key1 = isOnArchiveClick ||
                    isOnFavouriteClick ||
                    revealedRecordId != record.id
            ) {
                state.animateTo(DismissValue.Default)
                isOnFavouriteClick = false
                isOnArchiveClick = false
            }

            LaunchedEffect(key1 = state.direction < 0) {
                revealedRecordIdFlow.emit(record.id)
            }

            Surface(
                shape = RoundedCornerShape(16.dp),
                color = CustomTheme.colors.background.primary,
                onClick = onClick
            ) {
                Row(
                    modifier = Modifier
                        .padding(start = 16.dp, end = 20.dp, top = 16.dp, bottom = 16.dp)
                        .height(IntrinsicSize.Max)
                ) {
                    CardImage(
                        imageUrl = record.mainCard?.imageUrl,
                        hasSeveralImages = record.distinctCards.size > 1
                    )

                    Column(
                        modifier = Modifier
                            .weight(1.0f)
                            .fillMaxHeight()
                            .padding(start = 12.dp),
                        verticalArrangement = Arrangement.SpaceBetween
                    ) {
                        QuestionText(
                            modifier = Modifier.fillMaxWidth(),
                            questionText = record.mainQuestion?.text?.localizedByLocal()
                        )

                        InfoRow(
                            modifier = Modifier.padding(top = 4.dp),
                            commentCount = record.comments.size,
                            isFavourite = record.isFavourite,
                            isFromCourse = record.isFromCourse
                        )
                    }

                    Column(
                        modifier = Modifier.padding(start = 16.dp)
                    ) {

                        record.energyLevel?.let {
                            EnergyLevelIndicator(level = it)
                        }
                        record.moodLevel?.let {
                            MoodLevelIndicator(
                                level = it,
                                modifier = Modifier.padding(top = 2.dp)
                            )
                        }

                        if (record.moodLevel == null && record.energyLevel == null) {
                            LevelIndicatorPlaceholder()
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun DateText(date: LocalDate, modifier: Modifier) {
    Text(
        modifier = modifier,
        text = date.prettyFormat(LocalContext.current),
        color = CustomTheme.colors.text.secondary,
        style = CustomTheme.typography.caption.medium
    )
}

@Composable
private fun CardImage(imageUrl: String?, hasSeveralImages: Boolean, modifier: Modifier = Modifier) {
    val cardWidth = 52.dp
    val cardHeight = 73.dp
    val cornerRadius = 8.dp
    val secondCardOffset = 8.dp

    Box(
        modifier = modifier.size(cardWidth + secondCardOffset, cardHeight)
    ) {
        Surface(
            modifier = modifier
                .size(cardWidth, cardHeight)
                .align(Alignment.TopEnd),
            color = if (hasSeveralImages) {
                CustomTheme.colors.background.hint
            } else {
                Color.Transparent
            },
            shape = RoundedCornerShape(cornerRadius)
        ) {}

        AsyncImage(
            modifier = modifier
                .size(cardWidth, cardHeight)
                .clip(shape = RoundedCornerShape(cornerRadius))
                .align(Alignment.TopStart),
            model = ImageRequest.Builder(LocalContext.current)
                .data(imageUrl)
                .crossfade(true)
                .placeholder(R.drawable.img_medium_card_placeholder)
                .fallback(R.drawable.img_medium_card_placeholder)
                .let {
                    with(LocalDensity.current) {
                        it.size(cardWidth.roundToPx(), cardHeight.roundToPx())
                    }
                }
                .build(),
            contentScale = ContentScale.Crop,
            contentDescription = null,
        )
    }
}

@Composable
private fun QuestionText(questionText: String?, modifier: Modifier = Modifier) {
    val text = questionText ?: R.string.record_list_missing_question_text.strResDesc().localizedByLocal()

    val color = if (questionText != null) {
        CustomTheme.colors.text.primary
    } else {
        CustomTheme.colors.text.secondary
    }

    Text(
        modifier = modifier,
        text = text,
        color = color,
        style = CustomTheme.typography.body.primary,
        minLines = 2,
        maxLines = 2,
        overflow = TextOverflow.Ellipsis
    )
}

@Composable
private fun InfoRow(
    commentCount: Int,
    isFavourite: Boolean,
    isFromCourse: Boolean,
    modifier: Modifier
) {
    Row(modifier = modifier) {
        if (commentCount > 0) {
            Box(
                modifier = Modifier
                    .size(24.dp)
                    .background(CustomTheme.colors.background.primary, CircleShape)
            ) {
                Text(
                    modifier = Modifier.align(Alignment.Center),
                    text = commentCount.toString(),
                    style = CustomTheme.typography.caption.small,
                    color = CustomTheme.colors.text.primary
                )
            }
        }

        if (isFavourite) {
            Box(
                modifier = Modifier
                    .padding(start = 4.dp)
                    .size(24.dp)
                    .background(CustomTheme.colors.background.primary, CircleShape)
            ) {
                Icon(
                    modifier = Modifier.align(Alignment.Center),
                    painter = painterResource(R.drawable.ic_24_bookmark),
                    contentDescription = null,
                    tint = CustomTheme.colors.icons.primary
                )
            }
        }

        if (isFromCourse) {
            Box(
                modifier = Modifier
                    .padding(start = 4.dp)
                    .size(24.dp)
                    .background(CustomTheme.colors.background.primary, CircleShape)
            ) {
                Icon(
                    modifier = Modifier.align(Alignment.Center),
                    painter = painterResource(R.drawable.ic_24_study_hat),
                    contentDescription = null,
                    tint = CustomTheme.colors.icons.primary
                )
            }
        }
    }
}

@Composable
private fun MoodLevelIndicator(level: Int, modifier: Modifier = Modifier) {
    Surface(
        modifier = modifier.size(16.dp),
        shape = CircleShape,
        color = moodLevelToColor(level)
    ) {}
}

@Composable
private fun EnergyLevelIndicator(level: Int, modifier: Modifier = Modifier) {
    Surface(
        modifier = modifier.size(16.dp),
        shape = CircleShape,
        color = energyLevelToColor(level)
    ) {}
}

@Composable
private fun LevelIndicatorPlaceholder(modifier: Modifier = Modifier) {
    Spacer(modifier = modifier.size(16.dp))
}

@Preview
@Composable
fun RecordCardPreview() {
    AppTheme {
        RecordCard(
            record = Record.mock(1),
            showDate = true,
            onClick = {},
            onArchiveClick = {},
            onFavouriteClick = {}
        )
    }
}