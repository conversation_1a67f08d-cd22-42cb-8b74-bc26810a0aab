package com.metacards.metacards.features.auth.ui

import com.metacards.metacards.core.utils.createFakeChildStackStateFlow
import com.metacards.metacards.features.auth.ui.sign_in.FakeSignInComponent
import kotlinx.coroutines.CoroutineScope

class FakeAuthComponent(coroutineScope: CoroutineScope) : AuthComponent {
    override val childStack = createFakeChildStackStateFlow(
        AuthComponent.Child.SignIn(FakeSignInComponent(coroutineScope))
    )
}