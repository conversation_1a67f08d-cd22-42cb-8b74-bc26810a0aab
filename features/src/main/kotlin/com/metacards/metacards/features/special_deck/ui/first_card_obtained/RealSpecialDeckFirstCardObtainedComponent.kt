package com.metacards.metacards.features.special_deck.ui.first_card_obtained

import com.arkivanov.decompose.ComponentContext

class RealSpecialDeckFirstCardObtainedComponent(
    componentContext: ComponentContext,
    override val imageUrl: String,
    private val onOutput: (SpecialDeckFirstCardObtainedComponent.Output) -> Unit
) : ComponentContext by componentContext, SpecialDeckFirstCardObtainedComponent {

    override fun onCoolClick() {
        onOutput(SpecialDeckFirstCardObtainedComponent.Output.DismissRequested)
    }
}