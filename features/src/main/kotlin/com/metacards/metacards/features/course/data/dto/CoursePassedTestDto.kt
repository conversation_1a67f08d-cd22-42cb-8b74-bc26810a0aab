package com.metacards.metacards.features.course.data.dto

import com.google.firebase.Timestamp
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.course.domain.entity.CourseContentId
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CoursePassedTest
import com.metacards.metacards.features.course.domain.entity.CourseTestDocumentId
import com.metacards.metacards.features.course.domain.entity.CourseThemeId

data class CoursePassedTestDto(
    val id: String? = null,
    val name: Map<String, String?> = emptyMap(),
    val testId: String = "",
    val courseId: String = "",
    val themeId: String = "",
    val score: Int = 0,
    val resultDate: Timestamp = Timestamp.now(),
    val results: CoursePassedTestResultDto = CoursePassedTestResultDto()
) {
    fun toDomain() = CoursePassedTest(
        id = id?.let(::CourseTestDocumentId),
        name = LocalizableString(name),
        testId = CourseContentId(testId),
        courseId = CourseId(courseId),
        themeId = CourseThemeId(themeId),
        score = score,
        resultDate = resultDate.toDate(),
        results = results.toDomain()
    )

    companion object {
        fun fromDomain(entity: CoursePassedTest) = CoursePassedTestDto(
            name = entity.name.toMap(),
            testId = entity.testId.value,
            courseId = entity.courseId.value,
            themeId = entity.themeId.value,
            score = entity.score,
            resultDate = Timestamp(entity.resultDate),
            results = CoursePassedTestResultDto.fromDomain(entity.results)
        )
    }
}