package com.metacards.metacards.features.deeplink.domain

import com.metacards.metacards.core.deeplink.DeeplinkAction
import com.metacards.metacards.core.user.domain.SubscriptionId
import com.metacards.metacards.features.deck.domain.entity.DeckId
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
sealed class DeeplinkData(val type: Type, val deeplink: Deeplink?) : DeeplinkAction {

    @Serializable
    @SerialName(Type.AUTH)
    data class Auth(val link: Deeplink) : DeeplinkData(Type(Type.AUTH), Deeplink(Deeplink.AUTH))

    @Serializable
    @SerialName(Type.CLOSE_AUTH)
    data object CloseAuth : DeeplinkData(Type(Type.AUTH), Deeplink(Deeplink.AUTH))

    @Serializable
    @SerialName(Type.NEW_DECK)
    data class NewDeck(val deckId: DeckId) :
        DeeplinkData(Type(Type.NEW_DECK), Deeplink(Deeplink.DECK_ID))

    @Serializable
    @SerialName(Type.SUB_PURCHASE_SOON)
    data object SubPurchaseSoon : DeeplinkData(Type(Type.SUB_PURCHASE_SOON), Deeplink(Deeplink.SUB))

    @Serializable
    @SerialName(Type.SUB_PURCHASE_ERROR)
    data object SubPurchaseError : DeeplinkData(Type(Type.SUB_PURCHASE_ERROR), Deeplink(Deeplink.SUB))

    @Serializable
    @SerialName(Type.MAKE_RECORD)
    data object MakeRecord : DeeplinkData(Type(Type.MAKE_RECORD), Deeplink(Deeplink.MAIN))

    @Serializable
    @SerialName(Type.LOOK_ANALYTICS)
    data object LookAnalytics :
        DeeplinkData(Type(Type.LOOK_ANALYTICS), Deeplink(Deeplink.ANALYTICS_MONTH))

    @Serializable
    @SerialName(Type.GET_SUB)
    data object GetSub : DeeplinkData(Type(Type.GET_SUB), Deeplink(Deeplink.SUB))

    @Serializable
    @SerialName(Type.LINK_DECK)
    data class Deck(val deckId: DeckId) :
        DeeplinkData(Type(Type.LINK_DECK), null)

    @Serializable
    @SerialName(Type.DAILY_CARD)
    data object DailyCard : DeeplinkData(Type(Type.DAILY_CARD), null)

    @Serializable
    @SerialName(Type.PAYMENT_COMPLETED)
    sealed class PaymentCompleted :
        DeeplinkData(Type(Type.PAYMENT_COMPLETED), Deeplink(Deeplink.PAYMENT_COMPLETED)) {
        data class Deck(val deckId: DeckId) : PaymentCompleted()
        data class Subscription(val subId: SubscriptionId) : PaymentCompleted()
    }

    @Serializable
    @SerialName(Type.COURSE)
    data class Course(val themeId: String, val courseId: String) :
        DeeplinkData(Type(Type.COURSE), null)

    @Serializable
    @JvmInline
    value class Type(val value: String) {
        companion object {
            const val AUTH = "auth"
            const val CLOSE_AUTH = "close_auth"
            const val NEW_DECK = "new_deck"
            const val SUB_PURCHASE_SOON = "sub_purchase_soon"
            const val SUB_PURCHASE_ERROR = "sub_purchase_error"
            const val MAKE_RECORD = "make_record"
            const val LOOK_ANALYTICS = "look_analytics"
            const val GET_SUB = "get_sub"
            const val LINK_DECK = "deck"
            const val PAYMENT_COMPLETED = "success_payment"
            const val DAILY_CARD = "daily_card"
            const val COURSE = "course"
        }
    }

    @Serializable
    @JvmInline
    value class Deeplink(val value: String) {
        companion object {
            const val SCHEME = "metacards"

            const val DECK_ID = "deckid"
            const val COURSE = "course"
            const val SUB = "sub"
            const val MAIN = "main"
            const val ANALYTICS_MONTH = "analitycs/month"
            const val AUTH = "auth"
            const val PAYMENT_COMPLETED = "successPayment"
            const val DAILY_CARD = "dailyCard"

            fun create(deeplinkPath: String): Deeplink {
                return Deeplink("$SCHEME://$deeplinkPath")
            }
        }
    }
}