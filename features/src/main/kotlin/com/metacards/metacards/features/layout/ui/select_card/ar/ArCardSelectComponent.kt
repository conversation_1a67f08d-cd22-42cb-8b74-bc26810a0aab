package com.metacards.metacards.features.layout.ui.select_card.ar

import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardHint
import com.metacards.metacards.features.deck.domain.entity.CardWithComment
import com.metacards.metacards.features.deck.domain.entity.CardWithFavoriteAndComment
import com.metacards.metacards.features.layout.domain.ar.GridSettings
import com.metacards.metacards.features.layout.domain.ar.StashSettings
import com.metacards.metacards.features.layout.ui.card_list.CardListComponent
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow

interface ArCardSelectComponent {
    val isPredefined: Boolean
    val canSelectMoreCard: StateFlow<CardListComponent.CanSelectCard>
    val stashCardEvent: SharedFlow<StashAction>
    val reloadEvent: SharedFlow<Unit>
    val stashSettings: StashSettings
    val gridSettings: GridSettings
    val selectedCard: StateFlow<CardWithFavoriteAndComment?>
    val cardHint: StateFlow<CardHint?>
    val isShareLoading: StateFlow<Boolean>

    fun onToggleShare(cardUrl: String, questionText: String)
    fun onToggleFavorite(isOn: Boolean)
    fun onMoreCardClick()
    fun onFinishClick()
    fun selectCard(card: Card)
    fun onCardsLoaded()
    fun reload()
    fun onCardCommentValueChanged(newComment: String)

    sealed interface Output {
        data class OnFinished(val cards: List<CardWithComment>) : Output
        data class OnPredefinedFinished(val cards: List<CardWithComment>) : Output
        data object SubscriptionSuggestingRequested : Output
        data object AuthSuggestingRequested : Output
    }
}