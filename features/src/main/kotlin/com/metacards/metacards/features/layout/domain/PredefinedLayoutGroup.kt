package com.metacards.metacards.features.layout.domain

import android.os.Parcelable
import com.metacards.metacards.core.localization.ui.LocalizableString
import kotlinx.parcelize.Parcelize

@JvmInline
@Parcelize
value class LayoutGroupId(val value: String) : Parcelable

data class PredefinedLayoutGroup(
    val id: LayoutGroupId,
    val layoutIds: List<LayoutId>,
    val name: LocalizableString,
)

data class PredefinedLayoutGroupWithAvailable(
    val name: LocalizableString,
    val layouts: List<PredefinedLayoutWithAvailable>,
) {
    companion object {
        val MOCK = PredefinedLayoutGroupWithAvailable(
            name = LocalizableString.createNonLocalizable("Some Group"),
            layouts = List(1) { PredefinedLayoutWithAvailable.mock() }
        )
    }
}
