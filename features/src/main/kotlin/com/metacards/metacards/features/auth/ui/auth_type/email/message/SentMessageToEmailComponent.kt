package com.metacards.metacards.features.auth.ui.auth_type.email.message

import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.StateFlow

interface SentMessageToEmailComponent {
    val title: StateFlow<StringDesc>
    val text: StateFlow<StringDesc>
    val dismissDialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent>

    fun onCloseButtonClick()

    sealed interface Output {
        object MainScreenRequested : Output
        object AuthScreenRequested : Output
    }
}