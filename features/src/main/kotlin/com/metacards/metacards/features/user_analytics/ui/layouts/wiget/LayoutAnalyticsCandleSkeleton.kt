package com.metacards.metacards.features.user_analytics.ui.layouts.wiget

import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.widget.placeholder.PlaceholderHighlight
import com.metacards.metacards.core.widget.placeholder.placeholder
import com.metacards.metacards.core.widget.placeholder.shimmer
import com.metacards.metacards.features.user_analytics.ui.layouts.MAX_VISIBLE_CANDLES

@Composable
fun LayoutAnalyticsCandleSkeleton(modifier: Modifier = Modifier) {
    val boxHeight = (LocalConfiguration.current.screenHeightDp * SHARE_GRAPHICS).toInt()
    val boxWidth = ((LocalConfiguration.current.screenWidthDp) / MAX_VISIBLE_CANDLES).dp
    val candleHeight = (boxHeight * SHARE_HALF_CANDLE_HEIGHT_ON_GRAPHICS).toInt().dp

    Box(
        modifier = modifier
            .height(boxHeight.dp)
            .width(boxWidth)
    ) {
        Column(
            modifier = Modifier
                .padding(vertical = 12.dp)
                .align(Alignment.TopCenter)
        ) {
            Box(modifier = Modifier.padding(horizontal = boxWidth / SHARE_CANDLE_WIDTH_ON_GRAPHICS)) {
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .shimmer(
                            width = boxWidth / SHARE_CANDLE_WIDTH_ON_GRAPHICS,
                            height = candleHeight
                        )
                )
            }

            Divider(
                modifier = Modifier
                    .padding(vertical = 2.dp)
                    .fillMaxWidth(),
                color = CustomTheme.colors.background.primary,
                thickness = 2.dp
            )

            Box(modifier = Modifier.padding(horizontal = boxWidth / SHARE_CANDLE_WIDTH_ON_GRAPHICS)) {
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .shimmer(
                            width = boxWidth / SHARE_CANDLE_WIDTH_ON_GRAPHICS,
                            height = candleHeight
                        )
                )
            }
        }

        Box(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .shimmer(
                    width = boxWidth / SHARE_DATE_WIDTH_ON_GRAPHICS,
                    height = boxWidth / SHARE_DATE_WIDTH_ON_GRAPHICS
                )
        )
    }
}

@Composable
fun LayoutAnalyticsCardSkeleton(modifier: Modifier) {
    val layoutWidth = LocalConfiguration.current.screenWidthDp.dp

    Column(modifier = modifier) {
        Box(
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .shimmer(
                    width = layoutWidth / SHARE_CANDLE_WIDTH_ON_GRAPHICS,
                    height = 20.dp,
                    shape = 8.dp
                )
        )
        Spacer(modifier = Modifier.size(8.dp))
        Box(
            modifier = Modifier.shimmer(
                width = layoutWidth,
                height = 100.dp,
                shape = 16.dp
            )
        )
    }
}

private fun Modifier.shimmer(
    width: Dp,
    height: Dp,
    shape: Dp = 4.dp
): Modifier {
    return size(width = width, height = height)
        .placeholder(
            visible = true,
            color = CustomTheme.colors.background.placeholder,
            shape = RoundedCornerShape(shape),
            highlight = PlaceholderHighlight.shimmer(
                highlightColor = CustomTheme.colors.system.invert.copy(alpha = 0.6f)
            ),
            placeholderFadeTransitionSpec = { tween() },
            contentFadeTransitionSpec = { tween() }
        )
}
