package com.metacards.metacards.features.special_deck.ui.starry_sky.widgets

import androidx.compose.foundation.Canvas
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.drawscope.DrawScope
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCard

private const val STAR_SIZE = 24
private const val LINE_WIDTH = 2

@Composable
fun SpecialDeckStarrySkyConnectionLines(
    modifier: Modifier = Modifier,
    cards: List<SpecialDeckCard>,
    receivedStars: Set<CardId>,
    scaleCoefficient: Float,
) {
    Canvas(modifier = modifier) {
        receivedStars.forEach { id ->
            val currentStar = cards.find { it.id == id }
            if (currentStar == null) return@forEach
            currentStar.connections.forEach connection@{ connectionId ->
                if (!receivedStars.contains(connectionId)) return@connection
                val nextStar = cards.find { it.id == connectionId }
                if (nextStar == null) return@connection
                val start = calculateLinePosition(
                    currentStar.coordinates.x,
                    currentStar.coordinates.y,
                    scaleCoefficient
                )
                val end = calculateLinePosition(
                    nextStar.coordinates.x,
                    nextStar.coordinates.y,
                    scaleCoefficient
                )
                drawLine(
                    color = CustomTheme.colors.star.line,
                    start = start,
                    end = end,
                    strokeWidth = density * LINE_WIDTH
                )
            }
        }
    }
}

private fun DrawScope.calculateLinePosition(
    x: Int,
    y: Int,
    scaleCoefficient: Float
) = Offset(
    x.times(scaleCoefficient).plus(STAR_SIZE * density),
    y.times(scaleCoefficient).plus(STAR_SIZE * density),
)