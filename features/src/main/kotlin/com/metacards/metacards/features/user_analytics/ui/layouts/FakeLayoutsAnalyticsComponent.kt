package com.metacards.metacards.features.user_analytics.ui.layouts

import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.PagedData
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.record.ui.record_list.ScrollCommand
import com.metacards.metacards.features.user_analytics.domain.layout.LayoutRecord
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakeLayoutsAnalyticsComponent : LayoutsAnalyticsComponent {
    override val analyticsState = MutableStateFlow(
        LoadableState(loading = true, PagedData(emptyList<LayoutRecord>()))
    )
    override val selectedRecord: StateFlow<LayoutRecord?> = MutableStateFlow(null)
    override val scrollToRecordCommand = MutableSharedFlow<ScrollCommand?>()
    override val isPremiumUser = MutableStateFlow(false)

    override fun updateSelectedRecord(index: Int?) = Unit
    override fun onRecordClick(recordId: RecordId) = Unit
    override fun onLoadMore() = Unit
}
