package com.metacards.metacards.features.account.ui.profile.gender

import android.os.Parcelable
import com.metacards.metacards.core.user.data.UserDto
import kotlinx.coroutines.flow.StateFlow
import kotlinx.parcelize.Parcelize

interface ProfileGenderComponent {
    val selectedGenderFlow: StateFlow<UserDto.Gender?>

    fun onGenderClick(gender: UserDto.Gender)
    fun onSaveClick()
    fun onDismiss()

    @Parcelize
    object Config : Parcelable

    sealed interface Output {
        object DismissRequested : Output
    }
}