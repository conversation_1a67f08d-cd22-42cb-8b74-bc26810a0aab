package com.metacards.metacards.features.course.ui.details

import android.os.Parcelable
import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.features.course.domain.entity.CourseContentId
import com.metacards.metacards.features.course.domain.entity.CourseData
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CourseTestDocumentId
import com.metacards.metacards.features.course.domain.entity.CourseThemeId
import com.metacards.metacards.features.course.ui.lesson.CourseLessonComponent
import com.metacards.metacards.features.course.ui.page.CoursePageComponent
import com.metacards.metacards.features.course.ui.passed_test_result.CoursePassedTestResultComponent
import com.metacards.metacards.features.course.ui.test.CourseTestComponent
import com.metacards.metacards.features.course.ui.test_result.CourseTestResultComponent
import com.metacards.metacards.features.layout.domain.CardSource
import kotlinx.coroutines.flow.StateFlow
import kotlinx.parcelize.Parcelize

interface CourseDetailsComponent {
    val childStack: StateFlow<ChildStack<*, Child>>
    val courseData: StateFlow<CourseData?>
    val courseQuery: CourseData.Query

    sealed interface Child {
        data class CoursePage(val component: CoursePageComponent) : Child
        data class Lesson(val component: CourseLessonComponent) : Child
        data class Test(val component: CourseTestComponent) : Child
        data class TestResult(val component: CourseTestResultComponent) : Child
        data class PassedTestResult(val component: CoursePassedTestResultComponent) : Child
    }

    sealed interface Screen : Parcelable {

        @Parcelize
        data class Course(val courseQuery: CourseData.Query) : Screen

        @Parcelize
        data class CoursePassedTestResult(
            val courseQuery: CourseData.Query,
            val testId: CourseContentId,
            val testResultId: CourseTestDocumentId?
        ) : Screen
    }

    sealed interface Output {
        data object CloseRequested : Output
        data class SubscriptionBottomSheetRequested(val withAdv: Boolean) : Output
        data class LayoutDetailsRequested(
            val cardSource: CardSource,
            val courseId: CourseId?,
            val themeId: CourseThemeId?
        ) : Output
        data object AuthScreenRequested : Output
    }
}