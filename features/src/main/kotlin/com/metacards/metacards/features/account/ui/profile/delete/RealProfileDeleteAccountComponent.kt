package com.metacards.metacards.features.account.ui.profile.delete

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.createDefaultDialogComponent
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.model.DialogData
import com.metacards.metacards.core.dialog.dialogControl
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.user.domain.UserProvider
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.features.BuildConfig
import com.metacards.metacards.features.R
import com.metacards.metacards.features.account.domain.interactor.DeleteUserInteractor
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.auth.domain.GetAuthUserInteractor
import com.metacards.metacards.features.auth.domain.LoginType
import com.metacards.metacards.features.auth.domain.SSOType
import com.metacards.metacards.features.auth.domain.SetOnSSOSignInDismissInteractor
import com.metacards.metacards.features.auth.domain.sso.google.SignInWithGoogleInteractor
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.StateFlow

class RealProfileDeleteAccountComponent(
    componentContext: ComponentContext,
    componentFactory: ComponentFactory,
    private val errorHandler: ErrorHandler,
    userProvider: UserProvider,
    getAuthUserInteractor: GetAuthUserInteractor,
    private val deleteUserInteractor: DeleteUserInteractor,
    private val signInWithGoogleInteractor: SignInWithGoogleInteractor,
    private val setOnSSOSignInDismissInteractor: SetOnSSOSignInDismissInteractor,
    private val analyticsService: AnalyticsService,
    private val onOutput: (ProfileDeleteAccountComponent.Output) -> Unit
) : ComponentContext by componentContext, ProfileDeleteAccountComponent {

    private val debounce = Debounce()

    private val user = userProvider.getUser()

    private val authUserInfo = getAuthUserInteractor.execute()

    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        dialogControl(dialogComponentFactory = { config, componentContext, dialogControl ->
            componentFactory.createDefaultDialogComponent(
                componentContext,
                config.data,
                dialogControl::dismiss
            )
        })

    override val userLoginTypes: StateFlow<Set<LoginType>> = computed(authUserInfo) { userInfo ->
        userInfo?.authLoginTypes ?: emptySet()
    }

    private val deleteAccountDialogConfig by lazy {
        DefaultDialogComponent.Config(
            DialogData(
                title = StringDesc.Resource(R.string.account_profile_dialog_delete_account_title),
                message = StringDesc.Resource(R.string.account_profile_dialog_delete_account_text),
                buttons = listOf(
                    DialogData.Button(
                        title = StringDesc.Resource(R.string.account_profile_dialog_delete_account_button_dismiss),
                        action = {
                            dialogControl.dismiss()
                            onOutput(ProfileDeleteAccountComponent.Output.WebViewDismissRequested)
                        }
                    ),
                    DialogData.Button(
                        title = StringDesc.Resource(R.string.account_profile_dialog_delete_account_button_proceed),
                        action = {
                            user.value?.userId?.let {
                                DebounceClick(debounce, "onDeleteAccountClick") {
                                    deleteUserInteractor.execute()
                                    analyticsService.logEvent(AnalyticsEvent.ProfileDeleteEvent)
                                    onOutput(ProfileDeleteAccountComponent.Output.AuthScreenRequested)
                                }
                            }
                        }
                    )
                )
            )
        )
    }

    override fun onPasswordConfirmed() {
        DebounceClick(debounce, "onPasswordConfirmed", errorHandler) {
            if ((userLoginTypes.value.none { it is LoginType.Email })) {
                onOutput(ProfileDeleteAccountComponent.Output.WebViewDismissRequested)
            }
            dialogControl.show(deleteAccountDialogConfig)
        }
    }

    override fun startReauthentication(onLoaded: () -> Unit) {
        componentScope.safeLaunch(
            errorHandler = errorHandler,
            onErrorHandled = { onLoaded() }
        ) {
            userLoginTypes.collect { loginTypes ->
                if (loginTypes.none { it is LoginType.Email }) {
                    when ((loginTypes.first() as? LoginType.SSO)?.ssoType) {
                        is SSOType.Google -> {
                            signInWithGoogleInteractor.execute {
                                onPasswordConfirmed()
                            }
                        }

                        is SSOType.VK -> {
                            setOnSSOSignInDismissInteractor.execute { onPasswordConfirmed() }
                            onOutput(
                                ProfileDeleteAccountComponent.Output.SignInViaWebViewRequested(
                                    BuildConfig.VK_AUTH_URL,
                                    R.string.auth_user_type_sso_vk
                                )
                            )
                        }

                        is SSOType.OK -> {
                            setOnSSOSignInDismissInteractor.execute { onPasswordConfirmed() }
                            onOutput(
                                ProfileDeleteAccountComponent.Output.SignInViaWebViewRequested(
                                    BuildConfig.OK_AUTH_URL,
                                    R.string.auth_user_type_sso_ok
                                )
                            )
                        }

                        is SSOType.Yandex -> {
                            setOnSSOSignInDismissInteractor.execute { onPasswordConfirmed() }
                            onOutput(
                                ProfileDeleteAccountComponent.Output.SignInViaWebViewRequested(
                                    BuildConfig.YANDEX_AUTH_URL,
                                    R.string.auth_user_type_sso_yandex
                                )
                            )
                        }

                        else -> error("SSO provider not found")
                    }
                } else {
                    onOutput(
                        ProfileDeleteAccountComponent.Output.PasswordConfirmationRequested(
                            R.string.account_profile_delete_confirm_password_toolbar_text,
                            R.string.account_profile_delete_confirm_password_textfield_header
                        )
                    )
                }
                onLoaded()
            }
        }
    }
}