package com.metacards.metacards.features.tutorial.ui

import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.FakeDialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.FakeDefaultDialogComponent
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakeTutorialMessageComponent : TutorialMessageComponent {

    override val tutorialInProgress: StateFlow<Boolean> = MutableStateFlow(false)
    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent>
        get() = FakeDialogControl(FakeDefaultDialogComponent())

    override fun onTutorialCancel() = Unit
}