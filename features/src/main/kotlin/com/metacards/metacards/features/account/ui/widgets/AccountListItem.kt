package com.metacards.metacards.features.account.ui.widgets

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.clickable
import dev.icerock.moko.resources.desc.StringDesc

@Composable
fun AccountListItem(
    leadingText: StringDesc,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    contentPadding: PaddingValues = PaddingValues(horizontal = 16.dp, vertical = 8.dp),
    trailingContent: @Composable () -> Unit = { AccountListItemTrailing() },
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(contentPadding)
    ) {
        Text(
            text = leadingText.localizedByLocal(),
            color = CustomTheme.colors.text.primary,
            style = CustomTheme.typography.heading.small,
            modifier = Modifier.align(Alignment.CenterStart)
        )

        Box(modifier = Modifier.align(Alignment.CenterEnd)) {
            trailingContent()
        }
    }
}

@Preview
@Composable
private fun AccountListItemPreview() {
    AppTheme {
        AccountListItem(
            leadingText = StringDesc.Resource(com.metacards.metacards.features.R.string.about_the_app_terms_of_use),
            onClick = {}
        )
    }
}