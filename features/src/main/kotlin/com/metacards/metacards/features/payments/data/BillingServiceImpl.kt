package com.metacards.metacards.features.payments.data

import co.touchlab.kermit.Logger
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingClient.ProductType
import com.android.billingclient.api.BillingClientStateListener
import com.android.billingclient.api.BillingFlowParams
import com.android.billingclient.api.BillingResult
import com.android.billingclient.api.ProductDetails
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.PurchasesUpdatedListener
import com.android.billingclient.api.QueryProductDetailsParams
import com.android.billingclient.api.queryProductDetails
import com.metacards.metacards.core.activity.ActivityProvider
import com.metacards.metacards.core.functions.CloudFunctionsService
import com.metacards.metacards.core.price.Currency
import com.metacards.metacards.core.price.Price
import com.metacards.metacards.features.payments.domain.BillingService
import com.metacards.metacards.features.payments.domain.DeckInApp
import com.metacards.metacards.features.payments.domain.Error
import com.metacards.metacards.features.payments.domain.PurchaseState
import com.metacards.metacards.features.payments.domain.PurchaseType
import com.metacards.metacards.features.payments.domain.SubscriptionPlan
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class BillingServiceImpl(
    private val activityProvider: ActivityProvider,
    private val coroutineScope: CoroutineScope,
    private val cloudFunctionsService: CloudFunctionsService
) : BillingService {
    private val logger = Logger.withTag("BillingServiceImpl")
    private val currentBillingState: MutableStateFlow<BillingState> =
        MutableStateFlow(BillingState.NotInitialized)
    override val currentPurchaseState: MutableStateFlow<PurchaseState> =
        MutableStateFlow(PurchaseState.NotStarted)

    private val purchasesUpdatedListener =
        PurchasesUpdatedListener { billingResult, purchases ->
            handleResult(billingResult) {
                if (purchases != null) {
                    coroutineScope.launch {
                        for (purchase in purchases) {
                            handlePurchase(purchase)
                        }
                    }
                } else {
                    val message =
                        "No purchases with ${billingResult.responseCode} : ${billingResult.debugMessage}"
                    logger.e(message)
                    currentPurchaseState.value =
                        PurchaseState.Error(Error.PurchaseFlowError(message))
                }
            }
        }

    private val connectionListener = BillingClientStateListenerImpl(
        coroutineScope,
        currentBillingState,
        ::init
    )

    private val billingClient =
        BillingClient.newBuilder(activityProvider.activity!!.applicationContext)
            .setListener(purchasesUpdatedListener)
            .enablePendingPurchases()
            .build()

    override fun init() {
        if (currentBillingState.value == BillingState.NotInitialized) {
            currentBillingState.value = BillingState.Initializing
            logger.d("Billing Start connection")
            billingClient.startConnection(connectionListener)
        } else {
            logger.d("Billing client is already initialized")
        }
    }

    override suspend fun processPurchases(purchaseType: PurchaseType) {
        try {
            if (currentPurchaseState.value.isInProgress) {
                currentPurchaseState.value =
                    PurchaseState.Error(Error.BillingError("processPurchases is already in progress"))
                return
            }

            if (currentBillingState.value != BillingState.Initialized) {
                currentPurchaseState.value =
                    PurchaseState.Error(Error.BillingError("billing is not initialized"))
                return
            }

            currentPurchaseState.value = PurchaseState.Started(purchaseType)

            val result = billingClient.isFeatureSupported(BillingClient.FeatureType.PRODUCT_DETAILS)
            if (result.responseCode == BillingClient.BillingResponseCode.OK) {
                processPurchasesWithNewApi(purchaseType)
            } else {
                currentPurchaseState.value =
                    PurchaseState.Error(Error.BillingError(result.debugMessage))
            }
        } catch (e: Exception) {
            currentPurchaseState.value = PurchaseState.Error(Error.BillingError(e.message ?: ""))
            throw e
        }
    }

    override suspend fun fetchSubscriptions(
        storeId: String,
        promotionalOfferId: String?
    ): List<SubscriptionPlan> {
        val productDetails = fetchProduct(ProductType.SUBS, SUBSCRIPTION_ID)
        val offers = productDetails?.subscriptionOfferDetails ?: emptyList()

        val offersForPlan = offers.filter { it.basePlanId == storeId }

        /**
         * в firebase в коллекции subscriptions поле promotionalOfferId отвечает за offerId скидочного
         * предложения, в случа если скидка не нужна поле делают пустым, при маппинге оно становится
         * null, в итоге мы получаем базовые цены
         */
        val baseOffers = offersForPlan.filter { it.offerId == promotionalOfferId }

        /**
         * В рамках задачи META-1325 убираем упоминания о триале
         *  val hasTrial = if (promotionalOfferId != null) false else offersForPlan.any { it.offerId == TRIAL_ID }
         */

        val hasTrial = false

        return baseOffers.map { offer ->
            val pricingPhaseList = offer.pricingPhases.pricingPhaseList

            val pricePhase = pricingPhaseList.lastOrNull()
            val price = (pricePhase?.priceAmountMicros?.toDouble() ?: PRICE_FORMAT_NUMBER) / PRICE_FORMAT_NUMBER

            val priceWidthDiscountPhase = offer.pricingPhases.pricingPhaseList
                .getOrNull(pricingPhaseList.size - 2)
            val priceWithDiscount = (priceWidthDiscountPhase?.priceAmountMicros)
                ?.div(PRICE_FORMAT_NUMBER)

            val currency = Currency.Other(pricePhase?.priceCurrencyCode ?: "")
            SubscriptionPlan(
                id = offer.basePlanId,
                promotionalOfferId = promotionalOfferId,
                hasTrial = hasTrial,
                price = Price(
                    value = price,
                    currency = currency
                ),
                priceWithDiscount = Price(
                    value = priceWithDiscount ?: price,
                    currency = currency
                ),
                discountsAvailable = promotionalOfferId != null
            )
        }
    }

    override suspend fun fetchDeck(storeId: String): DeckInApp {
        val productDetails = fetchProduct(ProductType.INAPP, storeId)
        val offer = productDetails?.oneTimePurchaseOfferDetails
        val price = (offer?.priceAmountMicros ?: 1_000_000) / 1_000_000.0

        return DeckInApp(
            id = storeId,
            price = Price(
                value = price,
                currency = Currency.Other(offer?.priceCurrencyCode ?: "")
            )
        )
    }

    private suspend fun processPurchasesWithNewApi(purchaseType: PurchaseType) {
        when (purchaseType) {
            is PurchaseType.Subscription.New -> processNewSub(purchaseType)
            is PurchaseType.Subscription.Upgrade -> processUpgrade(purchaseType)
            is PurchaseType.Deck -> processDeck(purchaseType)
        }
    }

    private suspend fun processUpgrade(purchaseType: PurchaseType.Subscription.Upgrade) {
        val productDetails = fetchProduct(ProductType.SUBS, purchaseType.storeId)

        if (productDetails == null) {
            val message = "ProductDetails is empty"
            logger.e(message)
            currentPurchaseState.value = PurchaseState.Error(Error.BillingError(message))
        } else {
            currentPurchaseState.value = PurchaseState.InfoReceived(purchaseType)
            val billingFlowParams =
                getBillingFlowParamsForUpdateSubscription(
                    productDetails = productDetails,
                    oldBasePlanId = purchaseType.oldBasePlanId,
                    newBasePlanId = purchaseType.newBasePlanId,
                    discountPlanId = purchaseType.discountPlanId
                )
            startPurchaseFlow(billingFlowParams)
        }
    }

    private suspend fun processNewSub(purchaseType: PurchaseType.Subscription.New) {
        val productDetails = fetchProduct(ProductType.SUBS, purchaseType.storeId)

        if (productDetails == null) {
            val message = "oldProductDetails is empty"
            logger.e(message)
            currentPurchaseState.value = PurchaseState.Error(Error.BillingError(message))
        } else {
            currentPurchaseState.value = PurchaseState.InfoReceived(purchaseType)
            val billingFlowParams =
                getBillingFlowParamsForSubscription(
                    productDetails = productDetails,
                    basePlanId = purchaseType.basePlanId,
                    discountPlanId = purchaseType.discountPlanId
                )
            startPurchaseFlow(billingFlowParams)
        }
    }

    private suspend fun processDeck(purchaseType: PurchaseType.Deck) {
        val productDetails = fetchProduct(ProductType.INAPP, purchaseType.storeId)

        if (productDetails == null) {
            val message = "ProductDetails is empty"
            logger.e(message)
            currentPurchaseState.value = PurchaseState.Error(Error.BillingError(message))
        } else {
            currentPurchaseState.value = PurchaseState.InfoReceived(purchaseType)
            val billingFlowParams = getBillingFlowParams(productDetails)
            startPurchaseFlow(billingFlowParams)
        }
    }

    private suspend fun fetchProduct(
        @ProductType type: String,
        productId: String
    ): ProductDetails? {
        val productList = listOf(
            QueryProductDetailsParams.Product.newBuilder()
                .setProductId(productId)
                .setProductType(type)
                .build()
        )

        val params = QueryProductDetailsParams.newBuilder()
            .setProductList(productList)
            .build()

        val productDetailsResult = withContext(Dispatchers.IO) {
            return@withContext billingClient.queryProductDetails(params)
        }

        var productDetails: ProductDetails? = null
        handleResult(productDetailsResult.billingResult) {
            productDetails = productDetailsResult.productDetailsList?.firstOrNull()
        }

        return productDetails
    }

    private suspend fun startPurchaseFlow(billingFlowParams: BillingFlowParams) {
        val billingResult = billingClient.launchBillingFlow(
            activityProvider.awaitActivity(),
            billingFlowParams
        )

        handleResult(billingResult) {
            val purchaseType = currentPurchaseState.value.tryGetPurchaseType() ?: run {
                val message = "can't get purchaseType from state: ${currentPurchaseState.value}"
                logger.e(message)
                currentPurchaseState.value = PurchaseState.Error(Error.PurchaseFlowError(message))
                return@handleResult
            }

            currentPurchaseState.value = PurchaseState.PurchaseFlowStarted(purchaseType)
        }
    }

    private fun getBillingFlowParamsForSubscription(
        productDetails: ProductDetails,
        basePlanId: String,
        discountPlanId: String?
    ): BillingFlowParams {
        val offerToken =
            productDetails.subscriptionOfferDetails?.firstOrNull {
                if (discountPlanId != null) {
                    it.offerId == discountPlanId && it.basePlanId == basePlanId
                } else {
                    it.basePlanId == basePlanId
                }
            }?.offerToken
        require(offerToken != null) { "OfferToken is null" }
        val productDetailsParamsList = listOf(
            BillingFlowParams.ProductDetailsParams.newBuilder()
                .setProductDetails(productDetails)
                .setOfferToken(offerToken)
                .build()
        )

        val billingFlowParamsBuilder = BillingFlowParams.newBuilder()
            .setProductDetailsParamsList(productDetailsParamsList)

        return billingFlowParamsBuilder.build()
    }

    private fun getBillingFlowParams(productDetails: ProductDetails): BillingFlowParams {
        val productDetailsParamsList = listOf(
            BillingFlowParams.ProductDetailsParams.newBuilder()
                .setProductDetails(productDetails)
                .build()
        )

        val billingFlowParamsBuilder = BillingFlowParams.newBuilder()
            .setProductDetailsParamsList(productDetailsParamsList)

        return billingFlowParamsBuilder.build()
    }

    private fun getBillingFlowParamsForUpdateSubscription(
        productDetails: ProductDetails,
        oldBasePlanId: String,
        newBasePlanId: String,
        discountPlanId: String?
    ): BillingFlowParams {
        val oldOfferToken =
            productDetails.subscriptionOfferDetails?.firstOrNull { it.basePlanId == oldBasePlanId }?.offerToken

        val newOfferToken =
            productDetails.subscriptionOfferDetails?.firstOrNull {
                if (discountPlanId != null) {
                    it.offerId == discountPlanId && it.basePlanId == newBasePlanId
                } else {
                    it.basePlanId == newBasePlanId
                }
            }?.offerToken

        require(oldOfferToken != null) { "oldOfferToken is null" }
        require(newOfferToken != null) { "newOfferToken is null" }

        val updateParams: BillingFlowParams.SubscriptionUpdateParams =
            BillingFlowParams.SubscriptionUpdateParams.newBuilder()
                .setOldPurchaseToken(oldOfferToken)
                .setSubscriptionReplacementMode(
                    BillingFlowParams.SubscriptionUpdateParams.ReplacementMode.CHARGE_FULL_PRICE
                )
                .build()

        val productDetailsParamsList = listOf(
            BillingFlowParams.ProductDetailsParams.newBuilder()
                .setProductDetails(productDetails)
                .setOfferToken(newOfferToken)
                .build()
        )

        val billingFlowParamsBuilder = BillingFlowParams.newBuilder()
            .setProductDetailsParamsList(productDetailsParamsList)
            .setSubscriptionUpdateParams(updateParams)

        return billingFlowParamsBuilder.build()
    }

    private fun handleResult(result: BillingResult, onSuccess: (BillingResult) -> Unit) {
        when (result.responseCode) {
            BillingClient.BillingResponseCode.OK -> {
                onSuccess(result)
            }

            BillingClient.BillingResponseCode.USER_CANCELED -> {
                val message = "User canceled the purchase"
                logger.d(message)
                currentPurchaseState.value = PurchaseState.Error(Error.CanceledError(message))
            }

            else -> {
                val message =
                    "BillingFlow failed with ${result.responseCode} : ${result.debugMessage}"
                logger.e(message)
                currentPurchaseState.value = PurchaseState.Error(Error.PurchaseFlowError(message))
            }
        }
    }

    private suspend fun handlePurchase(purchase: Purchase) {
        logger.d(
            "Purchase: ${purchase.products.joinToString()}\n" +
                "with state: ${purchase.purchaseState}"
        )

        val data = mutableMapOf(
            "receipt" to purchase.purchaseToken,
            "platform" to "android"
        )

        val purchaseType = currentPurchaseState.value.tryGetPurchaseType()

        when (purchaseType) {
            is PurchaseType.Subscription -> {
                data += mapOf("subscriptionId" to purchaseType.firebaseId)
            }

            is PurchaseType.Deck -> {
                data += mapOf("deckId" to purchaseType.firebaseId)
            }

            null -> {
                currentPurchaseState.value = PurchaseState.Error(
                    Error.PurchaseAcceptingError("can't get purchaseType from state: ${currentPurchaseState.value}")
                )
                return
            }
        }

        logger.d("function request with data: $data")
        val result = cloudFunctionsService.call<Any>("verifyInappPurchase", data)
        logger.d("result: $result")

        currentPurchaseState.value = PurchaseState.Purchased(purchaseType)
        currentPurchaseState.value = PurchaseState.NotStarted
    }

    companion object {
        const val SUBSCRIPTION_ID = "base_premium"
        private const val TRIAL_ID = "base-trial"
        private const val PRICE_FORMAT_NUMBER = 1_000_000.0
    }
}

private sealed interface BillingState {
    data object NotInitialized : BillingState
    data object Initializing : BillingState
    data object Initialized : BillingState
}

private class BillingClientStateListenerImpl(
    private val coroutineScope: CoroutineScope,
    private val currentBillingState: MutableStateFlow<BillingState>,
    private val onRetryConnection: () -> Unit
) : BillingClientStateListener {

    private val logger = Logger.withTag("BillingServiceImpl.StateListener")
    private var reconnectMilliseconds: Long = 1000
    private var connectionJob: Job? = null
    override fun onBillingSetupFinished(billingResult: BillingResult) {
        reconnectMilliseconds = 1000

        logger.d("Billing client finished")
        if (billingResult.responseCode == BillingClient.BillingResponseCode.OK) {
            logger.d("Billing client connected")
            currentBillingState.value = BillingState.Initialized
        } else {
            logger.e("Billing client failed with ${billingResult.responseCode} : ${billingResult.debugMessage}")
        }
    }

    override fun onBillingServiceDisconnected() {
        logger.d("Billing client disconnected")

        if (currentBillingState.value == BillingState.Initializing && connectionJob == null) {
            connectionJob = coroutineScope.launch {
                currentBillingState.value = BillingState.NotInitialized
                delay(reconnectMilliseconds)
                reconnectMilliseconds *= 2
                logger.d("Billing client reinit")
                connectionJob = null
                onRetryConnection()
            }
        }
    }
}