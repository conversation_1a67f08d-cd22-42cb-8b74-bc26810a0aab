package com.metacards.metacards.features.layout.ui.card_list

import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardHint
import com.metacards.metacards.features.deck.domain.entity.CardHintWithExpanded
import com.metacards.metacards.features.deck.domain.entity.CardWithFavoriteAndComment
import com.metacards.metacards.features.deck.domain.entity.withExpanded
import com.metacards.metacards.features.layout.domain.CardListViewData
import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakeCardListComponent : CardListComponent {
    override val canSelectMoreCard = MutableStateFlow(CardListComponent.CanSelectCard.Can)
    override val selectedCards: StateFlow<List<CardWithFavoriteAndComment>> =
        MutableStateFlow(CardWithFavoriteAndComment.LIST_MOCK)
    override val cardHints: StateFlow<List<CardHintWithExpanded>?> =
        MutableStateFlow(listOf(CardHint.MOCK.withExpanded()))
    override val pageCount = MutableStateFlow(Card.getMockList().size)
    override val cardListViewData = CardListViewData.MOCK
    override val isShareLoading = MutableStateFlow(false)
    override val tutorialMessage: StateFlow<TutorialMessage?> = MutableStateFlow(null)
    override fun onToggleFavorite(cardIndex: Int, isOn: Boolean) = Unit
    override fun onToggleShare(cardUrl: String, cardIndex: Int) = Unit
    override fun onMoreCardClick() = Unit
    override fun onHintExpand(page: Int, isExpanded: Boolean) = Unit
    override fun onFinishClick() = Unit
    override fun onAddEntryClick() = Unit
    override fun onCardClick(card: Card) = Unit
    override fun onCardCommentValueChanged(cardIndex: Int, value: String) = Unit
    override fun onCloseClick() = Unit
}