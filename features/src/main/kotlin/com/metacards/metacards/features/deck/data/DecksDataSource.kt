package com.metacards.metacards.features.deck.data

import com.google.firebase.firestore.toObject
import com.metacards.metacards.core.device.DeviceInfoService
import com.metacards.metacards.core.functions.CloudFunctionsService
import com.metacards.metacards.core.network.firestore.FirestoreService
import com.metacards.metacards.core.network.firestore.getFlow
import com.metacards.metacards.core.network.firestore.getFlowWithLoadable
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.mapLoadable
import com.metacards.metacards.features.deck.data.dto.CardDto
import com.metacards.metacards.features.deck.data.dto.CardHintDto
import com.metacards.metacards.features.deck.data.dto.DailyCardWithHintDto
import com.metacards.metacards.features.deck.data.dto.DeckDto
import com.metacards.metacards.features.deck.domain.entity.DeckId
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.tasks.await
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.encodeToJsonElement
import kotlinx.serialization.json.put

class DecksDataSource(
    private val firestoreService: FirestoreService,
    private val cloudFunctionsService: CloudFunctionsService,
    private val deviceInfoService: DeviceInfoService
) {

    suspend fun getDecks(): List<DeckDto> {
        return firestoreService.db.collection(DECKS_COLLECTION_PATH).get()
            .await().documents.mapNotNull { it.toObject<DeckDto>()?.copy(id = it.id) }
    }

    fun getDecksFlow(): Flow<List<DeckDto>> {
        val flow = firestoreService.db.collection(DECKS_COLLECTION_PATH)
            .getFlow(firestoreService)
        return flow.map { querySnapshot ->
            querySnapshot.documents.mapNotNull { it.toObject<DeckDto>()?.copy(id = it.id) }
        }
    }

    fun getCardsByDeckFlow(deckId: DeckId): Flow<LoadableState<List<CardDto>>> {
        val deckDocument =
            firestoreService.db.collection(DECKS_COLLECTION_PATH).document(deckId.value)
        val cardCollection =
            deckDocument.collection(DECK_CARDS_COLLECTION_PATH)
                .getFlowWithLoadable(firestoreService)
        return cardCollection.mapLoadable { snapshot ->
            snapshot?.documents?.mapNotNull { it.toObject<CardDto>()?.copy(cardId = it.id) }
        }
    }

    suspend fun getCardsByDeck(deckId: DeckId): List<CardDto> {
        val deckDocument =
            firestoreService.db.collection(DECKS_COLLECTION_PATH).document(deckId.value)
        val cardCollection =
            deckDocument.collection(DECK_CARDS_COLLECTION_PATH).get().await()

        return cardCollection.documents.mapNotNull { it.toObject<CardDto?>()?.copy(cardId = it.id) }
    }

    fun getDeckByIdFlow(deckId: DeckId): Flow<LoadableState<DeckDto>> {
        val flow = firestoreService.db.collection(DECKS_COLLECTION_PATH).document(deckId.value)
            .getFlowWithLoadable(firestoreService)

        return flow.mapLoadable {
            it?.toObject<DeckDto>()?.copy(id = it.id)
        }
    }

    suspend fun getDeckById(deckId: DeckId): DeckDto? {
        val document = firestoreService.db.collection(DECKS_COLLECTION_PATH).document(deckId.value)
        return document.get().await().toObject<DeckDto>()?.copy(id = document.id)
    }

    suspend fun getCardHints(): List<CardHintDto> {
        return firestoreService.db.collection(HINTS_COLLECTION_PATH)
            .get().await().documents.mapNotNull { document ->
                document.toObject<CardHintDto?>()
            }
    }

    @Suppress("UNCHECKED_CAST")
    suspend fun getDailyCard(): DailyCardWithHintDto {
        val json = Json { ignoreUnknownKeys = true }
        val deviceId = deviceInfoService.getDeviceId()
        val functionResult = cloudFunctionsService.call<HashMap<String, Any?>>(
            functionName = "getDailyCard",
            data = mapOf(
                "deviceId" to deviceId.value
            )
        )

        val cardData = functionResult.data?.get("dailyCard") as HashMap<String, Any?>
        val cardImageContainer = cardData["card"] as HashMap<String, Any?>
        val hintData = functionResult.data?.get("hint") as HashMap<String, Any?>
        val hintTextMap = hintData["nameLocalized"] as HashMap<String, Any?>

        val hintJsonObject = buildJsonObject {
            hintTextMap.forEach { (key, value) ->
                put(key, json.encodeToJsonElement(value.toString()))
            }
        }
        val jsonElement = buildJsonObject {
            put("cardId", cardData["cardId"] as String)
            put("imageUrl", cardImageContainer["image"] as String)
            put("hint", hintJsonObject)
            put("gifUrl", cardImageContainer["animation"] as String?)
        }
        return json.decodeFromJsonElement<DailyCardWithHintDto>(jsonElement)
    }

    companion object {
        private const val DECKS_COLLECTION_PATH = "decks"
        private const val DECK_CARDS_COLLECTION_PATH = "cards"
        private const val HINTS_COLLECTION_PATH = "luhelp"
    }
}