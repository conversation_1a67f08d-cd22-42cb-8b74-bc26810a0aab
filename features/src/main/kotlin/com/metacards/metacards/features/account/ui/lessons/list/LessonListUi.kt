package com.metacards.metacards.features.account.ui.lessons.list

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.navigationBarsPaddingDp
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.LceWidget
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.ui.deck_info.widgets.MetaVideoPreviewCard
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun LessonListUi(
    component: LessonListComponent,
    modifier: Modifier = Modifier,
) {
    val lessonsLoadableState by component.lessonsState.collectAsState()
    val categoryLoadableState by component.lessonCategoryState.collectAsState()

    BoxWithFade(modifier, listOfColors = CustomTheme.colors.gradient.backgroundList) {
        Column(modifier = Modifier.matchParentSize()) {
            TopNavigationBar(
                title = categoryLoadableState.data?.name
                    ?: R.string.account_lessons_title.strResDesc(),
                leadingIcon = { BackNavigationItem() },
            )
            LceWidget(state = lessonsLoadableState, onRetryClick = { /*TODO*/ }) { lessons, _ ->
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    verticalArrangement = Arrangement.spacedBy(8.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    contentPadding = PaddingValues(
                        start = 16.dp,
                        end = 16.dp,
                        top = 8.dp,
                        bottom = 8.dp + navigationBarsPaddingDp
                    )
                ) {
                    items(
                        items = lessons,
                        key = { it.order }
                    ) { lesson ->
                        MetaVideoPreviewCard(
                            videoLesson = lesson,
                            onDetailsClick = component::onLessonDetailsClick,
                            onVideoPlay = { component.onVideoPlay(lesson) },
                        )
                    }
                }
            }
        }
    }
}

@Preview
@Composable
fun LessonsUiPreview() {
    AppTheme {
        LessonListUi(component = FakeLessonListComponent())
    }
}