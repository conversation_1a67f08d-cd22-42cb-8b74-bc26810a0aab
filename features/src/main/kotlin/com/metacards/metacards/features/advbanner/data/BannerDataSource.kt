package com.metacards.metacards.features.advbanner.data

import android.content.SharedPreferences
import com.google.firebase.firestore.QuerySnapshot
import com.metacards.metacards.core.network.firestore.FirestoreService
import com.metacards.metacards.core.network.firestore.getFlow
import com.metacards.metacards.core.settings.SettingsFactory
import com.metacards.metacards.features.advbanner.data.dto.AccountBannerDto
import com.metacards.metacards.features.advbanner.data.dto.HomeBannerDto
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

class BannerDataSource(
    private val firestoreService: FirestoreService,
    settingsFactory: SettingsFactory
) {

    private val settings = settingsFactory.createSettings(BANNER_SETTINGS_NAME)

    fun getHomeBanners(): Flow<List<HomeBannerDto>> {
        val flow = firestoreService.db.collection(HOME_BANNER_COLLECTION_PATH).getFlow(firestoreService)
        return flow.map { value: QuerySnapshot ->
            value.documents.mapNotNull { it.toObject(HomeBannerDto::class.java)?.copy(id = it.id) }
        }
    }

    fun getAccountBanners(): Flow<List<AccountBannerDto>> {
        val flow = firestoreService.db.collection(ACCOUNT_BANNER_COLLECTION_PATH).getFlow(firestoreService)
        return flow.map { value: QuerySnapshot ->
            value.documents.mapNotNull { it.toObject(AccountBannerDto::class.java) }
        }
    }

    suspend fun hideBanner(bannerId: String) = withContext(Dispatchers.IO) {
        val map = getHiddenBannersMap()
        map[bannerId] = System.currentTimeMillis()
        settings.putString(HIDDEN_BANNER_MAP_KEY, Json.encodeToString(map))
    }

    fun getHiddenBannerIdsFlow(): Flow<Set<String>> = callbackFlow {
        val listener = SharedPreferences.OnSharedPreferenceChangeListener { sharedPreferences, key ->
            if (key == HIDDEN_BANNER_MAP_KEY) {
                sharedPreferences.getString(key, null)?.let { stringMap ->
                    val map = Json.decodeFromString<Map<String, Long>>(stringMap)
                    trySend(map.keys)
                }
            }
        }
        trySend(getHiddenBannersMap().keys)
        settings.registerListener(listener)
        awaitClose {
            settings.unregisterListener(listener)
        }
    }

    suspend fun cleanHiddenBannerStorage() = withContext(Dispatchers.IO) {
        val hiddenBannersMap = getHiddenBannersMap()
        val now = System.currentTimeMillis()
        hiddenBannersMap.entries.removeIf { (_, v) -> now > v + WEEK_IN_MILLIS }
        settings.putString(HIDDEN_BANNER_MAP_KEY, Json.encodeToString(hiddenBannersMap))
    }

    private suspend fun getHiddenBannersMap(): MutableMap<String, Long> = settings
        .getString(HIDDEN_BANNER_MAP_KEY)?.let { stringMap ->
            Json.decodeFromString(stringMap)
        } ?: mutableMapOf()

    companion object {
        private const val HOME_BANNER_COLLECTION_PATH = "banners"
        private const val ACCOUNT_BANNER_COLLECTION_PATH = "accountBanners"
        private const val HIDDEN_BANNER_MAP_KEY = "hiddenBannerMapKey"
        private const val WEEK_IN_MILLIS = 60 * 60 * 24 * 7 * 1000
        private const val BANNER_SETTINGS_NAME = "banner_settings_name"
        const val HIGHEST_PRIORITY = 0
    }
}