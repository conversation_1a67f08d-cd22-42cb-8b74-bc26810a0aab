package com.metacards.metacards.features.layout.ui.select_card.ar.widget

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import co.touchlab.kermit.Logger
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.layout.domain.ar.GridSettings
import com.metacards.metacards.features.layout.domain.ar.Position2D
import com.metacards.metacards.features.layout.domain.ar.StashSettings
import com.metacards.metacards.features.layout.ui.select_card.CardsWithQuestionId
import com.metacards.metacards.features.layout.ui.select_card.ar.StashAction
import com.metacards.metacards.features.layout.ui.select_card.ar.sceneview.ArCardActor
import com.metacards.metacards.features.layout.ui.select_card.ar.sceneview.ArCardNode
import com.metacards.metacards.features.layout.ui.select_card.ar.sceneview.ArDeckNode
import com.metacards.metacards.features.layout.ui.select_card.ar.sceneview.ArPlaneNode
import com.metacards.metacards.features.layout.ui.select_card.ar.sceneview.ArResourceLoader
import com.metacards.metacards.features.layout.ui.select_card.ar.sceneview.ArSelectedCardBackgroundNode
import com.metacards.metacards.features.layout.ui.select_card.ar.sceneview.CardGrid
import io.github.sceneview.Filament
import io.github.sceneview.ar.node.ArCameraNode
import io.github.sceneview.ar.node.ArNode
import io.github.sceneview.math.Transform
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

data class ARCardLayoutState(
    private val initialCards: List<Card>,
    private val coroutineScope: CoroutineScope,
    private val resourceLoader: ArResourceLoader,
) {
    private val logger = Logger.withTag("ARCardLayoutState")

    private var actor: ArCardActor? = null
    private var planeNode: ArPlaneNode? = null
    private var cardGrid: CardGrid? = null
    private var deckNode: ArDeckNode? = null
    private var selectedCardBackgroundNode: ArSelectedCardBackgroundNode? = null
    private val cardNodePool: MutableList<ArCardNode> = mutableListOf()

    private val _nodes = MutableStateFlow<List<ArNode>>(emptyList())
    val nodes: StateFlow<List<ArNode>>
        get() = _nodes

    val currentState: MutableStateFlow<State> = MutableStateFlow(State.NotLoaded)

    var cards: List<Card> = initialCards
        private set

    suspend fun init(
        context: Context,
        cameraNode: ArCameraNode,
        gridSettings: GridSettings,
        stashSettings: StashSettings
    ) {
        actor = ArCardActor(cameraNode)
        load(context, gridSettings.sizeX * gridSettings.sizeZ + stashSettings.sizeZ)
        createGrid(gridSettings, stashSettings)
        createPlaneNode(cameraNode)
        createSelectedCardBackgroundNode(cameraNode)
    }

    fun stashCard(stashAction: StashAction) {
        val cardState = currentState.value
        val cardGrid = cardGrid ?: throw IllegalStateException("cardGrid is null")
        val actor = actor ?: throw IllegalStateException("actor is null")
        val deckNode = deckNode ?: throw IllegalStateException("deckNode is null")
        val cardBackgroundNode = selectedCardBackgroundNode ?: throw IllegalStateException("selectedCardBackgroundNode is null")

        if (cardState is State.Previewed) {
            actor.stashCard(cardGrid, cardState.node, cardBackgroundNode)
            currentState.value = State.Layout
            if (deckNode.hasCard() && stashAction is StashAction.WithRefilling) {
                actor.refillLayout(deckNode, cardGrid)
            }
        }
    }

    fun layoutCards() {
        if (currentState.value !is State.Loaded) return
        val planeNode = planeNode ?: throw IllegalStateException("planeNode is null")
        val actor = actor ?: throw IllegalStateException("actor is null")

        planeNode.tryStartLayout { transform ->
            coroutineScope.launch {
                setupBeforeLayout(transform)
                val deckNode = deckNode ?: throw IllegalStateException("deckNode is null")
                actor.placeDeck(deckNode, planeNode)
                layoutCards(1000)
            }
        }
    }

    fun destroy() {
        _nodes.value.forEach { it.destroy() }
        cardGrid?.destroy()
        planeNode?.destroy()
        selectedCardBackgroundNode?.destroy()
        cardGrid = null
    }

    private suspend fun load(context: Context, count: Int) {
        val texturesUri = cards.subList(0, count).map { it.textureUri }
        resourceLoader.loadAllResources(context, texturesUri)
        currentState.value = State.Loaded
    }

    private fun createGrid(gridSettings: GridSettings, stashSettings: StashSettings) {
        cardGrid = CardGrid(gridSettings, stashSettings)
    }

    private fun createDeckNode() {
        deckNode = ArDeckNode(Filament.engine, cardNodePool).apply {
            addMaterial(resourceLoader.cardMaterial, resourceLoader.cardsTexture?.firstOrNull())
            _nodes.update { it + listOf(this) }
        }
    }

    private fun setupCardNodePool() {
        val cardGrid = cardGrid ?: throw IllegalStateException("cardGrid is null")
        cardNodePool.clear()
        for (i in 0..<cardGrid.gridSettings.size + cardGrid.stashSettings.sizeZ) {
            val card = initialCards[i]
            val node = ArCardNode(Filament.engine, card)
            node.addMaterial(resourceLoader.cardMaterial, resourceLoader.cardsTexture?.get(i))
            node.onTap = { _, _ ->
                selectedCardBackgroundNode?.let {
                    pickCard(node, it)
                }
            }
            cardNodePool.add(node)
        }
    }

    private fun createPlaneNode(cameraNode: ArCameraNode) {
        val planeNode = ArPlaneNode(Filament.engine, cameraNode)
        this.planeNode = planeNode
        _nodes.update { it + listOf(planeNode) }
        planeNode.addMaterial(
            resourceLoader.planeMaterial,
            resourceLoader.planeNotFoundTexture,
            resourceLoader.planeFoundTexture
        )
    }

    private fun createSelectedCardBackgroundNode(cameraNode: ArCameraNode) {
        val selectedCardBackgroundNode = ArSelectedCardBackgroundNode(Filament.engine, cameraNode)
        this.selectedCardBackgroundNode = selectedCardBackgroundNode
        _nodes.update { it + listOf(selectedCardBackgroundNode) }
        selectedCardBackgroundNode.addMaterial(
            resourceLoader.planeMaterial,
            resourceLoader.selectedCardBackgroundTexture
        )
    }

    private fun setupGrid(transform: Transform) {
        val cardGrid = cardGrid ?: throw IllegalStateException("cardGrid is null")

        val nodes = cardGrid.setupGrid(transform)
        _nodes.update { it + nodes }
    }

    private fun setupBeforeLayout(transform: Transform) {
        setupCardNodePool()
        setupGrid(transform)
        createDeckNode()
    }

    private fun pickCard(cardNode: ArCardNode, cardBackgroundNode: ArSelectedCardBackgroundNode) {
        if (currentState.value != State.Layout) return
        val actor = actor ?: throw IllegalStateException("actor is null")
        val cardGrid = cardGrid ?: throw IllegalStateException("cardGrid is null")

        val isTakingSuccessful = actor.takeCard(cardGrid, cardNode, cardBackgroundNode)
        if (isTakingSuccessful) {
            currentState.value = State.Previewed(cardNode)
        }
    }

    private suspend fun layoutCards(delay: Long) {
        if (currentState.value != State.Loaded) return
        val actor = actor ?: throw IllegalStateException("actor is null")
        val deckNode = deckNode ?: throw IllegalStateException("deckNode is null")
        val cardGrid = cardGrid ?: throw IllegalStateException("cardGrid is null")

        delay(delay)

        for (r in 0..<cardGrid.gridSettings.sizeX) {
            for (c in 0..<cardGrid.gridSettings.sizeZ) {
                actor.layoutCard(deckNode, cardGrid, Position2D(r, c))
            }
        }
        currentState.value = State.Layout
    }

    sealed class State {
        data object NotLoaded : State()
        data object Loaded : State()
        data object Layout : State()
        data class Previewed(val node: ArCardNode) : State()
        data class Error(val message: String) : State()
        data object CameraPermissionDenied : State()
    }
}

@Composable
fun rememberARCardLayoutState(
    initialCards: CardsWithQuestionId,
    resourceLoader: ArResourceLoader
): ARCardLayoutState {
    val coroutineScope = rememberCoroutineScope()
    val layoutState = remember {
        ARCardLayoutState(initialCards.cards.map { it.card }, coroutineScope, resourceLoader)
    }

    DisposableEffect(layoutState) {
        onDispose {
            layoutState.destroy()
        }
    }
    return layoutState
}