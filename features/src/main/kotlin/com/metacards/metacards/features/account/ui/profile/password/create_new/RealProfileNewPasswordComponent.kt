package com.metacards.metacards.features.account.ui.profile.password.create_new

import com.ark<PERSON>nov.decompose.ComponentContext
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.message.data.MessageService
import com.metacards.metacards.core.message.domain.Message
import com.metacards.metacards.core.utils.PASSWORD_MIN_LENGTH
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.passwordInput
import com.metacards.metacards.features.R
import com.metacards.metacards.features.auth.domain.UpdatePasswordInteractor
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.MutableStateFlow
import ru.mobileup.kmm_form_validation.options.ImeAction
import ru.mobileup.kmm_form_validation.validation.control.equalsTo
import ru.mobileup.kmm_form_validation.validation.control.isNotBlank
import ru.mobileup.kmm_form_validation.validation.control.minLength
import ru.mobileup.kmm_form_validation.validation.form.RevalidateOnValueChanged
import ru.mobileup.kmm_form_validation.validation.form.SetFocusOnFirstInvalidControlAfterValidation
import ru.mobileup.kmm_form_validation.validation.form.ValidateOnFocusLost
import ru.mobileup.kmm_form_validation.validation.form.dynamicValidationResult
import ru.mobileup.kmm_form_validation.validation.form.formValidator

class RealProfileNewPasswordComponent(
    componentContext: ComponentContext,
    private val oldPassword: String,
    private val errorHandler: ErrorHandler,
    private val messageService: MessageService,
    private val updatePasswordInteractor: UpdatePasswordInteractor,
    private val onOutput: (ProfileNewPasswordComponent.Output) -> Unit
) : ComponentContext by componentContext, ProfileNewPasswordComponent {

    private val debounce = Debounce()

    override val newPasswordInputControl = passwordInput(componentScope, ImeAction.Next)

    override val confirmNewPasswordInputControl = passwordInput(componentScope, ImeAction.Done)

    private val formValidator = componentScope.formValidator {
        features = listOf(
            ValidateOnFocusLost,
            RevalidateOnValueChanged,
            SetFocusOnFirstInvalidControlAfterValidation
        )

        input(newPasswordInputControl) {
            isNotBlank(StringDesc.Resource(R.string.password_create_error))
            minLength(PASSWORD_MIN_LENGTH, StringDesc.Resource(R.string.password_create_error))
        }

        input(confirmNewPasswordInputControl) {
            isNotBlank(StringDesc.Resource(R.string.password_create_confirm_error))
            equalsTo(
                newPasswordInputControl,
                StringDesc.Resource(R.string.password_create_confirm_error)
            )
        }
    }

    private val dynamicResult = componentScope.dynamicValidationResult(formValidator)

    override val saveButtonState: MutableStateFlow<ButtonState> = computed(dynamicResult) { validationResult ->
        if (validationResult.isValid) {
            ButtonState.Enabled
        } else {
            ButtonState.Disabled
        }
    } as MutableStateFlow<ButtonState>

    override fun onSaveClick() {
        if (confirmNewPasswordInputControl.text.value != newPasswordInputControl.text.value) {
            confirmNewPasswordInputControl.error.value = StringDesc.Resource(R.string.password_create_confirm_error)
            return
        }

        DebounceClick(debounce, "onProfileNewPasswordSaveClick", errorHandler) {
            saveButtonState.value = ButtonState.Loading
            updatePasswordInteractor.execute(
                oldPassword = oldPassword,
                newPassword = newPasswordInputControl.text.value
            )
            messageService.showMessage(
                Message(
                    text = StringDesc.Resource(R.string.account_profile_edit_password_success_message),
                    isNotification = true
                )
            )
            onOutput(ProfileNewPasswordComponent.Output.ProfileScreenRequested)
        }
    }
}