package com.metacards.metacards.features.record.data

import android.net.Uri
import co.touchlab.kermit.Logger
import com.metacards.metacards.core.R
import com.metacards.metacards.core.error_handling.UnauthorizedException
import com.metacards.metacards.core.user.domain.UserId
import com.metacards.metacards.core.utils.ErrorMessage
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.endToInstant
import com.metacards.metacards.core.utils.mapLoadable
import com.metacards.metacards.core.utils.startToInstant
import com.metacards.metacards.core.utils.toTimestamp
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.record.data.dto.toDomain
import com.metacards.metacards.features.record.data.dto.toDto
import com.metacards.metacards.features.record.domain.NewRecord
import com.metacards.metacards.features.record.domain.Record
import com.metacards.metacards.features.record.domain.RecordFilter
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.user.domain.UserRepository
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDate

@OptIn(ExperimentalCoroutinesApi::class)
class RecordRepositoryImpl(
    private val recordsDataSource: RecordsDataSource,
    private val userRepository: UserRepository,
    private val recordsUpdateListener: Lazy<RecordsUpdateListener>,
) : RecordRepository {

    private val logger = Logger.withTag("RecordRepository")

    override fun getRecordById(userId: UserId, recordId: RecordId): Flow<LoadableState<Record>> {
        return recordsDataSource
            .getRecordById(userId, recordId)
            .mapLoadable { it?.toDomain() }
    }

    override suspend fun getNewestRecords(filter: RecordFilter, limit: Int): List<Record> {
        val userId = userRepository.user.value?.userId ?: return emptyList()

        return recordsDataSource
            .getNewestRecords(userId, filter, limit)
            .map { it.toDomain() }
    }

    override suspend fun getRecordsOlderThan(
        time: Instant,
        filter: RecordFilter,
        limit: Int,
        inclusive: Boolean,
    ): List<Record> {
        val userId = userRepository.user.value?.userId ?: return emptyList()

        return recordsDataSource
            .getRecordsOlderThan(userId, time.toTimestamp(), filter, limit, inclusive)
            .map { it.toDomain() }
    }

    override suspend fun getRecordsBetweenTimes(
        startTime: Instant,
        endTime: Instant,
        filter: RecordFilter,
    ): List<Record> {
        val userId = userRepository.user.value?.userId ?: return emptyList()

        return recordsDataSource
            .getRecordsBetweenTimes(userId, startTime.toTimestamp(), endTime.toTimestamp(), filter)
            .map { it.toDomain() }
    }

    override suspend fun getRecordsBetweenDates(
        startDate: LocalDate,
        endDate: LocalDate,
        filter: RecordFilter,
    ): List<Record> {
        val startTime = startDate.startToInstant()
        val endTime = endDate.endToInstant()
        return getRecordsBetweenTimes(startTime, endTime, filter)
    }

    override suspend fun getRecordsNewerThan(
        time: Instant,
        filter: RecordFilter,
        limit: Int,
    ): List<Record> {
        val userId = userRepository.user.value?.userId ?: return emptyList()

        return recordsDataSource
            .getRecordsNewerThan(userId, time.toTimestamp(), filter, limit)
            .map { it.toDomain() }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun getRecordsByCardId(cardId: CardId): Flow<LoadableState<List<Record>>> {
        return userRepository.user.flatMapLatest { user ->
            if (user != null) {
                recordsDataSource.getRecordsByCardId(user.userId, cardId)
                    .mapLoadable { recordDtos -> recordDtos?.map { it.toDomain() } }
            } else {
                flowOf(LoadableState(error = ErrorMessage(R.string.error_unauthorized.strResDesc())))
            }
        }
    }

    override suspend fun createRecord(newRecord: NewRecord) {
        userRepository.user.value?.userId ?: throw UnauthorizedException(
            IllegalStateException("userId is null")
        )

        val result = recordsDataSource.createRecord(newRecord.toDto())
        if (result.isError) {
            logger.e(result.toString())
            throw IllegalStateException("create record error ${result.data}")
        }
        logger.i("record created $result")
    }

    override suspend fun updateRecord(userId: UserId, updatedRecord: Record) {
        recordsUpdateListener.value.updateAnalyticsRecord(updatedRecord)
        recordsDataSource.updateRecord(userId, updatedRecord.toDto())
    }

    override suspend fun getCardPhotoUri(byte: ByteArray): Uri? {
        val userId = userRepository.user.value?.userId ?: throw UnauthorizedException(
            IllegalStateException("userId is null")
        )

        return recordsDataSource.getCardPhotoUri(
            userId = userId,
            byte = byte
        )
    }

    override suspend fun getDailyCardRecordId(userId: UserId): RecordId? {
        return recordsDataSource.getDailyCardRecordId(userId)?.let { RecordId(it) }
    }
}
