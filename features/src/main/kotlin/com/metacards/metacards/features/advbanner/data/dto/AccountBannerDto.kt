package com.metacards.metacards.features.advbanner.data.dto

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.advbanner.domain.entity.AccountAdvBanner
import com.metacards.metacards.features.advbanner.domain.entity.AccountAdvBannerStyle
import com.metacards.metacards.features.advbanner.domain.entity.AccountAdvBannerType
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
class AccountBannerDto(
    @SerialName("titleLocalized")
    val titleLocalized: Map<String, String?> = emptyMap(),
    @SerialName("descriptionLocalized")
    val descriptionLocalized: Map<String, String?>? = null,
    @SerialName("buttonTextLocalized")
    val buttonTextLocalized: Map<String, String?> = emptyMap(),
    @SerialName("type")
    val type: String = "",
    @SerialName("priority")
    val priority: Int = -1,
    @SerialName("style")
    val style: String = ""
) {
    fun toDomain() = AccountAdvBanner(
        title = LocalizableString(titleLocalized),
        description = descriptionLocalized?.let(::LocalizableString),
        buttonText = LocalizableString(buttonTextLocalized),
        type = AccountAdvBannerType.fromString(type),
        priority = priority,
        style = AccountAdvBannerStyle.fromString(style)
    )
}