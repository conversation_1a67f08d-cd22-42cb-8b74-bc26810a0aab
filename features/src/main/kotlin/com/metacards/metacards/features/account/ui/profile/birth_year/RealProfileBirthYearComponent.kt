package com.metacards.metacards.features.account.ui.profile.birth_year

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.user.domain.UserProvider
import com.metacards.metacards.features.user.domain.UpdateUserInteractor
import kotlinx.coroutines.flow.MutableStateFlow
import java.time.Year

class RealProfileBirthYearComponent(
    componentContext: ComponentContext,
    selectedYear: Int?,
    private val userProvider: UserProvider,
    private val updateUserInteractor: UpdateUserInteractor,
    private val onOutput: (ProfileBirthYearComponent.Output) -> Unit
) : ComponentContext by componentContext, ProfileBirthYearComponent {

    companion object {
        private const val USER_YEAR_MIN = 13L
        private const val USER_YEAR_MAX = 115L
    }

    private val debounce = Debounce()

    override val yearsList: List<Int> = (Year.now().minusYears(USER_YEAR_MIN).value
        .downTo(Year.now().minusYears(USER_YEAR_MAX).value)).toList()

    override val selectedYearFlow: MutableStateFlow<Int?> = MutableStateFlow(selectedYear)

    override fun onYearClick(year: Int) {
        selectedYearFlow.value = year
    }

    override fun onSaveClick() {
        DebounceClick(debounce, "onBirthYearSaveClick") {
            val user = userProvider.getUser().value
            val year = selectedYearFlow.value ?: return@DebounceClick

            user?.let {
                updateUserInteractor.updateYearOfBirth(it.userId, year)
                onOutput(ProfileBirthYearComponent.Output.DismissRequested)
            }
        }
    }

    override fun onDismiss() {
        onOutput(ProfileBirthYearComponent.Output.DismissRequested)
    }
}