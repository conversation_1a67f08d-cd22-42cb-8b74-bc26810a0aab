package com.metacards.metacards.features.layout.ui.select_card

import android.Manifest
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.childContext
import com.arkivanov.essenty.backhandler.BackCallback
import com.arkivanov.essenty.lifecycle.doOnCreate
import com.arkivanov.essenty.lifecycle.doOnDestroy
import com.arkivanov.essenty.lifecycle.doOnResume
import com.arkivanov.essenty.lifecycle.doOnStart
import com.google.android.filament.Engine
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.error_handling.safeRun
import com.metacards.metacards.core.external_apps.ExternalAppService
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.permissions.PermissionService
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.ResourceFormatted
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.mapData
import com.metacards.metacards.core.utils.stateIn
import com.metacards.metacards.features.R
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardHint
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.domain.repository.DecksRepository
import com.metacards.metacards.features.layout.createArCardSelectComponent
import com.metacards.metacards.features.layout.domain.CardSource
import com.metacards.metacards.features.layout.domain.CardWithCover
import com.metacards.metacards.features.layout.domain.GetCardsForLayoutInteractor
import com.metacards.metacards.features.layout.domain.PredefinedQuestion
import com.metacards.metacards.features.layout.ui.select_card.ar.ArCardSelectComponent
import com.metacards.metacards.features.tutorial.data.TutorialMessageService
import com.metacards.metacards.features.tutorial.data.TutorialRepository
import com.metacards.metacards.features.tutorial.data.TutorialStep
import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import dev.icerock.moko.resources.desc.Raw
import dev.icerock.moko.resources.desc.StringDesc
import io.github.sceneview.Filament
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update

data class CardsWithQuestionId(
    val cards: List<CardWithCover>,
    val questionId: Int
)

data class DeckIdWithQuestionId(
    val deckId: DeckId,
    val questionId: Int
)

class RealCardSelectComponent(
    componentContext: ComponentContext,
    componentFactory: ComponentFactory,
    private val cardSource: CardSource,
    private val tutorialMessageService: TutorialMessageService,
    tutorialRepository: TutorialRepository,
    initialQuestionText: String?,
    private val getCardsForLayoutInteractor: GetCardsForLayoutInteractor,
    private val analyticsService: AnalyticsService,
    decksRepository: DecksRepository,
    private val errorHandler: ErrorHandler,
    private val externalAppService: ExternalAppService,
    private val permissionService: PermissionService,
    private val onOutput: (CardSelectComponent.Output) -> Unit
) : CardSelectComponent, ComponentContext by componentContext {
    private val deckId = MutableStateFlow<DeckIdWithQuestionId?>(null)

    @OptIn(ExperimentalCoroutinesApi::class)
    private val _cards = deckId.flatMapLatest { getCardsForLayoutInteractor.execute(it?.deckId) }
        .stateIn(this, LoadableState())

    private val cardHints = MutableStateFlow<List<CardHint>?>(null)

    override val questionText: MutableStateFlow<StringDesc> =
        MutableStateFlow(StringDesc.Raw(initialQuestionText ?: ""))

    override val tutorialMessage: StateFlow<TutorialMessage?> =
        tutorialMessageService.tutorialMessageFlow
            .stateIn(componentScope, SharingStarted.Eagerly, null)

    override val tutorialStep: StateFlow<TutorialStep> = tutorialMessageService.tutorialStepFlow

    override val isPredefinedLayout: Boolean = cardSource is CardSource.Questions
    override val arEngine: Engine by lazy { Filament.retain() }

    override val tabs: List<CardSelectComponent.Tabs> =
        listOf(CardSelectComponent.Tabs.Flat, CardSelectComponent.Tabs.AR)

    override val selectedTab: MutableStateFlow<CardSelectComponent.Tabs> =
        MutableStateFlow(tabs.first())

    override val arComponent: ArCardSelectComponent by lazy {
        componentFactory.createArCardSelectComponent(
            componentContext.childContext("ar"),
            isPredefinedLayout,
            ::onArComponentOutput
        )
    }

    override val isInteractionEnabled: MutableStateFlow<Boolean> = MutableStateFlow(false)
    override val activeQuestionIndex: MutableStateFlow<Int> = MutableStateFlow(0)
    override val predefinedQuestions: List<PredefinedQuestion> =
        (cardSource as? CardSource.Questions)?.list ?: listOf()

    private val backCallback = BackCallback {
        onOutput(
            CardSelectComponent.Output.OnCloseRequested(
                isArCardSelect = selectedTab.value == CardSelectComponent.Tabs.AR
            )
        )
    }

    override val cards = computed(_cards, activeQuestionIndex) { cards, index ->
        cards.mapData { list ->
            CardsWithQuestionId(
                list ?: emptyList(),
                index
            )
        }
    }

    override val isCameraPermissionGranted = MutableStateFlow(false)

    init {
        val initialDeckId = when (cardSource) {
            is CardSource.Deck -> cardSource.deckId
            is CardSource.Questions -> cardSource.list[activeQuestionIndex.value].deckId
            else -> error("Invalid card source")
        }

        deckId.value = DeckIdWithQuestionId(
            initialDeckId,
            activeQuestionIndex.value
        )

        updateQuestion()
        backHandler.register(backCallback)

        lifecycle.doOnCreate {
            componentScope.safeLaunch(errorHandler) {
                cardHints.value = decksRepository.getCardHints()
            }
        }

        lifecycle.doOnStart {
            tutorialMessageService.handleCardSelectComponentTutorial(componentScope)
        }

        lifecycle.doOnResume {
            safeRun(errorHandler) {
                isCameraPermissionGranted.update {
                    permissionService.isPermissionGranted(Manifest.permission.CAMERA)
                }
            }
        }

        lifecycle.doOnDestroy {
            Filament.release()
        }
    }

    override fun onTabChange(tabModel: CardSelectComponent.Tabs) {
        when (tabModel) {
            CardSelectComponent.Tabs.Flat -> analyticsService.logEvent(AnalyticsEvent.SetupManualEvent)
            CardSelectComponent.Tabs.AR -> {
                analyticsService.logEvent(
                    if (isPredefinedLayout) {
                        AnalyticsEvent.LayoutArEvent
                    } else {
                        AnalyticsEvent.SetupArEvent
                    }
                )
                componentScope.safeLaunch(errorHandler) {
                    if (!permissionService.isPermissionGranted(Manifest.permission.CAMERA)) {
                        permissionService.requestPermission(Manifest.permission.CAMERA)
                    }
                }
            }
        }
        selectedTab.value = tabs.find { it == tabModel } ?: tabs.first()
    }

    override fun onCloseClick(isArCardSelect: Boolean) {
        onOutput(CardSelectComponent.Output.OnCloseRequested(isArCardSelect))
    }

    override fun onCardSelect(card: Card, questionText: String?) {
        val isLastPredefined = activeQuestionIndex.value + 1 ==
            (cardSource as? CardSource.Questions)?.list?.size
        val availableCards = cards.value.data?.cards?.map { it.card }?.toMutableList()
            ?: mutableListOf()
        availableCards.removeIf { it.id == card.id }

        analyticsService.logEvent(AnalyticsEvent.SetupManualCardTapEvent)

        val actualQuestionText = if (cardSource is CardSource.Questions) {
            cardSource.list[activeQuestionIndex.value].text
        } else {
            questionText?.let {
                LocalizableString.createNonLocalizable(questionText)
            }
        }

        cardHints.value?.let {
            onOutput(
                CardSelectComponent.Output.OnCardSelected(
                    card,
                    availableCards,
                    isLastPredefined,
                    activeQuestionIndex.value + 1,
                    it,
                    actualQuestionText
                )
            )
        }

        if (!isLastPredefined) {
            nextQuestion()
        }
    }

    override fun onCardLayoutAnimationEnd() {
        isInteractionEnabled.value = true
    }

    private fun onArComponentOutput(output: ArCardSelectComponent.Output) {
        when (output) {
            is ArCardSelectComponent.Output.OnFinished -> {
                analyticsService.logEvent(AnalyticsEvent.SetupArFinishEvent)
                onOutput(
                    CardSelectComponent.Output.OnArFinished(output.cards)
                )
            }

            is ArCardSelectComponent.Output.OnPredefinedFinished -> {
                val isLast =
                    activeQuestionIndex.value + 1 == (cardSource as? CardSource.Questions)?.list?.size
                if (isLast) {
                    analyticsService.logEvent(AnalyticsEvent.LayoutArFinishEvent)
                    onOutput(
                        CardSelectComponent.Output.OnArPredefinedFinished(
                            output.cards,
                            true
                        )
                    )
                } else {
                    analyticsService.logEvent(AnalyticsEvent.LayoutArNextQuestionEvent)
                    onOutput(
                        CardSelectComponent.Output.OnArPredefinedFinished(
                            output.cards,
                            false
                        )
                    )
                    nextQuestion()
                }
            }

            ArCardSelectComponent.Output.AuthSuggestingRequested -> onOutput(
                CardSelectComponent.Output.AuthSuggestingScreenRequested
            )
            ArCardSelectComponent.Output.SubscriptionSuggestingRequested -> onOutput(
                CardSelectComponent.Output.SubscriptionSuggestingScreenRequested
            )
        }
    }

    override fun openSettings() {
        safeRun(errorHandler) {
            externalAppService.openAppSettings()
        }
    }

    private fun nextQuestion() {
        if (!isPredefinedLayout) return

        activeQuestionIndex.value++
        updateQuestion()

        val deckId = (cardSource as? CardSource.Questions)
            ?.list
            ?.get(activeQuestionIndex.value)
            ?.deckId

        deckId?.let { this.deckId.value = DeckIdWithQuestionId(it, activeQuestionIndex.value) }
        arComponent.reload()
    }

    private fun updateQuestion() {
        if (cardSource is CardSource.Deck) return

        val string = StringDesc.ResourceFormatted(
            R.string.predefined_layout_question_number,
            activeQuestionIndex.value + 1,
            predefinedQuestions.size
        )
        questionText.value = string
    }
}
