package com.metacards.metacards.features.welcome.ui

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.button.MetaTransparentButton
import com.metacards.metacards.features.R
import com.metacards.metacards.features.welcome.ui.widgets.GifImage
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.flow.collectLatest
import com.metacards.metacards.core.R as CoreR

private const val GIF_HEIGHT_FRACTION = 0.55f
private const val GIF_ASPECT_RATIO = 0.5f

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun WelcomeScreensUi(
    component: WelcomeScreensComponent,
    modifier: Modifier = Modifier
) {
    val pages = component.pages
    val pagerState = rememberPagerState { pages.size }

    LaunchedEffect(key1 = Unit) {
        component.pageToOpen.collectLatest {
            pagerState.animateScrollToPage(it)
        }
    }

    LaunchedEffect(key1 = Unit) {
        snapshotFlow { pagerState.currentPage }.collect {
            component.onPageChanged(it)
        }
    }

    BoxWithFade(
        modifier = modifier
            .fillMaxSize()
            .safeDrawingPadding(),
        listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        Column(modifier = Modifier.fillMaxWidth()) {
            HorizontalPager(state = pagerState) { index ->
                WelcomePage(
                    index = index,
                    title = pages[index].title,
                    caption = pages[index].caption,
                    modifier = Modifier.padding(top = 8.dp)
                )
            }

            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier
                    .align(Alignment.CenterHorizontally)
                    .padding(vertical = 16.dp)
            ) {
                pages.forEachIndexed { index, _ ->
                    PageIndicator(isSelected = index == pagerState.currentPage)
                }
            }
        }

        AnimatedContent(
            targetState = pagerState.currentPage != pages.lastIndex,
            transitionSpec = {
                (fadeIn(animationSpec = tween(50)))
                    .togetherWith(fadeOut(animationSpec = tween(50)))
            },
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter),
            label = "ButtonAnimation"
        ) { isNotLastPage ->
            if (isNotLastPage) {
                MetaAccentButton(
                    text = CoreR.string.common_proceed.strResDesc().localizedByLocal(),
                    onClick = { component.onProceedClick(pagerState.currentPage + 1) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                        .padding(bottom = 4.dp)
                        .align(Alignment.BottomCenter)
                )
            } else {
                Column {
                    MetaAccentButton(
                        text = R.string.welcome_screen_button_auth.strResDesc().localizedByLocal(),
                        onClick = component::onAuthClick,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 8.dp)
                    )

                    MetaTransparentButton(
                        text = R.string.welcome_screen_button_auth_later.strResDesc()
                            .localizedByLocal(),
                        onClick = component::onAuthLaterClick,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        }
    }
}

@Composable
private fun WelcomePage(
    index: Int,
    title: StringDesc,
    caption: StringDesc,
    modifier: Modifier = Modifier
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier.fillMaxWidth()
    ) {
        Surface(
            shape = RoundedCornerShape(30.dp),
            color = CustomTheme.colors.system.transparent,
            modifier = Modifier
                .fillMaxHeight(GIF_HEIGHT_FRACTION)
                .aspectRatio(GIF_ASPECT_RATIO, true)
                .padding(horizontal = 16.dp)
                .padding(bottom = 48.dp, top = 16.dp)
        ) {
            val drawable = when (index) {
                0 -> R.drawable.im_welcome_1
                1 -> R.drawable.im_welcome_2
                2 -> R.drawable.im_welcome_3
                else -> R.drawable.im_welcome_4
            }
            GifImage(
                drawable = drawable,
                modifier = Modifier.fillMaxHeight()
            )
        }

        Text(
            text = title.localizedByLocal(),
            color = CustomTheme.colors.text.primary,
            style = CustomTheme.typography.heading.secondary,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        Text(
            text = caption.localizedByLocal(),
            color = CustomTheme.colors.text.primary,
            style = CustomTheme.typography.body.primary,
            textAlign = TextAlign.Center,
            minLines = 3,
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .padding(bottom = 11.dp)
        )
    }
}

@Composable
private fun PageIndicator(
    isSelected: Boolean,
    modifier: Modifier = Modifier
) {
    val initWidth = 8.dp
    val selectedWidth = 24.dp
    val initColor = CustomTheme.colors.icons.noActive
    val selectedColor = CustomTheme.colors.icons.primary

    var indicatorWidth by remember { mutableStateOf(initWidth) }
    var indicatorColor by remember { mutableStateOf(initColor) }

    LaunchedEffect(key1 = isSelected) {
        if (isSelected) {
            indicatorWidth = selectedWidth
            indicatorColor = selectedColor
        } else {
            indicatorWidth = initWidth
            indicatorColor = initColor
        }
    }

    val animatedWidth by animateDpAsState(targetValue = indicatorWidth)
    val animatedColor by animateColorAsState(targetValue = indicatorColor)

    Surface(
        shape = RoundedCornerShape(8.dp),
        color = animatedColor,
        modifier = modifier
            .height(8.dp)
            .width(animatedWidth)
    ) {}
}

@Preview
@Composable
fun WelcomeScreensUiPreview() {
    AppTheme {
        WelcomeScreensUi(component = FakeWelcomeScreensComponent())
    }
}