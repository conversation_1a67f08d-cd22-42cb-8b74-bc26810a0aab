package com.metacards.metacards.features.special_deck.domain.entity

import android.os.Parcelable
import com.metacards.metacards.features.deck.domain.entity.DeckId
import kotlinx.parcelize.Parcelize

@Parcelize
data class UserSpecialDeckInfo(
    val specialDeckId: DeckId,
    val isCompleted: Boolean,
    val cards: List<UserSpecialDeckCardInfo>,
    val cooldownTime: SpecialDeckCooldownTime,
    val todayReceivedCardsCount: Int
) : Parcelable {

    fun isCanSelectNewCard() = todayReceivedCardsCount < MAX_CARDS_PER_DAY
    fun isFirstCardToday() = todayReceivedCardsCount == 0

    companion object {
        fun mock(deckId: DeckId = DeckId("")) = UserSpecialDeckInfo(
            deckId,
            false,
            emptyList(),
            SpecialDeckCooldownTime.zero,
            todayReceivedCardsCount = 0
        )
        private const val MAX_CARDS_PER_DAY = 2
    }
}
