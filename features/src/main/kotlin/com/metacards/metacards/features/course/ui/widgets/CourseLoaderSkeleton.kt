package com.metacards.metacards.features.course.ui.widgets

import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.core.widget.placeholder.PlaceholderHighlight
import com.metacards.metacards.core.widget.placeholder.placeholder
import com.metacards.metacards.core.widget.placeholder.shimmer
import com.metacards.metacards.features.deck.ui.MetaCardDefaults
import dev.icerock.moko.resources.desc.desc

@Composable
fun CourseLoaderSkeleton() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .safeDrawingPadding()
    ) {
        TopNavigationBar(title = "".desc())

        val placeHolderModifier = Modifier
            .clip(RoundedCornerShape(16.dp))
            .placeholder(
                visible = true,
                color = CustomTheme.colors.background.placeholder,
                shape = MetaCardDefaults.shape,
                highlight = PlaceholderHighlight.shimmer(
                    highlightColor = CustomTheme.colors.system.invert.copy(alpha = 0.6f)
                ),
                placeholderFadeTransitionSpec = { tween() },
                contentFadeTransitionSpec = { tween() }
            )

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .aspectRatio(1.66f)
                .then(placeHolderModifier)
        )

        Spacer(modifier = Modifier.height(24.dp))

        Box(
            modifier = Modifier
                .padding(horizontal = 32.dp)
                .size(width = 69.dp, height = 16.dp)
                .then(placeHolderModifier)
        )

        Spacer(modifier = Modifier.height(8.dp))

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .aspectRatio(1.66f)
                .then(placeHolderModifier)
        )

        Spacer(modifier = Modifier.height(24.dp))

        Box(
            modifier = Modifier
                .padding(horizontal = 32.dp)
                .size(width = 69.dp, height = 16.dp)
                .then(placeHolderModifier)
        )

        Spacer(modifier = Modifier.height(8.dp))

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .aspectRatio(5.36f)
                .then(placeHolderModifier)
        )

        Spacer(modifier = Modifier.height(8.dp))

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .aspectRatio(5.36f)
                .then(placeHolderModifier)
        )

        Spacer(modifier = Modifier.height(8.dp))
    }
}