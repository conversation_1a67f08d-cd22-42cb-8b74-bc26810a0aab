package com.metacards.metacards.features.account.ui.lessons.list

import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.features.account.domain.entity.LessonCategory
import com.metacards.metacards.features.deck.domain.entity.Lesson
import kotlinx.coroutines.flow.MutableStateFlow

class FakeLessonListComponent : LessonListComponent {
    override val lessonCategoryState = MutableStateFlow(LoadableState(data = LessonCategory.mock()))
    override val lessonsState = MutableStateFlow(LoadableState(data = listOf<Lesson>()))

    override fun onLessonDetailsClick(lesson: Lesson) = Unit

    override fun onVideoPlay(lesson: Lesson) = Unit
}