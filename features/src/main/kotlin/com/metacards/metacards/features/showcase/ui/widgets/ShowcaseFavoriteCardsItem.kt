package com.metacards.metacards.features.showcase.ui.widgets

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.user.domain.FavoriteCard
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.strResDesc
import com.metacards.metacards.core.R as CoreR

@Composable
fun ColumnScope.ShowcaseFavoriteCardsItem(
    cards: List<FavoriteCard>,
    onFavoriteCardClick: (FavoriteCard) -> Unit,
) {
    val fiveCard = remember(cards) { cards.reversed().take(5) }
    ShowcaseItemContainer(
        title = R.string.showcase_favorite_cards_title.strResDesc().localizedByLocal(),
        subtitle = null
    ) {
        if (fiveCard.isEmpty()) {
            EmptyStub()
        } else {
            val cardModifier = Modifier
                .size(height = 312.dp, width = 209.dp)
                .clip(RoundedCornerShape(16.dp))
                .border(0.5.dp, CustomTheme.colors.stroke.secondary, RoundedCornerShape(16.dp))
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .horizontalScroll(rememberScrollState())
            ) {
                Spacer(modifier = Modifier.width(16.dp))

                fiveCard.forEach { card ->
                    CardItem(
                        url = card.imageUrl,
                        onClick = { onFavoriteCardClick(card) },
                        modifier = cardModifier
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                }

                Spacer(modifier = Modifier.width(8.dp))
            }
        }
    }
}

@Composable
private fun CardItem(
    url: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    AsyncImage(
        model = url,
        contentDescription = null,
        contentScale = ContentScale.FillBounds,
        modifier = modifier
            .clickable(onClick = onClick)
    )
}

@Composable
private fun EmptyStub() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
            .background(CustomTheme.colors.background.primary, RoundedCornerShape(16.dp)),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.height(32.dp))
        Icon(
            painter = painterResource(CoreR.drawable.ic_64_galactic),
            contentDescription = null,
            tint = CustomTheme.colors.icons.disabled
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = R.string.showcase_favorite_cards_empty_title.strResDesc().localizedByLocal(),
            style = CustomTheme.typography.heading.medium,
            color = CustomTheme.colors.text.primary
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = R.string.showcase_favorite_cards_empty_subtitle.strResDesc().localizedByLocal(),
            style = CustomTheme.typography.body.primary,
            color = CustomTheme.colors.text.primary,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        )

        Spacer(modifier = Modifier.height(32.dp))
    }
}