package com.metacards.metacards.features.layout.ui.select_card.ar.widget

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import com.google.ar.core.Config
import com.metacards.metacards.features.layout.domain.ar.GridSettings
import com.metacards.metacards.features.layout.domain.ar.StashSettings
import kotlinx.coroutines.launch

@Composable
fun ArCardLayoutScene(
    modifier: Modifier = Modifier,
    state: ARCardLayoutState,
    gridSettings: GridSettings,
    stashSettings: StashSettings
) {
    if (state.cards.size < 4 * 2 - 1) return

    val coroutineScope = rememberCoroutineScope()
    val nodes by state.nodes.collectAsState()

    ArScene(
        modifier = modifier,
        nodes = nodes.toList(),
        onCreate = { arSceneView ->
            arSceneView.planeFindingMode = Config.PlaneFindingMode.HORIZONTAL
            arSceneView.planeRenderer.isVisible = false
            arSceneView.planeRenderer.isEnabled = false
            arSceneView.depthMode = Config.DepthMode.AUTOMATIC
            arSceneView.focusMode = Config.FocusMode.AUTO

            coroutineScope.launch {
                state.init(
                    arSceneView.context,
                    arSceneView.cameraNode,
                    gridSettings,
                    stashSettings
                )
            }
        },
        onSessionCreate = { },
        onFrame = { },
        onTap = { state.layoutCards() }
    )
}