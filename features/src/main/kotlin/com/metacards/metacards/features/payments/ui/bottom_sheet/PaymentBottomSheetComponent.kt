package com.metacards.metacards.features.payments.ui.bottom_sheet

import android.os.Parcelable
import com.metacards.metacards.core.button.ButtonState
import kotlinx.coroutines.flow.StateFlow
import kotlinx.parcelize.Parcelize
import java.net.URL

interface PaymentBottomSheetComponent {

    val buttonState: StateFlow<ButtonState>

    fun onWebStoreClick()
    fun onDismiss()

    @Parcelize
    sealed interface Config : Parcelable {
        @Parcelize
        data class Deck(val deckId: String, val deckName: String) : Config

        @Parcelize
        sealed class Subscription(val id: String) : Config {
            @Parcelize
            data class New(val newId: String) : Subscription(newId)

            @Parcelize
            data class Upgrade(val oldId: String, val newId: String) : Subscription(newId)

            @Parcelize
            data class Cancel(val oldId: String) : Subscription(oldId)
        }
    }

    sealed interface Output {
        data class WebStoreRequested(val url: URL) : Output
        data object DismissRequested : Output
    }
}