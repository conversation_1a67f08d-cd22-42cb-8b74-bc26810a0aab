package com.metacards.metacards.features.showcase.ui

import com.metacards.metacards.core.user.domain.FavoriteCard
import com.metacards.metacards.features.account.domain.entity.LessonCategory
import com.metacards.metacards.features.account.domain.entity.LessonCategoryId
import com.metacards.metacards.features.course.domain.entity.CourseShortData
import com.metacards.metacards.features.course.domain.entity.CourseTheme
import com.metacards.metacards.features.course.ui.CourseComponent
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.domain.entity.DeckInfoWithCards
import com.metacards.metacards.features.layout.domain.CardSource
import com.metacards.metacards.features.layout.domain.PredefinedLayoutWithAvailable
import kotlinx.coroutines.flow.StateFlow

interface ShowcaseComponent {

    val decksForPurchaseInfoWithCards: StateFlow<List<DeckInfoWithCards>>
    val predefinedLayouts: StateFlow<List<PredefinedLayoutWithAvailable>>
    val videoLessonCategories: StateFlow<List<LessonCategory>>
    val favoriteCards: StateFlow<List<FavoriteCard>>
    val courses: StateFlow<List<CourseTheme>>
    val isPremiumUser: StateFlow<Boolean>

    fun onGameBannerClick()
    fun onShopDeckClick(deck: DeckInfoWithCards)
    fun onPredefinedLayoutClick(layout: PredefinedLayoutWithAvailable)
    fun onPredefinedLayoutBlockContentClick()
    fun onVideoLessonCategoryClick(category: LessonCategory)
    fun onAnalyticsBannerClick()
    fun onFavoriteCardClick(favoriteCard: FavoriteCard)
    fun onCourseThemeClick(courseTheme: CourseTheme)
    fun onCourseClick(course: CourseShortData)

    sealed interface Output {
        data object AuthSuggestingRequested : Output
        data class SpecialDeckRequested(val deckId: DeckId) : Output
        data class ShopDeckDetailsRequested(val deckId: DeckId) : Output
        data class PredefinedLayoutDetailsRequested(val cardSource: CardSource) : Output
        data class SubscriptionBottomSheetRequested(val withAdv: Boolean) : Output
        data class VideoCategorySelected(val lessonCategoryId: LessonCategoryId) : Output
        data object AnalyticsRequested : Output
        data class FavoriteCardDetailsRequested(val cardId: CardId) : Output
        data class CourseRequested(val screen: CourseComponent.Screen) : Output
    }
}