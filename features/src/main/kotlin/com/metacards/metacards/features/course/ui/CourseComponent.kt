package com.metacards.metacards.features.course.ui

import android.os.Parcelable
import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CourseTheme
import com.metacards.metacards.features.course.domain.entity.CourseThemeId
import com.metacards.metacards.features.course.ui.details.CourseDetailsComponent
import com.metacards.metacards.features.course.ui.list.CourseListComponent
import com.metacards.metacards.features.layout.domain.CardSource
import kotlinx.coroutines.flow.StateFlow
import kotlinx.parcelize.Parcelize

interface CourseComponent {

    val childStack: StateFlow<ChildStack<*, Child>>

    sealed interface Child {
        data class CourseList(val component: CourseListComponent) : Child
        data class Details(val component: CourseDetailsComponent) : Child
    }

    sealed interface Screen : Parcelable {
        @Parcelize
        data class CourseList(val theme: CourseTheme) : Screen

        @Parcelize
        data class Details(val screen: CourseDetailsComponent.Screen) : Screen
    }

    sealed interface Output {
        data object CloseRequested : Output
        data class SubscriptionBottomSheetRequested(val withAdv: Boolean) : Output
        data class LayoutDetailsRequested(
            val cardSource: CardSource,
            val courseId: CourseId?,
            val themeId: CourseThemeId?
        ) : Output
        data object AuthScreenRequested : Output
        data object AuthSuggestingRequested : Output
    }
}