package com.metacards.metacards.features.course.data.dto

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.course.domain.entity.CourseLessonCard
import com.metacards.metacards.features.course.domain.entity.CourseLessonCardType

data class CourseLessonCardDto(
    val cardType: String = "",
    val image: Map<String, String?>? = null,
    val video: Map<String, String?>? = null,
    val videoCover: String? = null,
    val order: Int = 0
) {
    fun toDomain() = CourseLessonCard(
        cardType = CourseLessonCardType.fromString(cardType),
        imageUrl = image?.let(::LocalizableString),
        videoUrl = video?.let(::LocalizableString),
        videoCoverUrl = videoCover,
        order = order
    )
}