package com.metacards.metacards.features.record.ui.create_record.add_card.widgets

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.features.R
import com.metacards.metacards.features.record.ui.create_record.add_card.AddCardComponent
import com.metacards.metacards.features.record.ui.create_record.add_card.FakeAddCardComponent

@Composable
fun PhotoSwitcher(component: AddCardComponent) {
    val currentState = component.currentState.collectAsState()
    val cameraColor = if (currentState.value is AddCardComponent.State.Camera) CustomTheme.colors.button.accent else CustomTheme.colors.button.small
    val imageTrackingColor = if (currentState.value is AddCardComponent.State.ImageTracking) CustomTheme.colors.button.accent else CustomTheme.colors.button.small
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center
    ) {
        IconButton(
            modifier = Modifier
                .background(
                    color = cameraColor,
                    shape = CircleShape
                )
                .clip(CircleShape),
            onClick = component::onCameraIconClick
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_24_photo),
                tint = CustomTheme.colors.icons.primary,
                contentDescription = null
            )
        }

        Spacer(modifier = Modifier.size(16.dp))

        IconButton(
            modifier = Modifier
                .background(
                    color = imageTrackingColor,
                    shape = CircleShape
                )
                .clip(CircleShape),
            onClick = component::onImageTrackingClick
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_24_frame),
                tint = CustomTheme.colors.icons.primary,
                contentDescription = null
            )
        }
    }
}

@Preview
@Composable
private fun PhotoSwitcherPreview() {
    AppTheme {
        PhotoSwitcher(component = FakeAddCardComponent())
    }
}