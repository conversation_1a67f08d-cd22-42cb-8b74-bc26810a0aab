package com.metacards.metacards.features.account.ui.profile.password

import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.core.utils.createFakeChildStackStateFlow
import com.metacards.metacards.features.account.ui.profile.password.confirm.FakeProfileConfirmPasswordComponent
import kotlinx.coroutines.flow.StateFlow

class FakeProfilePasswordComponent : ProfilePasswordComponent {
    override val childStack: StateFlow<ChildStack<*, ProfilePasswordComponent.Child>> =
        createFakeChildStackStateFlow(
            ProfilePasswordComponent.Child.ConfirmPassword(
                FakeProfileConfirmPasswordComponent()
            )
        )
}