package com.metacards.metacards.features.account.data

import android.content.Context
import android.os.Build
import com.google.firebase.functions.ktx.functions
import com.google.firebase.ktx.Firebase
import com.metacards.metacards.core.utils.getAppVersionCode
import com.metacards.metacards.core.utils.getAppVersionName
import com.metacards.metacards.core.utils.zonedDateTimeMoscow
import com.metacards.metacards.features.account.domain.repository.FeedbackRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.tasks.await
import java.util.TimeZone

class FeedbackRepositoryImpl(
    private val context: Context
) : FeedbackRepository {
    override fun sendFeedback(
        subject: String,
        email: String,
        question: String
    ): Flow<String> {
        return flow {
            val data = hashMapOf(
                "email" to email,
                "message" to question,
                "appVersion" to context.getAppVersionName(),
                "buildNumber" to context.getAppVersionCode(),
                "subject" to subject,
                "timestamb" to zonedDateTimeMoscow(),
                "deviceName" to "${Build.MANUFACTURER} ${Build.MODEL}",
                "osVersion" to "Android ${Build.VERSION.SDK_INT}",
                "timezone" to TimeZone.getDefault().rawOffset / 1000 / 60
            )

            val request = Firebase.functions
                .getHttpsCallable("createTicket")
                .call(data)
                .continueWith { it.result?.data.toString() }
                .await()
            emit(request)
        }
    }
}
