package com.metacards.metacards.features.special_deck.ui.card_opening_cooldown

import android.os.Parcelable
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCooldownTime
import kotlinx.parcelize.Parcelize

interface SpecialDeckCardOpeningCooldownComponent {

    val deckCooldownTime: SpecialDeckCooldownTime

    fun onUnderstoodClick()

    @Parcelize
    data class Config(val deckCooldownTime: SpecialDeckCooldownTime) : Parcelable

    sealed interface Output {
        data object DismissRequested : Output
    }
}