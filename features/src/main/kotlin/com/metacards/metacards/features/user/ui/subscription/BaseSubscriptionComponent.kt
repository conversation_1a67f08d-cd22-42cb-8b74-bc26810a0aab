package com.metacards.metacards.features.user.ui.subscription

import com.metacards.metacards.core.user.domain.SubscriptionTariff
import com.metacards.metacards.core.user.domain.SubscriptionType
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.features.payments.domain.PurchaseType
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

interface BaseSubscriptionComponent {
    val userSubscriptionState: StateFlow<User.SubscriptionState>
    val isTrialAvailable: StateFlow<Boolean>
    val selectedSubscriptionType: MutableStateFlow<SubscriptionType>
    val tariffs: StateFlow<LoadableState<List<SubscriptionTariff>>>

    fun onSubscriptionTypeSelect(subscriptionType: SubscriptionType)
    fun getPurchaseType(): PurchaseType.Subscription
    fun getIdForCancel(): String
}