package com.metacards.metacards.features.record.ui.record_list

import androidx.compose.foundation.interaction.DragInteraction
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.OnBeginReached
import com.metacards.metacards.core.utils.OnEndReached
import com.metacards.metacards.core.utils.PagedData
import com.metacards.metacards.core.utils.getTodayLocalDate
import com.metacards.metacards.core.widget.EmptyPlaceholder
import com.metacards.metacards.core.widget.ErrorPlaceholder
import com.metacards.metacards.core.widget.PagedLoadingProgress
import com.metacards.metacards.core.widget.PullRefreshLceWidget
import com.metacards.metacards.core.widget.button.MetaSecondaryButton
import com.metacards.metacards.features.R
import com.metacards.metacards.features.record.domain.Record
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.record.domain.RecordListType
import com.metacards.metacards.features.record.ui.record_list.widgets.DateText
import com.metacards.metacards.features.record.ui.record_list.widgets.RecordCard
import com.metacards.metacards.features.record.ui.record_list.widgets.RecordsLoadingSkeleton
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.flow.MutableSharedFlow

@Composable
fun RecordListUi(component: RecordListComponent, modifier: Modifier = Modifier) {
    val recordsState by component.visibleRecordsState.collectAsState()
    val scrollCommand by component.pendingScrollCommand.collectAsState()

    PullRefreshLceWidget(
        modifier = modifier,
        state = recordsState,
        onRefresh = component::onRefresh,
        onRetryClick = component::onRetryClick,
        pullRefreshEnabled = recordsState.data?.hasPreviousPage == false,
        loadingProgress = {
            RecordsLoadingSkeleton(component.hasAddButton, modifier)
        },
        errorPlaceholder = { message, onRetry ->
            ErrorPlaceholder(message, onRetry, modifier.padding(bottom = 130.dp))
        }
    ) { pagedData, _ ->
        if (pagedData.list.isEmpty()) {
            EmptyRecordPlaceholder(component.listType)
        } else {
            RecordsContent(
                pagedData,
                loadingFirstPage = recordsState.loading,
                hasError = recordsState.error != null,
                hasAddButton = component.hasAddButton,
                scrollCommand = scrollCommand,
                onLoadNext = component::onLoadNext,
                onLoadPrevious = component::onLoadPrevious,
                onRecordClick = component::onRecordClick,
                onArchiveClick = component::onArchiveClick,
                onFavouriteClick = component::onFavouriteClick,
                onAddClick = component::onAddClick,
                onScrollCommandExecuted = component::onScrollCommandExecuted,
            )
        }
    }
}

@Composable
private fun EmptyRecordPlaceholder(listType: RecordListType, modifier: Modifier = Modifier) {
    EmptyPlaceholder(
        modifier = modifier.padding(bottom = 130.dp),
        title = when (listType) {
            RecordListType.All -> R.string.record_list_all_empty_title.strResDesc().localizedByLocal()
            RecordListType.Favourite -> R.string.record_list_favourite_empty_title.strResDesc()
                .localizedByLocal()

            RecordListType.Archived -> R.string.record_list_archived_empty_title.strResDesc()
                .localizedByLocal()

            RecordListType.Analytics -> R.string.record_list_layout_title.strResDesc().localizedByLocal()
        },
        message = when (listType) {
            RecordListType.All -> R.string.record_list_all_empty_message.strResDesc().localizedByLocal()
            RecordListType.Favourite -> R.string.record_list_favourite_empty_message.strResDesc()
                .localizedByLocal()

            RecordListType.Archived -> R.string.record_list_archived_empty_message.strResDesc()
                .localizedByLocal()

            RecordListType.Analytics -> R.string.record_list_layout_message.strResDesc().localizedByLocal()
        }
    )
}

@Composable
private fun RecordsContent(
    pagedData: PagedData<Record>,
    loadingFirstPage: Boolean,
    hasError: Boolean,
    hasAddButton: Boolean,
    scrollCommand: ScrollCommand?,
    onLoadNext: () -> Unit,
    onLoadPrevious: () -> Unit,
    onRecordClick: (RecordId) -> Unit,
    onArchiveClick: (RecordId) -> Unit,
    onFavouriteClick: (RecordId) -> Unit,
    onAddClick: () -> Unit,
    onScrollCommandExecuted: () -> Unit,
) {
    val needShowAddButton = hasAddButton && !pagedData.hasPreviousPage
    val onStartScrollEventFlow =
        remember { MutableSharedFlow<Unit>() }
    val revealedRecordIdFlow = remember { MutableSharedFlow<RecordId>() }

    val lazyListState = rememberSaveable(saver = LazyListState.Saver) {
        val initialItemIndex = if (scrollCommand is ScrollCommand.ScrollToRecord) {
            getItemIndexForRecord(scrollCommand.recordId, pagedData, needShowAddButton)
                .coerceAtLeast(0)
        } else {
            0
        }
        LazyListState(initialItemIndex, 0)
    }

    LaunchedEffect(key1 = lazyListState.interactionSource) {
        lazyListState.interactionSource.interactions.collect {
            if (it is DragInteraction.Start) onStartScrollEventFlow.emit(Unit)
        }
    }

    HandleScrollCommands(
        scrollCommand,
        lazyListState,
        pagedData,
        needShowAddButton,
        onScrollCommandExecuted
    )

    if (pagedData.hasNextPage && !loadingFirstPage && !pagedData.loadingNextPage) {
        lazyListState.OnEndReached(
            callback = onLoadNext,
            itemCountGap = 3,
            scrollingToEndRequired = hasError
        )
    }

    if (pagedData.hasPreviousPage && !loadingFirstPage && !pagedData.loadingPreviousPage) {
        lazyListState.OnBeginReached(
            callback = onLoadPrevious,
            itemCountGap = 3,
            scrollingToBeginRequired = hasError
        )
    }

    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        state = lazyListState,
        verticalArrangement = Arrangement.spacedBy(8.dp),
        contentPadding = PaddingValues(start = 16.dp, end = 16.dp, bottom = 120.dp)
    ) {
        val firstRecord = pagedData.list.firstOrNull()
        // если дата первой записи == сегодня, то надпись "Сегодня" должна быть над кнопкой "Добавить запись", а инача над записью.
        val needShowDateOnTopOfAddButton =
            needShowAddButton && firstRecord?.creationDate == getTodayLocalDate()

        if (needShowAddButton) {
            item {
                if (firstRecord != null && needShowDateOnTopOfAddButton) {
                    DateText(
                        modifier = Modifier.padding(start = 16.dp, top = 8.dp),
                        date = firstRecord.creationDate
                    )
                }

                MetaSecondaryButton(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp),
                    text = R.string.record_list_add_button.strResDesc().localizedByLocal(),
                    onClick = onAddClick
                )
            }
        }

        itemsIndexed(
            pagedData.list,
            key = { _, record -> record.id }
        ) { index, record ->

            // Потребовалось добавить лоадер внутрь этого айтема, а не отдельным айтемом,
            // чтоб позиция скрола не сбрасывалась, когда лоадер исчезает.
            if (index == 0 && pagedData.loadingPreviousPage) {
                PagedLoadingProgress()
            }

            RecordCard(
                modifier = Modifier.fillMaxWidth(),
                showDate = needShowDate(
                    record,
                    previousRecord = pagedData.list.getOrNull(index - 1),
                    needShowDateOnTopOfAddButton = needShowDateOnTopOfAddButton
                ),
                record = record,
                onClick = { onRecordClick(record.id) },
                resetEvent = onStartScrollEventFlow,
                onArchiveClick = { onArchiveClick(record.id) },
                onFavouriteClick = { onFavouriteClick(record.id) },
                revealedRecordIdFlow = revealedRecordIdFlow
            )
        }

        if (pagedData.loadingNextPage) {
            item {
                PagedLoadingProgress()
            }
        }
    }
}

@Composable
private fun HandleScrollCommands(
    command: ScrollCommand?,
    lazyListState: LazyListState,
    pagedData: PagedData<Record>,
    needShowAddButton: Boolean,
    onScrollCommandExecuted: () -> Unit,
) {
    val currentPagedData by rememberUpdatedState(pagedData)
    val currentNeedShowAddButton by rememberUpdatedState(needShowAddButton)

    LaunchedEffect(command) {
        when (command) {
            null -> {
                // nothing
            }

            is ScrollCommand.ScrollToTop -> {
                lazyListState.scrollToItem(0)
                onScrollCommandExecuted()
            }

            is ScrollCommand.ScrollToRecord -> {
                val itemIndex = getItemIndexForRecord(
                    command.recordId,
                    currentPagedData,
                    currentNeedShowAddButton
                )

                if (itemIndex >= 0) {
                    lazyListState.scrollToItem(itemIndex)
                }
                onScrollCommandExecuted()
            }

            is ScrollCommand.ScrollToIndex -> {
                // nothing
            }
        }
    }
}

private fun getItemIndexForRecord(
    recordId: RecordId,
    pagedData: PagedData<Record>,
    needShowAddButton: Boolean
): Int {
    val index = pagedData.list.indexOfFirst { it.id == recordId }
    return if (index != -1 && needShowAddButton) {
        index + 1 // дополнительный сдвиг индекса, если сверху есть кнопка "Добавить запись"
    } else {
        index
    }
}

private fun needShowDate(
    record: Record,
    previousRecord: Record?,
    needShowDateOnTopOfAddButton: Boolean
): Boolean {
    return when {
        previousRecord != null -> record.creationDate != previousRecord.creationDate
        else -> !needShowDateOnTopOfAddButton
    }
}

@Preview(showSystemUi = true)
@Composable
fun RecordListUiPreview() {
    AppTheme {
        RecordListUi(FakeRecordListComponent())
    }
}