package com.metacards.metacards.features.layout.domain

import android.os.Parcelable
import com.metacards.metacards.core.localization.ui.LocalizableString
import kotlinx.parcelize.Parcelize

@JvmInline
@Parcelize
value class LayoutId(val value: String) : Parcelable

data class PredefinedLayout(
    val id: LayoutId,
    val name: LocalizableString,
    val coverUrl: String,
    val isFree: Boolean,
    val questions: List<PredefinedQuestion>
) {
    companion object {
        fun mock(): PredefinedLayout {
            return PredefinedLayout(
                id = LayoutId(""),
                name = LocalizableString.createNonLocalizable("Layout"),
                coverUrl = "",
                isFree = true,
                questions = listOf()
            )
        }
    }
}

data class PredefinedLayoutWithAvailable(
    val layout: PredefinedLayout,
    val isAvailable: Boolean
) {
    companion object {
        fun mock(): PredefinedLayoutWithAvailable {
            return PredefinedLayoutWithAvailable(
                layout = PredefinedLayout.mock(),
                isAvailable = true
            )
        }
    }
}