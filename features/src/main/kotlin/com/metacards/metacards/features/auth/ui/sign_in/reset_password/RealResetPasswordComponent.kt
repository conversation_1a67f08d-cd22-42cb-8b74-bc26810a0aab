package com.metacards.metacards.features.auth.ui.sign_in.reset_password

import android.os.Parcelable
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.router.stack.childStack
import com.arkivanov.decompose.router.stack.push
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.user.domain.Email
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.ResourceFormatted
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.toStateFlow
import com.metacards.metacards.features.R
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.auth.createEmailEnterComponent
import com.metacards.metacards.features.auth.createSentMessageToEmailComponent
import com.metacards.metacards.features.auth.domain.email.ResetPasswordInteractor
import com.metacards.metacards.features.auth.ui.auth_type.email.EmailEnterComponent
import com.metacards.metacards.features.auth.ui.auth_type.email.message.SentMessageToEmailComponent
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.parcelize.Parcelize

class RealResetPasswordComponent(
    componentContext: ComponentContext,
    private val componentFactory: ComponentFactory,
    private val resetPasswordInteractor: ResetPasswordInteractor,
    private val errorHandler: ErrorHandler,
    private val analyticsService: AnalyticsService,
    private val onOutput: (ResetPasswordComponent.Output) -> Unit
) : ComponentContext by componentContext, ResetPasswordComponent {
    private val navigation = StackNavigation<ChildConfig>()

    override val childStack = childStack(
        source = navigation,
        initialConfiguration = ChildConfig.EmailEnter,
        handleBackButton = true,
        childFactory = ::createChild
    ).toStateFlow(lifecycle)

    override fun onCloseButtonClick() {
        analyticsService.logEvent(AnalyticsEvent.AuthMailOpenEmailCloseEvent)
        onOutput(ResetPasswordComponent.Output.CloseScreenRequested)
    }

    private fun createChild(
        config: ChildConfig,
        componentContext: ComponentContext
    ): ResetPasswordComponent.Child = when (config) {
        is ChildConfig.EmailEnter -> {
            ResetPasswordComponent.Child.EmailEnter(
                componentFactory.createEmailEnterComponent(
                    componentContext,
                    ::onEmailEnterOutput,
                    isSignIn = true,
                    isResetPassword = true
                )
            )
        }

        is ChildConfig.SentMessageToEmail -> {
            ResetPasswordComponent.Child.SentMessageToEmail(
                componentFactory.createSentMessageToEmailComponent(
                    componentContext,
                    StringDesc.Resource(R.string.reset_password_sent_email_title),
                    StringDesc.ResourceFormatted(
                        R.string.reset_password_sent_email_text,
                        config.email.value
                    ),
                    false,
                    ::onSentMessageToEmailOutput
                )
            )
        }
    }

    private fun onEmailEnterOutput(output: EmailEnterComponent.Output) {
        when (output) {
            is EmailEnterComponent.Output.NextButtonPressed -> {
                componentScope.safeLaunch(errorHandler) {
                    resetPasswordInteractor.execute(output.email)
                    navigation.push(ChildConfig.SentMessageToEmail(output.email))
                }
            }

            EmailEnterComponent.Output.SignInRequested -> {
                onOutput(ResetPasswordComponent.Output.SignUpRequested)
            }

            EmailEnterComponent.Output.SignUpRequested -> Unit
            is EmailEnterComponent.Output.WebViewRequested -> {
                onOutput(ResetPasswordComponent.Output.WebViewRequested(output.url))
            }

            EmailEnterComponent.Output.GoBackRequested ->
                onOutput(ResetPasswordComponent.Output.GoBackRequested)
        }
    }

    private fun onSentMessageToEmailOutput(output: SentMessageToEmailComponent.Output) {
        when (output) {
            SentMessageToEmailComponent.Output.AuthScreenRequested -> {
                onOutput(ResetPasswordComponent.Output.CloseScreenRequested)
            }

            SentMessageToEmailComponent.Output.MainScreenRequested -> {
                onOutput(ResetPasswordComponent.Output.MainScreenRequested)
            }
        }
    }

    sealed interface ChildConfig : Parcelable {

        @Parcelize
        data object EmailEnter : ChildConfig

        @Parcelize
        data class SentMessageToEmail(val email: Email) : ChildConfig
    }
}