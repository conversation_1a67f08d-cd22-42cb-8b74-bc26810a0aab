package com.metacards.metacards.features.deck.ui

import androidx.compose.animation.core.tween
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.compose.AsyncImagePainter
import coil.decode.ImageDecoderDecoder
import coil.request.ImageRequest
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.placeholder.PlaceholderHighlight
import com.metacards.metacards.core.widget.placeholder.placeholder
import com.metacards.metacards.core.widget.placeholder.shimmer
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardId

@Immutable
object MetaCardDefaults {
    private val horizontalPadding = 16.dp
    private val borderWidth = 0.5.dp
    private val borderColor = CustomTheme.colors.stroke.secondary

    @Stable
    val aspectRatio = 0.666f

    @Stable
    val shape = RoundedCornerShape(30.dp)

    @Stable
    val paddingValues = PaddingValues(horizontal = horizontalPadding)

    @Stable
    val borderStroke = BorderStroke(borderWidth, borderColor)

    @Stable
    const val CROSSFADE_DURATION_MILLIS = 200
}

@Composable
fun MetaCard(
    card: Card,
    onCardClick: (Card) -> Unit,
    modifier: Modifier = Modifier,
    paddingValues: PaddingValues = MetaCardDefaults.paddingValues,
    shape: Shape = MetaCardDefaults.shape,
    borderStroke: BorderStroke = MetaCardDefaults.borderStroke,
    lockedPlaceholder: @Composable BoxScope.() -> Unit = {},
    shouldShowGif: Boolean = false,
) {
    MetaCard(
        image = if (shouldShowGif && card.gifUrl != null) card.gifUrl else card.imageUrl,
        modifier = modifier,
        onCardClick = { onCardClick(card) },
        paddingValues = paddingValues,
        shape = shape,
        borderStroke = borderStroke,
        lockedPlaceholder = lockedPlaceholder,
        isLocked = card.id == CardId.LOCKED_CARD_ID,
    )
}

/**
 * @param image - anything that is supported by Coil
 */
@Composable
fun MetaCard(
    image: Any?,
    modifier: Modifier = Modifier,
    onCardClick: (() -> Unit)? = null,
    paddingValues: PaddingValues = MetaCardDefaults.paddingValues,
    shape: Shape = MetaCardDefaults.shape,
    borderStroke: BorderStroke = MetaCardDefaults.borderStroke,
    lockedPlaceholder: @Composable BoxScope.() -> Unit = {},
    isLocked: Boolean = false,
) {
    val cardModifier = modifier
        .aspectRatio(MetaCardDefaults.aspectRatio, matchHeightConstraintsFirst = true)
        .padding(paddingValues)
        .clip(shape)
        .border(borderStroke, shape)

    if (isLocked) {
        Box(modifier = cardModifier) {
            lockedPlaceholder()
        }
    } else {
        var isLoading by remember { mutableStateOf(false) }
        AsyncImage(
            modifier = cardModifier
                .clickable(
                    enabled = onCardClick != null,
                    onClick = onCardClick ?: {}
                )
                .placeholder(
                    visible = isLoading,
                    color = CustomTheme.colors.background.placeholder,
                    shape = RoundedCornerShape(8.dp),
                    highlight = PlaceholderHighlight.shimmer(
                        highlightColor = CustomTheme.colors.system.invert.copy(alpha = 0.6f)
                    ),
                    placeholderFadeTransitionSpec = { tween() },
                    contentFadeTransitionSpec = { tween() }
                ),
            model = ImageRequest.Builder(LocalContext.current)
                .data(image)
                .decoderFactory(ImageDecoderDecoder.Factory())
                .crossfade(MetaCardDefaults.CROSSFADE_DURATION_MILLIS)
                .build(),
            contentDescription = null,
            contentScale = ContentScale.FillBounds,
            onState = { isLoading = it is AsyncImagePainter.State.Loading },
        )
    }
}