package com.metacards.metacards.features.home.ui.widget

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.desc

@Composable
fun ActionButton(
    text: StringDesc,
    modifier: Modifier = Modifier,
    textAlpha: Float = 1f,
    borderStroke: BorderStroke? = null,
    leadingIcon: @Composable RowScope.() -> Unit = {},
    backgroundColor: Color = CustomTheme.colors.button.small,
) {
    Surface(
        modifier = modifier,
        shape = RoundedCornerShape(30.dp),
        color = backgroundColor,
        border = borderStroke
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 24.dp, vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            leadingIcon()

            Text(
                modifier = Modifier
                    .alpha(textAlpha)
                    .padding(start = 4.dp),
                text = text.localizedByLocal(),
                textAlign = TextAlign.Center,
                style = CustomTheme.typography.button.small,
                color = CustomTheme.colors.text.caption
            )
        }
    }
}

@Preview
@Composable
fun ActionButtonPreview() {
    AppTheme {
        ActionButton(text = "Action".desc())
    }
}
