package com.metacards.metacards.features.root.ui

import com.metacards.metacards.core.bottom_sheet.BottomSheetControl
import com.metacards.metacards.core.bottom_sheet.FakeBottomSheetControl
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.FakeDialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.FakeDefaultDialogComponent
import com.metacards.metacards.core.localization.domain.AppLanguage
import com.metacards.metacards.core.message.ui.FakeMessageComponent
import com.metacards.metacards.core.utils.createFakeChildStackStateFlow
import com.metacards.metacards.features.auth.ui.FakeAuthComponent
import com.metacards.metacards.features.layout.ui.question_for_layout.FakeQuestionForLayoutComponent
import com.metacards.metacards.features.layout.ui.question_for_layout.QuestionForLayoutComponent
import com.metacards.metacards.features.payments.ui.FakePaymentComponent
import com.metacards.metacards.features.payments.ui.PaymentComponent
import com.metacards.metacards.features.tutorial.ui.FakeTutorialMessageComponent
import com.metacards.metacards.features.user.ui.subscription.subscription_bottom_sheet.FakeSubscriptionBottomSheetComponent
import com.metacards.metacards.features.user.ui.subscription.subscription_bottom_sheet.SubscriptionBottomSheetComponent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakeRootComponent(coroutineScope: CoroutineScope) : RootComponent {

    override val childStack = createFakeChildStackStateFlow(
        RootComponent.Child.MainAuth(FakeAuthComponent(coroutineScope))
    )

    override val messageComponent = FakeMessageComponent()
    override val tutorialMessageComponent = FakeTutorialMessageComponent()
    override val paymentComponent: PaymentComponent = FakePaymentComponent()
    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        FakeDialogControl(FakeDefaultDialogComponent())
    override val subscriptionBottomSheetControl:
        BottomSheetControl<SubscriptionBottomSheetComponent.Config, SubscriptionBottomSheetComponent> =
        FakeBottomSheetControl(FakeSubscriptionBottomSheetComponent())
    override val appLanguage: StateFlow<AppLanguage> = MutableStateFlow(AppLanguage.ENG)
    override val bottomSheetControl: BottomSheetControl<QuestionForLayoutComponent.Config, QuestionForLayoutComponent> =
        FakeBottomSheetControl(FakeQuestionForLayoutComponent())
    override val isShouldBecomeInvisible = MutableStateFlow(false)

    override fun onShakeDetected() = Unit
}
