package com.metacards.metacards.features.account.domain.repository

import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.features.account.domain.entity.LessonCategory
import com.metacards.metacards.features.account.domain.entity.LessonCategoryId
import com.metacards.metacards.features.deck.domain.entity.Lesson
import kotlinx.coroutines.flow.Flow

interface LessonRepository {

    fun getCategoryLessonsFlow(categoryId: LessonCategoryId): Flow<LoadableState<List<Lesson>>>

    fun getAllLessonCategoriesFlow(): Flow<List<LessonCategory>>
    fun getLessonCategoryByIdFlow(categoryId: LessonCategoryId): Flow<LoadableState<LessonCategory>>
}