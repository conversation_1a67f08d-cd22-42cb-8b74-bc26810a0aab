package com.metacards.metacards.features.layout.ui.predefined_layout

import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.features.layout.domain.CardSource
import com.metacards.metacards.features.layout.domain.LayoutId
import com.metacards.metacards.features.layout.domain.PredefinedLayoutGroupWithAvailable
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.StateFlow

interface PredefinedLayoutComponent {

    val filteredLayoutGroups: StateFlow<LoadableState<List<PredefinedLayoutGroupWithAvailable>>>
    val groupNames: StateFlow<List<StringDesc>>
    val currentGroupIndex: StateFlow<Int>
    val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent>

    fun onLayoutClick(layoutId: LayoutId)
    fun onBlockContentClick()
    fun onGroupClick(index: Int)

    sealed interface Output {
        data class SubscriptionBottomsheetRequested(val withAdv: Boolean) : Output
        data class LayoutDetailsRequested(val cardSource: CardSource) : Output
        data object AuthScreenRequested : Output
    }
}