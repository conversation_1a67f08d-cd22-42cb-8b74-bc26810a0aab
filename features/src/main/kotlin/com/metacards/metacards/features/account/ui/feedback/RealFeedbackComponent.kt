package com.metacards.metacards.features.account.ui.feedback

import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.doOnDestroy
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.message.domain.Message
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.widget.navigation_bar.NavigationPage
import com.metacards.metacards.features.R
import com.metacards.metacards.features.account.domain.entity.QuestionSubject
import com.metacards.metacards.features.account.domain.interactor.SendFeedbackInteractor
import com.metacards.metacards.features.account.ui.feedback.FeedbackComponent.Companion.MAX_QUESTION_CHARS
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.stateIn

class RealFeedbackComponent(
    componentContext: ComponentContext,
    private val errorHandler: ErrorHandler,
    private val sendFeedbackInteractor: SendFeedbackInteractor,
    private val analyticsService: AnalyticsService,
    private val onOutput: (FeedbackComponent.Output) -> Unit
) : ComponentContext by componentContext, FeedbackComponent {
    override val questionSubjects = MutableStateFlow(emptyList<QuestionSubject>())
    override val selectedQuestionSubject = MutableStateFlow(QuestionSubject(""))
    override val email = MutableStateFlow("")
    override val question = MutableStateFlow("")
    override val emailError = MutableStateFlow(false)
    override val loading = MutableStateFlow(false)
    override val questionError = question.map {
        it.length !in MIN_QUESTION_CHARS..MAX_QUESTION_CHARS
    }.stateIn(componentScope, SharingStarted.Eagerly, true)

    init {
        lifecycle.doOnDestroy {
            analyticsService.logEvent(AnalyticsEvent.FeedbackBackEvent)
        }
    }

    override fun selectQuestionSubject(value: QuestionSubject) {
        selectedQuestionSubject.value = value
    }

    override fun changeEmail(value: String) {
        email.value = value
        emailError.value = false
    }

    override fun changeQuestion(value: String) {
        if (value.length <= MAX_QUESTION_CHARS) question.value = value
    }

    override fun onSendClick() {
        emailError.value = !isValid(email.value)
        analyticsService.logEvent(AnalyticsEvent.FeedbackSendEvent)

        if (!emailError.value) {
            sendFeedbackInteractor.execute(
                subject = selectedQuestionSubject.value.value,
                email = email.value,
                question = question.value
            )
                .onStart { loading.value = true }
                .onEach { result ->
                    if (result.isSuccess()) {
                        onOutput(FeedbackComponent.Output.MainScreenRequested(NavigationPage.Account))
                        onOutput(
                            FeedbackComponent.Output.ShowMessageRequested(
                                message = Message(
                                    text = R.string.feedback_message_success.strResDesc(),
                                    isNotification = true
                                )
                            )
                        )
                    }
                }
                .catch { cause: Throwable -> errorHandler.handleError(cause as Exception) }
                .onCompletion { loading.value = false }
                .launchIn(componentScope)
        }
    }
}

private fun String.isSuccess(): Boolean {
    return Regex("result=(.*?),").find(this)?.groupValues?.get(1) == "Success"
}

private fun isValid(text: String): Boolean = Regex(EMAIL_PATTERN).matches(text)
private const val EMAIL_PATTERN = "[A-Z0-9a-z._-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
private const val MIN_QUESTION_CHARS = 3
