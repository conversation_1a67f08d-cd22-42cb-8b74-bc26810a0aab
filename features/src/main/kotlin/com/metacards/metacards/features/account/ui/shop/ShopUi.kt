package com.metacards.metacards.features.account.ui.shop

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Card
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.ScaleFactor
import androidx.compose.ui.layout.lerp
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.domain.entity.DeckInfoWithCards
import com.metacards.metacards.features.deck.ui.MetaCardDefaults
import com.metacards.metacards.features.deck.ui.deck_info.ENDLESS_PAGER_MULTIPLIER
import com.metacards.metacards.features.deck.ui.deck_info.floorMod
import com.metacards.metacards.features.home.ui.widget.ActionButton
import dev.icerock.moko.resources.PluralsResource
import dev.icerock.moko.resources.desc.PluralFormatted
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.desc
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.flow.SharedFlow
import kotlin.math.absoluteValue

@Composable
fun ShopUi(
    component: ShopComponent,
    modifier: Modifier = Modifier,
) {
    BoxWithFade(modifier, listOfColors = CustomTheme.colors.gradient.backgroundList) {
        Content(component = component)
    }
}

@Composable
private fun Content(
    component: ShopComponent,
    modifier: Modifier = Modifier,
) {
    val scrollState = rememberScrollState()
    val currentPage by component.currentDeckPosition.collectAsState()
    val decks by component.decksForPurchaseInfoWithCards.collectAsState()

    Column(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(scrollState)
    ) {
        TopNavigationBar(
            title = R.string.account_shop_title.strResDesc(),
            leadingIcon = { BackNavigationItem() },
            modifier = Modifier.padding(bottom = 8.dp)
        )

        if (decks.isNotEmpty()) {
            BoxWithConstraints(modifier = Modifier.fillMaxWidth()) {
                val width = maxWidth
                val padding = width * 0.15f

                DecksCarousel(
                    decks = decks,
                    currentPage = currentPage,
                    scrollCommand = component.scrollToPageCommand,
                    onNewDeckSelected = component::onDeckSwipe,
                    onDeckClick = component::onDeckClick,
                    padding = padding,
                    modifier = Modifier.padding(bottom = 24.dp)
                )
            }
        }
// Скрыто по требованию заказчика
//        Text(
//            text = R.string.account_shop_buy_text.strResDesc().localizedByLocal(),
//            color = CustomTheme.colors.text.secondary,
//            style = CustomTheme.typography.caption.medium,
//            textAlign = TextAlign.Center,
//            modifier = Modifier
//                .align(Alignment.CenterHorizontally)
//                .padding(bottom = 8.dp)
//        )
//
//        MetaSecondaryButton(
//            text = R.string.account_shop_button_to_website.strResDesc().localizedByLocal(),
//            onClick = component::onGoToWebSiteClick,
//            modifier = Modifier
//                .fillMaxWidth()
//                .padding(horizontal = 16.dp)
//                .padding(bottom = 100.dp)
//        )
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun DecksCarousel(
    decks: List<DeckInfoWithCards>,
    currentPage: Int,
    scrollCommand: SharedFlow<Int>,
    onNewDeckSelected: (Int) -> Unit,
    onDeckClick: (DeckId) -> Unit,
    padding: Dp,
    modifier: Modifier = Modifier,
) {
    val pageCount = ENDLESS_PAGER_MULTIPLIER * decks.size
    val startIndex = pageCount / 2
    var firstComposition by remember { mutableStateOf(true) }

    val pagerState = rememberPagerState(
        initialPage = startIndex + currentPage,
        initialPageOffsetFraction = 0f
    ) { pageCount }

    LaunchedEffect(key1 = pagerState.currentPage) {
        if (firstComposition) {
            firstComposition = false
            return@LaunchedEffect
        }

        val index = (pagerState.currentPage - startIndex).floorMod(decks.count())
        if (currentPage != index) {
            onNewDeckSelected(index)
        }
    }

    LaunchedEffect(key1 = Unit) {
        scrollCommand.collect { index ->
            if (currentPage != index) {
                pagerState.animateScrollToPage(startIndex + index)
            }
        }
    }

    HorizontalPager(
        state = pagerState,
        pageSpacing = 16.dp,
        contentPadding = PaddingValues(horizontal = padding),
        modifier = modifier
    ) { index ->
        val itemIndex = (index - startIndex).floorMod(decks.count())
        val deck = decks[itemIndex]

        Box(
            modifier = Modifier
                .graphicsLayer {
                    val direction = pagerState.currentPage - index
                    val pageOffset =
                        (direction + pagerState.currentPageOffsetFraction).absoluteValue

                    val scale = lerp(
                        start = ScaleFactor(0.9f, 0.9f),
                        stop = ScaleFactor(1f, 1f),
                        fraction = 1f - pageOffset.coerceIn(0f, 1f)
                    )

                    scaleX = scale.scaleX
                    scaleY = scale.scaleY
                    translationX = (1 - scale.scaleX) * direction * size.width / 2f
                }
        ) {
            DeckCarouselCard(
                deck = deck,
                modifier = Modifier
                    .clip(RoundedCornerShape(30.dp))
                    .clickable { onDeckClick(deck.deckInfo.deckId) }
            )
        }
    }
}

@Composable
private fun DeckCarouselCard(
    deck: DeckInfoWithCards,
    modifier: Modifier = Modifier,
) {
    Card(
        shape = RoundedCornerShape(30.dp),
        elevation = 0.dp,
        border = BorderStroke(width = 0.5.dp, color = CustomTheme.colors.stroke.secondary),
        modifier = modifier.aspectRatio(MetaCardDefaults.aspectRatio)
    ) {
        Box(modifier = Modifier.fillMaxSize()) {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(deck.deckInfo.coverUrl)
                    .crossfade(MetaCardDefaults.CROSSFADE_DURATION_MILLIS)
                    .build(),
                contentScale = ContentScale.Crop,
                contentDescription = null,
                modifier = Modifier.fillMaxSize()
            )

            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier
                    .fillMaxWidth()
                    .align(Alignment.TopCenter)
                    .padding(all = 16.dp)
            ) {
                deck.deckInfo.tags.firstOrNull()?.let { tag ->
                    ActionButton(text = tag.localizedByLocal().desc())
                }

                if (deck.deckInfo.hasAR) {
                    Surface(
                        shape = CircleShape,
                        color = CustomTheme.colors.button.small
                    ) {
                        Text(
                            modifier = Modifier.padding(all = 4.dp),
                            text = R.string.account_shop_deck_has_ar.strResDesc()
                                .localizedByLocal(),
                            textAlign = TextAlign.Center,
                            style = CustomTheme.typography.button.small,
                            color = CustomTheme.colors.text.caption
                        )
                    }
                }
            }

            Surface(
                color = CustomTheme.colors.background.modal,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
            ) {
                Column(
                    verticalArrangement = Arrangement.spacedBy(4.dp),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(all = 20.dp)
                ) {
                    Text(
                        text = deck.deckInfo.name.localizedByLocal(),
                        color = CustomTheme.colors.text.caption,
                        style = CustomTheme.typography.caption.bigSemiBold
                    )

                    Row(
                        horizontalArrangement = Arrangement.SpaceBetween,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(
                            text = StringDesc.PluralFormatted(
                                PluralsResource(R.plurals.account_shop_cards_in_deck),
                                deck.cardCount,
                                deck.cardCount
                            )
                                .localizedByLocal(),
                            color = CustomTheme.colors.text.secondary,
                            style = CustomTheme.typography.caption.medium
                        )

                        Text(
                            text = if (deck.deckAvailable) {
                                R.string.account_shop_deck_is_available.strResDesc()
                                    .localizedByLocal()
                            } else {
                                deck.deckInfo.price.toDecimalString()
                            },
                            color = CustomTheme.colors.text.caption,
                            style = CustomTheme.typography.caption.medium
                        )
                    }
                }
            }
        }
    }
}

@Preview
@Composable
fun ShopUiPreview() {
    AppTheme {
        ShopUi(component = FakeShopComponent())
    }
}