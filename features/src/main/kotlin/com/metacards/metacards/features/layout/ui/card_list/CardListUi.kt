package com.metacards.metacards.features.layout.ui.card_list

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.animateDp
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.updateTransition
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.utils.keyboardAsState
import com.metacards.metacards.core.utils.zoomAndDrag
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.button.MetaTransparentButton
import com.metacards.metacards.core.widget.navigation_bar.IconNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.LoadingIconNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.core.widget.text_field.MetaTextField
import com.metacards.metacards.core.widget.text_field.MetaTextFieldDefaults
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.ui.MetaCard
import com.metacards.metacards.features.layout.ui.select_card.widget.HintBlock
import com.metacards.metacards.features.tutorial.ui.MessagePopupContent
import dev.icerock.moko.resources.desc.desc
import dev.icerock.moko.resources.desc.strResDesc
import com.metacards.metacards.core.R as CoreR

private const val CARD_COMMENT_INPUT_MIN_LINES = 1
private const val CARD_COMMENT_INPUT_MAX_LINES = 3
private const val TEXT_LINE_HEIGHT_DP = 18
private const val CARD_ANIMATION_DURATION_MILLIS = 50
private const val CARD_ANIMATION_DELAY_MILLIS = 100

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun CardListUi(
    component: CardListComponent,
    modifier: Modifier = Modifier
) {
    val configuration = LocalConfiguration.current
    val focusManager = LocalFocusManager.current
    val density = LocalDensity.current
    val cardList by component.selectedCards.collectAsState()
    val hintsList by component.cardHints.collectAsState()
    val pageCount by component.pageCount.collectAsState()
    val lastPage by remember { derivedStateOf { pageCount - 1 } }
    var isFirstComposition by remember { mutableStateOf(true) }
    val tutorialMessage by component.tutorialMessage.collectAsState()

    val pagerState = rememberPagerState(
        initialPage = 0,
        initialPageOffsetFraction = 0f
    ) {
        cardList.size
    }
    var commentTextFieldHasFocus by remember { mutableStateOf(false) }

    val transition = updateTransition(
        targetState = commentTextFieldHasFocus,
        label = "cardListUiTransition"
    )

    val isShareLoading by component.isShareLoading.collectAsState()

    val commentTextFieldMaxLines by remember {
        derivedStateOf {
            if (commentTextFieldHasFocus) {
                CARD_COMMENT_INPUT_MAX_LINES
            } else {
                CARD_COMMENT_INPUT_MIN_LINES
            }
        }
    }
    val cardShapeCornerRadius by transition.animateDp(
        targetValueByState = { if (it) 16.dp else 30.dp },
        label = "cardShapeCornerRadius",
        transitionSpec = {
            tween(
                easing = LinearEasing,
                durationMillis = CARD_ANIMATION_DURATION_MILLIS,
                delayMillis = CARD_ANIMATION_DELAY_MILLIS
            )
        }
    )
    val cardHorizontalPadding by transition.animateDp(
        targetValueByState = { if (it) 4.dp else 16.dp },
        label = "cardHorizontalPadding",
        transitionSpec = {
            tween(
                easing = LinearEasing,
                durationMillis = CARD_ANIMATION_DURATION_MILLIS,
                delayMillis = CARD_ANIMATION_DELAY_MILLIS
            )
        }
    )
    val textFieldHeight by animateDpAsState(
        targetValue = (commentTextFieldMaxLines * TEXT_LINE_HEIGHT_DP).dp,
        label = "textFieldHeight"
    )

    val isKeyboardOpen by keyboardAsState()
    val ime = LocalSoftwareKeyboardController.current

    LaunchedEffect(isKeyboardOpen) {
        if (!isKeyboardOpen) focusManager.clearFocus()
    }

    LaunchedEffect(key1 = lastPage) {
        if (isFirstComposition) {
            isFirstComposition = false
            return@LaunchedEffect
        }
        pagerState.animateScrollToPage(lastPage)
    }

    var previousPage by remember { mutableIntStateOf(0) }
    LaunchedEffect(Unit) {
        snapshotFlow { pagerState.currentPage }.collect { currentPage ->
            if (currentPage != previousPage) {
                focusManager.clearFocus(true)
                ime?.hide()
            }
            previousPage = currentPage
        }
    }

    BoxWithFade(
        modifier = modifier
            .fillMaxSize()
            .pointerInput(Unit) { detectTapGestures { focusManager.clearFocus() } },
        listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        Column(modifier = Modifier.fillMaxSize()) {
            TopNavigationBar(
                title = if (component.cardListViewData.isDailyCard) {
                    R.string.random_card_deck_title.strResDesc()
                } else {
                    "".desc()
                },
                trailingContent = {
                    val isFavorite = cardList[pagerState.currentPage].isFavorite
                    val iconResource =
                        if (isFavorite) R.drawable.ic_24_favourite else R.drawable.ic_24_no_favourite
                    val currentCardUrl = cardList[pagerState.currentPage].card.imageUrl

                    LoadingIconNavigationItem(
                        paddingValues = PaddingValues(0.dp, 0.dp, 12.dp, 0.dp),
                        isLoading = isShareLoading,
                        iconRes = R.drawable.ic_24_share_outlined,
                        onClick = {
                            component.onToggleShare(currentCardUrl, pagerState.currentPage)
                        }
                    )

                    IconNavigationItem(
                        iconRes = iconResource,
                        onClick = {
                            component.onToggleFavorite(
                                pagerState.currentPage,
                                !isFavorite
                            )
                        }
                    )
                },
                modifier = Modifier.fillMaxWidth()
            )

            HorizontalPager(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .zoomAndDrag(),
                state = pagerState
            ) { page ->
                val cardWithFavoriteAndComment = cardList[page]

                Column {
                    component.cardListViewData.questionText?.let {
                        Text(
                            text = it.localizedByLocal(),
                            style = CustomTheme.typography.body.secondary,
                            color = CustomTheme.colors.text.primary,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 24.dp)
                                .padding(top = 4.dp, bottom = 16.dp)
                        )
                    }

                    MetaCard(
                        card = cardWithFavoriteAndComment.card,
                        onCardClick = {
                            focusManager.clearFocus(true)
                            ime?.hide()
                            component.onCardClick(it)
                        },
                        paddingValues = PaddingValues(horizontal = cardHorizontalPadding),
                        shape = RoundedCornerShape(cardShapeCornerRadius),
                        shouldShowGif = component.cardListViewData.isDailyCard,
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                    )

                    hintsList?.get(page)?.let { hintWithExpanded ->
                        HintBlock(
                            card = cardWithFavoriteAndComment.card,
                            defaultCardHint = hintWithExpanded.hint,
                            isExpanded = hintWithExpanded.isExpanded,
                            isDailyCard = component.cardListViewData.isDailyCard,
                            onExpansion = { component.onHintExpand(page, it) },
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 24.dp)
                        )
                    }

                    if (!component.cardListViewData.isDailyCard) {
                        MetaTextField(
                            value = cardWithFavoriteAndComment.comment,
                            singleLine = false,
                            onValueChanged = { value ->
                                component.onCardCommentValueChanged(
                                    page,
                                    value
                                )
                            },
                            minLines = CARD_COMMENT_INPUT_MIN_LINES,
                            maxLines = commentTextFieldMaxLines,
                            onFocusChanged = { commentTextFieldHasFocus = it.isFocused },
                            placeholder = R.string.record_card_list_card_comment_placeholder.strResDesc()
                                .localizedByLocal(),
                            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                            heightMin = textFieldHeight +
                                    MetaTextFieldDefaults.InnerPaddings.calculateBottomPadding() +
                                    MetaTextFieldDefaults.InnerPaddings.calculateTopPadding(),
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp, horizontal = 16.dp),
                            keyboardActions = KeyboardActions(onDone = { focusManager.clearFocus() })
                        )
                    }
                }
            }

            AnimatedVisibility(
                visible = !commentTextFieldHasFocus,
                enter = fadeIn(),
                exit = fadeOut()
            ) {
                CardListButtons(component)
            }
        }

        tutorialMessage?.let {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(CustomTheme.colors.background.disabledBackground)
                    .clickable(enabled = false) { },
                contentAlignment = Alignment.Center
            ) {
                MessagePopupContent(tutorialMessage = it)
            }
        }
    }
}

@Composable
private fun CardListButtons(
    component: CardListComponent,
    modifier: Modifier = Modifier
) {
    val focusManager = LocalFocusManager.current
    val isPredefined by remember { mutableStateOf(component.cardListViewData.isPredefined) }
    val isLastPredefined by remember { mutableStateOf(component.cardListViewData.isLastPredefined) }
    val canSelectMoreCard by component.canSelectMoreCard.collectAsState()

    if (!component.cardListViewData.isDailyCard) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            modifier = modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp, horizontal = 16.dp)
        ) {
            MetaAccentButton(
                modifier = Modifier.weight(1f),
                text = if (isPredefined && !isLastPredefined) {
                    CoreR.string.common_proceed.strResDesc().localizedByLocal()
                } else {
                    R.string.deck_layout_finish.strResDesc().localizedByLocal()
                },
                onClick = component::onFinishClick
            )

            when (canSelectMoreCard) {
                CardListComponent.CanSelectCard.Can -> {
                    MetaTransparentButton(
                        modifier = Modifier.weight(1f),
                        text = R.string.deck_layout_next_card.strResDesc().localizedByLocal(),
                        onClick = {
                            focusManager.clearFocus()
                            component.onMoreCardClick()
                        }
                    )
                }

                CardListComponent.CanSelectCard.CanLast -> {
                    MetaTransparentButton(
                        modifier = Modifier.weight(1f),
                        text = R.string.deck_layout_last_card.strResDesc().localizedByLocal(),
                        onClick = {
                            focusManager.clearFocus()
                            component.onMoreCardClick()
                        }
                    )
                }

                CardListComponent.CanSelectCard.Cant -> {}
            }
        }
    } else {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            modifier = modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp, horizontal = 16.dp)
        ) {
            MetaAccentButton(
                modifier = Modifier.fillMaxWidth(),
                text = if (component.cardListViewData.recordId == null) {
                    R.string.random_card_create_record
                } else {
                    R.string.random_card_go_to_record
                }.strResDesc().localizedByLocal(),
                onClick = component::onAddEntryClick
            )

            MetaTransparentButton(
                modifier = Modifier.fillMaxWidth(),
                text = CoreR.string.common_close.strResDesc().localizedByLocal(),
                onClick = component::onCloseClick
            )
        }
    }
}

@Preview
@Composable
fun CardListUiPreview() {
    AppTheme {
        CardListUi(component = FakeCardListComponent())
    }
}
