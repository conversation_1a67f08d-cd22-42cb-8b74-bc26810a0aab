package com.metacards.metacards.features.auth.ui.sign_in

import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.core.user.domain.Email
import com.metacards.metacards.features.auth.ui.auth_type.email.EmailEnterComponent
import com.metacards.metacards.features.auth.ui.auth_type.pass.PasswordEnterComponent
import com.metacards.metacards.features.auth.ui.sign_in.reset_password.ResetPasswordComponent
import kotlinx.coroutines.flow.StateFlow

interface SignInComponent {
    val childStack: StateFlow<ChildStack<*, Child>>
    val toolbarVisibility: StateFlow<Boolean>

    sealed interface Child {
        class EmailEnter(val component: EmailEnterComponent) : Child
        class PasswordEnter(val component: PasswordEnterComponent) : Child
        class ResetPassword(val component: ResetPasswordComponent) : Child
    }

    sealed interface Output {
        data object SignUpRequested : Output
        data object MainScreenRequested : Output
        data class EmailVerificationRequested(val email: Email) : Output
        data class WebViewRequested(val url: String) : Output
        data object GoBackRequested : Output
    }
}