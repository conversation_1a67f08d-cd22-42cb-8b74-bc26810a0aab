package com.metacards.metacards.features.special_deck.ui.card_details

import androidx.compose.ui.graphics.ImageBitmap
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCard
import com.metacards.metacards.features.special_deck.domain.entity.UserSpecialDeckInfo
import kotlinx.coroutines.flow.StateFlow
import kotlin.random.Random

interface SpecialDeckCardDetailsComponent {

    fun onReadyClick()
    fun onCloseClick(isCardReleased: Boolean)
    fun onCardReleased()

    val card: SpecialDeckCard
    val userSpecialDeckInfo: UserSpecialDeckInfo
    val isCardAlreadyReceived: Boolean
    val cardEffectType: CardEffectType
    val blurredImage: StateFlow<ImageBitmap?>
    val normalImage: StateFlow<ImageBitmap?>
    val mirrorImage: StateFlow<ImageBitmap?>
    val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent>

    enum class CardEffectType {
        Scratch, Glass;

        companion object {
            fun random(): CardEffectType {
                val isScratch = Random.nextBoolean()
                return if (isScratch) Scratch else Glass
            }
        }
    }

    sealed interface Output {
        data class CardReleased(val card: SpecialDeckCard) : Output
        data object CardNotReleased : Output
    }
}