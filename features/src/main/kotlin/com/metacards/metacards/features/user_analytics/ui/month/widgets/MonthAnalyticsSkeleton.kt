package com.metacards.metacards.features.user_analytics.ui.month.widgets

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.Divider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.SkeletonElement

@Composable
fun MonthAnalyticsSkeleton(modifier: Modifier = Modifier) {
    Column(modifier.padding(top = 4.dp)) {
        Row(
            Modifier
                .padding(horizontal = 16.dp, vertical = 12.dp)
                .fillMaxWidth()
        ) {
            MonthGraphSkeleton(
                modifier = Modifier
                    .weight(1.0f)
                    .aspectRatio(1.0f)
            )

            Spacer(modifier = Modifier.width(16.dp))

            MonthGraphSkeleton(
                modifier = Modifier
                    .weight(1.0f)
                    .aspectRatio(1.0f)
            )
        }

        Divider(
            modifier = Modifier.padding(top = 12.dp),
            color = CustomTheme.colors.background.primary,
            thickness = 2.dp
        )

        Row(
            Modifier
                .padding(horizontal = 16.dp, vertical = 8.dp)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            repeat(3) {
                SkeletonElement(
                    modifier = Modifier.size(56.dp, 16.dp),
                    cornerRadius = 8.dp
                )
            }
        }

        Row(
            Modifier
                .padding(horizontal = 16.dp)
                .padding(top = 32.dp)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            SkeletonElement(
                modifier = Modifier
                    .height(72.dp)
                    .weight(0.45f),
                cornerRadius = 8.dp
            )

            Spacer(
                modifier = Modifier.weight(0.1f)
            )

            SkeletonElement(
                modifier = Modifier
                    .padding(start = 16.dp)
                    .height(72.dp)
                    .weight(0.45f),
                cornerRadius = 8.dp
            )
        }

        SkeletonElement(
            modifier = Modifier
                .padding(start = 16.dp, top = 40.dp)
                .size(70.dp, 16.dp),
            cornerRadius = 8.dp
        )

        repeat(2) {
            SkeletonElement(
                modifier = Modifier
                    .padding(top = 8.dp)
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth()
                    .height(105.dp),
                cornerRadius = 16.dp
            )
        }
    }
}

@Composable
private fun MonthGraphSkeleton(modifier: Modifier) {
    Canvas(modifier) {
        val outerRadius = size.minDimension / 2
        val innerRadius = outerRadius / 3
        val strokeWidth = outerRadius - innerRadius

        drawCircle(
            radius = (outerRadius + innerRadius) / 2,
            color = CustomTheme.colors.background.primary,
            style = Stroke(strokeWidth)
        )
    }
}

@Preview
@Composable
fun MonthAnalyticsSkeletonPreview() {
    AppTheme {
        MonthAnalyticsSkeleton(Modifier.fillMaxSize())
    }
}