package com.metacards.metacards.features.analytics

sealed class AnalyticsEvent(
    val name: String,
    val parameters: Map<String, String>? = null
) {
    /**
     * Welcome
     */
    data object WelcomeFirstNextEvent : AnalyticsEvent(
        name = "welcome_first_next"
    )

    data object WelcomeSecondNextEvent : AnalyticsEvent(
        name = "welcome_second_next"
    )

    data object WelcomeThirdNextEvent : AnalyticsEvent(
        name = "welcome_third_next"
    )

    data object WelcomeRegEvent : AnalyticsEvent(
        name = "welcome_reg"
    )

    data object WelcomeRegSkipEvent : AnalyticsEvent(
        name = "welcome_reg_skip"
    )

    /**
     * Registration
     */
    object RegistrationEvent : AnalyticsEvent(
        name = "registration"
    )

    object RegistrationMailNextEvent : AnalyticsEvent(
        name = "registration_mail_next"
    )

    object RegistrationMailAuthEvent : AnalyticsEvent(
        name = "registration_mail_auth"
    )

    object RegistrationMailPasswordDoneEvent : AnalyticsEvent(
        name = "registration_mail_password_done"
    )

    object RegistrationEmailConfirmCloseEvent : AnalyticsEvent(
        name = "registration_email_confirm_close"
    )

    /**
     * Auth
     */
    object AuthEvent : AnalyticsEvent(
        name = "auth"
    )

    object AuthGoogleEvent : AnalyticsEvent(
        name = "auth_google"
    )

    object AuthGoogleDoneEvent : AnalyticsEvent(
        name = "auth_google_done"
    )

    object AuthYandexEvent : AnalyticsEvent(
        name = "auth_yandex"
    )

    object AuthYandexDoneEvent : AnalyticsEvent(
        name = "auth_yandex_done"
    )

    object AuthVkEvent : AnalyticsEvent(
        name = "auth_vk"
    )

    object AuthVkDoneEvent : AnalyticsEvent(
        name = "auth_vk_done"
    )

    object AuthOkEvent : AnalyticsEvent(
        name = "auth_ok"
    )

    object AuthOkDoneEvent : AnalyticsEvent(
        name = "auth_ok_done"
    )

    object AuthFbEvent : AnalyticsEvent(
        name = "auth_fb"
    )

    object AuthFbDoneEvent : AnalyticsEvent(
        name = "auth_fb_done"
    )

    object AuthMailNextEvent : AnalyticsEvent(
        name = "auth_mail_next"
    )

    object AuthMailRegEvent : AnalyticsEvent(
        name = "auth_mail_reg"
    )

    object AuthMailPasswordNextEvent : AnalyticsEvent(
        name = "auth_mail_password_next"
    )

    object AuthMailDoneEvent : AnalyticsEvent(
        name = "auth_mail_done"
    )

    object AuthMailForgetPassword : AnalyticsEvent(
        name = "auth_mail_forget_password"
    )

    object AuthMailForgetPasswordSend : AnalyticsEvent(
        name = "auth_mail_forget_password_send"
    )

    object AuthMailForgetPasswordRegEvent : AnalyticsEvent(
        name = "auth_mail_forget_password_reg"
    )

    object AuthMailOpenEmailCloseEvent : AnalyticsEvent(
        name = "auth_mail_open_email_close"
    )

    /**
     * Tutorial
     */
    object TutorOkEvent : AnalyticsEvent(
        name = "tutor_ok"
    )

    class TutorCloseEvent(stepNumber: Int) : AnalyticsEvent(
        name = "tutor_close",
        parameters = mapOf(
            "step_number" to stepNumber.toString()
        )
    )

    object TutorCloseConfirmEvent : AnalyticsEvent(
        name = "tutor_close_confirm"
    )

    object TutorCloseCancelEvent : AnalyticsEvent(
        name = "tutor_close_cancel"
    )

    object TutorAfterFormOkEvent : AnalyticsEvent(
        name = "tutor_after_form_ok"
    )

    object TutorFinishEvent : AnalyticsEvent(
        name = "tutor_finish"
    )

    /**
     * Banner
     */
    class BannerCloseEvent(bannerType: String, bannerId: String, bannerName: String) :
        AnalyticsEvent(
            name = "banner_close",
            parameters = mapOf(
                "banner_type" to bannerType,
                "banner_id" to bannerId,
                "banner_name" to bannerName
            )
        )

    class BannerTapEvent(bannerType: String, bannerId: String, bannerName: String) : AnalyticsEvent(
        name = "banner_tap",
        parameters = mapOf(
            "banner_type" to bannerType,
            "banner_id" to bannerId,
            "banner_name" to bannerName
        )
    )

    /**
     * Manual layout
     */
    object LayoutTapEvent : AnalyticsEvent(
        name = "layout_tap"
    )

    class LayoutChooseEvent(layoutId: String, layoutName: String) : AnalyticsEvent(
        name = "layout_choose",
        parameters = mapOf(
            "layout_id" to layoutId,
            "layout_name" to layoutName
        )
    )

    class LayoutOneMoreEvent(questionNumber: Int) : AnalyticsEvent(
        name = "layout_one_more",
        parameters = mapOf(
            "question_number" to questionNumber.toString()
        )
    )

    class LayoutNextQuestionEvent(questionNumber: Int) : AnalyticsEvent(
        name = "layout_one_more",
        parameters = mapOf(
            "question_number" to questionNumber.toString()
        )
    )

    object LayoutAddFavCardEvent : AnalyticsEvent(
        name = "layout_add_fav_card"
    )

    object LayoutFinishEvent : AnalyticsEvent(
        name = "layout_finish"
    )

    object LayoutCloseEvent : AnalyticsEvent(
        name = "layout_close"
    )

    object LayoutCloseConfirmEvent : AnalyticsEvent(
        name = "layout_close_confirm"
    )

    object LayoutCloseCancelEvent : AnalyticsEvent(
        name = "layout_close_cancel"
    )

    /**
     * Layout AR
     */
    object LayoutArEvent : AnalyticsEvent(
        name = "layout_ar"
    )

    object LayoutArOneMoreEvent : AnalyticsEvent(
        name = "layout_ar_one_more"
    )

    object LayoutArNextQuestionEvent : AnalyticsEvent(
        name = "layout_ar_next_question"
    )

    object LayoutArAddFavCardEvent : AnalyticsEvent(
        name = "layout_ar_fav_card"
    )

    object LayoutArFinishEvent : AnalyticsEvent(
        name = "layout_ar_finish"
    )

    object LayoutArCloseEvent : AnalyticsEvent(
        name = "layout_ar_close"
    )

    object LayoutArCloseConfirmEvent : AnalyticsEvent(
        name = "layout_ar_close_confirm"
    )

    object LayoutArCloseCancelEvent : AnalyticsEvent(
        name = "layout_ar_close_cancel"
    )

    /**
     * Main deck
     */
    class MainDeckInfoEvent(deckId: String, deckName: String) : AnalyticsEvent(
        name = "main_deck_info",
        parameters = mapOf(
            "deck_id" to deckId,
            "deck_name" to deckName
        )
    )

    class MainDeckChooseEvent(deckId: String, deckName: String) : AnalyticsEvent(
        name = "main_deck_choose",
        parameters = mapOf(
            "deck_id" to deckId,
            "deck_name" to deckName
        )
    )

    /**
     * Auth form
     */
    object AuthFormOkEvent : AnalyticsEvent(
        name = "auth_form_ok"
    )

    object AuthFormCancelEvent : AnalyticsEvent(
        name = "auth_form_cancel"
    )

    /**
     * Bottom sheet subscription
     */
    object SubFormMoreEvent : AnalyticsEvent(
        name = "sub_form_more"
    )

    object SubFormCloseEvent : AnalyticsEvent(
        name = "sub_form_close"
    )

    object SubFormTryEvent : AnalyticsEvent(
        name = "sub_form_try"
    )

    /**
     * Random card
     */
    object RandomCardShowEvent : AnalyticsEvent(
        name = "random_card_show"
    )

    object RandomCardFavAddEvent : AnalyticsEvent(
        name = "random_card_fav_add"
    )

    object RandomCardCreateRecordEvent : AnalyticsEvent(
        name = "random_card_create_record"
    )

    object RandomCardCloseEvent : AnalyticsEvent(
        name = "random_card_close"
    )

    /**
     * Setup manual
     */
    class SetupQuestionNextEvent(isQuestionInput: Boolean) : AnalyticsEvent(
        name = "setup_question_next",
        parameters = mapOf(
            "is_question_input" to isQuestionInput.toString()
        )
    )

    object SetupManualEvent : AnalyticsEvent(
        name = "setup_manual"
    )

    object SetupArEvent : AnalyticsEvent(
        name = "setup_ar"
    )

    object SetupManualCardTapEvent : AnalyticsEvent(
        name = "setup_manual_card_tap"
    )

    object SetupManualCardFavAddEvent : AnalyticsEvent(
        name = "setup_manual_card_fav_add"
    )

    object SetupManualCardOneMoreEvent : AnalyticsEvent(
        name = "setup_manual_card_one_more"
    )

    object SetupManualCloseEvent : AnalyticsEvent(
        name = "setup_manual_close"
    )

    object SetupManualCloseConfirmEvent : AnalyticsEvent(
        name = "setup_manual_close_confirm"
    )

    object SetupManualCloseCancelEvent : AnalyticsEvent(
        name = "setup_manual_close_cancel"
    )

    object SetupManualFinishEvent : AnalyticsEvent(
        name = "setup_manual_finish"
    )

    /**
     * New record
     */
    object NewRecordFavAddEvent : AnalyticsEvent(
        name = "new_record_fav_add"
    )

    class NewRecordSaveEvent(
        setupType: String,
        cardsNumber: Int,
        isQuestionInput: Boolean,
        isCommentInput: Boolean,
        isEnergySet: Boolean,
        isMoodSet: Boolean,
        deckId: String
    ) : AnalyticsEvent(
        name = "new_record_save",
        parameters = mapOf(
            "setup_type" to setupType,
            "cards_number" to cardsNumber.toString(),
            "is_question_input" to isQuestionInput.toString(),
            "is_comment_input" to isCommentInput.toString(),
            "is_energy_set" to isEnergySet.toString(),
            "is_mood_set" to isMoodSet.toString(),
            "deck_id" to deckId
        )
    )

    object NewRecordSkipEvent : AnalyticsEvent(
        name = "new_record_skip"
    )

    object NewRecordSkipConfirmEvent : AnalyticsEvent(
        name = "new_record_skip_confir"
    )

    object NewRecordSkipCancelEvent : AnalyticsEvent(
        name = "new_record_skip"
    )

    /**
     * Setup AR
     */
    object SetupArPrepEvent : AnalyticsEvent(
        name = "setup_ar_prep"
    )

    object SetupArCardTapEvent : AnalyticsEvent(
        name = "setup_ar_card_tap"
    )

    object SetupArCardFavAddEvent : AnalyticsEvent(
        name = "setup_ar_card_fav_add"
    )

    object SetupArOneMoreEvent : AnalyticsEvent(
        name = "setup_ar_one_more"
    )

    object SetupArCloseEvent : AnalyticsEvent(
        name = "setup_ar_close"
    )

    object SetupArCloseConfirmEvent : AnalyticsEvent(
        name = "setup_ar_close_confirm"
    )

    object SetupArCloseCancelEvent : AnalyticsEvent(
        name = "setup_ar_close_cancel"
    )

    object SetupArFinishEvent : AnalyticsEvent(
        name = "setup_ar_finish"
    )

    /**
     * Journal
     */
    object JournalEvent : AnalyticsEvent(
        name = "journal"
    )

    object JournalArchiveEvent : AnalyticsEvent(
        name = "journal_archive"
    )

    data object JournalFavsEvent : AnalyticsEvent(
        name = "journal_favs"
    )

    data object JournalAllEvent : AnalyticsEvent(
        name = "journal_layouts"
    )

    data object JournalTestsEvent : AnalyticsEvent(
        name = "journal_tests"
    )

    object JournalCalendarEvent : AnalyticsEvent(
        name = "journal_calendar"
    )

    object JournalCalendarChooseEvent : AnalyticsEvent(
        name = "journal_calendar_choose"
    )

    object JournalRecordAddArchiveEvent : AnalyticsEvent(
        name = "journal_record_add_archive"
    )

    /**
     * Record
     */
    object RecordTapEvent : AnalyticsEvent(
        name = "record_tap"
    )

    object RecordCommentAddEvent : AnalyticsEvent(
        name = "record_comment_add"
    )

    object RecordEnergyAddEvent : AnalyticsEvent(
        name = "record_energy_add"
    )

    object RecordMoodAddEvent : AnalyticsEvent(
        name = "record_mood_add"
    )

    object RecordAddFavEvent : AnalyticsEvent(
        name = "record_add_fav"
    )

    object ArchiveRecordTapEvent : AnalyticsEvent(
        name = "archive_record_tap"
    )

    object ArchiveRecordRestoreEvent : AnalyticsEvent(
        name = "archive_record_restore"
    )

    object RecordCommentShowEvent : AnalyticsEvent(
        name = "record_comment_show"
    )

    /**
     * Manual record
     */
    object ManualRecordTapEvent : AnalyticsEvent(
        name = "manual_record_tap"
    )

    object ManualRecordCardAddEvent : AnalyticsEvent(
        name = "manual_record_card_add"
    )

    object ManualRecordPhotoTakeEvent : AnalyticsEvent(
        name = "manual_record_photo_take"
    )

    object ManualRecordPhotoRetakeEvent : AnalyticsEvent(
        name = "manual_record_photo_retake"
    )

    object ManualRecordPhotoConfirmEvent : AnalyticsEvent(
        name = "manual_record_photo_confirm"
    )

    object ManualRecordFavAddEvent : AnalyticsEvent(
        name = "manual_record_fav_add"
    )

    class ManualRecordSaveEvent(
        isQuestionInput: Boolean,
        isCommentInput: Boolean,
        isEnergySet: Boolean,
        isMoodSet: Boolean
    ) : AnalyticsEvent(
        name = "manual_record_save",
        parameters = mapOf(
            "is_question_input" to isQuestionInput.toString(),
            "is_comment_input" to isCommentInput.toString(),
            "is_energy_set" to isEnergySet.toString(),
            "is_mood_set" to isMoodSet.toString()
        )
    )

    /**
     * Analytics
     */
    object AnalyticsTapEvent : AnalyticsEvent(
        name = "analytics_tap"
    )

    object AnalyticsWeekEvent : AnalyticsEvent(
        name = "analytics_week"
    )

    object AnalyticsMonthEvent : AnalyticsEvent(
        name = "analytics_month"
    )

    object AnalyticsSetupsEvent : AnalyticsEvent(
        name = "analytics_setups"
    )

    object AnalyticsSetupsRecordTapEvent : AnalyticsEvent(
        name = "analytics_setups_record_tap"
    )

    object AnalyticsSetupsPrevEvent : AnalyticsEvent(
        name = "analytics_setups_prev"
    )

    object AnalyticsSetupsNextEvent : AnalyticsEvent(
        name = "analytics_setups_next"
    )

    object AnalyticsWeekRecordTapEvent : AnalyticsEvent(
        name = "analytics_week_record_tap"
    )

    object AnalyticsWeekPrevEvent : AnalyticsEvent(
        name = "analytics_week_prev"
    )

    object AnalyticsWeekNextEvent : AnalyticsEvent(
        name = "analytics_week_next"
    )

    object AnalyticsMonthRecordTapEvent : AnalyticsEvent(
        name = "analytics_month_record_tap"
    )

    object AnalyticsMonthPrevEvent : AnalyticsEvent(
        name = "analytics_month_prev"
    )

    object AnalyticsMonthNextEvent : AnalyticsEvent(
        name = "analytics_month_next"
    )

    /**
     * Account
     */
    object AccountTapEvent : AnalyticsEvent(
        name = "account_tap"
    )

    object AccountAuthEvent : AnalyticsEvent(
        name = "account_auth"
    )

    object ProfileTapEvent : AnalyticsEvent(
        name = "profile_tap"
    )

    object ProfileLogoutEvent : AnalyticsEvent(
        name = "profile_logout"
    )

    object ProfileDeleteEvent : AnalyticsEvent(
        name = "profile_delete"
    )

    object AccountSubBannerTapEvent : AnalyticsEvent(
        name = "account_sub_banner_tap"
    )

    /**
     * Subscription
     */
    object SubTapEvent : AnalyticsEvent(
        name = "sub_tap"
    )

    object SubBackEvent : AnalyticsEvent(
        name = "sub_back"
    )

    object SubSiteEvent : AnalyticsEvent(
        name = "sub_site"
    )

    object SubSitePurchaseEvent : AnalyticsEvent(
        name = "sub_site_purchase"
    )

    object SubPurchaseEvent : AnalyticsEvent(
        name = "sub_purchase"
    )

    /**
     * Shop
     */
    object ShopTapEvent : AnalyticsEvent(
        name = "shop_tap"
    )

    object ShopToSiteEvent : AnalyticsEvent(
        name = "shop_to_site"
    )

    class ShopDeckTapEvent(deckId: String, deckName: String) : AnalyticsEvent(
        name = "shop_deck_tap",
        parameters = mapOf(
            "deck_id" to deckId,
            "deck_name" to deckName
        )
    )

    class ShopDeckShareEvent(deckId: String, deckName: String) : AnalyticsEvent(
        name = "shop_deck_share",
        parameters = mapOf(
            "deck_id" to deckId,
            "deck_name" to deckName
        )
    )

    object ShopDeckSetupEvent : AnalyticsEvent(
        name = "shop_deck_tap"
    )

    class ShopDeckSiteEvent(deckId: String, deckName: String) : AnalyticsEvent(
        name = "shop_deck_site",
        parameters = mapOf(
            "deck_id" to deckId,
            "deck_name" to deckName
        )
    )

    class ShopDeckSitePurchaseEvent(deckId: String, deckName: String) : AnalyticsEvent(
        name = "shop_deck_site_purchase",
        parameters = mapOf(
            "deck_id" to deckId,
            "deck_name" to deckName
        )
    )

    class ShopDeckPurchaseEvent(deckId: String, deckName: String) : AnalyticsEvent(
        name = "shop_deck_purchase",
        parameters = mapOf(
            "deck_id" to deckId,
            "deck_name" to deckName
        )
    )

    class ShopDeckBackEvent(deckId: String, deckName: String) : AnalyticsEvent(
        name = "shop_deck_back",
        parameters = mapOf(
            "deck_id" to deckId,
            "deck_name" to deckName
        )
    )

    class ShopDeckPhysicalSiteEvent(deckId: String, deckName: String) : AnalyticsEvent(
        name = "shop_deck_physical_site",
        parameters = mapOf(
            "deck_id" to deckId,
            "deck_name" to deckName
        )
    )

    class ShopDeckVideoEvent(deckId: String, deckName: String) : AnalyticsEvent(
        name = "shop_deck_video",
        parameters = mapOf(
            "deck_id" to deckId,
            "deck_name" to deckName
        )
    )

    /**
     * Education
     */
    object EduTapEvent : AnalyticsEvent(
        name = "edu_tap"
    )

    class EduVideoEvent() : AnalyticsEvent(
        name = "edu_video"
    )

    object EduTutorEvent : AnalyticsEvent(
        name = "edu_tutor"
    )

    /**
     * Favorite cards
     */
    object FavCardsTapEvent : AnalyticsEvent(
        name = "fav_cards_tap"
    )

    object FavCardsCardChooseEvent : AnalyticsEvent(
        name = "fav_cards_card_choose"
    )

    object FavCardsCardDeleteEvent : AnalyticsEvent(
        name = "fav_cards_card_delete"
    )

    object FavCardsCardRecordEvent : AnalyticsEvent(
        name = "fav_cards_card_record"
    )

    /**
     * Account settings
     */
    object AccountLangSwitchEvent : AnalyticsEvent(
        name = "account_lang_switch"
    )

    object AccountPushOffEvent : AnalyticsEvent(
        name = "account_push_off"
    )

    object AccountPushOnEvent : AnalyticsEvent(
        name = "account_push_on"
    )

    /**
     * Feedback
     */
    object FeedbackTapEvent : AnalyticsEvent(
        name = "feedback_tap"
    )

    object FeedbackBackEvent : AnalyticsEvent(
        name = "feedback_back"
    )

    object FeedbackSendEvent : AnalyticsEvent(
        name = "feedback_send"
    )

    /**
     * About
     */
    object AccountRateEvent : AnalyticsEvent(
        name = "account_rate"
    )

    class AccountSocialTapEvent(network: String) : AnalyticsEvent(
        name = "account_social_tap",
        parameters = mapOf(
            "network" to network
        )
    )

    object AboutAppTapEvent : AnalyticsEvent(
        name = "about_app_tap"
    )

    object AboutAppTermsEvent : AnalyticsEvent(
        name = "about_app_terms"
    )

    object AboutAppPolicyEvent : AnalyticsEvent(
        name = "about_app_policy"
    )

    object AboutAppCreatorsEvent : AnalyticsEvent(
        name = "about_app_creators"
    )

    class AboutAppCreatorsSocialTapEvent(network: String) : AnalyticsEvent(
        name = "about_app_creators",
        parameters = mapOf(
            "network" to network
        )
    )

    /**
     * Add deck
     */
    object AddDeckTapEvent : AnalyticsEvent(
        name = "add_deck_tap"
    )

    class AddDeckDoneEvent(deckId: String, deckName: String) : AnalyticsEvent(
        name = "add_deck_done",
        parameters = mapOf(
            "deckId" to deckId,
            "deckName" to deckName
        )
    )

    class AddDeckFailEvent(trace: String) : AnalyticsEvent(
        name = "add_deck_fail",
        parameters = mapOf(
            "trace" to trace
        )
    )
}
