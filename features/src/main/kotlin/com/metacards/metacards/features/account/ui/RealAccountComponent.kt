package com.metacards.metacards.features.account.ui

import android.Manifest
import android.os.Build
import androidx.annotation.RequiresApi
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.doOnCreate
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.bottom_sheet.BottomSheetControl
import com.metacards.metacards.core.bottom_sheet.bottomSheetControl
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.createDefaultDialogComponent
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.model.DialogData
import com.metacards.metacards.core.dialog.dialogControl
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.external_apps.ExternalAppService
import com.metacards.metacards.core.localization.data.AppLanguageService
import com.metacards.metacards.core.permissions.PermissionService
import com.metacards.metacards.core.permissions.SinglePermissionResult
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.user.domain.UserProvider
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.features.R
import com.metacards.metacards.features.account.createAccountLanguageComponent
import com.metacards.metacards.features.account.domain.interactor.GetUserSubscriptionToNotificationsInteractor
import com.metacards.metacards.features.account.ui.flow.AccountFlowComponent
import com.metacards.metacards.features.account.ui.language.AccountLanguageComponent
import com.metacards.metacards.features.account.ui.lessons.LessonsComponent
import com.metacards.metacards.features.advbanner.domain.GetAccountScreenAdvBannerInteractor
import com.metacards.metacards.features.advbanner.domain.entity.AccountAdvBannerType
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.auth.domain.SetNotificationSubscriptionInteractor
import com.metacards.metacards.features.special_deck.domain.repository.SpecialDeckRepository
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

class RealAccountComponent(
    componentContext: ComponentContext,
    componentFactory: ComponentFactory,
    private val errorHandler: ErrorHandler,
    private val permissionService: PermissionService,
    private val externalAppService: ExternalAppService,
    userProvider: UserProvider,
    appLanguageService: AppLanguageService,
    private val getUserSubscriptionToNotificationsInteractor: GetUserSubscriptionToNotificationsInteractor,
    private val setNotificationSubscriptionInteractor: SetNotificationSubscriptionInteractor,
    private val analyticsService: AnalyticsService,
    getAccountScreenAdvBannerInteractor: GetAccountScreenAdvBannerInteractor,
    private val specialDeckRepository: SpecialDeckRepository,
    private val onOutput: (AccountComponent.Output) -> Unit
) : ComponentContext by componentContext, AccountComponent {

    companion object {
        private const val NETWORK_INSTAGRAM = "Instargram"
        private const val NETWORK_FACEBOOK = "Facebook"
        private const val NETWORK_INTERNET = "Internet"
    }

    override var notificationToggleState: MutableStateFlow<AccountComponent.NotificationToggleState> =
        MutableStateFlow(AccountComponent.NotificationToggleState.On)

    private lateinit var isNotificationPermissionGranted: MutableStateFlow<Boolean>

    init {
        lifecycle.doOnCreate {
            componentScope.launch {
                val isUserSubscribedToNotifications =
                    getUserSubscriptionToNotificationsInteractor.execute()

                isNotificationPermissionGranted = MutableStateFlow(
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        permissionService.isPermissionGranted(Manifest.permission.POST_NOTIFICATIONS)
                    } else {
                        true
                    }
                )

                notificationToggleState = MutableStateFlow(
                    if (isUserSubscribedToNotifications && isNotificationPermissionGranted.value) {
                        AccountComponent.NotificationToggleState.On
                    } else {
                        AccountComponent.NotificationToggleState.Off
                    }
                )
            }
        }
    }

    private val debounce = Debounce()

    override val user: StateFlow<User?> = userProvider.getUser()

    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        dialogControl(dialogComponentFactory = { config, componentContext, dialogControl ->
            componentFactory.createDefaultDialogComponent(
                componentContext,
                config.data,
                dialogControl::dismiss
            )
        })

    override val appLanguage = appLanguageService.currentAppLanguage

    override val languageBottomSheetControl:
        BottomSheetControl<AccountLanguageComponent.Config, AccountLanguageComponent> =
        bottomSheetControl(
            key = "languageBottomSheetControl",
            bottomSheetComponentFactory = { _, context, _ ->
                componentFactory.createAccountLanguageComponent(
                    context,
                    ::onLanguageOutput
                )
            },
            halfExpandingSupported = false,
            hidingSupported = true,
            handleBackButton = true
        )

    override val accountAdvBanner = getAccountScreenAdvBannerInteractor.execute()
        .stateIn(componentScope, SharingStarted.Eagerly, null)

    override fun onPromocodesClick() {
        if (user.value == null) {
            onOutput(AccountComponent.Output.AuthSuggestingRequested)
        } else {
            navigateTo(AccountFlowComponent.Screen.Promocodes)
        }
    }

    private fun onLanguageOutput(output: AccountLanguageComponent.Output) {
        when (output) {
            is AccountLanguageComponent.Output.DismissRequested -> {
                languageBottomSheetControl.dismiss()
                onOutput(AccountComponent.Output.RecompositionRequested)
            }
        }
    }

    private val goToSettingsDialogConfig by lazy {
        DefaultDialogComponent.Config(
            DialogData(
                title = StringDesc.Resource(R.string.account_main_permission_notification_title),
                message = StringDesc.Resource(R.string.account_main_permission_notification_message),
                buttons = listOf(
                    DialogData.Button(
                        title = StringDesc.Resource(R.string.account_main_permission_notification_button_cancel),
                        action = dialogControl::dismiss
                    ),
                    DialogData.Button(
                        title = StringDesc.Resource(R.string.account_main_permission_notification_button_proceed),
                        action = {
                            dialogControl.dismiss()
                            externalAppService.openAppSettings()
                        }
                    )
                )
            )
        )
    }

    override fun onProfileBlockClick() {
        if (user.value == null) {
            analyticsService.logEvent(AnalyticsEvent.AccountAuthEvent)
            onOutput(AccountComponent.Output.AuthScreenRequested)
        } else {
            analyticsService.logEvent(AnalyticsEvent.ProfileTapEvent)
            navigateTo(AccountFlowComponent.Screen.ProfileFlow)
        }
    }

    override fun onAdvBannerClick(type: AccountAdvBannerType) {
        analyticsService.logEvent(AnalyticsEvent.AccountSubBannerTapEvent)
        when (type) {
            AccountAdvBannerType.SUB -> navigateTo(AccountFlowComponent.Screen.Subscription)
        }
    }

    override fun onSubscriptionClick() {
        analyticsService.logEvent(AnalyticsEvent.SubTapEvent)
        navigateTo(AccountFlowComponent.Screen.Subscription)
    }

    override fun onBuyDecksClick() {
        analyticsService.logEvent(AnalyticsEvent.ShopTapEvent)
        navigateTo(AccountFlowComponent.Screen.Shop)
    }

    override fun onLessonsClick() {
        analyticsService.logEvent(AnalyticsEvent.EduTapEvent)
        navigateTo(AccountFlowComponent.Screen.Lessons(LessonsComponent.Screen.Categories))
    }

    override fun onFavouriteCardsClick() {
        analyticsService.logEvent(AnalyticsEvent.FavCardsTapEvent)
        when (user.value?.subscriptionState) {
            is User.SubscriptionState.None -> onOutput(AccountComponent.Output.PremiumSuggestingScreenRequested)
            is User.SubscriptionState.Ongoing -> {
                analyticsService.logEvent(AnalyticsEvent.FavCardsCardChooseEvent)
                onOutput(AccountComponent.Output.FavoriteCardsRequested)
            }

            null -> onOutput(AccountComponent.Output.AuthSuggestingScreenRequested)
        }
    }

    override fun onNotificationToggleClick(isSubscribed: Boolean) {
        DebounceClick(debounce, "onNotificationToggleClick", errorHandler) {
            notificationToggleState.value = AccountComponent.NotificationToggleState.Loading
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                if (!isPermissionGranted()) {
                    dialogControl.show(goToSettingsDialogConfig)
                    notificationToggleState.value = AccountComponent.NotificationToggleState.Off
                    return@DebounceClick
                }
            }

            setNotificationSubscriptionInteractor.execute(isSubscribed)
            notificationToggleState.value = if (isSubscribed) {
                analyticsService.logEvent(AnalyticsEvent.AccountPushOnEvent)
                AccountComponent.NotificationToggleState.On
            } else {
                analyticsService.logEvent(AnalyticsEvent.AccountPushOffEvent)
                AccountComponent.NotificationToggleState.Off
            }
        }
    }

    override fun onFeedbackClick() {
        analyticsService.logEvent(AnalyticsEvent.FeedbackTapEvent)
        onOutput(
            AccountComponent.Output.AccountFlowScreenRequested(
                AccountFlowComponent.Screen.Feedback
            )
        )
    }

    override fun onLanguageClick() {
        analyticsService.logEvent(AnalyticsEvent.AccountLangSwitchEvent)
        languageBottomSheetControl.show(AccountLanguageComponent.Config)
    }

    override fun onRateClick() {
        analyticsService.logEvent(AnalyticsEvent.AccountRateEvent)
        externalAppService.rateApp()
    }

    override fun onAboutTheAppClick() {
        analyticsService.logEvent(AnalyticsEvent.AboutAppTapEvent)
        navigateTo(AccountFlowComponent.Screen.AboutTheApp)
    }

    override fun onAddYourDeckClick() {
        analyticsService.logEvent(AnalyticsEvent.AddDeckTapEvent)
        if (user.value?.subscriptionState == null) {
            onOutput(AccountComponent.Output.AuthSuggestingScreenRequested)
        } else {
            onOutput(AccountComponent.Output.AddPhysicalDeckRequested)
        }
    }

    override fun onInstagramClick() {
        analyticsService.logEvent(AnalyticsEvent.AccountSocialTapEvent(NETWORK_INSTAGRAM))
        // TODO
    }

    override fun onFacebookClick() {
        analyticsService.logEvent(AnalyticsEvent.AccountSocialTapEvent(NETWORK_FACEBOOK))
        // TODO
    }

    override fun onInternetClick() {
        analyticsService.logEvent(AnalyticsEvent.AccountSocialTapEvent(NETWORK_INTERNET))
        // TODO
    }

    override fun onSpecialDeckClick() {
        componentScope.safeLaunch(errorHandler) {
            if (user.value == null) {
                onOutput(AccountComponent.Output.AuthSuggestingRequested)
            } else {
                specialDeckRepository.getSpecialDecks().firstOrNull()?.let {
                    onOutput(AccountComponent.Output.SpecialDeckRequested(it.id))
                }
            }
        }
    }

    private fun navigateTo(screen: AccountFlowComponent.Screen) {
        onOutput(AccountComponent.Output.AccountFlowScreenRequested(screen))
    }

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private suspend fun isPermissionGranted(): Boolean {
        return permissionService.requestPermission(Manifest.permission.POST_NOTIFICATIONS) is
        SinglePermissionResult.Granted
    }
}
