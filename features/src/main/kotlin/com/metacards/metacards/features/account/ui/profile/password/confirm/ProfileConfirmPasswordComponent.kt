package com.metacards.metacards.features.account.ui.profile.password.confirm

import com.metacards.metacards.core.button.ButtonState
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.StateFlow
import ru.mobileup.kmm_form_validation.control.InputControl

interface ProfileConfirmPasswordComponent {
    val inputControl: InputControl
    val proceedButtonState: StateFlow<ButtonState>
    val toolbarText: StringDesc
    val textFieldHeader: StringDesc
    val textFieldCaption: StringDesc?
    val bottomButtonText: StringDesc

    fun onProceedClick()

    sealed interface Output {
        class PasswordConfirmed(val password: String) : Output
    }
}