package com.metacards.metacards.features.record.data

import android.net.Uri
import androidx.camera.core.ImageCapture
import com.metacards.metacards.features.record.domain.camera.CameraRepository
import com.metacards.metacards.features.record.domain.camera.CardPhoto

class CameraRepositoryImpl(private val cameraDataSource: CameraDataSource) : CameraRepository {
    override fun takePhoto(
        imageCapture: ImageCapture,
        image: (CardPhoto) -> Unit
    ) {
        cameraDataSource.takePhoto(imageCapture = imageCapture, photo = image)
    }

    override fun deletePhoto(uri: Uri) {
        cameraDataSource.deletePhoto(uri)
    }
}
