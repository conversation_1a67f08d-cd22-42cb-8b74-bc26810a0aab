package com.metacards.metacards.features.deck.ui.deck_info

import android.content.Context
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.features.deck.domain.entity.DeckInfoWithCards
import com.metacards.metacards.features.payments.ui.FakePaymentComponent
import com.metacards.metacards.features.payments.ui.PaymentComponent
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow

class FakeDeckInfoComponent : DeckInfoComponent {
    override val currentCardPosition = MutableStateFlow(0)
    override val deckInfo = MutableStateFlow(LoadableState(data = DeckInfoWithCards.mock))
    override val scrollToPageCommand: SharedFlow<Int> = MutableSharedFlow()
    override val actionButtonState: StateFlow<ButtonState> = MutableStateFlow(ButtonState.Enabled)
    override val paymentComponent: PaymentComponent
        get() = FakePaymentComponent()

    override fun onActionButtonClick() = Unit
    override fun onMaterialDeckBuyClick() = Unit
    override fun onCardClick(cardPosition: Int) = Unit
    override fun onCardSwipe(newPosition: Int) = Unit
    override fun onVideoPlay() = Unit
    override fun onShareClick(context: Context) = Unit
    override fun scrollToPage(page: Int) = Unit
}