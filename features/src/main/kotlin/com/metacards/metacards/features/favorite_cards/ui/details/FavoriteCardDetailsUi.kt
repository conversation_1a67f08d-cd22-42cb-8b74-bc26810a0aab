package com.metacards.metacards.features.favorite_cards.ui.details

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.applyStyleForSubstring
import com.metacards.metacards.core.utils.navigationBarsPaddingDp
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.LceWidget
import com.metacards.metacards.core.widget.SkeletonElement
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.IconNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.LoadingIconNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.ui.MetaCard
import com.metacards.metacards.features.favorite_cards.domain.FavoriteCardInfo
import com.metacards.metacards.features.record.domain.Record
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.record.ui.record_list.widgets.RecordCard
import dev.icerock.moko.resources.PluralsResource
import dev.icerock.moko.resources.desc.PluralFormatted
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun FavoriteCardDetailsUi(component: FavoriteCardDetailsComponent, modifier: Modifier = Modifier) {
    val cardInfoState by component.cardInfoState.collectAsState()
    val isShareLoading by component.isShareLoading.collectAsState()

    BoxWithFade(
        modifier = modifier.fillMaxSize(),
        listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        Column {
            TopNavigationBar(
                title = R.string.favorite_card_details_title.strResDesc(),
                leadingIcon = { BackNavigationItem() },
                trailingContent = {
                    LoadingIconNavigationItem(
                        paddingValues = PaddingValues(0.dp, 0.dp, 12.dp, 0.dp),
                        isLoading = isShareLoading,
                        iconRes = R.drawable.ic_24_share_outlined,
                        onClick = component::onToggleShare
                    )
                    IconNavigationItem(
                        iconRes = R.drawable.ic_24_favourite,
                        onClick = component::onFavoriteClick
                    )
                }
            )

            LceWidget(
                state = cardInfoState,
                onRetryClick = { /*TODO: error handling */ },
                loadingProgress = { CardInfoSkeleton() }
            ) { cardInfo, _ ->
                CardInfoContent(cardInfo = cardInfo, onRecordClick = component::onRecordClick)
            }
        }
    }
}

@Composable
private fun CardInfoContent(
    cardInfo: FavoriteCardInfo,
    onRecordClick: (RecordId) -> Unit,
    modifier: Modifier = Modifier,
) {
    LazyColumn(
        modifier = modifier,
        contentPadding = PaddingValues(
            top = 8.dp,
            bottom = 8.dp + navigationBarsPaddingDp
        )
    ) {
        item {
            MetaCard(
                modifier = Modifier.fillMaxWidth(),
                paddingValues = PaddingValues(horizontal = 24.dp),
                image = cardInfo.card.run { gifUrl ?: imageUrl }
            )

            Row(
                Modifier
                    .fillMaxSize()
                    .padding(horizontal = 24.dp)
                    .padding(top = 16.dp),
                horizontalArrangement = Arrangement.Center
            ) {
                Text(
                    modifier = Modifier
                        .alignByBaseline()
                        .padding(start = 8.dp),
                    text = StringDesc.PluralFormatted(
                        PluralsResource(R.plurals.favorite_card_records_count_message),
                        cardInfo.recordsCount,
                        cardInfo.recordsCount
                    )
                        .localizedByLocal()
                        .applyStyleForSubstring(
                            subString = cardInfo.recordsCount.toString(),
                            style = CustomTheme.typography.heading.primary.copy(
                                fontWeight = FontWeight(
                                    400
                                )
                            )
                        ),
                    style = CustomTheme.typography.caption.small,
                    color = CustomTheme.colors.text.primary
                )
            }
        }

        val records = cardInfo.unarchivedRecords

        item {
            if (records.isEmpty()) {
                Text(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 24.dp)
                        .padding(top = 32.dp),
                    text = R.string.favorite_card_empty_records_message.strResDesc()
                        .localizedByLocal(),
                    textAlign = TextAlign.Center,
                    style = CustomTheme.typography.body.primary,
                    color = CustomTheme.colors.text.primary
                )
            } else {
                Text(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 32.dp)
                        .padding(top = 32.dp),
                    text = R.string.favorite_card_records_title.strResDesc().localizedByLocal(),
                    style = CustomTheme.typography.heading.medium,
                    color = CustomTheme.colors.text.primary
                )
            }
        }

        itemsIndexed(
            records,
            key = { _, record -> record.id }
        ) { index, record ->
            RecordCard(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp)
                    .padding(horizontal = 16.dp),
                record = record,
                showDate = needShowDate(
                    record,
                    previousRecord = records.getOrNull(index - 1)
                ),
                revealEnabled = false,
                onClick = { onRecordClick(record.id) }
            )
        }
    }
}

private fun needShowDate(
    record: Record,
    previousRecord: Record?,
): Boolean {
    return previousRecord == null || record.creationDate != previousRecord.creationDate
}

@Composable
private fun CardInfoSkeleton(modifier: Modifier = Modifier) {
    Column(modifier.fillMaxSize()) {
        MetaCard(
            modifier = Modifier
                .padding(top = 8.dp)
                .fillMaxWidth(),
            paddingValues = PaddingValues(horizontal = 24.dp),
            image = R.drawable.bg_card_placeholder
        )

        SkeletonElement(
            modifier = Modifier
                .align(Alignment.CenterHorizontally)
                .padding(top = 16.dp)
                .size(195.dp, 16.dp),
            cornerRadius = 8.dp
        )

        SkeletonElement(
            modifier = Modifier
                .padding(start = 32.dp, top = 40.dp)
                .size(172.dp, 16.dp),
            cornerRadius = 8.dp
        )

        SkeletonElement(
            modifier = Modifier
                .padding(start = 32.dp, top = 16.dp)
                .size(69.dp, 16.dp),
            cornerRadius = 8.dp
        )

        SkeletonElement(
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .padding(top = 8.dp)
                .fillMaxWidth()
                .height(105.dp),
            cornerRadius = 16.dp
        )
    }
}

@Preview(showSystemUi = true)
@Composable
fun FavoriteCardDetailsUiPreview() {
    AppTheme {
        FavoriteCardDetailsUi(component = FakeFavoriteCardDetailsComponent())
    }
}