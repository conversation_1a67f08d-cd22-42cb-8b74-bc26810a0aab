package com.metacards.metacards.features.auth

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.user.domain.Email
import com.metacards.metacards.core.utils.KoinConsts
import com.metacards.metacards.features.auth.data.AuthRepositoryImpl
import com.metacards.metacards.features.auth.data.FirebaseAuthService
import com.metacards.metacards.features.auth.data.GoogleAuthService
import com.metacards.metacards.features.auth.data.GoogleAuthServiceImpl
import com.metacards.metacards.features.auth.data.SSOAuthService
import com.metacards.metacards.features.auth.data.SSOAuthServiceImpl
import com.metacards.metacards.features.auth.domain.AuthUserInteractor
import com.metacards.metacards.features.auth.domain.AuthUserProvider
import com.metacards.metacards.features.auth.domain.AwaitAuthUserInteractor
import com.metacards.metacards.features.auth.domain.CreateUserInteractor
import com.metacards.metacards.features.auth.domain.GetAuthUserInteractor
import com.metacards.metacards.features.auth.domain.LogoutInteractor
import com.metacards.metacards.features.auth.domain.ReloadAuthUserInteractor
import com.metacards.metacards.features.auth.domain.SetNotificationSubscriptionInteractor
import com.metacards.metacards.features.auth.domain.SetOnSSOSignInDismissInteractor
import com.metacards.metacards.features.auth.domain.TryPasswordInteractor
import com.metacards.metacards.features.auth.domain.UpdatePasswordInteractor
import com.metacards.metacards.features.auth.domain.email.EmailAuthRepository
import com.metacards.metacards.features.auth.domain.email.HasAccountByEmailInteractor
import com.metacards.metacards.features.auth.domain.email.ResetPasswordInteractor
import com.metacards.metacards.features.auth.domain.email.SendEmailVerifyMessageInteractor
import com.metacards.metacards.features.auth.domain.email.UpdateEmailInteractor
import com.metacards.metacards.features.auth.domain.sso.google.GoogleAuthRepository
import com.metacards.metacards.features.auth.domain.sso.google.SignInWithGoogleInteractor
import com.metacards.metacards.features.auth.ui.AuthComponent
import com.metacards.metacards.features.auth.ui.RealAuthComponent
import com.metacards.metacards.features.auth.ui.auth_type.email.EmailEnterComponent
import com.metacards.metacards.features.auth.ui.auth_type.email.RealEmailEnterComponent
import com.metacards.metacards.features.auth.ui.auth_type.email.message.RealSentMessageToEmailComponent
import com.metacards.metacards.features.auth.ui.auth_type.email.message.SentMessageToEmailComponent
import com.metacards.metacards.features.auth.ui.auth_type.pass.PasswordEnterComponent
import com.metacards.metacards.features.auth.ui.auth_type.pass.RealPasswordEnterComponent
import com.metacards.metacards.features.auth.ui.auth_type.pass.create_pass.CreatePasswordComponent
import com.metacards.metacards.features.auth.ui.auth_type.pass.create_pass.RealCreatePasswordComponent
import com.metacards.metacards.features.auth.ui.main_auth.MainAuthComponent
import com.metacards.metacards.features.auth.ui.main_auth.RealMainAuthComponent
import com.metacards.metacards.features.auth.ui.sign_in.RealSignInComponent
import com.metacards.metacards.features.auth.ui.sign_in.SignInComponent
import com.metacards.metacards.features.auth.ui.sign_in.reset_password.RealResetPasswordComponent
import com.metacards.metacards.features.auth.ui.sign_in.reset_password.ResetPasswordComponent
import com.metacards.metacards.features.auth.ui.sign_up.RealSignUpComponent
import com.metacards.metacards.features.auth.ui.sign_up.SignUpComponent
import dev.icerock.moko.resources.desc.StringDesc
import org.koin.core.component.get
import org.koin.core.qualifier.named
import org.koin.dsl.binds
import org.koin.dsl.module

val authModule = module {
    single { FirebaseAuthService() }
    single<GoogleAuthService> { GoogleAuthServiceImpl(get(), get()) }
    single<SSOAuthService> { SSOAuthServiceImpl(get(), get()) }
    single {
        AuthRepositoryImpl(
            get(),
            get(),
            get(),
            get(named(KoinConsts.DefaultIoDispatcherName)),
            get(),
            get(),
            get()
        )
    } binds arrayOf(
        AuthUserProvider::class,
        EmailAuthRepository::class,
        GoogleAuthRepository::class
    )
    single { GetAuthUserInteractor(get()) }
    single { AuthUserInteractor(get()) }
    single { HasAccountByEmailInteractor(get()) }
    single { LogoutInteractor(get()) }
    single { ResetPasswordInteractor(get()) }
    single { SendEmailVerifyMessageInteractor(get()) }
    single { CreateUserInteractor(get()) }
    single { ReloadAuthUserInteractor(get()) }
    single { AwaitAuthUserInteractor(get()) }
    single { SignInWithGoogleInteractor(get()) }
    single { SetOnSSOSignInDismissInteractor(get()) }
    single { TryPasswordInteractor(get()) }
    single { UpdatePasswordInteractor(get()) }
    single { UpdateEmailInteractor(get()) }
    single { SetNotificationSubscriptionInteractor(get(), get()) }
}

fun ComponentFactory.createEmailEnterComponent(
    componentContext: ComponentContext,
    onOutput: (EmailEnterComponent.Output) -> Unit,
    isSignIn: Boolean,
    isResetPassword: Boolean
): EmailEnterComponent {
    return RealEmailEnterComponent(
        componentContext,
        isSignIn,
        isResetPassword,
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createSignUpComponent(
    componentContext: ComponentContext,
    onOutput: (SignUpComponent.Output) -> Unit,
    startCommand: SignUpComponent.StartCommand?
): SignUpComponent {
    return RealSignUpComponent(
        componentContext,
        startCommand,
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createSignInComponent(
    componentContext: ComponentContext,
    onOutput: (SignInComponent.Output) -> Unit,
): SignInComponent {
    return RealSignInComponent(
        componentContext,
        get(),
        onOutput
    )
}

fun ComponentFactory.createAuthComponent(
    componentContext: ComponentContext,
    startCommand: AuthComponent.StartCommand?,
    onOutput: (AuthComponent.Output) -> Unit
): AuthComponent {
    return RealAuthComponent(componentContext, startCommand, get(), onOutput, get())
}

fun ComponentFactory.createCreatePasswordComponent(
    componentContext: ComponentContext,
    email: Email,
    onOutput: (CreatePasswordComponent.Output) -> Unit
): CreatePasswordComponent {
    return RealCreatePasswordComponent(componentContext, email, get(), get(), get(), onOutput)
}

fun ComponentFactory.createSentMessageToEmailComponent(
    componentContext: ComponentContext,
    title: StringDesc,
    text: StringDesc,
    shouldAutoClose: Boolean,
    onOutput: (SentMessageToEmailComponent.Output) -> Unit
): SentMessageToEmailComponent {
    return RealSentMessageToEmailComponent(
        componentContext,
        get(),
        title,
        text,
        shouldAutoClose,
        onOutput,
        get(),
        get(),
        get(),
        get(),
        get()
    )
}

fun ComponentFactory.createEnterPasswordComponent(
    componentContext: ComponentContext,
    email: Email,
    onOutput: (PasswordEnterComponent.Output) -> Unit
): PasswordEnterComponent {
    return RealPasswordEnterComponent(
        componentContext,
        email,
        onOutput,
        get(),
        get(),
        get(),
        get(),
        get()
    )
}

fun ComponentFactory.createResetPasswordComponent(
    componentContext: ComponentContext,
    onOutput: (ResetPasswordComponent.Output) -> Unit
): ResetPasswordComponent {
    return RealResetPasswordComponent(
        componentContext,
        get(),
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createMainAuthComponent(
    componentContext: ComponentContext,
    shouldAnimate: Boolean,
    onOutput: (MainAuthComponent.Output) -> Unit,
): MainAuthComponent {
    return RealMainAuthComponent(
        componentContext,
        shouldAnimate,
        get(),
        get(),
        get(),
        get(),
        get(),
        onOutput
    )
}
