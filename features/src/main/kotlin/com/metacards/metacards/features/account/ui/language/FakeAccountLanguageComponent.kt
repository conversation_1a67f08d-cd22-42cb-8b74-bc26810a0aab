package com.metacards.metacards.features.account.ui.language

import com.metacards.metacards.core.localization.domain.AppLanguage
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakeAccountLanguageComponent : AccountLanguageComponent {
    override val languageFlow: StateFlow<AppLanguage> = MutableStateFlow(AppLanguage.RUS)
    override fun onLanguageClick(language: AppLanguage) = Unit
    override fun onSaveClick() = Unit
    override fun onDismiss() = Unit
}