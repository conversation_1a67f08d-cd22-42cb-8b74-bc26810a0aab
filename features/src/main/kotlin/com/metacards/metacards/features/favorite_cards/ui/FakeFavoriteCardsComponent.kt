package com.metacards.metacards.features.favorite_cards.ui

import com.metacards.metacards.core.utils.createFakeChildStackStateFlow
import com.metacards.metacards.features.favorite_cards.ui.list.FakeFavoriteCardListComponent

class FakeFavoriteCardsComponent : FavoriteCardsComponent {
    override val childStack = createFakeChildStackStateFlow(
        FavoriteCardsComponent.Child.List(FakeFavoriteCardListComponent())
    )
}