package com.metacards.metacards.features.root.data

import com.metacards.metacards.core.localization.data.AppLanguageUpdateListener
import com.metacards.metacards.core.localization.domain.AppLanguage
import com.metacards.metacards.features.user.domain.UserRepository

class UpdateUserLanguageAtFirebase(
    private val userRepository: UserRepository
) : AppLanguageUpdateListener {
    override suspend fun onAppLanguageChanged(appLanguage: AppLanguage) {
        val user = userRepository.user.value ?: return
        userRepository.updateLanguage(user.userId, appLanguage)
    }
}