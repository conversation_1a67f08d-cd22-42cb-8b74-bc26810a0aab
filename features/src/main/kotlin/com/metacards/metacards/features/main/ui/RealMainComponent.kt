package com.metacards.metacards.features.main.ui

import android.os.Parcelable
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.router.stack.bringToFront
import com.arkivanov.decompose.router.stack.childStack
import com.arkivanov.decompose.router.stack.pop
import com.arkivanov.decompose.router.stack.push
import com.arkivanov.essenty.lifecycle.doOnResume
import com.arkivanov.essenty.lifecycle.doOnStart
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.alert.domain.ShakerPopupState
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.preferences.PreferencesService
import com.metacards.metacards.core.preferences.models.TutorialState
import com.metacards.metacards.core.user.domain.UserState
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.toStateFlow
import com.metacards.metacards.core.widget.navigation_bar.NavigationItem
import com.metacards.metacards.core.widget.navigation_bar.NavigationPage
import com.metacards.metacards.features.account.createAccountComponent
import com.metacards.metacards.features.account.ui.AccountComponent
import com.metacards.metacards.features.account.ui.flow.AccountFlowComponent
import com.metacards.metacards.features.account.ui.lessons.LessonsComponent
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.course.domain.entity.CourseData
import com.metacards.metacards.features.course.ui.CourseComponent
import com.metacards.metacards.features.course.ui.details.CourseDetailsComponent
import com.metacards.metacards.features.deck.domain.interactor.GetDailyCardInteractor
import com.metacards.metacards.features.favorite_cards.ui.FavoriteCardsComponent
import com.metacards.metacards.features.home.createHomeComponent
import com.metacards.metacards.features.home.ui.HomeComponent
import com.metacards.metacards.features.layout.domain.CardSource
import com.metacards.metacards.features.layout.domain.GetDailyCardRecordIdInteractor
import com.metacards.metacards.features.record.createJournalComponent
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.record.ui.journal.JournalComponent
import com.metacards.metacards.features.showcase.createShowcaseComponent
import com.metacards.metacards.features.showcase.ui.ShowcaseComponent
import com.metacards.metacards.features.tutorial.data.TutorialMessageService
import com.metacards.metacards.features.tutorial.data.TutorialRepository
import com.metacards.metacards.features.tutorial.data.TutorialStep
import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import com.metacards.metacards.features.user.domain.UserRepository
import com.metacards.metacards.features.user_analytics.ui.UserAnalyticsComponent
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.stateIn
import kotlinx.datetime.Instant
import kotlinx.parcelize.Parcelize

class RealMainComponent(
    componentContext: ComponentContext,
    private val componentFactory: ComponentFactory,
    private val tutorialMessageService: TutorialMessageService,
    tutorialRepository: TutorialRepository,
    private val errorHandler: ErrorHandler,
    private val analyticsService: AnalyticsService,
    userRepository: UserRepository,
    getDailyCardRecordIdInteractor: GetDailyCardRecordIdInteractor,
    getDailyCardInteractor: GetDailyCardInteractor,
    private val preferencesService: PreferencesService,
    private val onOutput: (MainComponent.Output) -> Unit,
) : ComponentContext by componentContext, MainComponent {

    private val navigation = StackNavigation<ChildConfig>()
    private val debounce = Debounce()

    override val childStack = childStack(
        source = navigation,
        initialConfiguration = ChildConfig.Home,
        handleBackButton = true,
        childFactory = ::createChild
    ).toStateFlow(lifecycle)

    override val navigationItems = computed(childStack) { childStack ->
        val selectedPage = when (childStack.active.instance) {
            is MainComponent.Child.Home -> NavigationPage.Home
            is MainComponent.Child.Journal -> NavigationPage.Journal
            is MainComponent.Child.Showcase -> NavigationPage.Showcase
            is MainComponent.Child.Account -> NavigationPage.Account
        }

        NavigationItem.ALL.map { item ->
            if (item.page == selectedPage) item.copy(isSelected = true) else item
        }
    }

    override val tutorialState: StateFlow<TutorialState> =
        tutorialRepository.tutorialStateFlow

    override val dailyCardState = getDailyCardInteractor.execute()
        .stateIn(componentScope, SharingStarted.Eagerly, null)

    override val isShakerPopupVisible = MutableStateFlow(false)

    override val tutorialMessage: StateFlow<TutorialMessage?> =
        tutorialMessageService.tutorialMessageFlow
            .stateIn(componentScope, SharingStarted.Eagerly, null)

    override val tutorialStep: StateFlow<TutorialStep> = tutorialMessageService.tutorialStepFlow

    private val dailyCardRecordId = MutableStateFlow<RecordId?>(RecordId.MOCK)

    private val userState = userRepository.user
        .map {
            if (it == null) {
                UserState.None
            } else {
                UserState.Success(it)
            }
        }.stateIn(componentScope, SharingStarted.Eagerly, UserState.Loading)

    init {
        lifecycle.doOnStart {
            tutorialMessageService.handleMainComponentTutorial()

            componentScope.safeLaunch(errorHandler, showError = false) {
                getDailyCardInteractor.reLoad()
            }
        }

        lifecycle.doOnResume {
            with(userState.value) {
                when (this) {
                    UserState.Loading -> dailyCardRecordId.value = RecordId.MOCK
                    UserState.None -> dailyCardRecordId.value = null
                    is UserState.Success -> componentScope.safeLaunch(errorHandler) {
                        dailyCardRecordId.value = getDailyCardRecordIdInteractor.execute(user.userId)
                    }
                }
            }
        }

        userState.onEach {
            when (it) {
                UserState.Loading -> dailyCardRecordId.value = RecordId.MOCK
                UserState.None -> dailyCardRecordId.value = null
                is UserState.Success -> componentScope.safeLaunch(errorHandler) {
                    dailyCardRecordId.value = getDailyCardRecordIdInteractor.execute(it.user.userId)
                }
            }
        }.launchIn(componentScope)
    }

    override fun onNavigationPageChange(
        navigationPage: NavigationPage,
        analyticsInitialTab: UserAnalyticsComponent.Tab,
    ) {
        val selectedPage = navigationItems.value.find { it.isSelected }?.page
        if (selectedPage == navigationPage) {
            onAlreadySelectedPageChosen(navigationPage)
            return
        }

        when (navigationPage) {
            NavigationPage.Journal -> analyticsService.logEvent(AnalyticsEvent.JournalEvent)
            NavigationPage.Showcase -> analyticsService.logEvent(AnalyticsEvent.AnalyticsTapEvent)
            NavigationPage.Account -> analyticsService.logEvent(AnalyticsEvent.AccountTapEvent)
            else -> {
                // Do nothing
            }
        }

        changePage(navigationPage)
    }

    override fun showJournalRecordsForDate(time: Instant) {
        (childStack.value.active.instance as? MainComponent.Child.Journal)
            ?.component
            ?.showJournalRecordsForDate(time)
    }

    override fun onShakeDetected() {
        if (tutorialState.value != TutorialState.COMPLETED) return
        val signInDialogOnAccountVisible = (
            (childStack.value.active.instance as? MainComponent.Child.Account)
                ?.component?.dialogControl?.dialogOverlay?.value?.child != null
            )
        if (signInDialogOnAccountVisible) return

        val signInDialogOnHomeVisible = (
            (childStack.value.active.instance as? MainComponent.Child.Home)
                ?.component?.dialogControl?.dialogOverlay?.value?.child != null
            )
        if (signInDialogOnHomeVisible) return

        val selectLanguageBottomSheetVisible = (
            (childStack.value.active.instance as? MainComponent.Child.Account)
                ?.component?.languageBottomSheetControl?.sheetOverlay?.value?.child != null
            )

        if (selectLanguageBottomSheetVisible) return

        componentScope.safeLaunch(errorHandler) {
            val shakerPopupState = preferencesService.getShakerPopupState()
            if (shakerPopupState == ShakerPopupState.OVER) {
                openDailyCard()
            } else {
                preferencesService.setShakerPopupState(ShakerPopupState.VISIBLE)
                isShakerPopupVisible.value = true
            }
        }
    }

    override fun onShakerPopupDismiss() {
        DebounceClick(debounce, "onShakerPopupDismiss") {
            isShakerPopupVisible.value = false
            preferencesService.setShakerPopupState(ShakerPopupState.OVER)
            openDailyCard()
        }
    }

    private fun onAlreadySelectedPageChosen(navigationPage: NavigationPage) {
        if (navigationPage == NavigationPage.Journal) {
            (childStack.value.active.instance as? MainComponent.Child.Journal)?.component?.onScrollToTopClick()
        }
    }

    private fun changePage(
        navigationPage: NavigationPage,
    ) {
        val config = when (navigationPage) {
            NavigationPage.Home -> ChildConfig.Home
            NavigationPage.Journal -> ChildConfig.Journal
            NavigationPage.Showcase -> ChildConfig.Showcase
            NavigationPage.Account -> ChildConfig.Account
        }

        navigation.bringToFront(config)
    }

    private fun openDailyCard() {
        if (tutorialState.value != TutorialState.COMPLETED) return
        if (dailyCardRecordId.value == RecordId.MOCK) return
        dailyCardState.value?.let { dailyCard ->
            DebounceClick(debounce, "openDailyCard", errorHandler) {
                val cardSource = CardSource.DailyCardSource(
                    dailyCard = dailyCard,
                    recordId = dailyCardRecordId.value
                )
                onOutput(MainComponent.Output.DailyCardRequested(cardSource))
            }
        }
    }

    private fun createChild(
        config: ChildConfig,
        componentContext: ComponentContext,
    ): MainComponent.Child {
        return when (config) {
            is ChildConfig.Home -> {
                MainComponent.Child.Home(
                    componentFactory.createHomeComponent(
                        componentContext,
                        ::onHomeOutput
                    )
                )
            }

            is ChildConfig.Journal -> {
                MainComponent.Child.Journal(
                    componentFactory.createJournalComponent(componentContext, ::onJournalOutput)
                )
            }

            is ChildConfig.Account -> {
                MainComponent.Child.Account(
                    componentFactory.createAccountComponent(componentContext, ::onAccountOutput)
                )
            }

            is ChildConfig.Showcase -> {
                MainComponent.Child.Showcase(
                    componentFactory.createShowcaseComponent(
                        componentContext,
                        ::onShowcaseOutput
                    )
                )
            }
        }
    }

    private fun onShowcaseOutput(output: ShowcaseComponent.Output) {
        when (output) {
            is ShowcaseComponent.Output.SpecialDeckRequested -> onOutput(
                MainComponent.Output.SpecialDeckRequested(output.deckId, false)
            )

            ShowcaseComponent.Output.AuthSuggestingRequested -> onOutput(
                MainComponent.Output.AuthSuggestingScreenRequested
            )

            is ShowcaseComponent.Output.ShopDeckDetailsRequested -> onOutput(
                MainComponent.Output.DeckScreenRequested(output.deckId)
            )

            is ShowcaseComponent.Output.PredefinedLayoutDetailsRequested -> onOutput(
                MainComponent.Output.PredefinedLayoutDetailsRequested(output.cardSource)
            )
            is ShowcaseComponent.Output.SubscriptionBottomSheetRequested -> onOutput(
                MainComponent.Output.SubscriptionSuggestingScreenRequested(output.withAdv)
            )

            is ShowcaseComponent.Output.VideoCategorySelected -> onOutput(
                MainComponent.Output.AccountFlowScreenRequested(
                    AccountFlowComponent.Screen.Lessons(
                        LessonsComponent.Screen.Details(output.lessonCategoryId)
                    )
                )
            )

            ShowcaseComponent.Output.AnalyticsRequested -> onOutput(
                MainComponent.Output.AnalyticsRequested
            )

            is ShowcaseComponent.Output.FavoriteCardDetailsRequested -> onOutput(
                MainComponent.Output.FavoriteCardsRequested(
                    FavoriteCardsComponent.Screen.Details(output.cardId)
                )
            )

            is ShowcaseComponent.Output.CourseRequested -> onOutput(
                MainComponent.Output.CourseRequested(output.screen)
            )
        }
    }

    private fun onAccountOutput(output: AccountComponent.Output) {
        when (output) {
            is AccountComponent.Output.AccountFlowScreenRequested -> {
                onOutput(MainComponent.Output.AccountFlowScreenRequested(output.screen))
            }

            is AccountComponent.Output.FavoriteCardsRequested -> {
                onOutput(MainComponent.Output.FavoriteCardsRequested(FavoriteCardsComponent.Screen.List))
            }

            is AccountComponent.Output.AuthScreenRequested -> {
                onOutput(MainComponent.Output.AuthScreenRequested)
            }

            is AccountComponent.Output.AddPhysicalDeckRequested -> {
                onOutput(MainComponent.Output.AddPhysicalDeckRequested)
            }

            is AccountComponent.Output.AuthSuggestingScreenRequested -> {
                onOutput(MainComponent.Output.AuthSuggestingScreenRequested)
            }

            is AccountComponent.Output.PremiumSuggestingScreenRequested -> {
                onOutput(MainComponent.Output.SubscriptionSuggestingScreenRequested(withAdv = false))
            }

            is AccountComponent.Output.RecompositionRequested -> {
                navigation.pop()
                navigation.push(ChildConfig.Account)
            }

            AccountComponent.Output.AuthSuggestingRequested -> {
                onOutput(MainComponent.Output.AuthSuggestingScreenRequested)
            }

            is AccountComponent.Output.SpecialDeckRequested -> {
                onOutput(MainComponent.Output.SpecialDeckRequested(output.deckId, false))
            }
        }
    }

    private fun onJournalOutput(output: JournalComponent.Output) {
        when (output) {
            is JournalComponent.Output.CalendarRequested -> {
                onOutput(MainComponent.Output.CalendarRequested)
            }

            is JournalComponent.Output.ArchiveRequested -> {
                onOutput(MainComponent.Output.ArchiveRequested)
            }

            is JournalComponent.Output.RecordDetailsRequested -> {
                onOutput(
                    MainComponent.Output.RecordDetailsRequested(
                        output.recordSource,
                        output.recordType,
                        output.courseId,
                        output.courseThemeId
                    )
                )
            }

            JournalComponent.Output.CreateRecordFlowRequested -> {
                onOutput(MainComponent.Output.CreateRecordFlowScreenRequested(isDailyCard = false))
            }

            JournalComponent.Output.SubscriptionSuggestingRequested -> {
                onOutput(MainComponent.Output.SubscriptionSuggestingScreenRequested(withAdv = false))
            }

            is JournalComponent.Output.TestDetailsRequested -> {
                val query = CourseData.Query(
                    themeId = output.coursePassedTest.themeId,
                    courseId = output.coursePassedTest.courseId
                )
                val screen = CourseComponent.Screen.Details(
                    CourseDetailsComponent.Screen.CoursePassedTestResult(
                        query, output.coursePassedTest.testId, output.coursePassedTest.id
                    )
                )
                onOutput(MainComponent.Output.CourseRequested(screen))
            }
        }
    }

    private fun onHomeOutput(output: HomeComponent.Output) {
        when (output) {
            is HomeComponent.Output.DeckScreenRequested -> onOutput(
                MainComponent.Output.DeckScreenRequested(output.deckId)
            )

            is HomeComponent.Output.NewDeckLayoutRequested -> onOutput(
                MainComponent.Output.NewDeckLayoutRequested(output.deckId)
            )

            is HomeComponent.Output.PredefinedLayoutsRequested -> onOutput(
                MainComponent.Output.PredefinedLayoutsRequested
            )

            is HomeComponent.Output.SubscriptionBottomSheetRequested -> {
                onOutput(
                    MainComponent.Output.SubscriptionSuggestingScreenRequested(withAdv = true)
                )
            }

            HomeComponent.Output.SubscriptionScreenRequested ->
                onOutput(MainComponent.Output.SubscriptionScreenRequested)

            HomeComponent.Output.AuthScreenRequested -> onOutput(MainComponent.Output.AuthScreenRequested)
            is HomeComponent.Output.DailyCardRequested -> openDailyCard()
            HomeComponent.Output.ShowShakerPopup -> isShakerPopupVisible.value = true
            is HomeComponent.Output.SpecialDeckRequested ->
                onOutput(MainComponent.Output.SpecialDeckRequested(output.deckId, output.forceOnboarding))

            HomeComponent.Output.AuthSuggestingRequested -> onOutput(
                MainComponent.Output.AuthSuggestingScreenRequested
            )
        }
    }

    sealed interface ChildConfig : Parcelable {
        @Parcelize
        data object Home : ChildConfig

        @Parcelize
        data object Journal : ChildConfig

        @Parcelize
        data object Account : ChildConfig

        @Parcelize
        data object Showcase : ChildConfig
    }
}
