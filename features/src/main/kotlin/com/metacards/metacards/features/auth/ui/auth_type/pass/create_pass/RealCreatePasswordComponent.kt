package com.metacards.metacards.features.auth.ui.auth_type.pass.create_pass

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.user.domain.Email
import com.metacards.metacards.core.utils.PASSWORD_MIN_LENGTH
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.passwordInput
import com.metacards.metacards.features.R
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.auth.domain.CreateUserInteractor
import com.metacards.metacards.features.auth.domain.Password
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.StateFlow
import ru.mobileup.kmm_form_validation.control.InputControl
import ru.mobileup.kmm_form_validation.options.ImeAction
import ru.mobileup.kmm_form_validation.validation.control.equalsTo
import ru.mobileup.kmm_form_validation.validation.control.isNotBlank
import ru.mobileup.kmm_form_validation.validation.control.minLength
import ru.mobileup.kmm_form_validation.validation.form.RevalidateOnValueChanged
import ru.mobileup.kmm_form_validation.validation.form.SetFocusOnFirstInvalidControlAfterValidation
import ru.mobileup.kmm_form_validation.validation.form.ValidateOnFocusLost
import ru.mobileup.kmm_form_validation.validation.form.dynamicValidationResult
import ru.mobileup.kmm_form_validation.validation.form.formValidator

class RealCreatePasswordComponent(
    componentContext: ComponentContext,
    private val email: Email,
    private val createUserInteractor: CreateUserInteractor,
    private val errorHandler: ErrorHandler,
    private val analyticsService: AnalyticsService,
    private val onOutput: (CreatePasswordComponent.Output) -> Unit
) : ComponentContext by componentContext, CreatePasswordComponent {

    private val debounce = Debounce()

    override val passwordInputControl: InputControl = passwordInput(componentScope, ImeAction.Next)

    override val confirmPasswordInputControl: InputControl = passwordInput(componentScope, ImeAction.Done)

    private val formValidator = componentScope.formValidator {
        features = listOf(
            ValidateOnFocusLost,
            RevalidateOnValueChanged,
            SetFocusOnFirstInvalidControlAfterValidation
        )

        input(passwordInputControl) {
            isNotBlank(StringDesc.Resource(R.string.password_create_error))
            minLength(PASSWORD_MIN_LENGTH, StringDesc.Resource(R.string.password_create_error))
        }

        input(confirmPasswordInputControl) {
            isNotBlank(StringDesc.Resource(R.string.password_create_confirm_error))
            equalsTo(
                passwordInputControl,
                StringDesc.Resource(R.string.password_create_confirm_error)
            )
        }
    }

    private val dynamicResult = componentScope.dynamicValidationResult(formValidator)

    private val isEnterButtonEnabled =
        computed(
            dynamicResult,
            passwordInputControl.error,
            confirmPasswordInputControl.error
        ) { result, passwordHasError, confirmHasError ->
            (passwordHasError == null && confirmHasError == null && result.isValid)
        }

    override val enterButtonState: StateFlow<ButtonState> = computed(isEnterButtonEnabled) {
        ButtonState.create(disabled = !it, loading = false)
    }

    override fun onEnterButtonClick() {
        if (!formValidator.validate().isValid) return

        componentScope.safeLaunch(errorHandler) {
            analyticsService.logEvent(AnalyticsEvent.RegistrationMailPasswordDoneEvent)

            DebounceClick(
                debounce = debounce,
                id = "onEnterButtonClick",
                errorHandler = errorHandler,
                onClick = ::enterButtonAction
            )
        }
    }

    private suspend fun enterButtonAction() {
        val password = Password(passwordInputControl.text.value)

        createUserInteractor.execute(email, password)
        onOutput(CreatePasswordComponent.Output.EnterButtonPressed(email))
    }
}