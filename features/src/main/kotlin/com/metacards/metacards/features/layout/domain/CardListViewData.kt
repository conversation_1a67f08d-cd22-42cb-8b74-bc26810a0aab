package com.metacards.metacards.features.layout.domain

import android.os.Parcelable
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardHint
import com.metacards.metacards.features.record.domain.RecordId
import kotlinx.parcelize.Parcelize

@Parcelize
data class CardListViewData(
    val selectedCard: Card,
    val questionText: LocalizableString?,
    val availableCards: List<Card>,
    val isPredefined: Boolean,
    val isLastPredefined: Boolean,
    val isDailyCard: <PERSON>olean,
    val questionIndex: Int,
    val cardHintList: List<CardHint>?,
    val recordId: RecordId? = null
) : Parcelable {

    companion object {
        val MOCK = CardListViewData(
            selectedCard = Card.mock(123),
            questionText = LocalizableString.createNonLocalizable("Просто вопрос. Ничего лишнего"),
            availableCards = Card.getMockList(),
            isPredefined = false,
            isLastPredefined = false,
            isDailyCard = false,
            questionIndex = 0,
            cardHintList = listOf(CardHint.MOCK),
            recordId = null
        )

        fun forDailyCard(cardSource: CardSource.DailyCardSource) = CardListViewData(
            selectedCard = cardSource.dailyCard.toCard(),
            questionText = null,
            availableCards = mutableListOf(),
            isPredefined = false,
            isLastPredefined = false,
            isDailyCard = true,
            questionIndex = 0,
            cardHintList = listOf(cardSource.dailyCard.hint),
            recordId = cardSource.recordId
        )
    }
}
