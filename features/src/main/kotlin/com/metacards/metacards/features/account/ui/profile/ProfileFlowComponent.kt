package com.metacards.metacards.features.account.ui.profile

import android.os.Parcelable
import androidx.annotation.StringRes
import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.features.account.ui.profile.email.ProfileEmailComponent
import com.metacards.metacards.features.account.ui.profile.main.ProfileMainComponent
import com.metacards.metacards.features.account.ui.profile.name.ProfileNameComponent
import com.metacards.metacards.features.account.ui.profile.password.ProfilePasswordComponent
import com.metacards.metacards.features.account.ui.profile.password.confirm.ProfileConfirmPasswordComponent
import kotlinx.coroutines.flow.StateFlow
import kotlinx.parcelize.Parcelize

interface ProfileFlowComponent {
    val childStack: StateFlow<ChildStack<*, Child>>

    sealed interface Child {
        class Main(val component: ProfileMainComponent) : Child
        class Name(val component: ProfileNameComponent) : Child
        class Email(val component: ProfileEmailComponent) : Child
        class Password(val component: ProfilePasswordComponent) : Child
        class ConfirmPassword(val component: ProfileConfirmPasswordComponent) : Child
    }

    sealed interface Screen : Parcelable {
        @Parcelize
        object Main : Screen

        @Parcelize
        data class Name(val initialUserName: String) : Screen

        @Parcelize
        object Email : Screen

        @Parcelize
        object Password : Screen

        @Parcelize
        data class ConfirmPassword(
            @StringRes val toolbarTextRes: Int,
            @StringRes val textFieldHeaderRes: Int,
            @StringRes val bottomButtonTextRes: Int
        ) : Screen
    }

    sealed interface Output {
        data object AuthScreenRequested : Output
        data object MainScreenRequested : Output
        data class SignInViaWebViewRequested(val url: String, @StringRes val title: Int) : Output
        data object WebViewDismissRequested : Output
        data object SubscriptionRequested : Output
    }
}