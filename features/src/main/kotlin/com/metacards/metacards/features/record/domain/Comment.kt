package com.metacards.metacards.features.record.domain

import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlin.time.Duration.Companion.hours

data class Comment(
    val text: String,
    val creationTime: Instant
) {

    companion object {

        fun create(text: String): Comment = Comment(text, Clock.System.now())

        fun mock(number: Int) = Comment(
            text = "Some comment $number",
            creationTime = Clock.System.now() - number.hours
        )

        val LIST_MOCK = listOf(mock(1), mock(2), mock(3), mock(4))
    }
}