package com.metacards.metacards.features.auth.ui.sign_in

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.arkivanov.decompose.extensions.compose.jetpack.stack.Children
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.auth.ui.auth_type.email.EmailEnterUi
import com.metacards.metacards.features.auth.ui.auth_type.pass.PasswordEnterUi
import com.metacards.metacards.features.auth.ui.sign_in.reset_password.ResetPasswordUi
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun SignInUi(component: SignInComponent) {
    val childStack by component.childStack.collectAsState()
    val toolbarVisibility by component.toolbarVisibility.collectAsState()

    BoxWithFade(
        modifier = Modifier
            .fillMaxSize()
            .imePadding(),
        listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        Column(
            modifier = Modifier.padding(horizontal = 16.dp),
            horizontalAlignment = Alignment.Start,
        ) {
            if (toolbarVisibility) {
                TopNavigationBar(
                    contentPadding = PaddingValues(vertical = 16.dp),
                    title = R.string.sign_in_toolbar_text.strResDesc(),
                    leadingIcon = { BackNavigationItem() }
                )
            }

            Children(stack = childStack) { child ->
                when (val instance = child.instance) {
                    is SignInComponent.Child.EmailEnter -> EmailEnterContent(instance)
                    is SignInComponent.Child.ResetPassword -> ResetPasswordUi(instance.component)
                    is SignInComponent.Child.PasswordEnter -> PasswordEnterUi(instance.component)
                }
            }
        }
    }
}

@Composable
private fun EmailEnterContent(
    instance: SignInComponent.Child.EmailEnter
) {
    Column {
        Text(
            text = R.string.auth_user_type_email.strResDesc().localizedByLocal(),
            style = CustomTheme.typography.caption.large,
            color = CustomTheme.colors.text.primary,
            modifier = Modifier.padding(bottom = 16.dp, top = 68.dp)
        )

        EmailEnterUi(
            component = instance.component,
            isSignIn = true,
            nextButtonText = com.metacards.metacards.core.R.string.common_proceed.strResDesc().localizedByLocal()
        )
    }
}

@Preview
@Composable
fun SignUpUiPreview() {
    val coroutineScope = rememberCoroutineScope()
    AppTheme {
        SignInUi(component = FakeSignInComponent(coroutineScope))
    }
}