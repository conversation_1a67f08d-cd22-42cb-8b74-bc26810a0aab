package com.metacards.metacards.features.auth.ui.sign_in

import com.metacards.metacards.core.utils.createFakeChildStackStateFlow
import com.metacards.metacards.features.auth.ui.auth_type.email.FakeEmailEnterComponent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakeSignInComponent(coroutineScope: CoroutineScope) : SignInComponent {
    override val childStack = createFakeChildStackStateFlow(
        SignInComponent.Child.EmailEnter(FakeEmailEnterComponent(coroutineScope))
    )
    override val toolbarVisibility: StateFlow<Boolean> = MutableStateFlow(true)
}