package com.metacards.metacards.features.account.ui.about_the_app.about_us

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun AboutUsUi(modifier: Modifier = Modifier) {
    BoxWithFade(modifier, listOfColors = CustomTheme.colors.gradient.backgroundList) {
        Column {
            TopNavigationBar(
                title = R.string.about_the_app_about_creators_of_application.strResDesc(),
                leadingIcon = { BackNavigationItem() }
            )

            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 24.dp)
            ) {
                Column(
                    verticalArrangement = Arrangement.spacedBy(24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier
                        .fillMaxWidth()
                        .verticalScroll(rememberScrollState())
                ) {
                    // Удаленно по требованию заказчика
//                    Text(
//                        text = R.string.about_the_app_about_us_leading_text.strResDesc()
//                            .localizedByLocal(),
//                        color = CustomTheme.colors.text.primary,
//                        style = CustomTheme.typography.heading.medium
//                    )

                    Text(
                        text = R.string.about_the_app_about_us_main_text.strResDesc().localizedByLocal(),
                        color = CustomTheme.colors.text.primary,
                        style = CustomTheme.typography.body.secondary,
                        textAlign = TextAlign.Start
                    )
                    // Удаленно по требованию заказчика
//                    MediaBlock(
//                        onInstagramClick = component::onInstagramClick,
//                        onFacebookClick = component::onFacebookClick,
//                        onInternetClick = component::onInternetClick
//                    )
                }
            }
        }
    }
}

@Preview
@Composable
fun AboutUsUiPreview() {
    AppTheme {
        AboutUsUi()
    }
}
