package com.metacards.metacards.features.user.ui.subscription

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.user.domain.SubscriptionType
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.user.domain.UserProvider
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.stateIn
import com.metacards.metacards.features.payments.domain.PurchaseType
import com.metacards.metacards.features.user.domain.subscription.interactor.GetTariffsInteractor
import kotlinx.coroutines.flow.MutableStateFlow

class RealBaseSubscriptionComponent(
    componentContext: ComponentContext,
    userProvider: UserProvider,
    getTariffsInteractor: GetTariffsInteractor
) : ComponentContext by componentContext, BaseSubscriptionComponent {
    private val user = userProvider.getUser()
    override val tariffs = getTariffsInteractor.execute().stateIn(this, LoadableState())
    override val userSubscriptionState =
        computed(user) { it?.subscriptionState ?: User.SubscriptionState.None(false, false) }
    override val selectedSubscriptionType =
        MutableStateFlow(SubscriptionType.SUBSCRIPTION_PLAN_YEAR)
    override val isTrialAvailable = computed(tariffs, selectedSubscriptionType) { allTariffs, subsType ->
        allTariffs.data?.find { it.type == subsType }?.hasTrial ?: false
    }

    override fun onSubscriptionTypeSelect(subscriptionType: SubscriptionType) {
        selectedSubscriptionType.value = subscriptionType
    }

    override fun getPurchaseType(): PurchaseType.Subscription {
        val subscriptionType = selectedSubscriptionType.value
        val tariff = tariffs.value.data?.find { it.type == subscriptionType }
        require(tariff != null) { "Can't find tariff with type: $subscriptionType" }
        val oldSubscription = userSubscriptionState.value as? User.SubscriptionState.Ongoing
        val purchaseType =
            if (oldSubscription != null) {
                val subscriptionId =
                    tariffs.value.data?.find { it.type == oldSubscription.subscriptionType }
                require(subscriptionId != null) { "Can't find tariff with type: ${oldSubscription.subscriptionType}" }
                PurchaseType.Subscription.Upgrade(
                    oldBasePlanId = subscriptionId.storeId,
                    newBasePlanId = tariff.storeId,
                    discountPlanId = tariff.offerId,
                    firebaseId = tariff.id.value
                )
            } else {
                PurchaseType.Subscription.New(
                    basePlanId = tariff.storeId,
                    discountPlanId = tariff.offerId,
                    tariff.id.value
                )
            }
        return purchaseType
    }

    override fun getIdForCancel(): String {
        val userSubscriptionState = userSubscriptionState.value as? User.SubscriptionState.Ongoing
        require(userSubscriptionState != null)
        val subscriptionId =
            tariffs.value.data?.find { it.type == userSubscriptionState.subscriptionType }
        require(subscriptionId != null) { "Can't find tariff with type: ${userSubscriptionState.subscriptionType}" }
        return subscriptionId.storeId
    }
}