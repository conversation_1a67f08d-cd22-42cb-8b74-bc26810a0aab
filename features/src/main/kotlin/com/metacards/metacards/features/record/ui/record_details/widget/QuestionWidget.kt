package com.metacards.metacards.features.record.ui.record_details.widget

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.widget.text_field.MetaTextField
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.record.domain.Question
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.flow.MutableStateFlow

private const val QUESTION_MAX_LENGTH = 72

@Composable
fun QuestionWidget(
    question: Question,
    isEditable: Boolean,
    isDailyCardLayout: Boolean,
    questionIndex: Int,
    onCardClick: (Int, Int) -> Unit,
    onValueChanged: (LocalizableString) -> Unit,
    modifier: Modifier = Modifier
) {
    val questionText = remember { MutableStateFlow(question.text ?: LocalizableString.createNonLocalizable("")) }
    val questionTextValue by questionText.collectAsState()

    MetaTextField(
        modifier = modifier,
        singleLine = false,
        heightMax = Dp.Infinity,
        isEnabled = isEditable,
        value = questionTextValue.localizedByLocal().take(
            if (isDailyCardLayout) Int.MAX_VALUE else QUESTION_MAX_LENGTH
        ),
        onValueChanged = {
            questionText.value = LocalizableString.createNonLocalizable(it)
            onValueChanged(questionText.value)
        },
        placeholder = R.string.add_record_question_hint.strResDesc().localizedByLocal(),
        headerMessage = {
            Text(
                modifier = Modifier.padding(bottom = 8.dp, start = 16.dp),
                text = R.string.add_record_question_label.strResDesc().localizedByLocal(),
                style = CustomTheme.typography.caption.medium,
                color = CustomTheme.colors.text.secondary
            )
        },
        leadingContent = if (isDailyCardLayout) {
            {
                CardOfQuestion(
                    cardList = question.cardsWithComment.map { it.card },
                    questionIndex = questionIndex,
                    onClick = onCardClick,
                    modifier = Modifier.align(Alignment.TopStart)
                )
            }
        } else {
            null
        },
        innerFooterContent = if (!isDailyCardLayout) {
            {
                CardOfQuestion(
                    cardList = question.cardsWithComment.map { it.card },
                    questionIndex = questionIndex,
                    onClick = onCardClick,
                    modifier = Modifier.padding(top = 16.dp)
                )
            }
        } else {
            null
        }
    )
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun CardOfQuestion(
    cardList: List<Card>,
    questionIndex: Int,
    onClick: (Int, Int) -> Unit,
    modifier: Modifier = Modifier
) {
    // todo: try remove hardcoded sizes
    // maybe width / max card count
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        cardList.forEachIndexed { index, card ->
            Surface(
                modifier = Modifier
                    .height(64.dp)
                    .width(43.dp),
                shape = RoundedCornerShape(8.dp),
                onClick = { onClick(index, questionIndex) }
            ) {
                AsyncImage(
                    model = card.imageUrl,
                    contentScale = ContentScale.Crop,
                    contentDescription = null,
                )
            }
        }
    }
}