package com.metacards.metacards.features.deck.ui.card_preview

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material.DismissDirection
import androidx.compose.material.DismissState
import androidx.compose.material.DismissValue
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.FixedThreshold
import androidx.compose.material.ResistanceConfig
import androidx.compose.material.SwipeableDefaults
import androidx.compose.material.ThresholdConfig
import androidx.compose.material.rememberDismissState
import androidx.compose.material.swipeable
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.dialog.default_component.DefaultDialog
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.ResourceFormatted
import com.metacards.metacards.core.utils.zoomAndDrag
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.LoadingIconNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.ui.MetaCard
import com.metacards.metacards.features.deck.ui.deck_info.ENDLESS_PAGER_MULTIPLIER
import com.metacards.metacards.features.deck.ui.deck_info.LockedCardContent
import com.metacards.metacards.features.deck.ui.deck_info.floorMod
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.desc
import kotlin.math.roundToInt

@Composable
fun CardsPreviewUi(
    component: CardsPreviewComponent,
    modifier: Modifier = Modifier,
) {
    val currentCardIndex by component.currentCardIndex.collectAsState()
    val isShareLoading by component.isShareLoading.collectAsState()
    val isToggleShareEnable by component.isToggleShareEnable.collectAsState()
    val isSingleItem = component.cards.size == 1
    val shouldShowGif = component.shouldShowGif

    BoxWithFade(
        modifier = modifier
            .fillMaxSize()
            .safeDrawingPadding(),
        listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        Column(Modifier.fillMaxSize()) {
            NavigationBar(
                isSingleItem = isSingleItem,
                selectedCardIndex = currentCardIndex,
                cardsCount = component.cards.size,
                isShareLoading = isShareLoading,
                onToggleShare = component::onToggleShare,
                isToggleShareEnable = isToggleShareEnable
            )

            FullScreenCardViewer(
                component = component,
                currentPage = currentCardIndex,
                isSingleItem = isSingleItem,
                shouldShowGif = shouldShowGif,
                modifier = Modifier.weight(1f)
            )
        }
    }

    DefaultDialog(dialogControl = component.dialogControl)
}

@Composable
private fun NavigationBar(
    isSingleItem: Boolean,
    selectedCardIndex: Int,
    cardsCount: Int,
    isShareLoading: Boolean,
    onToggleShare: () -> Unit,
    isToggleShareEnable: Boolean,
) {
    TopNavigationBar(
        modifier = Modifier.background(Color.Transparent),
        title = if (isSingleItem) {
            "".desc()
        } else {
            StringDesc.ResourceFormatted(
                R.string.random_card_deck_title_card_counter,
                selectedCardIndex + 1,
                cardsCount,
            )
        },
        trailingContent = {
            AnimatedVisibility(isToggleShareEnable) {
                LoadingIconNavigationItem(
                    paddingValues = PaddingValues(0.dp, 0.dp, 12.dp, 0.dp),
                    isLoading = isShareLoading,
                    iconRes = R.drawable.ic_24_share_outlined,
                    onClick = onToggleShare
                )
            }
            BackNavigationItem(iconRes = R.drawable.ic_24_close)
        }
    )
}

@OptIn(ExperimentalFoundationApi::class, ExperimentalMaterialApi::class)
@Composable
fun FullScreenCardViewer(
    component: CardsPreviewComponent,
    currentPage: Int,
    isSingleItem: Boolean,
    shouldShowGif: Boolean,
    modifier: Modifier = Modifier,
) {
    val pageCount = if (isSingleItem) 1 else ENDLESS_PAGER_MULTIPLIER * component.cards.size
    val startIndex = if (isSingleItem) 0 else pageCount / 2

    var firstComposition by remember { mutableStateOf(true) }

    val pagerState = rememberPagerState(
        initialPage = startIndex + currentPage,
        initialPageOffsetFraction = 0f
    ) { pageCount }

    val pagerScrollEnabled = remember {
        mutableStateOf(!isSingleItem)
    }

    LaunchedEffect(key1 = pagerState.currentPage) {
        if (firstComposition) {
            firstComposition = false
            return@LaunchedEffect
        }

        val index =
            (pagerState.currentPage - startIndex).floorMod(component.cards.count())
        if (currentPage != index) {
            component.onPageSelected(index)
        }
    }

    HorizontalPager(
        // TODO: compose pager gestures with zoomAndDrag
        modifier = modifier.zoomAndDrag(pagerScrollEnabledState = pagerScrollEnabled),
        state = pagerState,
        pageSpacing = 32.dp,
        userScrollEnabled = pagerScrollEnabled.value
    ) { index ->
        val itemIndex = (index - startIndex).floorMod(component.cards.count())
        val card = component.cards[itemIndex]
        val dismissState = rememberDismissState()

        VerticalSwipeToDismiss(
            state = dismissState,
            isEnabled = pagerScrollEnabled.value,
            dismissContent = { CardViewer(card = card, shouldShowGif = shouldShowGif) },
            onDismiss = component::onCloseButtonClick
        )
    }
}

private val SWIPE_THRESHOLD = 64.dp

@Composable
@ExperimentalMaterialApi
fun VerticalSwipeToDismiss(
    state: DismissState,
    isEnabled: Boolean,
    dismissContent: @Composable BoxScope.() -> Unit,
    dismissThresholds: (DismissDirection) -> ThresholdConfig = {
        FixedThreshold(SWIPE_THRESHOLD)
    },
    onDismiss: () -> Unit,
) = BoxWithConstraints {
    val width = constraints.maxHeight.toFloat()
    val anchors = mutableMapOf(0f to DismissValue.Default)
    anchors += width to DismissValue.DismissedToEnd

    val thresholds = { _: DismissValue, _: DismissValue ->
        dismissThresholds(DismissDirection.StartToEnd)
    }

    if (state.isAnimationRunning) {
        DisposableEffect(Unit) {
            onDispose {
                if (state.offset.value > 0) {
                    onDismiss()
                }
            }
        }
    }

    Box(
        Modifier.swipeable(
            state = state,
            anchors = anchors,
            thresholds = thresholds,
            orientation = Orientation.Vertical,
            enabled = isEnabled && state.currentValue == DismissValue.Default,
            resistance = ResistanceConfig(
                basis = width,
                factorAtMin = SwipeableDefaults.StiffResistanceFactor,
                factorAtMax = SwipeableDefaults.StandardResistanceFactor
            )
        )
    ) {
        Box(
            content = dismissContent,
            modifier = Modifier.offset { IntOffset(0, state.offset.value.roundToInt()) }
        )
    }
}

@Composable
fun CardViewer(
    card: Card,
    modifier: Modifier = Modifier,
    shouldShowGif: Boolean = false,
) {
    MetaCard(
        modifier = modifier,
        card = card,
        paddingValues = PaddingValues(0.dp),
        onCardClick = {},
        lockedPlaceholder = {
            LockedCardContent()
        },
        shouldShowGif = shouldShowGif
    )
}

@Preview
@Composable
private fun CardsPreviewUiPreview() {
    AppTheme {
        CardsPreviewUi(component = FakeCardsPreviewComponent())
    }
}