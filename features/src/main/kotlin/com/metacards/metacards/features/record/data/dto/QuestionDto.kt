package com.metacards.metacards.features.record.data.dto

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.deck.data.dto.CardDto
import com.metacards.metacards.features.deck.data.dto.toDomain
import com.metacards.metacards.features.deck.data.dto.toDto
import com.metacards.metacards.features.deck.domain.entity.CardWithComment
import com.metacards.metacards.features.record.domain.Question
import kotlinx.serialization.Serializable

@Serializable
class QuestionDto(
    val order: Int = 0,
    val question: String? = null,
    val cards: List<CardDto> = emptyList()
)

fun QuestionDto.toDomain(): Question {
    return Question(
        order = order,
        text = question?.takeIf { it.isNotBlank() }?.let { LocalizableString.createNonLocalizable(it) },
        cardsWithComment = cards.map { CardWithComment(it.toDomain(), it.comment.orEmpty()) }
    )
}

fun Question.toDto(): QuestionDto {
    val cardsDto = cardsWithComment.map { it.toDto() }
    return if (text == null) {
        QuestionDto(order = order, cards = cardsDto)
    } else {
        QuestionDto(order = order, question = text.toString(), cards = cardsDto)
    }
}