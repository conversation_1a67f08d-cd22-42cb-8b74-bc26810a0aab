package com.metacards.metacards.features.deck

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.features.deck.data.DecksDataSource
import com.metacards.metacards.features.deck.data.DecksRepositoryImpl
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.domain.interactor.AddDeckToPurchasedInteractor
import com.metacards.metacards.features.deck.domain.interactor.GetDailyCardInteractor
import com.metacards.metacards.features.deck.domain.interactor.GetDeckInfoWithCardsInteractor
import com.metacards.metacards.features.deck.domain.repository.DecksRepository
import com.metacards.metacards.features.deck.ui.deck_info.DeckInfoComponent
import com.metacards.metacards.features.deck.ui.deck_info.RealDeckInfoComponent
import com.metacards.metacards.features.deck.ui.scanning.DeckScanningComponent
import com.metacards.metacards.features.deck.ui.scanning.RealDeckScanningComponent
import org.koin.core.component.get
import org.koin.dsl.bind
import org.koin.dsl.module

val deckModule = module {
    single { DecksDataSource(get(), get(), get()) }
    single { DecksRepositoryImpl(get(), get()) } bind DecksRepository::class
    factory { GetDeckInfoWithCardsInteractor(get(), get()) }
    factory { AddDeckToPurchasedInteractor(get(), get()) }
    single { GetDailyCardInteractor(get(), get()) }
}

fun ComponentFactory.createDeckInfoComponent(
    componentContext: ComponentContext,
    deckId: DeckId,
    isPaymentCompleted: Boolean,
    onOutput: (DeckInfoComponent.Output) -> Unit
): DeckInfoComponent {
    return RealDeckInfoComponent(
        componentContext,
        deckId,
        isPaymentCompleted,
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createDeckScanningComponent(
    componentContext: ComponentContext,
    onOutput: (DeckScanningComponent.Output) -> Unit
): DeckScanningComponent {
    return RealDeckScanningComponent(
        componentContext,
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        onOutput
    )
}