package com.metacards.metacards.features.showcase

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.features.showcase.ui.RealShowcaseComponent
import com.metacards.metacards.features.showcase.ui.ShowcaseComponent
import org.koin.core.component.get

fun ComponentFactory.createShowcaseComponent(
    componentContext: ComponentContext,
    onOutput: (ShowcaseComponent.Output) -> Unit
): ShowcaseComponent = RealShowcaseComponent(
    componentContext,
    onOutput,
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
    get(),
)