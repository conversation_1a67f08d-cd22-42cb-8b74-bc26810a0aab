package com.metacards.metacards.features.user.ui.subscription.subscription_bottom_sheet

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.button.MetaCustomButton
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun SubscriptionBottomSheetUi(
    component: SubscriptionBottomSheetComponent,
    modifier: Modifier = Modifier
) {
    if (component.isWithAdv) {
        val showAdvButtonState by component.showAdvButtonState.collectAsState()
        WithAdvUi(
            modifier = modifier,
            onLookTariffsClick = component::onLookTariffsClick,
            showAdvButtonState = showAdvButtonState,
            onShowAdvClick = component::onShowAdvClick,
            onCloseClick = component::onCloseClick,
            isSpecialDeck = component.isSpecialDeckCardRequested
        )
    } else {
        NoAdvUi(
            modifier = modifier,
            onLookTariffsClick = component::onLookTariffsClick,
            onCloseClick = component::onCloseClick
        )
    }
}

@Composable
private fun NoAdvUi(
    modifier: Modifier = Modifier,
    onLookTariffsClick: () -> Unit,
    onCloseClick: () -> Unit
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(CustomTheme.colors.background.modal)
            .padding(horizontal = 16.dp)
            .verticalScroll(rememberScrollState())
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 24.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                modifier = Modifier,
                text = R.string.subscription_bottom_sheet_header.strResDesc().localizedByLocal(),
                style = CustomTheme.typography.heading.secondary,
                color = CustomTheme.colors.text.primary
            )

            Icon(
                modifier = Modifier.clickable(
                    boundedRipple = false,
                    onClick = onCloseClick
                ),
                painter = painterResource(id = R.drawable.ic_24_close),
                contentDescription = null,
                tint = CustomTheme.colors.icons.primary
            )
        }

        Text(
            modifier = Modifier.padding(bottom = 24.dp),
            text = R.string.subscription_bottom_sheet_trial_text.strResDesc()
                .localizedByLocal(),
            style = CustomTheme.typography.body.primary,
            color = CustomTheme.colors.text.primary
        )

        SubscriptionOption(
            iconResource = R.drawable.ic_24_card,
            optionText = R.string.subscription_options_layouts.strResDesc().localizedByLocal(),
            modifier = Modifier.padding(bottom = 12.dp)
        )

        SubscriptionOption(
            iconResource = R.drawable.ic_24_charts,
            optionText = R.string.subscription_options_analytics.strResDesc().localizedByLocal(),
            modifier = Modifier.padding(bottom = 12.dp)
        )

        SubscriptionOption(
            iconResource = R.drawable.ic_24_no_favourite,
            optionText = R.string.subscription_options_favourite.strResDesc().localizedByLocal(),
            modifier = Modifier.padding(bottom = 12.dp)
        )

        SubscriptionOption(
            iconResource = R.drawable.ic_24_archive,
            optionText = R.string.subscription_options_archive.strResDesc().localizedByLocal(),
            modifier = Modifier.padding(bottom = 24.dp)
        )

        MetaAccentButton(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp),
            text = R.string.adv_bottomsheet_subscription_view_plans.strResDesc().localizedByLocal(),
            onClick = onLookTariffsClick
        )
    }
}

@Composable
private fun WithAdvUi(
    modifier: Modifier = Modifier,
    onLookTariffsClick: () -> Unit,
    onCloseClick: () -> Unit,
    onShowAdvClick: () -> Unit,
    showAdvButtonState: ButtonState,
    isSpecialDeck: Boolean
) {

    val scrollState = rememberScrollState()

    var isScreenSmall by remember {
        mutableStateOf(false)
    }

    LaunchedEffect(key1 = scrollState.maxValue) {
        if (scrollState.maxValue != 0) {
            isScreenSmall = true
        }
    }

    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(CustomTheme.colors.background.modal)
            .padding(horizontal = 16.dp)
            .verticalScroll(scrollState)
    ) {
        Box(
            modifier = Modifier.fillMaxWidth(),
            contentAlignment = Alignment.CenterEnd
        ) {
            Icon(
                modifier = Modifier
                    .clickable(
                        boundedRipple = false,
                        onClick = onCloseClick
                    )
                    .padding(top = 24.dp, end = 16.dp),
                painter = painterResource(id = R.drawable.ic_24_close),
                contentDescription = null,
                tint = CustomTheme.colors.icons.primary
            )
        }

        val titleText = if (isSpecialDeck) {
            R.string.adv_bottomsheet_header_special_deck
        } else {
            R.string.adv_bottomsheet_header
        }.strResDesc().localizedByLocal()

        Text(
            text = titleText,
            style = CustomTheme.typography.heading.primary.copy(
                brush = Brush.horizontalGradient(CustomTheme.colors.text.linearCustomGradient)
            ),
            textAlign = TextAlign.Center,
            modifier = Modifier
                .padding(bottom = if (isScreenSmall) 24.dp else 36.dp)
        )

        Text(
            text = R.string.adv_bottomsheet_title.strResDesc().localizedByLocal(),
            style = CustomTheme.typography.heading.secondary,
            color = CustomTheme.colors.text.primary,
            modifier = Modifier
                .padding(bottom = if (isScreenSmall) 16.dp else 24.dp)
        )
        val descriptionText = if (isSpecialDeck) {
            R.string.adv_bottomsheet_description_special_deck
        } else {
            R.string.adv_bottomsheet_description
        }.strResDesc().localizedByLocal()

        Text(
            text = descriptionText,
            style = CustomTheme.typography.body.primary,
            color = CustomTheme.colors.text.primary,
            modifier = Modifier
                .padding(bottom = if (isScreenSmall) 16.dp else 24.dp)
        )

        MetaAccentButton(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = if (isScreenSmall) 12.dp else 16.dp),
            text = R.string.adv_bottomsheet_button_watch_adv.strResDesc().localizedByLocal(),
            state = showAdvButtonState,
            onClick = onShowAdvClick
        )

        Text(
            text = R.string.adv_bottomsheet_bridge_to_subscription.strResDesc().localizedByLocal(),
            style = CustomTheme.typography.heading.medium,
            color = CustomTheme.colors.text.primary,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = if (isScreenSmall) 12.dp else 24.dp)
        )

        Text(
            modifier = Modifier
                .padding(bottom = if (isScreenSmall) 12.dp else 24.dp),
            text = R.string.adv_bottomsheet_subscription_title.strResDesc().localizedByLocal(),
            style = CustomTheme.typography.heading.secondary,
            color = CustomTheme.colors.text.primary,
        )

        val firstOptionText = if (isSpecialDeck) {
            R.string.adv_bottomsheet_subscription_pros_1_special_deck
        } else {
            R.string.adv_bottomsheet_subscription_pros_1
        }.strResDesc().localizedByLocal()

        SubscriptionOption(
            iconResource = R.drawable.ic_24_card,
            optionText = firstOptionText,
            modifier = Modifier.padding(bottom = if (isScreenSmall) 8.dp else 12.dp)
        )

        SubscriptionOption(
            iconResource = R.drawable.ic_24_charts,
            optionText = R.string.adv_bottomsheet_subscription_pros_2.strResDesc().localizedByLocal(),
            modifier = Modifier.padding(bottom = if (isScreenSmall) 8.dp else 12.dp)
        )

        SubscriptionOption(
            iconResource = R.drawable.ic_24_star,
            optionText = R.string.adv_bottomsheet_subscription_pros_3.strResDesc().localizedByLocal(),
            modifier = Modifier
                .padding(bottom = if (isScreenSmall) 16.dp else 24.dp)
        )

        MetaCustomButton(
            modifier = Modifier
                .padding(bottom = if (isScreenSmall) 0.dp else 8.dp),
            text = R.string.adv_bottomsheet_subscription_view_plans.strResDesc().localizedByLocal(),
            onClick = onLookTariffsClick
        )
    }
}

@Composable
private fun SubscriptionOption(
    iconResource: Int,
    optionText: String,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            painter = painterResource(id = iconResource),
            contentDescription = null,
            tint = CustomTheme.colors.icons.primary,
            modifier = Modifier.padding(end = 8.dp)
        )

        Text(
            text = optionText,
            style = CustomTheme.typography.body.primary,
            color = CustomTheme.colors.text.primary
        )
    }
}

@Preview
@Composable
private fun SubscriptionBottomSheetWithAdvPreview() {
    AppTheme {
        SubscriptionBottomSheetUi(FakeSubscriptionBottomSheetComponent(isWithAdv = true))
    }
}

@Preview
@Composable
private fun SubscriptionBottomSheetNoAdvPreview() {
    AppTheme {
        SubscriptionBottomSheetUi(FakeSubscriptionBottomSheetComponent(isWithAdv = false))
    }
}
