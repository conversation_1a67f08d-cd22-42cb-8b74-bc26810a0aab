package com.metacards.metacards.features.account.domain.interactor

import android.webkit.URLUtil
import com.metacards.metacards.core.localization.data.AppLanguageService
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.mapLoadable
import com.metacards.metacards.features.account.domain.entity.LessonCategoryId
import com.metacards.metacards.features.account.domain.repository.LessonRepository
import com.metacards.metacards.features.deck.domain.entity.Lesson
import kotlinx.coroutines.flow.Flow

class GetCategoryLessonsInteractor(
    private val lessonsRepository: LessonRepository,
    private val appLanguageService: AppLanguageService
) {
    fun execute(categoryId: LessonCategoryId): Flow<LoadableState<List<Lesson>>> {
        return lessonsRepository
            .getCategoryLessonsFlow(categoryId)
            .mapLoadable { lessons ->
                lessons
                    ?.filter { URLUtil.isValidUrl(it.videoUrl.toString(appLanguageService.getLanguage())) }
                    ?.sortedBy { it.order }
            }
    }
}