package com.metacards.metacards.features.tutorial

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.features.tutorial.data.TutorialMessageService
import com.metacards.metacards.features.tutorial.data.TutorialMessageServiceImpl
import com.metacards.metacards.features.tutorial.data.TutorialRepository
import com.metacards.metacards.features.tutorial.data.TutorialRepositoryImpl
import com.metacards.metacards.features.tutorial.ui.RealTutorialMessageComponent
import com.metacards.metacards.features.tutorial.ui.TutorialMessageComponent
import org.koin.core.component.get
import org.koin.dsl.module

val tutorialModule = module {
    single<TutorialMessageService> { TutorialMessageServiceImpl(get(), get(), get()) }
    single<TutorialRepository> { TutorialRepositoryImpl(get(), get()) }
}

fun ComponentFactory.createTutorialMessageComponent(
    componentContext: ComponentContext,
    componentFactory: ComponentFactory,
    onOutput: (TutorialMessageComponent.Output) -> Unit
): TutorialMessageComponent {
    return RealTutorialMessageComponent(
        componentContext,
        componentFactory,
        get(),
        get(),
        get(),
        onOutput
    )
}