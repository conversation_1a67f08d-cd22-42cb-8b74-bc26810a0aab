package com.metacards.metacards.features.course.ui.page

import com.metacards.metacards.features.course.domain.entity.CourseContentId
import com.metacards.metacards.features.course.domain.entity.CourseData
import com.metacards.metacards.features.course.domain.entity.CourseDataContent
import com.metacards.metacards.features.course.domain.entity.CourseLesson
import com.metacards.metacards.features.course.domain.entity.CourseTest
import com.metacards.metacards.features.course.domain.entity.CourseTestDocumentId
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.StateFlow

interface CoursePageComponent {
    val courseQuery: CourseData.Query
    val courseData: StateFlow<CourseData?>
    val bottomButtonText: StateFlow<StringDesc>
    val shouldBlur: StateFlow<Boolean>

    fun onStartClick()
    fun onContentClick(courseDataContent: CourseDataContent)
    fun onShareClick()
    fun onTestResultClick(courseDataContent: CourseDataContent)
    fun onBlockContentClick()
    fun onToOtherCoursesClick()

    sealed interface Output {
        data class LessonRequested(
            val query: CourseLesson.Query,
            val order: Int
        ) : Output
        data class TestRequested(
            val query: CourseTest.Query,
            val order: Int
        ) : Output
        data class TestResultsRequested(
            val testId: CourseContentId,
            val testResultId: CourseTestDocumentId?
        ) : Output
        data object PremiumSuggestingRequested : Output
        data object AuthSuggestingRequested : Output
        data object CloseRequested : Output
    }
}