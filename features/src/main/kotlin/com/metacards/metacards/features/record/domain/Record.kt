package com.metacards.metacards.features.record.domain

import android.os.Parcelable
import com.metacards.metacards.core.utils.toLocalDate
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CourseThemeId
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardId
import kotlinx.datetime.Clock
import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDate
import kotlinx.datetime.plus
import kotlinx.parcelize.Parcelize

@JvmInline
@Parcelize
value class RecordId(val value: String) : Parcelable {
    companion object {

        val MOCK = RecordId("mock")
    }
}

enum class RecordListType {
    All, Favourite, Archived, Analytics
}

data class RecordFilter(
    val onlyFavourite: Boolean,
    val archived: Boolean
) {
    companion object {
        fun fromRecordListType(type: RecordListType): RecordFilter {
            return RecordFilter(
                onlyFavourite = when (type) {
                    RecordListType.All, RecordListType.Archived, RecordListType.Analytics -> false
                    RecordListType.Favourite -> true
                },
                archived = type == RecordListType.Archived,
            )
        }
    }
}

data class Record(
    val id: RecordId,
    val creationTime: Instant,
    val creationDate: LocalDate,
    val questions: List<Question>,
    val comments: List<Comment>,
    val moodLevel: Int?,
    val energyLevel: Int?,
    val isFavourite: Boolean,
    val isArchived: Boolean,
    val totalCardIds: List<CardId>,
    val isDailyCardLayout: Boolean,
    val courseId: CourseId?,
    val courseThemeId: CourseThemeId?
) {

    val mainQuestion: Question? get() = questions.firstOrNull()

    val distinctCards = questions.flatMap { it.cardsWithComment }.distinctBy { it.card.id }

    val mainCard: Card? = distinctCards.firstOrNull()?.card

    val isFromCourse: Boolean get() = courseId != null && courseThemeId != null

    companion object {

        const val MAX_FEELING_LEVEL = 5

        fun empty() = Record(
            id = RecordId(""),
            creationTime = Clock.System.now(),
            creationDate = Clock.System.now().toLocalDate(),
            questions = emptyList(),
            comments = emptyList(),
            moodLevel = null,
            energyLevel = null,
            isFavourite = false,
            isArchived = false,
            totalCardIds = emptyList(),
            isDailyCardLayout = false,
            courseId = null,
            courseThemeId = null
        )

        fun mock(id: Int) = Record(
            id = RecordId(id.toString()),
            creationTime = Clock.System.now(),
            creationDate = Clock.System.now().toLocalDate().plus((id - 1), DateTimeUnit.DAY),
            questions = Question.getMockList(),
            comments = Comment.LIST_MOCK,
            moodLevel = 1 + (2 + id) % MAX_FEELING_LEVEL,
            energyLevel = 1 + (3 + id) % MAX_FEELING_LEVEL,
            isFavourite = true,
            isArchived = false,
            totalCardIds = listOf(CardId("1")),
            isDailyCardLayout = false,
            courseId = null,
            courseThemeId = null
        )

        val LIST_MOCK = listOf(mock(1), mock(2), mock(3), mock(4), mock(5), mock(6), mock(7))
    }
}