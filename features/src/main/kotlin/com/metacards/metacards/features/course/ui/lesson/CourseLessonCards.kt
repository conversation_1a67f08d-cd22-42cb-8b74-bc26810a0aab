package com.metacards.metacards.features.course.ui.lesson

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.course.domain.entity.CourseLessonContent

data class CourseLessonCards(
    val name: LocalizableString,
    val cards: List<CourseLessonContent>
) {
    companion object {
        val mock = CourseLessonCards(LocalizableString.createNonLocalizable("CourseLessonCards"), emptyList())
    }
}