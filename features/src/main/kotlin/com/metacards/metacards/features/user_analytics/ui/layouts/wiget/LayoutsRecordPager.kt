package com.metacards.metacards.features.user_analytics.ui.layouts.wiget

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import com.metacards.metacards.core.utils.OnEndReached
import com.metacards.metacards.core.utils.PagedData
import com.metacards.metacards.features.record.ui.record_list.ScrollCommand
import com.metacards.metacards.features.user_analytics.domain.layout.LayoutRecord
import dev.chrisbanes.snapper.ExperimentalSnapperApi
import dev.chrisbanes.snapper.SnapOffsets
import dev.chrisbanes.snapper.SnapperFlingBehavior
import dev.chrisbanes.snapper.SnapperLayoutInfo
import dev.chrisbanes.snapper.SnapperLayoutItemInfo
import dev.chrisbanes.snapper.rememberSnapperFlingBehavior
import kotlinx.coroutines.flow.SharedFlow

@OptIn(ExperimentalSnapperApi::class)
@Composable
fun LayoutsRecordPager(
    pagedData: PagedData<LayoutRecord>,
    scrollCommand: SharedFlow<ScrollCommand?>,
    loadingFirstPage: Boolean,
    hasError: Boolean,
    onLoadMore: () -> Unit,
    indexSelectedRecord: (Int?) -> Unit,
    item: @Composable LazyItemScope.(item: LayoutRecord) -> Unit,
) {
    val lazyListState = rememberLazyListState()
    val flingBehavior = rememberSingleItemFlingBehavior(lazyListState, SnapOffsets.Center)
    val fullyVisibleIndex by rememberFullyVisibleIndex(lazyListState)

    if (pagedData.hasNextPage && !loadingFirstPage && !pagedData.loadingNextPage) {
        lazyListState.OnEndReached(
            callback = onLoadMore,
            itemCountGap = 5,
            scrollingToEndRequired = hasError
        )
    }

    LaunchedEffect(key1 = Unit) {
        scrollCommand.collect { command ->
            when (command) {
                is ScrollCommand.ScrollToIndex -> lazyListState.scrollToItem(command.index)
                else -> {
                    // nothing
                }
            }
        }
    }

    LaunchedEffect(key1 = fullyVisibleIndex) {
        indexSelectedRecord(fullyVisibleIndex)
    }

    LazyRow(
        state = lazyListState,
        flingBehavior = flingBehavior,
        modifier = Modifier.fillMaxWidth(),
        reverseLayout = true
    ) {
        items(
            if (pagedData.loadingNextPage) pagedData.list.dropLast(AMOUNT_EMPTY_RECORD_ITEM) else pagedData.list
        ) { item ->
            item(item)
        }

        if (pagedData.loadingNextPage) {
            repeat(AMOUNT_EMPTY_RECORD_ITEM) {
                item {
                    LayoutAnalyticsCandleSkeleton()
                }
            }
        }
    }
}

@Composable
private fun rememberFullyVisibleIndex(state: LazyListState): State<Int?> {
    return remember {
        derivedStateOf {
            val layoutInfo = state.layoutInfo
            val visibleItemsInfo = layoutInfo.visibleItemsInfo

            if (visibleItemsInfo.isNotEmpty()) {
                val index = visibleItemsInfo
                    .slice(0..visibleItemsInfo.size / 2)
                    .lastOrNull { item ->
                        item.offset + item.size / 2 in layoutInfo.viewportStartOffset..layoutInfo.viewportEndOffset
                    }?.index

                when {
                    !state.canScrollForward -> index
                    visibleItemsInfo.size == 8 -> index?.minus(1)
                    else -> index
                }
            } else {
                null
            }
        }
    }
}

@OptIn(ExperimentalSnapperApi::class)
@Composable
private fun rememberSingleItemFlingBehavior(
    lazyListState: LazyListState,
    snapOffsetForItem: (SnapperLayoutInfo, SnapperLayoutItemInfo) -> Int = SnapOffsets.Start,
): SnapperFlingBehavior {
    return rememberSnapperFlingBehavior(
        lazyListState = lazyListState,
        snapOffsetForItem = snapOffsetForItem,
        snapIndex = { layoutInfo, startIndex, targetIndex ->
            targetIndex
                .coerceIn(startIndex - 1, startIndex + 1)
                .coerceIn(0, layoutInfo.totalItemsCount - 1)
        }
    )
}

private const val AMOUNT_EMPTY_RECORD_ITEM = 3
