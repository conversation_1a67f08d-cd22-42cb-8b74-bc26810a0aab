package com.metacards.metacards.features.special_deck.domain.entity

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.deck.domain.entity.Deck
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.domain.entity.DeckWithAvailable

data class SpecialDeck(
    val id: DeckId,
    val coverUrl: String,
    val hasAR: Boolean,
    val constellations: List<SpecialDeckConstellation>,
    val name: LocalizableString,
    val description: LocalizableString,
) {
    fun toDeck() = Deck(
        id = id,
        name = name,
        availability = Deck.Availability.FREE,
        coverUrl = coverUrl,
        isSpecial = true
    )

    fun toDeckWithAvailable(
        isCompleted: Boolean
    ) = DeckWithAvailable(
        deck = toDeck(),
        available = true,
        isSpecialDeckCompleted = isCompleted
    )
}
