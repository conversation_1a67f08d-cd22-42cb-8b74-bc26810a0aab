package com.metacards.metacards.features.layout.ui.question_for_layout

import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import ru.mobileup.kmm_form_validation.control.InputControl

class FakeQuestionForLayoutComponent : QuestionForLayoutComponent {
    override val maxSymbolsCount: Int = 70
    override val currentSymbolsCount: StateFlow<Int> = MutableStateFlow(0)
    override val questionInputControl: InputControl = InputControl(CoroutineScope(Dispatchers.Main))
    override val tutorialMessage: StateFlow<TutorialMessage?> = MutableStateFlow(null)

    override fun onNextButtonClick() = Unit
    override fun onCloseButtonClick() = Unit
}