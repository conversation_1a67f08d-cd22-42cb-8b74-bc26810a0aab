package com.metacards.metacards.features.course.data.dto

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.course.domain.entity.CoursePassedTestResult
import com.metacards.metacards.features.layout.domain.LayoutId

data class CoursePassedTestResultDto(
    val title: Map<String, String?> = emptyMap(),
    val subtitle: Map<String, String?>? = null,
    val description: Map<String, String?>? = null,
    val cover: String? = null,
    val video: Map<String, String?>? = null,
    val linkedLayouts: List<String>? = null,
    val linkedVideos: List<LinkedVideoDto>? = null
) {
    fun toDomain() = CoursePassedTestResult(
        title = LocalizableString(title),
        subtitle = subtitle?.let(::LocalizableString),
        description = description?.let(::LocalizableString),
        coverUrl = cover,
        videoUrl = video?.let(::LocalizableString),
        linkedLayouts = linkedLayouts?.map(::LayoutId),
        linkedVideos = linkedVideos?.mapNotNull { it.toDomain() }
    )

    companion object {
        fun fromDomain(entity: CoursePassedTestResult) = CoursePassedTestResultDto(
            title = entity.title.toMap(),
            subtitle = entity.subtitle?.toMap(),
            description = entity.description?.toMap(),
            cover = entity.coverUrl,
            video = entity.videoUrl?.toMap(),
            linkedLayouts = entity.linkedLayouts?.map { it.value },
            linkedVideos = entity.linkedVideos?.map { LinkedVideoDto.fromDomain(it) }
        )
    }
}