package com.metacards.metacards.features.auth.ui.auth_type.email.message

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.dialog.default_component.DefaultDialog
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.features.R

@Composable
fun SentMessageToEmailUi(component: SentMessageToEmailComponent) {
    val title by component.title.collectAsState()
    val text by component.text.collectAsState()

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .fillMaxHeight(0.45f)
    ) {
        Column(
            modifier = Modifier
                .matchParentSize()
                .align(Alignment.BottomCenter),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Bottom
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_64_mail),
                contentDescription = null,
                tint = CustomTheme.colors.icons.primary
            )

            Text(
                modifier = Modifier
                    .padding(bottom = 16.dp)
                    .padding(horizontal = 16.dp),
                text = title.localizedByLocal(),
                style = CustomTheme.typography.heading.medium,
                color = CustomTheme.colors.text.primary,
                textAlign = TextAlign.Center
            )

            Text(
                modifier = Modifier.padding(horizontal = 16.dp),
                text = text.localizedByLocal(),
                style = CustomTheme.typography.body.primary,
                color = CustomTheme.colors.text.caption,
                textAlign = TextAlign.Center
            )
        }
    }

    DefaultDialog(dialogControl = component.dismissDialogControl)
}

@Preview
@Composable
fun SentMessageToEmailUiPreview() {
    AppTheme {
        SentMessageToEmailUi(component = FakeSentMessageToEmailComponent())
    }
}