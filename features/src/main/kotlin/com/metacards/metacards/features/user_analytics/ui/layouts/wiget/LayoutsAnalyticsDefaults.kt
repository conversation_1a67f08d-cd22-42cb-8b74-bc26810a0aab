package com.metacards.metacards.features.user_analytics.ui.layouts.wiget

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.TOTAL_DAYS_IN_WEEK
import com.metacards.metacards.core.utils.firstDayOfWeek
import com.metacards.metacards.core.utils.getTodayLocalDate
import com.metacards.metacards.features.R
import com.metacards.metacards.features.user_analytics.ui.layouts.MAX_VISIBLE_CANDLES
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.datetime.DatePeriod
import kotlinx.datetime.LocalDate
import kotlinx.datetime.plus
import java.time.format.TextStyle

@Composable
fun LayoutsAnalyticsDefaults() {
    val locale = LocalContext.current.resources.configuration.locales[0]
    val boxHeight = (LocalConfiguration.current.screenHeightDp * SHARE_GRAPHICS).toInt()
    val boxWidth = ((LocalConfiguration.current.screenWidthDp) / MAX_VISIBLE_CANDLES).dp
    val candleHeight = (boxHeight * SHARE_HALF_CANDLE_HEIGHT_ON_GRAPHICS).toInt().dp
    val weekDays: List<LocalDate> by lazy {
        (0 until TOTAL_DAYS_IN_WEEK).map { day ->
            getTodayLocalDate().firstDayOfWeek() + DatePeriod(days = day)
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row {
            weekDays.forEach { date ->
                Box(
                    modifier = Modifier
                        .height(boxHeight.dp)
                        .width(boxWidth)
                ) {
                    Column(
                        modifier = Modifier
                            .padding(vertical = 12.dp)
                            .align(Alignment.TopCenter)
                    ) {
                        Box(
                            modifier = Modifier
                                .padding(horizontal = boxWidth / SHARE_CANDLE_WIDTH_ON_GRAPHICS)
                        ) {
                            Box(
                                modifier = Modifier
                                    .align(Alignment.BottomCenter)
                                    .size(
                                        width = boxWidth / SHARE_CANDLE_WIDTH_ON_GRAPHICS,
                                        height = candleHeight
                                    )
                                    .drawDashedLine()
                            )
                        }

                        Divider(
                            modifier = Modifier
                                .fillMaxWidth(),
                            color = CustomTheme.colors.background.primary,
                            thickness = 2.dp
                        )

                        Box(
                            modifier = Modifier
                                .padding(horizontal = boxWidth / SHARE_CANDLE_WIDTH_ON_GRAPHICS)
                        ) {
                            Box(
                                modifier = Modifier
                                    .align(Alignment.BottomCenter)
                                    .size(
                                        width = boxWidth / SHARE_CANDLE_WIDTH_ON_GRAPHICS,
                                        height = candleHeight
                                    )
                                    .drawDashedLine()
                            )
                        }
                    }

                    Column(
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .padding(top = 8.dp, bottom = 12.dp)
                            .width(boxWidth / SHARE_DATE_WIDTH_ON_GRAPHICS),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = date.dayOfMonth.toString(),
                            color = CustomTheme.colors.text.secondary,
                            style = CustomTheme.typography.caption.small

                        )
                        Text(
                            text = date.month.getDisplayName(TextStyle.SHORT, locale),
                            color = CustomTheme.colors.text.secondary,
                            style = CustomTheme.typography.caption.small,
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.size(40.dp))
        Icon(
            painter = painterResource(com.metacards.metacards.core.R.drawable.ic_64_galactic),
            contentDescription = null,
            tint = CustomTheme.colors.icons.disabled
        )

        Text(
            text = R.string.record_list_layout_title.strResDesc().localizedByLocal(),
            textAlign = TextAlign.Center,
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .padding(top = 24.dp)
                .fillMaxWidth(),
            style = CustomTheme.typography.heading.medium,
            color = CustomTheme.colors.text.primary
        )

        Text(
            text = R.string.record_list_layout_message.strResDesc().localizedByLocal(),
            textAlign = TextAlign.Center,
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .padding(top = 8.dp)
                .fillMaxWidth(),
            style = CustomTheme.typography.body.primary,
            color = CustomTheme.colors.text.primary
        )
        Spacer(modifier = Modifier.size(100.dp))
    }
}

private fun Modifier.drawDashedLine(): Modifier {
    return Modifier.then(
        drawWithContent {
            drawContent()
            drawLine(
                color = CustomTheme.colors.stroke.secondary,
                start = Offset.Zero,
                end = Offset(0f, size.height),
                strokeWidth = 2f,
                pathEffect = PathEffect.dashPathEffect(floatArrayOf(10f, 10f))
            )
        }
    )
}

const val SHARE_GRAPHICS = 0.45
const val SHARE_HALF_CANDLE_HEIGHT_ON_GRAPHICS = 0.38
const val SHARE_CANDLE_WIDTH_ON_GRAPHICS = 3
const val SHARE_DATE_WIDTH_ON_GRAPHICS = 2
const val MAX_LEVEL_TYPE = 5
