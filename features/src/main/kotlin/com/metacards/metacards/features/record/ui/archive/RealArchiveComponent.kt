package com.metacards.metacards.features.record.ui.archive

import android.os.Parcelable
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.router.stack.childStack
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.utils.toStateFlow
import com.metacards.metacards.features.record.createRecordListComponent
import com.metacards.metacards.features.record.domain.RecordListType
import com.metacards.metacards.features.record.domain.RecordSource
import com.metacards.metacards.features.record.ui.record_list.RecordListComponent
import kotlinx.parcelize.Parcelize

class RealArchiveComponent(
    componentContext: ComponentContext,
    private val componentFactory: ComponentFactory,
    private val onOutput: (ArchiveComponent.Output) -> Unit
) : ComponentContext by componentContext, ArchiveComponent {

    private val navigation = StackNavigation<ChildConfig>()

    override val childStack = childStack(
        source = navigation,
        initialConfiguration = ChildConfig.ArchivedRecordList,
        childFactory = ::createChild
    ).toStateFlow(lifecycle)

    private fun createChild(
        childConfig: ChildConfig,
        componentContext: ComponentContext
    ): ArchiveComponent.Child = when (childConfig) {
        is ChildConfig.ArchivedRecordList -> {
            ArchiveComponent.Child.ArchivedRecordList(
                componentFactory.createRecordListComponent(
                    componentContext,
                    RecordListType.Archived,
                    ::onRecordListOutput
                )
            )
        }
    }

    private fun onRecordListOutput(output: RecordListComponent.Output) {
        when (output) {
            is RecordListComponent.Output.RecordDetailsRequested -> {
                onOutput(
                    ArchiveComponent.Output.RecordDetailsRequested(
                        RecordSource.Viewing(output.recordId),
                        output.recordType,
                        output.isDailyCardLayout,
                        output.courseId,
                        output.courseThemeId
                    )
                )
            }

            RecordListComponent.Output.CreateRecordFlowRequested -> {
                onOutput(ArchiveComponent.Output.CreateRecordFlowRequested)
            }

            RecordListComponent.Output.PremiumSuggestingRequested -> onOutput(ArchiveComponent.Output.SubscriptionSuggestingRequested)
        }
    }

    sealed interface ChildConfig : Parcelable {

        @Parcelize
        object ArchivedRecordList : ChildConfig
    }
}