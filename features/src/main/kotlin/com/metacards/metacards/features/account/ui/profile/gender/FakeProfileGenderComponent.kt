package com.metacards.metacards.features.account.ui.profile.gender

import com.metacards.metacards.core.user.data.UserDto
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakeProfileGenderComponent : ProfileGenderComponent {
    override val selectedGenderFlow: StateFlow<UserDto.Gender?> = MutableStateFlow(UserDto.Gender.MALE)

    override fun onGenderClick(gender: UserDto.Gender) = Unit
    override fun onSaveClick() = Unit
    override fun onDismiss() = Unit
}