package com.metacards.metacards.features.special_deck.data.repository

import com.metacards.metacards.core.error_handling.UnauthorizedException
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.special_deck.data.data_source.SpecialDeckDataSource
import com.metacards.metacards.features.special_deck.data.dto.UserSpecialDeckInfoDto
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeck
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCard
import com.metacards.metacards.features.special_deck.domain.entity.UserSpecialDeckInfo
import com.metacards.metacards.features.special_deck.domain.repository.SpecialDeckRepository
import com.metacards.metacards.features.user.domain.UserRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map

class SpecialDeckRepositoryImpl(
    private val specialDeckDataSource: SpecialDeckDataSource,
    private val userRepository: UserRepository
) : SpecialDeckRepository {

    override suspend fun getSpecialDecks(): List<SpecialDeck> =
        specialDeckDataSource.getSpecialDecks().map { it.toDomain() }

    override suspend fun getSpecialDeckById(deckId: DeckId): SpecialDeck? {
        return specialDeckDataSource.getSpecialDeckById(deckId)?.toDomain()
    }

    override suspend fun updateUserSpecialDeckInfo(info: UserSpecialDeckInfo) {
        val user = userRepository.user.value ?: return
        specialDeckDataSource.updateUserSpecialDeckInfo(user.userId, UserSpecialDeckInfoDto.fromDomain(info))
    }

    override suspend fun getUserSpecialDeckInfoById(
        specialDeckId: DeckId
    ): UserSpecialDeckInfo? {
        val userId = userRepository.user.value?.userId ?: throw UnauthorizedException(
            IllegalStateException("userId is null")
        )

        return specialDeckDataSource
            .getUserSpecialDeckInfoById(userId, specialDeckId)
            ?.toDomain()
    }

    override fun getSpecialDecksFlow(): Flow<List<SpecialDeck>> =
        specialDeckDataSource.getSpecialDecksFlow().map { list ->
            list.map { it.toDomain() }
        }

    override fun getSpecialDeckByIdFlow(deckId: DeckId): Flow<SpecialDeck?> =
        specialDeckDataSource.getSpecialDeckByIdFlow(deckId).map { it?.toDomain() }

    override fun getSpecialDeckCardsByIdFlow(deckId: DeckId): Flow<List<SpecialDeckCard>> =
        specialDeckDataSource.getSpecialDeckCardsByIdFlow(deckId).map {
            it.map { dto -> dto.toDomain() }
        }

    override fun getUserSpecialDecksFlow(): Flow<List<UserSpecialDeckInfo>> {
        val userId = userRepository.user.value?.userId ?: return flowOf(emptyList())
        return specialDeckDataSource.getUserSpecialDecksInfoFlow(userId).map { list ->
            list.map { it.toDomain() }
        }
    }

    override fun getUserSpecialDeckInfoByIdFlow(
        specialDeckId: DeckId
    ): Flow<UserSpecialDeckInfo> = userRepository.user.value?.userId?.let { userId ->
        specialDeckDataSource.getUserSpecialDeckInfoByIdFlow(userId, specialDeckId).map { info ->
            info?.toDomain() ?: UserSpecialDeckInfo.mock(specialDeckId)
        }
    } ?: flowOf(UserSpecialDeckInfo.mock(specialDeckId))
}
