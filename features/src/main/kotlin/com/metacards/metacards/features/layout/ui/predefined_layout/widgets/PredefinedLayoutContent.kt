package com.metacards.metacards.features.layout.ui.predefined_layout.widgets

import androidx.compose.animation.core.tween
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.placeholder.PlaceholderHighlight
import com.metacards.metacards.core.widget.placeholder.placeholder
import com.metacards.metacards.core.widget.placeholder.shimmer
import com.metacards.metacards.features.layout.domain.LayoutId
import com.metacards.metacards.features.layout.domain.PredefinedLayoutWithAvailable

@Composable
fun PredefinedLayoutContent(
    predefinedLayout: PredefinedLayoutWithAvailable,
    onLayoutClick: (LayoutId) -> Unit,
    modifier: Modifier = Modifier,
) {
    var isSkeleton by remember { mutableStateOf(true) }
    val shape = RoundedCornerShape(16.dp)

    Box(
        modifier = modifier
            .fillMaxWidth()
            .aspectRatio(2f)
            .clip(shape)
            .border(
                border = BorderStroke(width = 0.5.dp, color = CustomTheme.colors.stroke.secondary),
                shape = shape
            )
            .clickable { onLayoutClick(predefinedLayout.layout.id) }
            .placeholder(
                visible = isSkeleton,
                color = CustomTheme.colors.background.placeholder,
                shape = shape,
                highlight = PlaceholderHighlight.shimmer(
                    highlightColor = CustomTheme.colors.system.shimmer
                ),
                placeholderFadeTransitionSpec = { tween() },
                contentFadeTransitionSpec = { tween() }
            )
    ) {
        AsyncImage(
            model = predefinedLayout.layout.coverUrl,
            contentDescription = null,
            contentScale = ContentScale.Crop,
            onSuccess = { isSkeleton = false },
            modifier = Modifier
                .fillMaxSize()
                .placeholder(
                    visible = isSkeleton,
                    color = CustomTheme.colors.background.placeholder,
                    shape = shape,
                    highlight = PlaceholderHighlight.shimmer(
                        highlightColor = CustomTheme.colors.system.shimmer
                    ),
                    placeholderFadeTransitionSpec = { tween() },
                    contentFadeTransitionSpec = { tween() }
                )
        )

        Text(
            modifier = Modifier
                .padding(16.dp)
                .clip(CircleShape)
                .background(CustomTheme.colors.button.small, CircleShape)
                .padding(horizontal = 24.dp, vertical = 8.dp),
            text = predefinedLayout.layout.name.localizedByLocal(),
            style = CustomTheme.typography.button.small,
            color = CustomTheme.colors.text.caption,
        )
    }
}

@Preview
@Composable
fun MetaPredefinedLayoutPreview() {
    AppTheme {
        PredefinedLayoutContent(
            predefinedLayout = PredefinedLayoutWithAvailable.mock(),
            onLayoutClick = {}
        )
    }
}