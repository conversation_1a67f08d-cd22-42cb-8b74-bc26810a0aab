package com.metacards.metacards.features.record.domain

import com.metacards.metacards.features.record.data.RecordRepository
import kotlinx.datetime.Instant

class GetRecordsBetweenTimesInteractor(
    private val recordRepository: RecordRepository
) {

    suspend fun execute(startTime: Instant, endTime: Instant): List<Record> {
        return recordRepository.getRecordsBetweenTimes(
            startTime,
            endTime,
            RecordFilter.fromRecordListType(RecordListType.All)
        )
    }
}