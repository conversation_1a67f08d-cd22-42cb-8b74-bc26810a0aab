package com.metacards.metacards.features.user_analytics.ui

import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.createFakeChildStackStateFlow
import com.metacards.metacards.features.user_analytics.ui.layouts.FakeLayoutsAnalyticsComponent
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakeUserAnalyticsComponent : UserAnalyticsComponent {

    override val childStack = createFakeChildStackStateFlow(
        UserAnalyticsComponent.Child.Layouts(FakeLayoutsAnalyticsComponent())
    )

    override val selectedTab = MutableStateFlow(UserAnalyticsComponent.Tab.Layouts)
    override val userSubscriptionState: StateFlow<User.SubscriptionState?> =
        MutableStateFlow(null)

    override fun onTabSelected(tab: UserAnalyticsComponent.Tab) = Unit
    override fun onBlockContentClick() = Unit
}