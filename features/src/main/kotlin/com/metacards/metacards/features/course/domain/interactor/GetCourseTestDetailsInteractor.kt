package com.metacards.metacards.features.course.domain.interactor

import com.metacards.metacards.features.course.domain.entity.CourseTest
import com.metacards.metacards.features.course.domain.repository.CourseRepository

class GetCourseTestDetailsInteractor(
    private val courseRepository: CourseRepository
) {
    suspend fun execute(courseTestQuery: CourseTest.Query): CourseTest? =
        courseRepository.getCourseTestDetails(courseTestQuery)?.let { details ->
            details.copy(questions = details.questions.sortedBy { it.order })
        }
}