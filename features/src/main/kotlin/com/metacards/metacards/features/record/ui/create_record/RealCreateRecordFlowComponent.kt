package com.metacards.metacards.features.record.ui.create_record

import android.os.Parcelable
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.router.stack.childStack
import com.arkivanov.decompose.router.stack.pop
import com.arkivanov.decompose.router.stack.popWhile
import com.arkivanov.decompose.router.stack.push
import com.arkivanov.decompose.router.stack.replaceCurrent
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.utils.toStateFlow
import com.metacards.metacards.features.record.createCameraComponent
import com.metacards.metacards.features.record.createCameraPermissionComponent
import com.metacards.metacards.features.record.createConfirmPhotoComponent
import com.metacards.metacards.features.record.createCreateRecordComponent
import com.metacards.metacards.features.record.domain.camera.CardPhoto
import com.metacards.metacards.features.record.ui.create_record.add_card.AddCardComponent
import com.metacards.metacards.features.record.ui.create_record.camera_permission.CameraPermissionComponent
import com.metacards.metacards.features.record.ui.create_record.main.CreateRecordComponent
import com.metacards.metacards.features.record.ui.create_record.photo_confirm.ConfirmPhotoComponent
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.parcelize.Parcelize

class RealCreateRecordFlowComponent(
    componentContext: ComponentContext,
    screen: CreateRecordFlowComponent.Screen,
    private val componentFactory: ComponentFactory,
    private val onOutput: (CreateRecordFlowComponent.Output) -> Unit
) : ComponentContext by componentContext, CreateRecordFlowComponent {
    private var popToCreateRecordAfterTakePhoto: ChildConfig? = null
    private val createRecordInputFlow =
        MutableSharedFlow<CardPhoto?>(
            replay = 1,
            extraBufferCapacity = 1,
            onBufferOverflow = BufferOverflow.DROP_OLDEST
        )
    private val navigation = StackNavigation<ChildConfig>()

    override val childStack = childStack(
        source = navigation,
        initialConfiguration = screen.toChildConfig(),
        handleBackButton = true,
        childFactory = ::createChild
    ).toStateFlow(lifecycle)

    private fun createChild(
        childConfig: ChildConfig,
        componentContext: ComponentContext
    ): CreateRecordFlowComponent.Child = when (childConfig) {
        is ChildConfig.CreateRecord -> {
            CreateRecordFlowComponent.Child.CreateRecord(
                componentFactory.createCreateRecordComponent(
                    componentContext,
                    createRecordInputFlow,
                    childConfig.isDailyCard,
                    ::onCreateRecordOutput
                )
            )
        }

        is ChildConfig.PermissionCamera -> {
            CreateRecordFlowComponent.Child.PermissionCamera(
                componentFactory.createCameraPermissionComponent(
                    componentContext,
                    ::onCameraPermissionOutput
                )
            )
        }

        is ChildConfig.Camera -> {
            CreateRecordFlowComponent.Child.AddCard(
                componentFactory.createCameraComponent(
                    componentContext,
                    ::onCameraOutput
                )
            )
        }

        is ChildConfig.ConfirmPhoto -> {
            CreateRecordFlowComponent.Child.ConfirmPhoto(
                componentFactory.createConfirmPhotoComponent(
                    componentContext,
                    childConfig.cardPhoto,
                    ::onConfirmPhotoOutput
                )
            )
        }
    }

    sealed interface ChildConfig : Parcelable {
        @Parcelize
        data class CreateRecord(val isDailyCard: Boolean) : ChildConfig

        @Parcelize
        data object PermissionCamera : ChildConfig

        @Parcelize
        data object Camera : ChildConfig

        @Parcelize
        data class ConfirmPhoto(val cardPhoto: CardPhoto) : ChildConfig
    }

    private fun onCreateRecordOutput(output: CreateRecordComponent.Output) {
        popToCreateRecordAfterTakePhoto = childStack.value.active.configuration
        when (output) {
            CreateRecordComponent.Output.PermissionCameraRequested -> {
                navigation.push(ChildConfig.PermissionCamera)
            }

            CreateRecordComponent.Output.CameraRequested -> {
                navigation.push(ChildConfig.Camera)
            }

            CreateRecordComponent.Output.JournalRequested -> {
                onOutput(CreateRecordFlowComponent.Output.JournalRequested)
            }

            CreateRecordComponent.Output.AuthSuggestingRequested -> onOutput(
                CreateRecordFlowComponent.Output.AuthSuggestingRequested
            )

            CreateRecordComponent.Output.SubscriptionSuggestingRequested -> onOutput(
                CreateRecordFlowComponent.Output.SubscriptionSuggestingRequested
            )
        }
    }

    private fun onCameraPermissionOutput(output: CameraPermissionComponent.Output) {
        when (output) {
            CameraPermissionComponent.Output.CameraRequested -> {
                navigation.replaceCurrent(ChildConfig.Camera)
            }

            CameraPermissionComponent.Output.CreateRecordRequested -> {
                navigation.pop()
            }
        }
    }

    private fun onCameraOutput(output: AddCardComponent.Output) {
        when (output) {
            is AddCardComponent.Output.ConfirmPhotoRequested -> {
                navigation.push(ChildConfig.ConfirmPhoto(output.cardPhoto))
            }

            AddCardComponent.Output.PermissionCameraRequested -> {
                navigation.replaceCurrent(ChildConfig.PermissionCamera)
            }
        }
    }

    private fun onConfirmPhotoOutput(output: ConfirmPhotoComponent.Output) {
        when (output) {
            is ConfirmPhotoComponent.Output.CreateRecordRequested -> {
                navigation.popWhile { it != popToCreateRecordAfterTakePhoto }
                createRecordInputFlow.tryEmit(output.cardPhoto)
            }

            is ConfirmPhotoComponent.Output.CameraRequested -> navigation.pop()
        }
    }

    private fun CreateRecordFlowComponent.Screen.toChildConfig(): ChildConfig {
        return when (this) {
            is CreateRecordFlowComponent.Screen.CreateRecord -> ChildConfig.CreateRecord(isDailyCard)
            is CreateRecordFlowComponent.Screen.PermissionCamera -> ChildConfig.PermissionCamera
            is CreateRecordFlowComponent.Screen.Camera -> ChildConfig.Camera
            is CreateRecordFlowComponent.Screen.ConfirmPhoto -> ChildConfig.ConfirmPhoto(cardPhoto)
        }
    }
}
