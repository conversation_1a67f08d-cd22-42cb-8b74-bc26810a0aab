package com.metacards.metacards.features.auth.ui

import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import com.arkivanov.decompose.extensions.compose.jetpack.stack.Children
import com.metacards.metacards.core.utils.defaultTransitionAnimation
import com.metacards.metacards.features.auth.ui.main_auth.MainAuthUi
import com.metacards.metacards.features.auth.ui.sign_in.SignInUi
import com.metacards.metacards.features.auth.ui.sign_up.SignUpUi

@Composable
fun AuthUi(
    component: AuthComponent,
    modifier: Modifier = Modifier,
) {
    val childStack by component.childStack.collectAsState()

    Children(
        stack = childStack,
        modifier = modifier.systemBarsPadding(),
        animation = defaultTransitionAnimation()
    ) { child ->
        when (val instance = child.instance) {
            is AuthComponent.Child.SignIn -> SignInUi(component = instance.component)
            is AuthComponent.Child.SignUp -> SignUpUi(component = instance.component)
            is AuthComponent.Child.MainAuth -> MainAuthUi(component = instance.component)
        }
    }
}