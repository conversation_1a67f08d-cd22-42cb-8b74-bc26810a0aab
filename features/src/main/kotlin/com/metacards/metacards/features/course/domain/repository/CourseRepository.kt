package com.metacards.metacards.features.course.domain.repository

import com.metacards.metacards.features.course.domain.entity.CourseContentId
import com.metacards.metacards.features.course.domain.entity.CourseData
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CourseLesson
import com.metacards.metacards.features.course.domain.entity.CoursePassedCourse
import com.metacards.metacards.features.course.domain.entity.CoursePassedTest
import com.metacards.metacards.features.course.domain.entity.CourseTest
import com.metacards.metacards.features.course.domain.entity.CourseTestDocumentId
import com.metacards.metacards.features.course.domain.entity.CourseTheme
import com.metacards.metacards.features.course.domain.entity.CourseThemeId
import kotlinx.coroutines.flow.Flow

interface CourseRepository {
    fun getCourseThemesFlow(): Flow<List<CourseTheme>>
    fun getCourseDataFlow(themeId: CourseThemeId, courseId: CourseId): Flow<CourseData?>
    fun getUserPassedCoursesFlow(): Flow<List<CoursePassedCourse>>
    fun getUserPassedContentFlow(courseId: CourseId): Flow<CoursePassedCourse?>
    fun getUserPassedTestsFlow(): Flow<List<CoursePassedTest>>
    suspend fun getCourseData(themeId: CourseThemeId, courseId: CourseId): CourseData?
    suspend fun getCourseLessonDetails(query: CourseLesson.Query): CourseLesson?
    suspend fun getUserPassedContent(courseId: CourseId): CoursePassedCourse?
    suspend fun updateUserPassedContent(courseId: CourseId, info: CoursePassedCourse)
    suspend fun getUserPassedTestById(
        testResultId: CourseTestDocumentId?,
        testId: CourseContentId
    ): CoursePassedTest?
    suspend fun addUserPassedTest(info: CoursePassedTest)
    suspend fun getCourseTestDetails(query: CourseTest.Query): CourseTest?
    suspend fun getIsTestTutorialShown(): Boolean
    suspend fun setTestTutorialShown()
}