package com.metacards.metacards.features.user.ui.subscription.subscription_bottom_sheet

import android.os.Parcelable
import com.metacards.metacards.core.button.ButtonState
import kotlinx.coroutines.flow.StateFlow
import kotlinx.parcelize.Parcelize

interface SubscriptionBottomSheetComponent {

    val isWithAdv: Boolean
    val isSpecialDeckCardRequested: Boolean
    val showAdvButtonState: StateFlow<ButtonState>

    fun onLookTariffsClick()
    fun onShowAdvClick()
    fun onCloseClick()

    sealed interface Output {
        data object TariffsRequested : Output
        data object DismissRequested : Output
    }

    @Parcelize
    data class Config(
        val isWithAdv: Boolean = false,
        val isSpecialDeckCardRequested: Boolean = false
    ) : Parcelable
}