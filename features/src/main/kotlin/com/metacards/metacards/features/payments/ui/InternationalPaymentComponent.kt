package com.metacards.metacards.features.payments.ui

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.external_apps.ExternalAppService
import com.metacards.metacards.core.message.data.MessageService
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.features.payments.domain.BillingService
import com.metacards.metacards.features.payments.domain.PurchaseState
import com.metacards.metacards.features.payments.domain.PurchaseType
import dev.icerock.moko.resources.desc.desc
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn

class InternationalPaymentComponent(
    componentContext: ComponentContext,
    private val billingServiceImpl: BillingService,
    private val errorHandler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    private val messageService: MessageService,
    private val externalAppService: ExternalAppService,
    private val onOutput: (PaymentComponent.Output) -> Unit
) : PaymentComponent, ComponentContext by componentContext {
    private val currentPaymentState: StateFlow<PaymentComponent.PaymentState> =
        billingServiceImpl.currentPurchaseState.map {
            when (it) {
                PurchaseState.NotStarted -> PaymentComponent.PaymentState.NotStarted
                is PurchaseState.Purchased -> PaymentComponent.PaymentState.Purchased
                is PurchaseState.InfoReceived,
                is PurchaseState.PurchaseFlowStarted,
                is PurchaseState.Started -> PaymentComponent.PaymentState.Started

                is PurchaseState.Error -> PaymentComponent.PaymentState.Error(it.error.message.desc())
            }
        }.stateIn(componentScope, SharingStarted.Eagerly, PaymentComponent.PaymentState.NotStarted)

    init {
        // maybe for future
//        currentPaymentState
//            .filterIsInstance<PaymentComponent.PaymentState.Error>()
//            .onEach { messageService.showMessage(Message(it.message)) }
//            .launchIn(componentScope)
    }

    override fun startPayment(purchaseType: PurchaseType) {
        componentScope.safeLaunch(errorHandler) {
            billingServiceImpl.processPurchases(purchaseType)
        }
    }

    override fun cancelPaymentFlow() {
        // no-op
    }

    override fun cancelPurchase(id: String): Boolean {
        externalAppService.openSubscriptionSettings(id)
        return false
    }
}