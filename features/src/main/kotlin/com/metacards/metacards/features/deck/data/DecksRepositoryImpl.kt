package com.metacards.metacards.features.deck.data

import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.mapLoadable
import com.metacards.metacards.core.utils.mapLoadableSuspend
import com.metacards.metacards.features.deck.data.dto.DeckDto
import com.metacards.metacards.features.deck.data.dto.toDeckInfoDomain
import com.metacards.metacards.features.deck.data.dto.toDomain
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardHint
import com.metacards.metacards.features.deck.domain.entity.DailyCard
import com.metacards.metacards.features.deck.domain.entity.Deck
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.domain.entity.DeckInfo
import com.metacards.metacards.features.deck.domain.repository.DecksRepository
import com.metacards.metacards.features.payments.domain.BillingService
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

class DecksRepositoryImpl(
    private val decksDataSource: DecksDataSource,
    private val billingService: BillingService,
) : DecksRepository {
    override suspend fun getDecksForPurchase(): List<Deck> {
        return decksDataSource.getDecks().filter { it.availability == Deck.Availability.BUY }
            .map { it.toDomain() }
    }

    override fun getDecksForPurchaseFlow(): Flow<List<Deck>> {
        return decksDataSource.getDecksFlow().map { list ->
            list.map { it.toDomain() }
        }
    }

    override suspend fun getDecksForSubscription(): List<Deck> {
        return decksDataSource.getDecks().filter { it.availability == Deck.Availability.SUB }
            .map { it.toDomain() }
    }

    override suspend fun getFreeDecks(): List<Deck> {
        return decksDataSource.getDecks().filter { it.availability == Deck.Availability.FREE }
            .map { it.toDomain() }
    }

    override fun getCardsByDeckFlow(deckId: DeckId): Flow<LoadableState<List<Card>>> {
        return decksDataSource.getCardsByDeckFlow(deckId).mapLoadable { it?.map { it.toDomain() } }
    }

    override suspend fun getCardsByDeck(deckId: DeckId): List<Card> {
        return decksDataSource.getCardsByDeck(deckId).map { it.toDomain() }
    }

    override fun getDeckById(deckId: DeckId): Flow<LoadableState<Deck>> {
        return decksDataSource.getDeckByIdFlow(deckId).mapLoadable { it?.toDomain() }
    }

    override fun getDeckInfoByIdFlow(deckId: DeckId): Flow<LoadableState<DeckInfo>> {
        return decksDataSource.getDeckByIdFlow(deckId).mapLoadableSuspend { mergeDeck(it) }
    }

    override suspend fun getDeckInfoById(deckId: DeckId): DeckInfo? {
        return decksDataSource.getDeckById(deckId)?.toDeckInfoDomain()
    }

    override suspend fun getCardHints(): List<CardHint> {
        return decksDataSource.getCardHints().map { it.toDomain() }
    }

    override suspend fun getDailyCard(): DailyCard {
        return decksDataSource.getDailyCard().toDomain()
    }

    private suspend fun mergeDeck(deckDto: DeckDto?): DeckInfo? {
        var deckInfo = deckDto?.toDeckInfoDomain()

        if (deckDto?.storeId != null) {
            val inApp = billingService.fetchDeck(deckDto.storeId)
            deckInfo = deckInfo?.copy(price = inApp.price)
        }

        return deckInfo
    }
}