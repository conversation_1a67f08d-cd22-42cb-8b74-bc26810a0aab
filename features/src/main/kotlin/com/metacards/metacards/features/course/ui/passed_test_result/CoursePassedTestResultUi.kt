package com.metacards.metacards.features.course.ui.passed_test_result

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.features.R
import com.metacards.metacards.features.course.ui.test_result.TestResultContent
import com.metacards.metacards.features.course.ui.widgets.CourseLoaderSkeleton
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun CoursePassedTestResultUi(
    component: CoursePassedTestResultComponent,
) {
    val courseResult by component.courseResult.collectAsState()
    val layouts by component.predefinedLayouts.collectAsState()
    val isShareInProgress by component.isShareInProgress.collectAsState()
    val title by component.title.collectAsState()

    courseResult?.let {
        TestResultContent(
            courseResult = it,
            courseName = component.courseName,
            layouts = layouts,
            onBottomButtonClick = component::onRepeatTestClick,
            bottomButtonText = R.string.course_result_button_repeat_test.strResDesc().localizedByLocal(),
            onCloseClick = component::onCloseClick,
            onShareClick = component::onShareClick,
            onVideoFullScreenClick = component::onVideoFullScreenClick,
            isShareInProgress = isShareInProgress,
            onLayoutClick = component::onLayoutClick,
            onBlockContentClick = component::onBlockContentClick,
            title = title
        )
    } ?: CourseLoaderSkeleton()
}