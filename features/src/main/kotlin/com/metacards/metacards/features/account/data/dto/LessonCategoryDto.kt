package com.metacards.metacards.features.account.data.dto

import com.metacards.metacards.core.error_handling.DeserializationException
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.account.domain.entity.LessonCategory
import com.metacards.metacards.features.account.domain.entity.LessonCategoryId

data class LessonCategoryDto(
    val id: String? = null,
    val nameLocalized: Map<String, String?>? = null,
    val order: Int = 0,
    val previewUrl: String? = null
)

fun LessonCategoryDto.toDomain(): LessonCategory {
    val id = id ?: throw DeserializationException(this::id)
    val name = nameLocalized ?: throw DeserializationException(this::nameLocalized)
    val order = order
    val previewUrl = previewUrl ?: throw DeserializationException(this::previewUrl)
    return LessonCategory(
        id = LessonCategoryId(id),
        name = LocalizableString(name),
        order = order,
        previewUrl = previewUrl
    )
}