package com.metacards.metacards.features.account.ui.profile.main

import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.childContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.bottom_sheet.BottomSheetControl
import com.metacards.metacards.core.bottom_sheet.bottomSheetControl
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.clipboard.ClipboardManager
import com.metacards.metacards.core.createDefaultDialogComponent
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.model.DialogData
import com.metacards.metacards.core.dialog.dialogControl
import com.metacards.metacards.core.message.domain.Message
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.user.domain.UserProvider
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.withProgress
import com.metacards.metacards.features.R
import com.metacards.metacards.features.account.createProfileBirthYearComponent
import com.metacards.metacards.features.account.createProfileDeleteAccountComponent
import com.metacards.metacards.features.account.createProfileGenderComponent
import com.metacards.metacards.features.account.ui.profile.ProfileFlowComponent
import com.metacards.metacards.features.account.ui.profile.birth_year.ProfileBirthYearComponent
import com.metacards.metacards.features.account.ui.profile.delete.ProfileDeleteAccountComponent
import com.metacards.metacards.features.account.ui.profile.gender.ProfileGenderComponent
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.auth.domain.GetAuthUserInteractor
import com.metacards.metacards.features.auth.domain.LoginType
import com.metacards.metacards.features.auth.domain.LogoutInteractor
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import com.metacards.metacards.core.R as CoreR

class RealProfileMainComponent(
    componentContext: ComponentContext,
    private val componentFactory: ComponentFactory,
    userProvider: UserProvider,
    getAuthUserInteractor: GetAuthUserInteractor,
    logoutInteractor: LogoutInteractor,
    private val analyticsService: AnalyticsService,
    private val clipboardManager: ClipboardManager,
    private val onOutput: (ProfileMainComponent.Output) -> Unit
) : ComponentContext by componentContext, ProfileMainComponent {

    private val debounce = Debounce()

    private val authUser = getAuthUserInteractor.execute()

    override val userLoginTypes = computed(authUser) { it?.authLoginTypes }

    override val user: StateFlow<User?> = userProvider.getUser()

    override val userEmail = computed(user) { user ->
        user?.email?.value
    }

    override val operationInProgress: MutableStateFlow<Boolean> = MutableStateFlow(false)

    override var deleteAccountComponent = componentFactory.createProfileDeleteAccountComponent(
        childContext("deleteAccountComponent"),
        ::onDeleteAccountOutput
    )

    override val deleteAccountButtonState = MutableStateFlow(ButtonState.Enabled)

    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        dialogControl(dialogComponentFactory = { config, componentContext, dialogControl ->
            componentFactory.createDefaultDialogComponent(
                componentContext,
                config.data,
                dialogControl::dismiss
            )
        })

    private fun onDeleteAccountOutput(output: ProfileDeleteAccountComponent.Output) {
        when (output) {
            is ProfileDeleteAccountComponent.Output.AuthScreenRequested -> {
                onOutput(ProfileMainComponent.Output.AuthScreenRequested)
            }

            is ProfileDeleteAccountComponent.Output.PasswordConfirmationRequested -> navigateTo(
                ProfileFlowComponent.Screen.ConfirmPassword(
                    output.toolbarTextRes,
                    output.textFieldHeaderRes,
                    R.string.profile_delete_password_confirm_button_confirm
                )
            )

            is ProfileDeleteAccountComponent.Output.SignInViaWebViewRequested -> {
                onOutput(
                    ProfileMainComponent.Output.SignInViaWebViewRequested(
                        output.url,
                        output.title
                    )
                )
            }

            is ProfileDeleteAccountComponent.Output.WebViewDismissRequested -> {
                onOutput(ProfileMainComponent.Output.WebViewDismissRequested)
            }
        }
    }

    override val birthYearBottomSheetControl:
            BottomSheetControl<ProfileBirthYearComponent.Config, ProfileBirthYearComponent> =
        bottomSheetControl(
            key = "birthYearBottomSheetControl",
            bottomSheetComponentFactory = { _, context, _ ->
                componentFactory.createProfileBirthYearComponent(
                    context,
                    user.value?.yearOfBirth,
                    ::onBirthYearOutput
                )
            },
            halfExpandingSupported = false,
            hidingSupported = true,
            handleBackButton = true
        )

    override val genderBottomSheetControl: BottomSheetControl<ProfileGenderComponent.Config, ProfileGenderComponent> =
        bottomSheetControl(
            key = "genderBottomSheetControl",
            bottomSheetComponentFactory = { _, context, _ ->
                componentFactory.createProfileGenderComponent(
                    context,
                    user.value?.gender,
                    ::onGenderOutput
                )
            },
            halfExpandingSupported = false,
            hidingSupported = true,
            handleBackButton = true
        )

    private fun onGenderOutput(output: ProfileGenderComponent.Output) {
        when (output) {
            is ProfileGenderComponent.Output.DismissRequested -> genderBottomSheetControl.dismiss()
        }
    }

    private fun onBirthYearOutput(output: ProfileBirthYearComponent.Output) {
        when (output) {
            is ProfileBirthYearComponent.Output.DismissRequested -> birthYearBottomSheetControl.dismiss()
        }
    }

    private val logoutDialogConfig by lazy {
        DefaultDialogComponent.Config(
            DialogData(
                title = StringDesc.Resource(R.string.account_profile_dialog_logout_title),
                message = StringDesc.Resource(R.string.account_profile_dialog_logout_text),
                buttons = listOf(
                    DialogData.Button(
                        title = StringDesc.Resource(R.string.account_profile_dialog_logout_button_dismiss),
                        action = dialogControl::dismiss
                    ),
                    DialogData.Button(
                        title = StringDesc.Resource(R.string.account_profile_dialog_logout_button_proceed),
                        action = {
                            DebounceClick(debounce, "onLogoutClick") {
                                dialogControl.dismiss()
                                withProgress(operationInProgress) {
                                    logoutInteractor.execute()
                                    analyticsService.logEvent(AnalyticsEvent.ProfileLogoutEvent)
                                    user.first { it == null } // ждем пока данные не обновятся
                                    onOutput(ProfileMainComponent.Output.AuthScreenRequested)
                                }
                            }
                        }
                    )
                )
            )
        )
    }

    private val nonEditableFieldDialogConfig by lazy {
        DefaultDialogComponent.Config(
            DialogData(
                title = StringDesc.Resource(R.string.account_profile_dialog_non_editable_field_title),
                message = StringDesc.Resource(R.string.account_profile_dialog_non_editable_field_text),
                buttons = listOf(
                    DialogData.Button(
                        title = StringDesc.Resource(CoreR.string.common_ok),
                        action = dialogControl::dismiss
                    )
                )
            )
        )
    }

    override fun onNameClick() =
        navigateTo(ProfileFlowComponent.Screen.Name(user.value?.name ?: ""))

    override fun onGenderClick() {
        genderBottomSheetControl.show(ProfileGenderComponent.Config)
    }

    override fun onBirthYearClick() {
        birthYearBottomSheetControl.show(ProfileBirthYearComponent.Config)
    }

    override fun onEmailClick() {
        if (userLoginTypes.value?.any { it is LoginType.SSO } == true) {
            dialogControl.show(nonEditableFieldDialogConfig)
        } else {
            navigateTo(ProfileFlowComponent.Screen.Email)
        }
    }

    override fun onChangePasswordClick() = navigateTo(ProfileFlowComponent.Screen.Password)

    override fun onLogoutClick() {
        dialogControl.show(logoutDialogConfig)
    }

    override fun onDeleteAccountClick() {
        deleteAccountButtonState.update { ButtonState.Loading }
        deleteAccountComponent.startReauthentication {
            deleteAccountButtonState.update { ButtonState.Enabled }
        }
    }

    override fun onPasswordConfirmed() {
        deleteAccountComponent.onPasswordConfirmed()
    }

    override fun onSubscriptionStatusClick() {
        onOutput(ProfileMainComponent.Output.SubscriptionRequested)
    }

    override fun onSubscriptionStatusLongPress() {
        val userId = user.value?.userId?.value ?: return
        val message = Message(StringDesc.Resource(R.string.account_profile_subscription_user_id_copied))
        clipboardManager.copyToClipboard(userId, message)
    }

    private fun navigateTo(screen: ProfileFlowComponent.Screen) {
        onOutput(ProfileMainComponent.Output.ProfileFlowScreenRequested(screen))
    }
}
