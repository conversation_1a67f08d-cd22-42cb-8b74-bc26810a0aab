package com.metacards.metacards.features.record.data.dto

import com.google.firebase.Timestamp
import com.google.firebase.firestore.PropertyName
import com.metacards.metacards.core.utils.toKotlinInstant
import com.metacards.metacards.core.utils.toLocalDate
import com.metacards.metacards.core.utils.toTimestamp
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CourseThemeId
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.record.domain.Record
import com.metacards.metacards.features.record.domain.RecordId

class RecordDto(
    var id: String = "",
    val creationDate: Timestamp = Timestamp.now(),
    val questions: List<QuestionDto> = emptyList(),
    val comments: List<CommentDto>? = null,
    val moodLevel: Int? = null,
    val energyLevel: Int? = null,

    @get:PropertyName("isFavourite") // required for a field that starts with "is". See: https://medium.com/@eeddeellee/boolean-fields-that-start-with-is-in-firebase-firestore-49afb65e3639
    @set:PropertyName("isFavourite")
    var isFavourite: Boolean = false,

    @get:PropertyName("isArchived")
    @set:PropertyName("isArchived")
    var isArchived: Boolean = false,

    val totalCards: List<String> = emptyList(),

    @get:PropertyName("isDailyCardLayout")
    val isDailyCardLayout: Boolean? = null,
    val courseId: String? = null,
    val courseThemeId: String? = null
) {
    fun copy(
        id: String = this.id,
        creationDate: Timestamp = this.creationDate,
        questions: List<QuestionDto> = this.questions,
        comments: List<CommentDto>? = this.comments,
        moodLevel: Int? = this.moodLevel,
        energyLevel: Int? = this.energyLevel,
        isFavourite: Boolean = this.isFavourite,
        isArchived: Boolean = this.isArchived,
        totalCards: List<String> = this.totalCards,
        isDailyCardLayout: Boolean = this.isDailyCardLayout ?: false,
        courseId: String? = this.courseId,
        courseThemeId: String? = this.courseThemeId
    ): RecordDto {
        return RecordDto(
            id,
            creationDate,
            questions,
            comments,
            moodLevel,
            energyLevel,
            isFavourite,
            isArchived,
            totalCards,
            isDailyCardLayout,
            courseId,
            courseThemeId
        )
    }
}

fun RecordDto.toDomain(): Record {
    return Record(
        id = RecordId(id),
        creationTime = creationDate.toKotlinInstant(),
        creationDate = creationDate.toKotlinInstant().toLocalDate(),
        questions = questions.map { it.toDomain() }.sortedBy { it.order },
        comments = comments.orEmpty().filter { it.comment.isNotBlank() }.map { it.toDomain() },
        moodLevel = moodLevel,
        energyLevel = energyLevel,
        isFavourite = isFavourite,
        isArchived = isArchived,
        totalCardIds = totalCards.map { CardId(it) },
        isDailyCardLayout = isDailyCardLayout ?: false,
        courseId = courseId?.let(::CourseId),
        courseThemeId = courseThemeId?.let(::CourseThemeId)
    )
}

fun Record.toDto(): RecordDto {
    return RecordDto(
        id = id.value,
        creationDate = creationTime.toTimestamp(),
        questions = questions.map { it.toDto() },
        comments = comments.map { it.toDto() },
        moodLevel = moodLevel,
        energyLevel = energyLevel,
        isFavourite = isFavourite,
        isArchived = isArchived,
        totalCards = totalCardIds.map { it.value },
        isDailyCardLayout = isDailyCardLayout,
        courseId = courseId?.value
    )
}