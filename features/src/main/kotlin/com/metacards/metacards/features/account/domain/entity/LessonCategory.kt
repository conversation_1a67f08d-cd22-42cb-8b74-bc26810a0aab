package com.metacards.metacards.features.account.domain.entity

import android.os.Parcelable
import com.metacards.metacards.core.localization.ui.LocalizableString
import kotlinx.parcelize.Parcelize

@Parcelize
data class LessonCategoryId(val value: String) : Parcelable

data class LessonCategory(
    val id: LessonCategoryId,
    val name: LocalizableString,
    val order: Int,
    val previewUrl: String
) {
    companion object {
        fun mock() = LessonCategory(
            id = LessonCategoryId("1"),
            name = LocalizableString.createNonLocalizable(""),
            order = 0,
            previewUrl = ""
        )
    }
}
