package com.metacards.metacards.features.advbanner.ui

import androidx.annotation.DrawableRes
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.features.R
import com.metacards.metacards.features.advbanner.domain.entity.AccountAdvBanner
import com.metacards.metacards.features.advbanner.domain.entity.AccountAdvBannerStyle
import com.metacards.metacards.features.advbanner.domain.entity.AccountAdvBannerType
import com.metacards.metacards.features.home.ui.widget.ActionButton

@Composable
fun AccountAdvBannerUi(
    accountAdvBanner: AccountAdvBanner,
    onClick: (AccountAdvBannerType) -> Unit,
    modifier: Modifier = Modifier
) {
    @DrawableRes val bg = when (accountAdvBanner.style) {
        AccountAdvBannerStyle.STARS -> R.drawable.ic_account_banner_bg_discount
        AccountAdvBannerStyle.CIRCLES -> R.drawable.ic_account_banner_bg_premium
    }

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(16.dp))
            .clickable { onClick(accountAdvBanner.type) }
            .paint(painter = painterResource(bg), contentScale = ContentScale.FillBounds)
    ) {
        Text(
            text = accountAdvBanner.title.localizedByLocal(),
            color = CustomTheme.colors.text.primary,
            style = CustomTheme.typography.heading.additional,
            modifier = Modifier
                .padding(horizontal = 32.dp)
                .padding(top = 20.dp)
        )

        accountAdvBanner.description?.let {
            Text(
                text = it.localizedByLocal(),
                color = CustomTheme.colors.text.primary,
                style = CustomTheme.typography.caption.medium,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .padding(horizontal = 32.dp)
                    .padding(top = 4.dp)
            )
        }

        ActionButton(
            text = accountAdvBanner.buttonText,
            modifier = Modifier
                .padding(top = 12.dp, bottom = 16.dp)
        )
    }
}
