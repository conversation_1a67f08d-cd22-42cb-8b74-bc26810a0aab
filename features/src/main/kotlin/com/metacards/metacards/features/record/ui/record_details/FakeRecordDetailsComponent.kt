package com.metacards.metacards.features.record.ui.record_details

import com.metacards.metacards.core.bottom_sheet.BottomSheetControl
import com.metacards.metacards.core.bottom_sheet.FakeBottomSheetControl
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.FakeDialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.FakeDefaultDialogComponent
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.features.record.domain.Comment
import com.metacards.metacards.features.record.domain.Record
import com.metacards.metacards.features.record.ui.model.FeelingItem
import com.metacards.metacards.features.record.ui.record_details.comment_details.CommentDetailsComponent
import com.metacards.metacards.features.record.ui.record_details.comment_details.FakeCommentDetailsComponent
import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import ru.mobileup.kmm_form_validation.control.InputControl

class FakeRecordDetailsComponent : RecordDetailsComponent {
    override val recordType = RecordDetailsComponent.RecordType.Common
    override val record: StateFlow<LoadableState<Record>> =
        MutableStateFlow(LoadableState(data = null))
    override val userSubscriptionState: StateFlow<User.SubscriptionState?> =
        MutableStateFlow<User.SubscriptionState?>(null)
    @OptIn(DelicateCoroutinesApi::class)
    override val commentInputControl: InputControl = InputControl(GlobalScope)
    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        FakeDialogControl(FakeDefaultDialogComponent())
    override val bottomSheetControl: BottomSheetControl<CommentDetailsComponent.Config, CommentDetailsComponent> =
        FakeBottomSheetControl(FakeCommentDetailsComponent())
    override val moodLevel: StateFlow<FeelingItem<Int>?> = MutableStateFlow(null)
    override val energyLevel: StateFlow<FeelingItem<Int>?> = MutableStateFlow(null)
    override val isDataChanged: StateFlow<Boolean> = MutableStateFlow(false)
    override val isLoading: StateFlow<Boolean> = MutableStateFlow(false)
    override val buttonState: StateFlow<ButtonState> = MutableStateFlow(ButtonState.Enabled)
    override val isRecordInFavorites: StateFlow<Boolean> = MutableStateFlow(false)
    override val isDailyCard: Boolean = false
    override val courseName: StateFlow<LocalizableString?> = MutableStateFlow(null)
    override val tutorialMessage: StateFlow<TutorialMessage?> = MutableStateFlow(null)

    override fun onCardClick(cardIndex: Int, questionIndex: Int) = Unit
    override fun onFavoriteClick() = Unit
    override fun onBlockAreaClick() = Unit
    override fun onAuthButtonClick() = Unit
    override fun onSkipButtonClick() = Unit
    override fun onCompleteButtonClick() = Unit
    override fun onQuestionChanged(text: LocalizableString) = Unit
    override fun onMoodLevelChange(newItem: FeelingItem<Int>?) = Unit
    override fun onEnergyLevelChange(newItem: FeelingItem<Int>?) = Unit
    override fun onCommentClick(comment: Comment) = Unit
    override fun onCardCommentClick(cardImageUrl: String, commentText: String) = Unit
}
