package com.metacards.metacards.features.record.ui.journal.test_list

import android.content.Context
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsBottomHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.utils.prettyFormat
import com.metacards.metacards.core.widget.EmptyPlaceholder
import com.metacards.metacards.features.R
import com.metacards.metacards.features.course.domain.entity.CoursePassedTest
import dev.icerock.moko.resources.StringResource
import dev.icerock.moko.resources.desc.ResourceFormattedStringDesc
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun JournalTestListUi(
    component: JournalTestListComponent,
    navBarHeight: Dp,
    modifier: Modifier = Modifier
) {
    val tests by component.tests.collectAsState()

    if (tests.isEmpty()) {
        EmptyPlaceholder(
            title = R.string.journal_tests_empty_title.strResDesc().localizedByLocal(),
            message = R.string.journal_tests_empty_text.strResDesc().localizedByLocal(),
            modifier = modifier.padding(bottom = 130.dp)
        )
    } else {
        Content(
            tests = tests,
            onTestClick = component::onTestClick,
            navBarHeight = navBarHeight,
            modifier = modifier
        )
    }
}

@Composable
private fun Content(
    tests: List<CoursePassedTest>,
    onTestClick: (CoursePassedTest) -> Unit,
    navBarHeight: Dp,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    LazyColumn(
        modifier = modifier,
    ) {
        itemsIndexed(
            items = tests,
            key = { _, test -> test.id ?: test.hashCode() }
        ) { index, test ->
            TestItem(
                test = test,
                onClick = { onTestClick(test) },
                date = getDateIfNeed(test, tests.getOrNull(index - 1), context)
            )
        }
        item {
            Spacer(Modifier.height(navBarHeight - 8.dp))
        }
        item {
            Spacer(Modifier.windowInsetsBottomHeight(WindowInsets.systemBars))
        }
    }
}

private fun getDateIfNeed(
    test: CoursePassedTest,
    previousTest: CoursePassedTest?,
    context: Context
): String? = previousTest?.getLocalDate().let { previousDate ->
    val current = test.getLocalDate()
    if (current != previousDate) current.prettyFormat(context) else null
}

@Composable
private fun TestItem(
    test: CoursePassedTest,
    onClick: () -> Unit,
    date: String?,
) {
    date?.let {
        Text(
            text = it,
            style = CustomTheme.typography.caption.medium,
            color = CustomTheme.colors.text.secondary,
            modifier = Modifier
                .padding(horizontal = 32.dp, vertical = 8.dp)
        )
    }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
            .clip(RoundedCornerShape(16.dp))
            .background(CustomTheme.colors.background.primary)
            .clickable(onClick = onClick)

    ) {
        Spacer(modifier = Modifier.width(16.dp))

        Image(
            painter = painterResource(id = R.drawable.ic_journal_test_preview),
            contentDescription = null,
            modifier = Modifier
                .padding(vertical = 16.dp)
        )

        Spacer(modifier = Modifier.width(20.dp))

        Column(
            modifier = Modifier
                .weight(1f)
        ) {
            Row {
                Text(
                    text = R.string.journal_tests_test_results.strResDesc().localizedByLocal(),
                    color = CustomTheme.colors.text.primary,
                    style = CustomTheme.typography.body.primary,
                    modifier = Modifier
                        .padding(top = 16.dp)
                )

                Spacer(modifier = Modifier.weight(1f))

                Icon(
                    painter = painterResource(R.drawable.star_6),
                    contentDescription = null,
                    tint = CustomTheme.colors.icons.disabled,
                    modifier = Modifier
                        .padding(top = 16.dp, end = 26.dp)
                        .size(14.dp)
                )
            }

            Row {
                Text(
                    text = ResourceFormattedStringDesc(
                        StringResource(R.string.journal_tests_course_name),
                        listOf(test.name.localizedByLocal())
                    ).localizedByLocal(),
                    color = CustomTheme.colors.text.primary,
                    style = CustomTheme.typography.body.primary,
                )

                Spacer(modifier = Modifier.weight(1f))

                Icon(
                    painter = painterResource(R.drawable.star_6),
                    contentDescription = null,
                    tint = CustomTheme.colors.icons.disabled,
                    modifier = Modifier
                        .padding(top = 4.dp, end = 14.dp)
                        .size(12.dp)
                )
            }
        }
    }

    Spacer(modifier = Modifier.height(8.dp))
}