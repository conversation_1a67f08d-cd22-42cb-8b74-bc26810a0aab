package com.metacards.metacards.features.main.ui

import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.core.preferences.models.TutorialState
import com.metacards.metacards.core.widget.navigation_bar.NavigationItem
import com.metacards.metacards.core.widget.navigation_bar.NavigationPage
import com.metacards.metacards.features.account.ui.AccountComponent
import com.metacards.metacards.features.account.ui.flow.AccountFlowComponent
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CourseThemeId
import com.metacards.metacards.features.course.ui.CourseComponent
import com.metacards.metacards.features.deck.domain.entity.DailyCard
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.favorite_cards.ui.FavoriteCardsComponent
import com.metacards.metacards.features.home.ui.HomeComponent
import com.metacards.metacards.features.layout.domain.CardSource
import com.metacards.metacards.features.record.domain.RecordSource
import com.metacards.metacards.features.record.ui.journal.JournalComponent
import com.metacards.metacards.features.record.ui.record_details.RecordDetailsComponent
import com.metacards.metacards.features.showcase.ui.ShowcaseComponent
import com.metacards.metacards.features.tutorial.data.TutorialStep
import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import com.metacards.metacards.features.user_analytics.ui.UserAnalyticsComponent
import kotlinx.coroutines.flow.StateFlow
import kotlinx.datetime.Instant

interface MainComponent {
    val childStack: StateFlow<ChildStack<*, Child>>
    val navigationItems: StateFlow<List<NavigationItem>>

    val dailyCardState: StateFlow<DailyCard?>
    val tutorialState: StateFlow<TutorialState>
    val isShakerPopupVisible: StateFlow<Boolean>

    val tutorialMessage: StateFlow<TutorialMessage?>
    val tutorialStep: StateFlow<TutorialStep>

    fun onNavigationPageChange(
        navigationPage: NavigationPage,
        analyticsInitialTab: UserAnalyticsComponent.Tab = UserAnalyticsComponent.Tab.Layouts
    )

    fun onShakeDetected()
    fun onShakerPopupDismiss()
    fun showJournalRecordsForDate(time: Instant)

    sealed interface Child {
        class Home(val component: HomeComponent) : Child
        class Journal(val component: JournalComponent) : Child
        class Account(val component: AccountComponent) : Child
        class Showcase(val component: ShowcaseComponent) : Child
    }

    sealed interface Output {
        class NewDeckLayoutRequested(val deckId: DeckId) : Output
        class DeckScreenRequested(val deckId: DeckId) : Output
        data object PredefinedLayoutsRequested : Output
        data object CalendarRequested : Output
        data object ArchiveRequested : Output
        data class RecordDetailsRequested(
            val recordSource: RecordSource,
            val recordType: RecordDetailsComponent.RecordType,
            val courseId: CourseId?,
            val courseThemeId: CourseThemeId?
        ) : Output

        data class AccountFlowScreenRequested(val screen: AccountFlowComponent.Screen) : Output
        data class FavoriteCardsRequested(val screen: FavoriteCardsComponent.Screen) : Output
        data object AuthScreenRequested : Output
        data object AddPhysicalDeckRequested : Output
        data class CreateRecordFlowScreenRequested(val isDailyCard: Boolean) : Output
        data class DailyCardRequested(val cardSource: CardSource.DailyCardSource) : Output
        data object AuthSuggestingScreenRequested : Output
        data class SubscriptionSuggestingScreenRequested(val withAdv: Boolean) : Output
        data object SubscriptionScreenRequested : Output
        data class SpecialDeckRequested(val deckId: DeckId, val forceOnboarding: Boolean) : Output
        data class PredefinedLayoutDetailsRequested(val cardSource: CardSource) : Output
        data object AnalyticsRequested : Output
        data class CourseRequested(val screen: CourseComponent.Screen) : Output
    }
}
