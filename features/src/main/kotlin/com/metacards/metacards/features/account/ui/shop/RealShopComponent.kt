package com.metacards.metacards.features.account.ui.shop

import android.net.Uri
import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.external_apps.ExternalAppService
import com.metacards.metacards.core.localization.data.AppLanguageService
import com.metacards.metacards.features.account.domain.interactor.GetDecksForPurchaseWithCardsInteractor
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.deck.domain.entity.DeckId
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow

class RealShopComponent(
    componentContext: ComponentContext,
    getDecksForPurchaseWithCardsInteractor: GetDecksForPurchaseWithCardsInteractor,
    private val externalAppService: ExternalAppService,
    private val appLanguageService: AppLanguageService,
    private val analyticsService: AnalyticsService,
    private val onOutput: (ShopComponent.Output) -> Unit
) : ComponentContext by componentContext, ShopComponent {

    private val debounce = Debounce()

    companion object {
        private const val DECKS_SHOP_WEBSITE = "https://feodoridy.com" // TODO: replace by real
    }

    override val decksForPurchaseInfoWithCards = getDecksForPurchaseWithCardsInteractor.execute()

    override val currentDeckPosition: MutableStateFlow<Int> = MutableStateFlow(3)

    override val scrollToPageCommand: MutableSharedFlow<Int> =
        MutableSharedFlow(
            replay = 1,
            extraBufferCapacity = 1,
            onBufferOverflow = BufferOverflow.DROP_OLDEST
        )

    override fun onGoToWebSiteClick() {
        DebounceClick(debounce, "onGoToWebSiteClick") {
            analyticsService.logEvent(AnalyticsEvent.ShopToSiteEvent)
            externalAppService.openBrowser(Uri.parse(DECKS_SHOP_WEBSITE))
        }
    }

    override fun onDeckSwipe(newPosition: Int) {
        currentDeckPosition.value = newPosition
    }

    override fun onDeckClick(deckId: DeckId) {
        val deckName = decksForPurchaseInfoWithCards.value.find {
            it.deckInfo.deckId.value == deckId.value
        }?.deckInfo?.name?.toString(appLanguageService.getLanguage())
        analyticsService.logEvent(
            AnalyticsEvent.ShopDeckTapEvent(
                deckId = deckId.value,
                deckName = deckName ?: ""
            )
        )
        onOutput(ShopComponent.Output.DeckDetailsRequested(deckId))
    }
}