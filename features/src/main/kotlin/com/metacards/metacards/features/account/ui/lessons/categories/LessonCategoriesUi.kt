package com.metacards.metacards.features.account.ui.lessons.categories

import android.webkit.URLUtil
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.localization.ui.localizedByLocalNullable
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.utils.navigationBarsPaddingDp
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.account.domain.entity.LessonCategory
import com.metacards.metacards.features.deck.ui.MetaCardDefaults
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun LessonCategoriesUi(
    component: LessonCategoriesComponent,
    modifier: Modifier = Modifier,
) {
    val categories by component.categoriesState.collectAsState()

    BoxWithFade(modifier, listOfColors = CustomTheme.colors.gradient.backgroundList) {
        Column(modifier = Modifier.matchParentSize()) {
            TopNavigationBar(
                title = R.string.account_lessons_title.strResDesc(),
                leadingIcon = { BackNavigationItem() },
            )
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.spacedBy(8.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                contentPadding = PaddingValues(
                    start = 16.dp,
                    end = 16.dp,
                    top = 8.dp,
                    bottom = 8.dp + navigationBarsPaddingDp
                )
            ) {
                item {
                    SuggestionBlock(
                        onClick = component::onTutorialClick,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )
                }
                items(
                    items = categories,
                    key = { it.order }
                ) { lessonCategory ->
                    LessonCategoryCard(
                        lessonCategory = lessonCategory,
                        onClick = { component.onCategoryClick(lessonCategory.id) },
                    )
                }
            }
        }
    }
}

@Composable
private fun SuggestionBlock(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Card(
        backgroundColor = CustomTheme.colors.background.primary,
        shape = RoundedCornerShape(16.dp),
        elevation = 0.dp,
        modifier = modifier
            .clip(RoundedCornerShape(16.dp))
            .fillMaxWidth()
            .clickable(onClick = onClick)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Image(
                painter = painterResource(id = R.drawable.bg_variant_3),
                contentDescription = null
            )
            Column {
                Text(
                    text = R.string.account_lessons_tutorial_title.strResDesc().localizedByLocal(),
                    color = CustomTheme.colors.text.primary,
                    style = CustomTheme.typography.heading.medium
                )
                Text(
                    text = R.string.account_lessons_tutorial_caption.strResDesc()
                        .localizedByLocal(),
                    color = CustomTheme.colors.text.secondary,
                    style = CustomTheme.typography.caption.medium
                )
            }
        }
    }
}

@Composable
fun LessonCategoryCard(
    lessonCategory: LessonCategory,
    modifier: Modifier = Modifier,
    backgroundColor: Color = CustomTheme.colors.background.primary,
    onClick: (() -> Unit)? = null,
) {
    Surface(
        modifier = modifier,
        color = backgroundColor,
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .then(
                    if (onClick != null) {
                        Modifier.clickable(onClick = onClick)
                    } else {
                        Modifier
                    }
                )
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Box {
                if (URLUtil.isValidUrl(lessonCategory.previewUrl)) {
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(lessonCategory.previewUrl)
                            .crossfade(MetaCardDefaults.CROSSFADE_DURATION_MILLIS)
                            .build(),
                        contentScale = ContentScale.Crop,
                        contentDescription = null,
                        modifier = Modifier
                            .fillMaxSize()
                            .aspectRatio(2.05f)
                            .padding(bottom = 8.dp)
                            .clip(RoundedCornerShape(16.dp)),
                    )
                } else {
                    Image(
                        painter = painterResource(R.drawable.logo_512),
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier
                            .fillMaxSize()
                            .aspectRatio(2.05f)
                            .padding(bottom = 8.dp)
                            .clip(RoundedCornerShape(16.dp)),
                    )
                }
            }

            lessonCategory.name.localizedByLocalNullable()?.let { name ->
                Text(
                    modifier = Modifier
                        .padding(vertical = 4.dp),
                    text = name,
                    color = CustomTheme.colors.text.primary,
                    style = CustomTheme.typography.body.secondary
                )
            }
        }
    }
}