package com.metacards.metacards.features.account.ui.profile.main

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Card
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.bottom_sheet.ModalBottomSheet
import com.metacards.metacards.core.dialog.default_component.DefaultDialog
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.formatWithPattern
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.FullscreenCircularProgress
import com.metacards.metacards.core.widget.button.MetaSecondaryButton
import com.metacards.metacards.core.widget.button.MetaTransparentButton
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.account.ui.profile.birth_year.ProfileBirthYearUi
import com.metacards.metacards.features.account.ui.profile.gender.ProfileGenderUi
import com.metacards.metacards.features.account.ui.widgets.AccountListItemTrailing
import com.metacards.metacards.features.account.ui.widgets.ProfileListItem
import com.metacards.metacards.features.auth.domain.LoginType
import dev.icerock.moko.resources.StringResource
import dev.icerock.moko.resources.desc.ResourceFormattedStringDesc
import dev.icerock.moko.resources.desc.strResDesc

private const val DATE_FORMAT_PATTERN = "dd.MM.yyyy"

@Composable
fun ProfileMainUi(
    component: ProfileMainComponent,
    modifier: Modifier = Modifier
) {
    val user by component.user.collectAsState()
    val userLoginTypes by component.userLoginTypes.collectAsState()
    val userEmail by component.userEmail.collectAsState()
    val density = LocalDensity.current
    var bottomButtonsPadding by remember { mutableStateOf(0.dp) }
    val deleteAccountButtonState by component.deleteAccountButtonState.collectAsState()
    val operationInProgress by component.operationInProgress.collectAsState()

    BoxWithFade(modifier, listOfColors = CustomTheme.colors.gradient.backgroundList) {
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = bottomButtonsPadding + 16.dp)
                .verticalScroll(rememberScrollState())
        ) {
            TopNavigationBar(
                title = R.string.account_profile_title.strResDesc(),
                leadingIcon = { BackNavigationItem() },
                modifier = Modifier.padding(bottom = 8.dp)
            )

            ProfileListItem(
                leadingText = R.string.account_profile_name.strResDesc(),
                trailingContent = {
                    AccountListItemTrailing(trailingText = user?.name)
                },
                onClick = component::onNameClick,
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            ProfileListItem(
                leadingText = R.string.account_profile_gender.strResDesc(),
                trailingContent = {
                    AccountListItemTrailing(trailingText = user?.gender?.string?.localizedByLocal())
                },
                onClick = component::onGenderClick,
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            ProfileListItem(
                leadingText = R.string.account_profile_birth_year.strResDesc(),
                trailingContent = {
                    AccountListItemTrailing(trailingText = user?.yearOfBirth?.toString())
                },
                onClick = component::onBirthYearClick,
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            if (userEmail != null) { // Google also has email
                ProfileListItem(
                    leadingText = R.string.account_profile_email.strResDesc(),
                    trailingContent = {
                        AccountListItemTrailing(trailingText = userEmail)
                    },
                    onClick = component::onEmailClick,
                    modifier = Modifier.padding(horizontal = 16.dp)
                )
            }

            // But we show next part only when we have Email as auth provider
            if (userLoginTypes?.any { it is LoginType.Email } == true) {
                ProfileListItem(
                    leadingText = R.string.account_profile_change_password.strResDesc(),
                    onClick = component::onChangePasswordClick,
                    modifier = Modifier.padding(horizontal = 16.dp)
                )
            }

            val subscriptionBlockTrailingText =
                (user?.subscriptionState as? User.SubscriptionState.Ongoing)?.let {
                    ResourceFormattedStringDesc(
                        StringResource(R.string.account_profile_subscription_until),
                        listOf(it.expirationDate.formatWithPattern(DATE_FORMAT_PATTERN))
                    ).localizedByLocal()
                } ?: R.string.account_profile_subscription_not_active.strResDesc().localizedByLocal()

            SubscriptionStatusBlock(
                trailingText = subscriptionBlockTrailingText,
                userId = user?.userId?.value ?: "",
                onClick = component::onSubscriptionStatusClick,
                onLongPress = component::onSubscriptionStatusLongPress
            )
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter)
                .navigationBarsPadding()
                .padding(horizontal = 16.dp)
                .padding(bottom = 4.dp)
                .onSizeChanged { with(density) { bottomButtonsPadding = it.height.toDp() } }
        ) {
            MetaSecondaryButton(
                text = R.string.account_profile_button_logout.strResDesc().localizedByLocal(),
                onClick = component::onLogoutClick,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 8.dp)
            )

            MetaTransparentButton(
                text = R.string.account_profile_button_delete_account.strResDesc().localizedByLocal(),
                onClick = component::onDeleteAccountClick,
                modifier = Modifier.fillMaxWidth(),
                state = deleteAccountButtonState
            )
        }
    }

    DefaultDialog(dialogControl = component.dialogControl)

    component.deleteAccountComponent?.let {
        DefaultDialog(dialogControl = it.dialogControl)
    }

    ModalBottomSheet(
        control = component.birthYearBottomSheetControl,
        sheetShape = RectangleShape
    ) {
        ProfileBirthYearUi(component = it)
    }

    ModalBottomSheet(
        control = component.genderBottomSheetControl,
        sheetShape = RectangleShape
    ) {
        ProfileGenderUi(component = it)
    }

    if (operationInProgress) {
        FullscreenCircularProgress(overlay = true)
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun SubscriptionStatusBlock(
    trailingText: String,
    userId: String,
    onClick: () -> Unit,
    onLongPress: () -> Unit
) {
    val haptics = LocalHapticFeedback.current
    Text(
        text = R.string.account_profile_subscription_user_id.strResDesc().localizedByLocal(),
        color = CustomTheme.colors.text.secondary,
        style = CustomTheme.typography.heading.small,
        modifier = Modifier.padding(start = 32.dp, top = 16.dp)
    )
    Card(
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .clip(RoundedCornerShape(16.dp))
            .combinedClickable(
                onClick = onClick,
                onLongClick = {
                    haptics.performHapticFeedback(HapticFeedbackType.LongPress)
                    onLongPress()
                }
            ),
        elevation = 0.dp,
        shape = RoundedCornerShape(16.dp),
        backgroundColor = CustomTheme.colors.background.primary,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(all = 16.dp)
        ) {
            Text(
                text = userId,
                color = CustomTheme.colors.text.primary,
                style = CustomTheme.typography.heading.small,
            )

            Spacer(modifier = Modifier.height(24.dp))

            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier
                    .fillMaxWidth()
            ) {
                Text(
                    text = R.string.account_profile_subscription.strResDesc().localizedByLocal(),
                    color = CustomTheme.colors.text.primary,
                    style = CustomTheme.typography.heading.small
                )

                AccountListItemTrailing(trailingText = trailingText)
            }
        }
    }
}

@Preview
@Composable
fun ProfileMainUiPreview() {
    AppTheme {
        ProfileMainUi(component = FakeProfileMainComponent())
    }
}