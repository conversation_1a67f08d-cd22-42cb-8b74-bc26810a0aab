package com.metacards.metacards.features.deck.domain.interactor

import com.metacards.metacards.core.user.domain.UserProvider
import com.metacards.metacards.features.deck.domain.entity.DailyCard
import com.metacards.metacards.features.deck.domain.repository.DecksRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import java.util.Calendar
import java.util.Date

class GetDailyCardInteractor(
    private val decksRepository: DecksRepository,
    private val userProvider: UserProvider
) {
    private val cache = MutableStateFlow<DailyCard?>(null)

    fun execute(): Flow<DailyCard?> = cache

    suspend fun reLoad() {
        val shouldReload = userProvider.getUser().value?.dailyCardGeneratedAt?.let { lastDate ->
            val timeWhenNeedUpdate = Calendar.getInstance().apply {
                time = lastDate
                set(Calendar.HOUR_OF_DAY, 3)
                set(Calendar.MINUTE, 0)
                set(Calendar.SECOND, 0)
                add(Calendar.DATE, 1)
            }.time
            Date().after(timeWhenNeedUpdate)
        } ?: true
        if (shouldReload || cache.value == null) cache.value = decksRepository.getDailyCard()
    }
}
