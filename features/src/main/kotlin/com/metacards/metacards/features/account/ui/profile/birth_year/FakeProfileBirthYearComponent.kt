package com.metacards.metacards.features.account.ui.profile.birth_year

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakeProfileBirthYearComponent : ProfileBirthYearComponent {
    override val selectedYearFlow: StateFlow<Int?> = MutableStateFlow(null)
    override val yearsList: List<Int> = (2023.downTo(2018)).toList()

    override fun onYearClick(year: Int) = Unit
    override fun onDismiss() = Unit
    override fun onSaveClick() = Unit
}