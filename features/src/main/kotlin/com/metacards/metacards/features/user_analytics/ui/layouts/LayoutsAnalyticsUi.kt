package com.metacards.metacards.features.user_analytics.ui.layouts

import android.content.Context
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.ErrorMessage
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.PagedData
import com.metacards.metacards.core.utils.getDisplayName
import com.metacards.metacards.core.utils.getTodayLocalDate
import com.metacards.metacards.core.utils.toLocalDate
import com.metacards.metacards.core.widget.ErrorPlaceholder
import com.metacards.metacards.core.widget.FullscreenCircularProgress
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.record.ui.record_list.ScrollCommand
import com.metacards.metacards.features.record.ui.record_list.widgets.RecordCard
import com.metacards.metacards.features.user_analytics.domain.layout.LayoutRecord
import com.metacards.metacards.features.user_analytics.ui.layouts.wiget.AnalyticsLayoutGraph
import com.metacards.metacards.features.user_analytics.ui.layouts.wiget.LayoutAnalyticsCandleSkeleton
import com.metacards.metacards.features.user_analytics.ui.layouts.wiget.LayoutAnalyticsCardSkeleton
import com.metacards.metacards.features.user_analytics.ui.layouts.wiget.LayoutsAnalyticsDefaults
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.launch
import kotlinx.datetime.DatePeriod
import kotlinx.datetime.Instant
import kotlinx.datetime.minus
import com.metacards.metacards.core.R as CoreR

@Composable
fun LayoutsAnalyticsUi(
    component: LayoutsAnalyticsComponent,
    modifier: Modifier = Modifier
) {
    val analyticsInfoState by component.analyticsState.collectAsState()
    val selectedRecord by component.selectedRecord.collectAsState()
    val isPremiumUser by component.isPremiumUser.collectAsState()

    LceWidget(
        state = analyticsInfoState,
        onRetryClick = { /*TODO: error handling*/ },
        modifier = modifier,
        loadingProgress = {
            Column(modifier.fillMaxSize()) {
                Row {
                    repeat(MAX_VISIBLE_CANDLES) {
                        LayoutAnalyticsCandleSkeleton()
                    }
                }
                Spacer(modifier = Modifier.size(40.dp))
                LayoutAnalyticsCardSkeleton(modifier = Modifier.padding(horizontal = 16.dp))
            }
        },
        errorPlaceholder = { message, onRetry ->
            ErrorPlaceholder(message, onRetry, modifier.padding(bottom = 130.dp))
        },
        emptyPlaceholderContent = {
            LayoutsAnalyticsDefaults()
        },
    ) { pagedData, _ ->
        LayoutAnalyticsContent(
            pagedData = pagedData,
            scrollCommand = component.scrollToRecordCommand,
            selectedRecord = selectedRecord,
            loadingFirstPage = analyticsInfoState.loading,
            hasError = analyticsInfoState.error != null,
            onLoadMore = component::onLoadMore,
            onRecordClick = component::onRecordClick,
            updateSelectedRecord = component::updateSelectedRecord,
            isPremiumUser = isPremiumUser
        )
    }
}

@Composable
private fun LceWidget(
    state: LoadableState<PagedData<LayoutRecord>>,
    onRetryClick: () -> Unit,
    modifier: Modifier = Modifier,
    loadingProgress: @Composable () -> Unit = { FullscreenCircularProgress(modifier) },
    errorPlaceholder: @Composable (message: ErrorMessage, onRetry: () -> Unit) -> Unit = { message, onRetry ->
        ErrorPlaceholder(message, onRetry, modifier)
    },
    emptyPlaceholderContent: @Composable () -> Unit,
    content: @Composable (data: PagedData<LayoutRecord>, refreshing: Boolean) -> Unit,
) {
    val (loading, data, error) = state

    when {
        loading -> loadingProgress()
        data != null && data.list.isEmpty() -> emptyPlaceholderContent()
        data != null -> content(data, loading)
        error != null -> errorPlaceholder(error, onRetryClick)
    }
}

@Composable
private fun LayoutAnalyticsContent(
    pagedData: PagedData<LayoutRecord>,
    selectedRecord: LayoutRecord?,
    scrollCommand: SharedFlow<ScrollCommand?>,
    loadingFirstPage: Boolean,
    hasError: Boolean,
    onLoadMore: () -> Unit,
    onRecordClick: (RecordId) -> Unit,
    updateSelectedRecord: (Int?) -> Unit,
    isPremiumUser: Boolean,
    modifier: Modifier = Modifier
) {
    val columnScroll = rememberScrollState()
    val scope = rememberCoroutineScope()
    val blurModifier = if (!isPremiumUser) Modifier.blur(4.dp) else Modifier
    val context = LocalContext.current

    BoxWithConstraints(
        modifier = modifier
            .fillMaxSize()
            .then(blurModifier)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .verticalScroll(columnScroll)
        ) {
            AnalyticsLayoutGraph(
                pagedData = pagedData,
                scrollCommand = scrollCommand,
                loadingFirstPage = loadingFirstPage,
                hasError = hasError,
                onLoadMore = onLoadMore,
                indexSelectedRecord = { index ->
                    updateSelectedRecord(index)
                    scope.launch { columnScroll.scrollTo(TOP_POSITION) }
                }
            )
            selectedRecord?.let { record ->
                Spacer(modifier = Modifier.size(32.dp))
                val date = remember(record) {
                    record.record.creationTime.getSelectedRecordDate(context)
                }
                Text(
                    modifier = Modifier.padding(horizontal = 32.dp),
                    text = date,
                    color = CustomTheme.colors.text.secondary,
                    style = CustomTheme.typography.caption.small,
                )
                Spacer(modifier = Modifier.size(8.dp))
                RecordCard(
                    modifier = Modifier.padding(horizontal = 16.dp),
                    revealEnabled = false,
                    record = record.record,
                    showDate = false,
                    onClick = { onRecordClick(record.record.id) },
                )
                Spacer(modifier = Modifier.size(100.dp))
            }
        }
    }
}

private const val RECORD_CREATION_DATE_TIME_DISPLAY_PATTERN = "dd MMMM • HH:mm"
private const val RECORD_CREATION_TIME_DISPLAY_PATTERN = " • HH:mm"
private const val TOP_POSITION = 0
const val MAX_VISIBLE_CANDLES = 7

private fun Instant.getSelectedRecordDate(context: Context): String {
    val today = getTodayLocalDate()
    val yesterday = today - DatePeriod(days = 1)
    val date = toLocalDate()
    return when {
        today == date ->
            "${CoreR.string.common_today.strResDesc().toString(context)}${getDisplayName(RECORD_CREATION_TIME_DISPLAY_PATTERN)}"
        yesterday == date ->
            "${CoreR.string.common_yesterday.strResDesc().toString(context)}${getDisplayName(RECORD_CREATION_TIME_DISPLAY_PATTERN)}"
        else -> getDisplayName(RECORD_CREATION_DATE_TIME_DISPLAY_PATTERN)
    }
}

@Preview
@Composable
private fun AnalyticsUiPreview() {
    AppTheme {
        LayoutsAnalyticsUi(component = FakeLayoutsAnalyticsComponent())
    }
}
