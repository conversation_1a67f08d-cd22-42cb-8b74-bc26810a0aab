package com.metacards.metacards.features.auth.ui.sign_in

import android.os.Parcelable
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.router.stack.childStack
import com.arkivanov.decompose.router.stack.pop
import com.arkivanov.decompose.router.stack.push
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.user.domain.Email
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.toStateFlow
import com.metacards.metacards.features.auth.createEmailEnterComponent
import com.metacards.metacards.features.auth.createEnterPasswordComponent
import com.metacards.metacards.features.auth.createResetPasswordComponent
import com.metacards.metacards.features.auth.ui.auth_type.email.EmailEnterComponent
import com.metacards.metacards.features.auth.ui.auth_type.pass.PasswordEnterComponent
import com.metacards.metacards.features.auth.ui.sign_in.reset_password.ResetPasswordComponent
import kotlinx.coroutines.flow.StateFlow
import kotlinx.parcelize.Parcelize

class RealSignInComponent(
    componentContext: ComponentContext,
    private val componentFactory: ComponentFactory,
    private val onOutput: (SignInComponent.Output) -> Unit
) : ComponentContext by componentContext, SignInComponent {

    private val navigation = StackNavigation<ChildConfig>()

    override val childStack = childStack(
        source = navigation,
        initialConfiguration = ChildConfig.EmailEnter,
        handleBackButton = true,
        childFactory = ::createChild
    ).toStateFlow(lifecycle)

    override val toolbarVisibility: StateFlow<Boolean> = computed(childStack) { childStack ->
        childStack.active.instance !is SignInComponent.Child.ResetPassword
    }

    private fun createChild(
        config: ChildConfig,
        componentContext: ComponentContext
    ): SignInComponent.Child = when (config) {
        is ChildConfig.EmailEnter -> {
            SignInComponent.Child.EmailEnter(
                componentFactory.createEmailEnterComponent(
                    componentContext,
                    ::onEmailEnterOutput,
                    isSignIn = true,
                    isResetPassword = false
                )
            )
        }

        is ChildConfig.PasswordEnter -> {
            SignInComponent.Child.PasswordEnter(
                componentFactory.createEnterPasswordComponent(
                    componentContext,
                    config.email,
                    ::onEnterPasswordOutput,
                )
            )
        }

        is ChildConfig.ResetPassword -> {
            SignInComponent.Child.ResetPassword(
                componentFactory.createResetPasswordComponent(
                    componentContext,
                    ::onResetPasswordOutput
                )
            )
        }
    }

    private fun onEnterPasswordOutput(output: PasswordEnterComponent.Output) {
        when (output) {
            is PasswordEnterComponent.Output.EnterButtonPressed -> {
                onOutput(SignInComponent.Output.MainScreenRequested)
            }

            PasswordEnterComponent.Output.ResetPasswordRequested -> {
                navigation.push(ChildConfig.ResetPassword)
            }

            is PasswordEnterComponent.Output.EmailVerificationRequested -> {
                onOutput(SignInComponent.Output.EmailVerificationRequested(output.email))
            }
        }
    }

    private fun onResetPasswordOutput(output: ResetPasswordComponent.Output) {
        when (output) {
            is ResetPasswordComponent.Output.SignUpRequested -> {
                onOutput(SignInComponent.Output.SignUpRequested)
            }

            ResetPasswordComponent.Output.PasswordWasReset -> {
                navigation.pop()
            }

            ResetPasswordComponent.Output.CloseScreenRequested -> {
                navigation.pop()
            }

            ResetPasswordComponent.Output.MainScreenRequested -> {
                onOutput(SignInComponent.Output.MainScreenRequested)
            }

            is ResetPasswordComponent.Output.WebViewRequested -> {
                onOutput(SignInComponent.Output.WebViewRequested(output.url))
            }

            ResetPasswordComponent.Output.GoBackRequested -> navigation.pop()
        }
    }

    private fun onEmailEnterOutput(output: EmailEnterComponent.Output) {
        when (output) {
            is EmailEnterComponent.Output.NextButtonPressed -> {
                navigation.push(ChildConfig.PasswordEnter(output.email))
            }

            EmailEnterComponent.Output.SignInRequested -> Unit

            EmailEnterComponent.Output.SignUpRequested -> {
                onOutput(SignInComponent.Output.SignUpRequested)
            }

            is EmailEnterComponent.Output.WebViewRequested -> {
                onOutput(SignInComponent.Output.WebViewRequested(output.url))
            }

            EmailEnterComponent.Output.GoBackRequested -> onOutput(SignInComponent.Output.GoBackRequested)
        }
    }

    sealed interface ChildConfig : Parcelable {

        @Parcelize
        data object EmailEnter : ChildConfig

        @Parcelize
        data object ResetPassword : ChildConfig

        @Parcelize
        data class PasswordEnter(val email: Email) : ChildConfig
    }
}