package com.metacards.metacards.features.record.ui.create_record.photo_confirm

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.rememberAsyncImagePainter
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.button.MetaTransparentButton
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun ConfirmPhotoUi(
    component: ConfirmPhotoComponent,
    modifier: Modifier = Modifier,
) {
    BoxWithFade(
        modifier, listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        val cardPhoto by component.cardPhoto.collectAsState()

        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
                .align(Alignment.Center),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            if (cardPhoto.uri != null || cardPhoto.assetsPath != null) {
                Image(
                    modifier = Modifier
                        .clip(RoundedCornerShape(30.dp))
                        .fillMaxSize()
                        .weight(1f),
                    contentScale = ContentScale.Crop,
                    painter = rememberAsyncImagePainter(cardPhoto.takeUri()),
                    contentDescription = null
                )
            }

            Spacer(modifier = Modifier.size(16.dp))
            MetaAccentButton(
                modifier = Modifier.fillMaxWidth(),
                text = R.string.confirm_photo_accept_photo.strResDesc().localizedByLocal(),
                onClick = component::savePhoto
            )

            Spacer(modifier = Modifier.size(8.dp))

            if (cardPhoto.uri != null) {
                MetaTransparentButton(
                    modifier = Modifier.fillMaxWidth(),
                    text = R.string.confirm_photo_retake_photo.strResDesc().localizedByLocal(),
                    onClick = component::retakePhoto
                )
            }
            Spacer(modifier = Modifier.size(16.dp))
        }
    }
}

@Preview
@Composable
private fun ConfirmPhotoUiPreview() {
    AppTheme {
        ConfirmPhotoUi(component = FakeConfirmPhotoComponent())
    }
}
