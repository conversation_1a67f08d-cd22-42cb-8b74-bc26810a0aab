package com.metacards.metacards.features.special_deck.ui.deck_obtained

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material.Scaffold
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.rememberLottieComposition
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.delay
import kotlin.random.Random

@Composable
fun SpecialDeckDeckObtainedUi(
    component: SpecialDeckDeckObtainedComponent,
    modifier: Modifier = Modifier
) {
    Scaffold(
        modifier = modifier,
        topBar = {
            TopNavigationBar(
                title = R.string.special_deck_deck_obtained_title.strResDesc(),
                leadingIcon = { BackNavigationItem() },
                modifier = Modifier
                    .statusBarsPadding()
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .padding(paddingValues)
                .fillMaxSize()
        ) {

            SpecialDeckObtainedBackgroundAnimation(modifier = Modifier.matchParentSize())

            Column {
                Column(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = R.string.special_deck_deck_obtained_subtitle.strResDesc().localizedByLocal(),
                        style = CustomTheme.typography.heading.secondary,
                        color = CustomTheme.colors.text.primary,
                        textAlign = TextAlign.Center,
                        modifier = Modifier
                            .padding(horizontal = 16.dp)
                            .padding(bottom = 8.dp)
                            .fillMaxWidth()
                    )

                    Text(
                        text = R.string.special_deck_deck_obtained_subtitle_2.strResDesc().localizedByLocal(),
                        style = CustomTheme.typography.body.primary,
                        color = CustomTheme.colors.text.primary,
                        textAlign = TextAlign.Center,
                        modifier = Modifier
                            .padding(horizontal = 16.dp)
                            .fillMaxWidth()
                    )
                }

                MetaAccentButton(
                    text = R.string.special_deck_deck_obtained_button_go.strResDesc().localizedByLocal(),
                    onClick = component::onGoToStarrySkyClick,
                    modifier = Modifier
                        .navigationBarsPadding()
                        .fillMaxWidth()
                        .padding(16.dp)
                )
            }
        }
    }
}

@Composable
fun SpecialDeckObtainedBackgroundAnimation(
    modifier: Modifier = Modifier
) {
    val lottieComposition by rememberLottieComposition(
        spec = LottieCompositionSpec.RawRes(R.raw.special_deck_stars)
    )

    var firstVisible by remember { mutableStateOf(false) }
    var secondVisible by remember { mutableStateOf(false) }
    var thirdVisible by remember { mutableStateOf(false) }
    var fourthVisible by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        delay(Random.nextLong(1000))
        firstVisible = true
        delay(Random.nextLong(1000))
        secondVisible = true
        delay(Random.nextLong(1000))
        thirdVisible = true
        delay(Random.nextLong(1000))
        fourthVisible = true
    }

    val lc = LocalConfiguration.current
    val animModifier = Modifier
        .size(lc.screenWidthDp.dp.div(1.4f))
        .padding(16.dp)

    Box(modifier) {
        if (firstVisible) {
            LottieAnimation(
                composition = lottieComposition,
                iterations = LottieConstants.IterateForever,
                modifier = animModifier
                    .align(Alignment.TopStart)
                    .padding(top = lc.screenHeightDp.dp.div(10))
            )
        }

        if (secondVisible) {
            LottieAnimation(
                composition = lottieComposition,
                iterations = LottieConstants.IterateForever,
                modifier = animModifier
                    .align(Alignment.TopEnd)
            )
        }

        if (thirdVisible) {
            LottieAnimation(
                composition = lottieComposition,
                iterations = LottieConstants.IterateForever,
                modifier = animModifier
                    .align(Alignment.BottomStart)
            )
        }

        if (fourthVisible) {
            LottieAnimation(
                composition = lottieComposition,
                iterations = LottieConstants.IterateForever,
                modifier = animModifier
                    .align(Alignment.BottomEnd)
                    .padding(bottom = lc.screenHeightDp.dp.div(10))
            )
        }
    }
}