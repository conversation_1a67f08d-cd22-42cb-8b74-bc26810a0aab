package com.metacards.metacards.features.account.ui.promocodes

import com.metacards.metacards.core.button.ButtonState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import ru.mobileup.kmm_form_validation.control.InputControl

class FakePromocodeComponent : PromocodeComponent {
    override val promocodeInputControl: InputControl = InputControl(CoroutineScope(Dispatchers.IO))
    override val buttonState: StateFlow<ButtonState> = MutableStateFlow(ButtonState.Enabled)

    override fun onActivateButtonClick() = Unit
    override fun onCloseButtonClick() = Unit
}