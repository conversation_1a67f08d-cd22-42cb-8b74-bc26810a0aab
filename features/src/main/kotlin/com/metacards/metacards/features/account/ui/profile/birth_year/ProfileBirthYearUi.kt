package com.metacards.metacards.features.account.ui.profile.birth_year

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.features.R
import com.metacards.metacards.features.account.ui.widgets.BottomSheetListItem
import dev.icerock.moko.resources.desc.strResDesc
import com.metacards.metacards.core.R as CoreR

private const val VISIBLE_ITEM_COUNT = 5

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ProfileBirthYearUi(
    component: ProfileBirthYearComponent,
    modifier: Modifier = Modifier
) {
    val lazyListState = rememberLazyListState()
    val headerHeight = 56.dp
    val itemHeight = 48.dp
    val yearsList = component.yearsList
    val selectedYear by component.selectedYearFlow.collectAsState()

    LazyColumn(
        state = lazyListState,
        modifier = modifier.heightIn(max = headerHeight + itemHeight * VISIBLE_ITEM_COUNT),
        contentPadding = PaddingValues(bottom = 8.dp)
    ) {
        stickyHeader {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .background(CustomTheme.colors.background.modal)
                    .height(headerHeight)
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_24_close),
                    contentDescription = null,
                    tint = CustomTheme.colors.icons.primary,
                    modifier = Modifier
                        .padding(start = 8.dp, top = 8.dp, bottom = 8.dp)
                        .clip(CircleShape)
                        .clickable { component.onDismiss() }
                        .padding(8.dp)
                )

                Text(
                    text = R.string.account_profile_edit_birth_year_title.strResDesc().localizedByLocal(),
                    color = CustomTheme.colors.text.primary,
                    style = CustomTheme.typography.heading.medium,
                    modifier = Modifier.padding(start = 24.dp)
                )

                Spacer(modifier = Modifier.weight(1f))

                Box(
                    contentAlignment = Alignment.Center,
                    modifier = Modifier
                        .height(36.dp)
                        .padding(end = 16.dp)
                        .clip(CircleShape)
                        .clickable(enabled = selectedYear != null) { component.onSaveClick() }
                ) {
                    Text(
                        text = CoreR.string.common_ok.strResDesc().localizedByLocal(),
                        color = if (selectedYear != null) {
                            CustomTheme.colors.text.primary
                        } else {
                            CustomTheme.colors.text.disabled
                        },
                        style = CustomTheme.typography.caption.medium,
                        modifier = Modifier.padding(horizontal = 10.dp)
                    )
                }
            }
        }

        items(
            items = yearsList,
            key = { "year $it" }
        ) {
            BottomSheetListItem(
                text = it.toString(),
                isSelected = it == selectedYear,
                onClick = { component.onYearClick(it) },
                modifier = Modifier.height(itemHeight)
            )
        }
    }
}

@Preview
@Composable
fun ProfileBirthYearUiPreview() {
    AppTheme {
        ProfileBirthYearUi(component = FakeProfileBirthYearComponent())
    }
}