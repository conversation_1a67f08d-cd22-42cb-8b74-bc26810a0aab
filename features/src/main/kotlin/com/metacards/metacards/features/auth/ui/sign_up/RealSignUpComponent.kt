package com.metacards.metacards.features.auth.ui.sign_up

import android.os.Parcelable
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.router.stack.childStack
import com.arkivanov.decompose.router.stack.push
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.user.domain.Email
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.ResourceFormatted
import com.metacards.metacards.core.utils.toStateFlow
import com.metacards.metacards.features.R
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.auth.createCreatePasswordComponent
import com.metacards.metacards.features.auth.createEmailEnterComponent
import com.metacards.metacards.features.auth.createSentMessageToEmailComponent
import com.metacards.metacards.features.auth.ui.auth_type.email.EmailEnterComponent
import com.metacards.metacards.features.auth.ui.auth_type.email.message.SentMessageToEmailComponent
import com.metacards.metacards.features.auth.ui.auth_type.pass.create_pass.CreatePasswordComponent
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.parcelize.Parcelize

class RealSignUpComponent(
    componentContext: ComponentContext,
    startCommand: SignUpComponent.StartCommand?,
    private val componentFactory: ComponentFactory,
    private val analyticsService: AnalyticsService,
    private val onOutput: (SignUpComponent.Output) -> Unit
) : ComponentContext by componentContext, SignUpComponent {

    private val navigation = StackNavigation<ChildConfig>()
    private val initialConfig = when (startCommand) {
        is SignUpComponent.StartCommand.OpenEmailVerification -> {
            ChildConfig.SentMessageToEmail(startCommand.email)
        }

        null -> ChildConfig.EmailEnter
    }

    override val childStack = childStack(
        source = navigation,
        initialConfiguration = initialConfig,
        handleBackButton = true,
        childFactory = ::createChild
    ).toStateFlow(lifecycle)

    override fun onCloseButtonClick() {
        analyticsService.logEvent(AnalyticsEvent.RegistrationEmailConfirmCloseEvent)
        onOutput(SignUpComponent.Output.MainScreenRequested)
    }

    private fun createChild(
        config: ChildConfig,
        componentContext: ComponentContext
    ): SignUpComponent.Child = when (config) {
        is ChildConfig.EmailEnter -> {
            SignUpComponent.Child.EmailEnter(
                componentFactory.createEmailEnterComponent(
                    componentContext,
                    ::onEmailEnterOutput,
                    isSignIn = false,
                    isResetPassword = false
                )
            )
        }

        is ChildConfig.CreatePassword -> {
            SignUpComponent.Child.CreatePassword(
                componentFactory.createCreatePasswordComponent(
                    componentContext,
                    config.email,
                    ::onCreatePasswordOutput
                )
            )
        }

        is ChildConfig.SentMessageToEmail -> {
            SignUpComponent.Child.SentMessageToEmail(
                componentFactory.createSentMessageToEmailComponent(
                    componentContext,
                    StringDesc.Resource(R.string.sign_up_sent_email_title),
                    StringDesc.ResourceFormatted(
                        R.string.sign_up_sent_email_text,
                        config.email.value
                    ),
                    true,
                    ::onSentMessageToEmailOutput
                )
            )
        }

        else -> {
            // TODO: Replace by other components
            SignUpComponent.Child.EmailEnter(
                componentFactory.createEmailEnterComponent(
                    componentContext,
                    ::onEmailEnterOutput,
                    isSignIn = false,
                    isResetPassword = false
                )
            )
        }
    }

    private fun onEmailEnterOutput(output: EmailEnterComponent.Output) {
        when (output) {
            is EmailEnterComponent.Output.SignInRequested -> {
                onOutput(SignUpComponent.Output.SignInRequested)
            }

            is EmailEnterComponent.Output.NextButtonPressed -> {
                navigation.push(ChildConfig.CreatePassword(output.email))
            }

            EmailEnterComponent.Output.SignUpRequested -> Unit
            is EmailEnterComponent.Output.WebViewRequested -> {
                onOutput(SignUpComponent.Output.WebViewRequested(output.url))
            }

            EmailEnterComponent.Output.GoBackRequested -> onOutput(SignUpComponent.Output.GoBackRequested)
        }
    }

    private fun onSentMessageToEmailOutput(output: SentMessageToEmailComponent.Output) {
        when (output) {
            is SentMessageToEmailComponent.Output.MainScreenRequested -> {
                onOutput(SignUpComponent.Output.MainScreenRequested)
            }

            is SentMessageToEmailComponent.Output.AuthScreenRequested -> {
                onOutput(SignUpComponent.Output.AuthScreenRequested)
            }
        }
    }

    private fun onCreatePasswordOutput(output: CreatePasswordComponent.Output) {
        when (output) {
            is CreatePasswordComponent.Output.EnterButtonPressed -> {
                navigation.push(ChildConfig.SentMessageToEmail(output.email))
            }
        }
    }

    sealed interface ChildConfig : Parcelable {

        @Parcelize
        data object EmailEnter : ChildConfig

        @Parcelize
        data class CreatePassword(val email: Email) : ChildConfig

        @Parcelize
        data class SentMessageToEmail(val email: Email) : ChildConfig
    }
}