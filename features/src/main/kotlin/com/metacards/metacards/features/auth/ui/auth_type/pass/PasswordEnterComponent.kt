package com.metacards.metacards.features.auth.ui.auth_type.pass

import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.user.domain.Email
import kotlinx.coroutines.flow.StateFlow
import ru.mobileup.kmm_form_validation.control.InputControl

interface PasswordEnterComponent {
    val passwordInputControl: InputControl
    val enterButtonState: StateFlow<ButtonState>

    fun onEnterButtonClick()
    fun onResetPasswordButtonClick()
    fun onFocusRequest()

    sealed interface Output {
        data object EnterButtonPressed : Output
        data object ResetPasswordRequested : Output
        data class EmailVerificationRequested(val email: Email) : Output
    }
}