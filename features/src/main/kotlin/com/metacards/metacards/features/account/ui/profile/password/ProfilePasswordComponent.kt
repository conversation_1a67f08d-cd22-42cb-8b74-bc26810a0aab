package com.metacards.metacards.features.account.ui.profile.password

import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.features.account.ui.profile.password.confirm.ProfileConfirmPasswordComponent
import com.metacards.metacards.features.account.ui.profile.password.create_new.ProfileNewPasswordComponent
import kotlinx.coroutines.flow.StateFlow

interface ProfilePasswordComponent {
    val childStack: StateFlow<ChildStack<*, Child>>

    sealed interface Child {
        class ConfirmPassword(val component: ProfileConfirmPasswordComponent) : Child
        class NewPassword(val component: ProfileNewPasswordComponent) : Child
    }

    sealed interface Output {
        object DismissRequested : Output
    }
}