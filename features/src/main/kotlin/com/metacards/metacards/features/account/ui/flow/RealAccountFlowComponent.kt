package com.metacards.metacards.features.account.ui.flow

import android.os.Parcelable
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.router.stack.childStack
import com.arkivanov.decompose.router.stack.push
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.message.data.MessageService
import com.metacards.metacards.core.message.domain.Message
import com.metacards.metacards.core.preferences.models.TutorialState
import com.metacards.metacards.core.utils.toStateFlow
import com.metacards.metacards.core.widget.navigation_bar.NavigationPage
import com.metacards.metacards.features.account.createAboutTheAppComponent
import com.metacards.metacards.features.account.createAboutUsComponent
import com.metacards.metacards.features.account.createFeedbackComponent
import com.metacards.metacards.features.account.createLessonsComponent
import com.metacards.metacards.features.account.createProfileFlowComponent
import com.metacards.metacards.features.account.createPromocodeComponent
import com.metacards.metacards.features.account.createShopComponent
import com.metacards.metacards.features.account.ui.about_the_app.AboutTheAppComponent
import com.metacards.metacards.features.account.ui.feedback.FeedbackComponent
import com.metacards.metacards.features.account.ui.lessons.LessonsComponent
import com.metacards.metacards.features.account.ui.profile.ProfileFlowComponent
import com.metacards.metacards.features.account.ui.promocodes.PromocodeComponent
import com.metacards.metacards.features.account.ui.shop.ShopComponent
import com.metacards.metacards.features.tutorial.data.TutorialRepository
import com.metacards.metacards.features.user.createSubscriptionComponent
import com.metacards.metacards.features.user.ui.subscription.SubscriptionComponent
import kotlinx.parcelize.Parcelize

class RealAccountFlowComponent(
    componentContext: ComponentContext,
    private val componentFactory: ComponentFactory,
    private val tutorialRepository: TutorialRepository,
    private val messageService: MessageService,
    screen: AccountFlowComponent.Screen,
    private val onOutput: (AccountFlowComponent.Output) -> Unit
) : ComponentContext by componentContext, AccountFlowComponent {

    private val navigation = StackNavigation<ChildConfig>()

    override val childStack = childStack(
        source = navigation,
        initialConfiguration = screen.toChildConfig(),
        handleBackButton = true,
        childFactory = ::createChild
    ).toStateFlow(lifecycle)

    private fun createChild(
        childConfig: ChildConfig,
        componentContext: ComponentContext
    ): AccountFlowComponent.Child = when (childConfig) {
        is ChildConfig.Subscription -> {
            AccountFlowComponent.Child.Subscription(
                componentFactory.createSubscriptionComponent(
                    componentContext,
                    ::onSubscriptionOutput
                )
            )
        }

        is ChildConfig.Lessons -> {
            AccountFlowComponent.Child.Lessons(
                componentFactory.createLessonsComponent(
                    componentContext,
                    ::onLessonsOutput,
                    screen = childConfig.screen
                )
            )
        }

        is ChildConfig.AboutTheApp -> {
            AccountFlowComponent.Child.AboutTheApp(
                componentFactory.createAboutTheAppComponent(
                    componentContext,
                    ::onAboutTheAppOutput
                )
            )
        }

        is ChildConfig.AboutUs -> {
            AccountFlowComponent.Child.AboutUs(
                componentFactory.createAboutUsComponent(componentContext)
            )
        }

        is ChildConfig.Shop -> {
            AccountFlowComponent.Child.Shop(
                componentFactory.createShopComponent(
                    componentContext,
                    ::onShopOutput
                )
            )
        }

        is ChildConfig.ProfileFlow -> {
            AccountFlowComponent.Child.ProfileFlow(
                componentFactory.createProfileFlowComponent(
                    componentContext,
                    ProfileFlowComponent.Screen.Main,
                    ::onProfileOutput
                )
            )
        }

        is ChildConfig.Feedback -> {
            AccountFlowComponent.Child.Feedback(
                componentFactory.createFeedbackComponent(componentContext, ::onFeedbackOutput)
            )
        }

        ChildConfig.Promocodes -> {
            AccountFlowComponent.Child.Promocodes(
                componentFactory.createPromocodeComponent(componentContext, ::onPromocodesOutput)
            )
        }
    }

    private fun onPromocodesOutput(output: PromocodeComponent.Output) {
        when (output) {
            is PromocodeComponent.Output.CloseScreenRequested -> {
                onOutput(AccountFlowComponent.Output.MainScreenRequested(NavigationPage.Account))
                output.message?.let {
                    messageService.showMessage(Message(it, isNotification = true))
                }
            }
        }
    }

    private fun onProfileOutput(output: ProfileFlowComponent.Output) {
        when (output) {
            is ProfileFlowComponent.Output.AuthScreenRequested -> {
                onOutput(AccountFlowComponent.Output.AuthScreenRequested)
            }

            is ProfileFlowComponent.Output.MainScreenRequested -> {
                onOutput(AccountFlowComponent.Output.MainScreenRequested(NavigationPage.Account))
            }

            is ProfileFlowComponent.Output.SignInViaWebViewRequested -> {
                onOutput(
                    AccountFlowComponent.Output.SignInViaWebViewRequested(
                        output.url,
                        output.title
                    )
                )
            }

            is ProfileFlowComponent.Output.WebViewDismissRequested -> {
                onOutput(AccountFlowComponent.Output.WebViewDismissRequested)
            }

            ProfileFlowComponent.Output.SubscriptionRequested -> {
                navigation.push(ChildConfig.Subscription)
            }
        }
    }

    private fun onShopOutput(output: ShopComponent.Output) {
        when (output) {
            is ShopComponent.Output.DeckDetailsRequested -> {
                onOutput(AccountFlowComponent.Output.DeckDetailsRequested(output.deckId))
            }
        }
    }

    private fun onAboutTheAppOutput(output: AboutTheAppComponent.Output) {
        when (output) {
            is AboutTheAppComponent.Output.WebViewRequested -> {
                onOutput(AccountFlowComponent.Output.WebViewRequested(output.url, output.titleRes))
            }

            is AboutTheAppComponent.Output.WebViewResourceRequested -> {
                onOutput(AccountFlowComponent.Output.WebViewRequestedResource(output.url, output.titleRes))
            }

            is AboutTheAppComponent.Output.AboutUsRequested -> {
                navigation.push(ChildConfig.AboutUs)
            }
        }
    }

    private fun onLessonsOutput(output: LessonsComponent.Output) {
        when (output) {
            is LessonsComponent.Output.TutorialRequested -> {
                tutorialRepository.updateTutorialState(TutorialState.START)
                onOutput(AccountFlowComponent.Output.MainScreenRequested(NavigationPage.Home))
            }
        }
    }

    private fun onSubscriptionOutput(output: SubscriptionComponent.Output) {
        when (output) {
            is SubscriptionComponent.Output.MainScreenRequested -> {
                onOutput(AccountFlowComponent.Output.MainScreenRequested(NavigationPage.Home))
            }

            is SubscriptionComponent.Output.WebViewRequested -> {
                onOutput(
                    AccountFlowComponent.Output.WebViewRequested(
                        output.url.toString(),
                        output.title
                    )
                )
            }

            is SubscriptionComponent.Output.AuthScreenRequested -> {
                onOutput(AccountFlowComponent.Output.AuthScreenRequested)
            }
        }
    }

    private fun onFeedbackOutput(output: FeedbackComponent.Output) {
        when (output) {
            is FeedbackComponent.Output.MainScreenRequested -> {
                onOutput(AccountFlowComponent.Output.MainScreenRequested(output.navigationPage))
            }

            is FeedbackComponent.Output.ShowMessageRequested -> {
                onOutput(AccountFlowComponent.Output.ShowMessageRequested(output.message))
            }
        }
    }

    sealed interface ChildConfig : Parcelable {

        @Parcelize
        data object Promocodes : ChildConfig

        @Parcelize
        object Subscription : ChildConfig

        @Parcelize
        data class Lessons(val screen: LessonsComponent.Screen) : ChildConfig

        @Parcelize
        object AboutTheApp : ChildConfig

        @Parcelize
        object AboutUs : ChildConfig

        @Parcelize
        object Shop : ChildConfig

        @Parcelize
        object ProfileFlow : ChildConfig

        @Parcelize
        object Feedback : ChildConfig
    }

    private fun AccountFlowComponent.Screen.toChildConfig(): ChildConfig {
        return when (this) {
            is AccountFlowComponent.Screen.Subscription -> ChildConfig.Subscription
            is AccountFlowComponent.Screen.Lessons -> ChildConfig.Lessons(screen)
            is AccountFlowComponent.Screen.AboutTheApp -> ChildConfig.AboutTheApp
            is AccountFlowComponent.Screen.Shop -> ChildConfig.Shop
            is AccountFlowComponent.Screen.ProfileFlow -> ChildConfig.ProfileFlow
            is AccountFlowComponent.Screen.Feedback -> ChildConfig.Feedback
            AccountFlowComponent.Screen.Promocodes -> ChildConfig.Promocodes
        }
    }
}
