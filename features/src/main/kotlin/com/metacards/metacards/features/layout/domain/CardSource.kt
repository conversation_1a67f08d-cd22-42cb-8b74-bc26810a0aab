package com.metacards.metacards.features.layout.domain

import android.os.Parcelable
import com.metacards.metacards.features.deck.domain.entity.DailyCard
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.record.domain.RecordId
import kotlinx.parcelize.Parcelize

@Parcelize
sealed class CardSource : Parcelable {
    data class Deck(val deckId: DeckId) : CardSource()
    data class Questions(val list: List<PredefinedQuestion>) : CardSource()
    data class DailyCardSource(val dailyCard: DailyCard, val recordId: RecordId?) : CardSource()
}