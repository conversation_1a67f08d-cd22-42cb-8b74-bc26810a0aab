package com.metacards.metacards.features.record.ui.calendar

import com.kizitonwose.calendar.core.CalendarDay
import kotlinx.coroutines.flow.StateFlow
import kotlinx.datetime.Instant
import java.time.YearMonth

interface CalendarComponent {

    val isDataLoaded: StateFlow<Boolean>

    val daysWithRecords: StateFlow<List<CalendarDay>>

    val startMonth: StateFlow<YearMonth>

    val endMonth: StateFlow<YearMonth>

    fun onDateClick(date: Instant)

    fun onCalendarPageSettled(page: Int)

    sealed interface Output {
        data class RecordsViewRequested(val date: Instant) : Output
    }
}