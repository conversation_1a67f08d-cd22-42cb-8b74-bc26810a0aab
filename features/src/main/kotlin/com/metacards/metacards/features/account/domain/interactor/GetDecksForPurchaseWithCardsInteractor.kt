package com.metacards.metacards.features.account.domain.interactor

import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.features.deck.domain.entity.DeckInfoWithCards
import com.metacards.metacards.features.deck.domain.interactor.GetDeckInfoWithCardsInteractor
import com.metacards.metacards.features.deck.domain.repository.DecksRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update

class GetDecksForPurchaseWithCardsInteractor(
    coroutineScope: CoroutineScope,
    errorHandler: <PERSON><PERSON>r<PERSON><PERSON><PERSON>,
    decksRepository: DecksRepository,
    private val getDeckInfoWithCardsInteractor: GetDeckInfoWithCardsInteractor
) {

    private val stateFlow = MutableStateFlow<List<DeckInfoWithCards>>(emptyList())

    init {
        val decksForPurchaseWithCardsFlow: Flow<List<DeckInfoWithCards>> = decksRepository
            .getDecksForPurchaseFlow().flatMapLatest { decks ->
                combine(decks.map { getDeckInfoWithCardsInteractor.execute(it.id) }) {
                    it.asList().getData() ?: emptyList()
                }
            }
        decksForPurchaseWithCardsFlow
            .catch { e -> errorHandler.handleError(Exception(e)) }
            .onEach { list -> stateFlow.update { list } }
            .launchIn(coroutineScope)
    }

    fun execute(): StateFlow<List<DeckInfoWithCards>> = stateFlow.asStateFlow()

    private fun List<LoadableState<DeckInfoWithCards>>.getData(): List<DeckInfoWithCards>? {
        return map { it.data ?: return null }
    }
}