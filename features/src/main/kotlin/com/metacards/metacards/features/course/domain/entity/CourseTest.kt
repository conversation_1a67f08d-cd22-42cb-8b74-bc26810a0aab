package com.metacards.metacards.features.course.domain.entity

import android.os.Parcelable
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.layout.domain.LayoutId
import kotlinx.parcelize.Parcelize

data class CourseTest(
    val testId: CourseContentId,
    val name: LocalizableString,
    val questions: List<CourseQuestion>,
    val results: List<CourseResult>
) {
    @Parcelize
    data class Query(
        val themeId: CourseThemeId,
        val courseId: CourseId,
        val testId: CourseContentId
    ) : Parcelable

    companion object {
        val Mock = CourseTest(
            testId = CourseContentId(""),
            name = LocalizableString(emptyMap()),
            questions = emptyList(),
            results = emptyList()
        )
    }
}

data class CourseQuestion(
    val name: LocalizableString,
    val image: String?,
    val order: Int,
    val yesScore: Int,
    val noScore: Int
)

@Parcelize
data class CourseResult(
    val cover: String?,
    val video: LocalizableString?,
    val title: LocalizableString,
    val subtitle: LocalizableString?,
    val description: LocalizableString?,
    val minScore: Int,
    val maxScore: Int,
    val linkedLayouts: List<LayoutId>?,
    val linkedVideos: List<LinkedVideo>?
) : Parcelable {
    companion object {
        val mock = CourseResult(
            cover = null,
            video = null,
            title = LocalizableString.createNonLocalizable(""),
            subtitle = LocalizableString.createNonLocalizable(""),
            description = LocalizableString.createNonLocalizable(""),
            minScore = 0,
            maxScore = 0,
            linkedLayouts = listOf(),
            linkedVideos = listOf()
        )
    }
}

@Parcelize
data class LinkedVideo(
    val cover: String?,
    val video: LocalizableString
) : Parcelable