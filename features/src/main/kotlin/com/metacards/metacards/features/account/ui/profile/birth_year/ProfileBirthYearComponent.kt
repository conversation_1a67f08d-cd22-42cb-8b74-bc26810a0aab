package com.metacards.metacards.features.account.ui.profile.birth_year

import android.os.Parcelable
import kotlinx.coroutines.flow.StateFlow
import kotlinx.parcelize.Parcelize

interface ProfileBirthYearComponent {
    val selectedYearFlow: StateFlow<Int?>
    val yearsList: List<Int>

    fun onYearClick(year: Int)
    fun onSaveClick()
    fun onDismiss()

    @Parcelize
    object Config : Parcelable

    sealed interface Output {
        object DismissRequested : Output
    }
}