package com.metacards.metacards.features.course.data.dto

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.course.domain.entity.CourseAvailability
import com.metacards.metacards.features.course.domain.entity.CourseData
import com.metacards.metacards.features.course.domain.entity.CourseStatus

data class CourseDataDto(
    val nameLocalized: Map<String, String?> = emptyMap(),
    val descriptionLocalized: Map<String, String?>? = null,
    val shortDescriptionLocalized: Map<String, String?>? = null,
    val profits: List<CourseDataProfitDto>? = null,
    val cover: String = "",
    val duration: Int? = null,
    val availability: String = "",
    val lessonsAmount: Int? = null,
    val testsAmount: Int? = null,
    val content: List<CourseDataContentDto> = emptyList(),
    val status: String = ""
) {
    fun toDomain() = CourseData(
        name = LocalizableString(nameLocalized),
        description = descriptionLocalized?.let(::LocalizableString),
        shortDescription = shortDescriptionLocalized?.let(::LocalizableString),
        profits = profits?.map { it.toDomain() },
        coverUrl = cover,
        duration = duration,
        availability = CourseAvailability.fromString(availability),
        lessonsAmount = lessonsAmount,
        testsAmount = testsAmount,
        content = content.map { it.toDomain() },
        status = CourseStatus.fromString(status)
    )
}
