package com.metacards.metacards.features.record.ui.create_record.photo_confirm

import com.metacards.metacards.features.record.domain.camera.CardPhoto
import kotlinx.coroutines.flow.StateFlow

interface ConfirmPhotoComponent {
    val cardPhoto: StateFlow<CardPhoto>

    fun savePhoto()
    fun retakePhoto()

    sealed interface Output {
        data class CreateRecordRequested(val cardPhoto: CardPhoto?) : Output
        object CameraRequested : Output
    }
}
