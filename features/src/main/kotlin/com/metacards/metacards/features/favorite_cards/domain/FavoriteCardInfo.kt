package com.metacards.metacards.features.favorite_cards.domain

import com.metacards.metacards.core.user.domain.FavoriteCard
import com.metacards.metacards.features.record.domain.Record

data class FavoriteCardInfo(
    val card: FavoriteCard,
    val records: List<Record>
) {
    val recordsCount: Int get() = records.size

    val unarchivedRecords: List<Record> by lazy {
        records.filter { !it.isArchived }
    }

    companion object {
        val MOCK = FavoriteCardInfo(
            card = FavoriteCard.mock("1"),
            records = Record.LIST_MOCK
        )
    }
}