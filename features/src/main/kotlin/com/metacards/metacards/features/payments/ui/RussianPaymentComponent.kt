package com.metacards.metacards.features.payments.ui

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.bottom_sheet.BottomSheetControl
import com.metacards.metacards.core.bottom_sheet.bottomSheetControl
import com.metacards.metacards.features.R
import com.metacards.metacards.features.payments.createPaymentBottomSheetComponent
import com.metacards.metacards.features.payments.domain.PurchaseType
import com.metacards.metacards.features.payments.ui.bottom_sheet.PaymentBottomSheetComponent
import kotlinx.coroutines.flow.MutableStateFlow

@Deprecated("Use InternationalPaymentComponent")
class RussianPaymentComponent(
    componentContext: ComponentContext,
    componentFactory: ComponentFactory,
    private val onOutput: (PaymentComponent.Output) -> Unit
) : ComponentContext by componentContext, PaymentComponent {

    val paymentBottomSheetControl: BottomSheetControl<PaymentBottomSheetComponent.Config, PaymentBottomSheetComponent> by lazy {
        bottomSheetControl(
            bottomSheetComponentFactory = { config, context: ComponentContext, _ ->
                componentFactory.createPaymentBottomSheetComponent(
                    context,
                    config,
                    ::onPaymentBottomSheetOutput
                )
            },
            halfExpandingSupported = false,
            hidingSupported = true,
            handleBackButton = true
        )
    }

    override fun cancelPaymentFlow() {
        onOutput(PaymentComponent.Output.Canceled)
    }

    private val currentPaymentState =
        MutableStateFlow<PaymentComponent.PaymentState>(PaymentComponent.PaymentState.NotStarted)

    override fun startPayment(purchaseType: PurchaseType) {
        val config = when (purchaseType) {
            is PurchaseType.Deck -> {
                PaymentBottomSheetComponent.Config.Deck(
                    purchaseType.firebaseId,
                    purchaseType.name
                )
            }

            is PurchaseType.Subscription -> {
                when (purchaseType) {
                    is PurchaseType.Subscription.New -> {
                        PaymentBottomSheetComponent.Config.Subscription.New(purchaseType.basePlanId)
                    }

                    is PurchaseType.Subscription.Upgrade -> {
                        PaymentBottomSheetComponent.Config.Subscription.Upgrade(
                            purchaseType.oldBasePlanId,
                            purchaseType.newBasePlanId
                        )
                    }
                }
            }
        }

        paymentBottomSheetControl.show(config)
    }

    override fun cancelPurchase(id: String): Boolean {
        return true
        // TODO: implement when backend ready
    }

    private fun onPaymentBottomSheetOutput(output: PaymentBottomSheetComponent.Output) {
        when (output) {
            is PaymentBottomSheetComponent.Output.WebStoreRequested -> {
                paymentBottomSheetControl.dismiss()
                onOutput(
                    PaymentComponent.Output.WebStoreRequested(
                        output.url,
                        R.string.deck_info_payment_web_view_title
                    )
                )
            }

            is PaymentBottomSheetComponent.Output.DismissRequested -> paymentBottomSheetControl.dismiss()
        }
    }
}