package com.metacards.metacards.features.course.domain.interactor

import com.metacards.metacards.features.course.domain.entity.CourseContentType
import com.metacards.metacards.features.course.domain.entity.CoursePassedTest
import com.metacards.metacards.features.course.domain.repository.CourseRepository

class UpdateUserPassedTestInteractor(
    private val courseRepository: CourseRepository,
    private val updateUserPassedContentInteractor: UpdateUserPassedContentInteractor
) {
    suspend fun execute(result: CoursePassedTest) {
        courseRepository.addUserPassedTest(result)
        updateUserPassedContentInteractor.execute(
            themeId = result.themeId,
            courseId = result.courseId,
            contentId = result.testId,
            score = result.score,
            type = CourseContentType.TEST
        )
    }
}