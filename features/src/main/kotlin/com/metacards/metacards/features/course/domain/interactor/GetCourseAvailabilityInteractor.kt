package com.metacards.metacards.features.course.domain.interactor

import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.features.course.domain.entity.CourseAvailability
import com.metacards.metacards.features.course.domain.entity.CourseData
import com.metacards.metacards.features.course.domain.repository.CourseRepository
import com.metacards.metacards.features.user.domain.GetUserSubscriptionStateInteractor
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine

class GetCourseAvailabilityInteractor(
    private val userSubscriptionStateInteractor: GetUserSubscriptionStateInteractor,
    private val courseRepository: CourseRepository,
) {

    fun execute(query: CourseData.Query): Flow<Boolean> = combine(
        userSubscriptionStateInteractor.execute(),
        courseRepository.getCourseDataFlow(query.themeId, query.courseId)
    ) { subscriptionState, courseData ->
        courseData?.availability?.let { availability ->
            when (subscriptionState) {
                is User.SubscriptionState.None -> availability == CourseAvailability.FREE
                is User.SubscriptionState.Ongoing -> true
                null -> false
            }
        } ?: false
    }
}
