package com.metacards.metacards.features.special_deck.ui.card_details.widgets

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.metacards.metacards.features.deck.ui.MetaCardDefaults
import com.metacards.metacards.features.special_deck.ui.card_details.StarDetailsCardLoadingStub

@Composable
fun SpecialDeckCardContainer(
    cardContent: @Composable BoxScope.() -> Unit,
    hintContent: @Composable () -> Unit,
    isLoading: Boolean,
    cardModifier: Modifier = Modifier
) {

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        if (isLoading) {
            StarDetailsCardLoadingStub()
        } else {
            Box(
                modifier = Modifier
                    .weight(1f, fill = false)
                    .aspectRatio(MetaCardDefaults.aspectRatio)
                    .then(cardModifier),
                contentAlignment = Alignment.Center,
                content = cardContent
            )
            Spacer(modifier = Modifier.height(16.dp))
            hintContent()
        }
    }
}