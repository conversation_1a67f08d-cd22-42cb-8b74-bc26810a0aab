package com.metacards.metacards.features.tutorial.data

import com.metacards.metacards.core.preferences.PreferencesService
import com.metacards.metacards.core.preferences.models.TutorialState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

class TutorialRepositoryImpl(
    private val coroutineScope: CoroutineScope,
    private val preferencesService: PreferencesService
) : TutorialRepository {

    override val tutorialStateFlow: MutableStateFlow<TutorialState> =
        MutableStateFlow(TutorialState.NONE).apply {
            coroutineScope.launch {
                <EMAIL> = preferencesService.getTutorialState()
            }
        }

    override fun updateTutorialState(tutorialState: TutorialState) {
        coroutineScope.launch {
            preferencesService.setTutorialState(tutorialState)
            tutorialStateFlow.value = tutorialState
        }
    }
}