package com.metacards.metacards.features.layout.ui.predefined_layout.widgets

import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.navigationBarsPaddingDp
import com.metacards.metacards.core.widget.placeholder.PlaceholderHighlight
import com.metacards.metacards.core.widget.placeholder.placeholder
import com.metacards.metacards.core.widget.placeholder.shimmer
import com.metacards.metacards.features.deck.ui.MetaCardDefaults
import com.metacards.metacards.features.layout.domain.PredefinedLayoutWithAvailable

@Composable
fun PredefinedLayoutSkeleton(modifier: Modifier = Modifier) {
    val placeHolderModifier = Modifier
        .clip(RoundedCornerShape(16.dp))
        .placeholder(
            visible = true,
            color = CustomTheme.colors.background.placeholder,
            shape = MetaCardDefaults.shape,
            highlight = PlaceholderHighlight.shimmer(
                highlightColor = CustomTheme.colors.system.invert.copy(alpha = 0.6f)
            ),
            placeholderFadeTransitionSpec = { tween() },
            contentFadeTransitionSpec = { tween() }
        )

    Column(modifier) {
        LazyRow(
            modifier = Modifier.fillMaxWidth(),
            contentPadding = PaddingValues(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            items(2) {
                PredefinedLayoutGroupItem(
                    text = "Some group $it",
                    isSelected = false,
                    onClick = { },
                    modifier = Modifier.then(placeHolderModifier)
                )
            }
        }

        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            userScrollEnabled = false,
            verticalArrangement = Arrangement.spacedBy(12.dp),
            contentPadding = PaddingValues(
                start = 16.dp,
                end = 16.dp,
                top = 8.dp,
                bottom = navigationBarsPaddingDp
            )
        ) {
            repeat(2) { group ->
                item {
                    Text(
                        modifier = Modifier
                            .padding(bottom = 4.dp, top = 8.dp)
                            .then(placeHolderModifier),
                        text = "Some group $group",
                        style = CustomTheme.typography.heading.medium,
                        color = CustomTheme.colors.text.primary,
                    )
                }
                items(2) { layoutWithAvailable ->
                    PredefinedLayoutItem(
                        layoutWithAvailable = PredefinedLayoutWithAvailable.mock(),
                        onLayoutClick = { },
                        onBlockContentClick = { },
                        modifier = Modifier
                            .fillMaxWidth()
                            .aspectRatio(2f)
                            .then(placeHolderModifier)
                    )
                }
                item {
                    Spacer(Modifier.height(12.dp))
                }
            }
        }
    }
}
