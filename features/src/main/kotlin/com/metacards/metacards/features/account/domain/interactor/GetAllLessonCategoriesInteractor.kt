package com.metacards.metacards.features.account.domain.interactor

import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.features.account.domain.entity.LessonCategory
import com.metacards.metacards.features.account.domain.repository.LessonRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update

class GetAllLessonCategoriesInteractor(
    coroutineScope: CoroutineScope,
    errorHandler: <PERSON><PERSON>r<PERSON><PERSON><PERSON>,
    lessonsRepository: LessonRepository
) {

    private val stateFlow = MutableStateFlow<List<LessonCategory>>(emptyList())

    init {
        lessonsRepository.getAllLessonCategoriesFlow()
            .catch { e -> errorHandler.handleError(Exception(e)) }
            .onEach { lessons -> stateFlow.update { lessons.sortedBy(LessonCategory::order) } }
            .launchIn(coroutineScope)
    }

    fun execute() = stateFlow.asStateFlow()
}