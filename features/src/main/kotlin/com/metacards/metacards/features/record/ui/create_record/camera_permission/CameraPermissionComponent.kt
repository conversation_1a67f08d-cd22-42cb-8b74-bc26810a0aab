package com.metacards.metacards.features.record.ui.create_record.camera_permission

import kotlinx.coroutines.flow.StateFlow

interface CameraPermissionComponent {
    val permissionDialogViewed: StateFlow<Boolean>

    fun confirmDialog()
    fun dismissDialog()
    fun openSettings()

    sealed interface Output {
        object CameraRequested : Output
        object CreateRecordRequested : Output
    }
}
