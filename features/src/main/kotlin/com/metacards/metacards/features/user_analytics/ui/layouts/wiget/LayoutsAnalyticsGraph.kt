package com.metacards.metacards.features.user_analytics.ui.layouts.wiget

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.PagedData
import com.metacards.metacards.features.record.domain.LevelType
import com.metacards.metacards.features.record.domain.Record
import com.metacards.metacards.features.record.ui.model.FeelingItem
import com.metacards.metacards.features.record.ui.record_list.ScrollCommand
import com.metacards.metacards.features.user_analytics.domain.layout.LayoutRecord
import com.metacards.metacards.features.user_analytics.ui.layouts.MAX_VISIBLE_CANDLES
import kotlinx.coroutines.flow.SharedFlow
import java.time.format.TextStyle

@Composable
fun AnalyticsLayoutGraph(
    pagedData: PagedData<LayoutRecord>,
    scrollCommand: SharedFlow<ScrollCommand?>,
    loadingFirstPage: Boolean,
    hasError: Boolean,
    onLoadMore: () -> Unit,
    indexSelectedRecord: (Int?) -> Unit,
) {
    var firstVisibleRecord by remember { mutableStateOf<Record?>(null) }
    val boxHeight = (LocalConfiguration.current.screenHeightDp * SHARE_GRAPHICS).toInt()
    val boxWidth = ((LocalConfiguration.current.screenWidthDp) / MAX_VISIBLE_CANDLES).dp
    val candleHeight = (boxHeight * SHARE_HALF_CANDLE_HEIGHT_ON_GRAPHICS).toInt().dp

    Box(modifier = Modifier.height(boxHeight.dp)) {
        Box(
            modifier = Modifier
                .fillMaxHeight()
                .width(boxWidth)
                .align(Alignment.Center)
                .background(
                    color = CustomTheme.colors.background.primary,
                    shape = RoundedCornerShape(8.dp)
                )
        )
        LayoutsRecordPager(
            pagedData = pagedData,
            scrollCommand = scrollCommand,
            loadingFirstPage = loadingFirstPage,
            hasError = hasError,
            onLoadMore = onLoadMore,
            indexSelectedRecord = { index ->
                if (index != null) firstVisibleRecord = pagedData.list.firstVisibleRecord(index)
                indexSelectedRecord(index)
            }
        ) { (date, record, showDate) ->
            Box(modifier = Modifier.fillMaxSize()) {
                Column(
                    modifier = Modifier
                        .padding(vertical = 12.dp)
                        .align(Alignment.TopCenter)
                ) {
                    LayoutReactionCandles(
                        modifier = Modifier
                            .height(candleHeight)
                            .padding(horizontal = boxWidth / SHARE_CANDLE_WIDTH_ON_GRAPHICS),
                        record = record,
                        levelType = LevelType.Energy,
                        oneLevelTypeHeight = candleHeight / MAX_LEVEL_TYPE,
                        candleWidth = boxWidth / SHARE_CANDLE_WIDTH_ON_GRAPHICS
                    )

                    Divider(
                        modifier = Modifier
                            .padding(vertical = 2.dp)
                            .fillMaxWidth(),
                        color = CustomTheme.colors.background.primary,
                        thickness = 2.dp
                    )

                    LayoutReactionCandles(
                        modifier = Modifier
                            .height(candleHeight)
                            .padding(horizontal = boxWidth / SHARE_CANDLE_WIDTH_ON_GRAPHICS),
                        record = record,
                        levelType = LevelType.Mood,
                        oneLevelTypeHeight = candleHeight / MAX_LEVEL_TYPE,
                        candleWidth = boxWidth / SHARE_CANDLE_WIDTH_ON_GRAPHICS
                    )
                }

                if (date != null && showDate || date != null && firstVisibleRecord == record) {
                    val locale = LocalContext.current.resources.configuration.locales[0]
                    Column(
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .padding(top = 8.dp, bottom = 12.dp)
                            .width(boxWidth / SHARE_DATE_WIDTH_ON_GRAPHICS),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = date.dayOfMonth.toString(),
                            color = CustomTheme.colors.text.secondary,
                            style = CustomTheme.typography.caption.small

                        )
                        Text(
                            text = date.month.getDisplayName(TextStyle.SHORT, locale),
                            color = CustomTheme.colors.text.secondary,
                            style = CustomTheme.typography.caption.small,
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun LayoutReactionCandles(
    modifier: Modifier = Modifier,
    record: Record,
    levelType: LevelType,
    oneLevelTypeHeight: Dp,
    candleWidth: Dp
) {
    Column(
        modifier = modifier,
        verticalArrangement = when (levelType) {
            LevelType.Energy -> Arrangement.Bottom
            else -> Arrangement.Top
        },
    ) {
        val icon = when (levelType) {
            LevelType.Mood -> FeelingItem.moon_list.firstOrNull { it.value == record.moodLevel }?.imageResource
            else -> FeelingItem.mars_list.firstOrNull { it.value == record.energyLevel }?.imageResource
        }

        val candleLevel = when (levelType) {
            LevelType.Mood -> record.moodLevel
            else -> record.energyLevel
        }

        Box {
            if (candleLevel != null && icon != null) {
                AnalyticsCandle(
                    candleLevel = candleLevel,
                    levelType = levelType,
                    oneLevelTypeHeight = oneLevelTypeHeight,
                    candleWidth = candleWidth
                )

                Image(
                    modifier = Modifier
                        .size(candleWidth)
                        .align(
                            when (levelType) {
                                LevelType.Energy -> Alignment.TopCenter
                                else -> Alignment.BottomCenter
                            }
                        ),
                    painter = painterResource(id = icon),
                    contentDescription = null
                )
            } else {
                Spacer(modifier = Modifier.size(candleWidth))
            }
        }
    }
}

private fun List<LayoutRecord>.firstVisibleRecord(index: Int): Record? {
    return this.getOrNull(index.plus(VISIBLE_RECORDS_FROM_CENTRE))?.record
}

private const val VISIBLE_RECORDS_FROM_CENTRE = 3
