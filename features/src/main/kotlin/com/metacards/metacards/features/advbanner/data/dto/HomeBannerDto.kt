package com.metacards.metacards.features.advbanner.data.dto

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.advbanner.domain.entity.HomeAdvBanner
import com.metacards.metacards.features.advbanner.domain.entity.HomeBannerStyle
import com.metacards.metacards.features.advbanner.domain.entity.HomeBannerType
import com.metacards.metacards.features.deck.domain.entity.DeckId
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
class HomeBannerDto(
    @SerialName("id")
    val id: String = "",
    @SerialName("titleLocalized")
    val titleLocalized: Map<String, String?> = emptyMap(),
    @SerialName("descriptionLocalized")
    val descriptionLocalized: Map<String, String?>? = null,
    @SerialName("cover")
    val cover: String = "",
    @SerialName("type")
    val type: String = "",
    @SerialName("deckId")
    val deckId: String? = null,
    @SerialName("priority")
    val priority: Int = -1,
    @SerialName("style")
    val style: String = ""
) {
    fun copy(
        id: String,
        titleLocalized: Map<String, String?> = this.titleLocalized,
        descriptionLocalized: Map<String, String?>? = this.descriptionLocalized,
        cover: String = this.cover,
        type: String = this.type,
        deckId: String? = this.deckId,
        priority: Int = this.priority,
        style: String = this.style
    ): HomeBannerDto = HomeBannerDto(id, titleLocalized, descriptionLocalized, cover, type, deckId, priority, style)

    fun toDomain() = HomeAdvBanner(
        id = id,
        deckId = deckId?.let(::DeckId),
        title = LocalizableString(titleLocalized),
        description = descriptionLocalized?.let(::LocalizableString),
        coverUrl = cover,
        type = HomeBannerType.fromString(type),
        priority = priority,
        style = HomeBannerStyle.fromString(style)
    )
}
