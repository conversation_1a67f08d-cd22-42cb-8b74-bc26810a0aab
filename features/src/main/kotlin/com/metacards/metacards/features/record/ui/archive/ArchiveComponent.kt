package com.metacards.metacards.features.record.ui.archive

import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CourseThemeId
import com.metacards.metacards.features.record.domain.RecordSource
import com.metacards.metacards.features.record.ui.record_details.RecordDetailsComponent
import com.metacards.metacards.features.record.ui.record_list.RecordListComponent
import kotlinx.coroutines.flow.StateFlow

interface ArchiveComponent {
    val childStack: StateFlow<ChildStack<*, Child>>

    sealed interface Child {
        class ArchivedRecordList(val component: RecordListComponent) : Child
    }

    sealed interface Output {
        data class RecordDetailsRequested(
            val recordSource: RecordSource,
            val recordType: RecordDetailsComponent.RecordType,
            val isDailyCardLayout: Boolean,
            val courseId: CourseId?,
            val courseThemeId: CourseThemeId?
        ) : Output

        data object CreateRecordFlowRequested : Output
        data object SubscriptionSuggestingRequested : Output
    }
}