package com.metacards.metacards.features.special_deck.ui.starry_sky.widgets

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.widget.navigation_bar.IconNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCard
import com.metacards.metacards.features.special_deck.domain.entity.UserSpecialDeckInfo
import dev.icerock.moko.resources.StringResource
import dev.icerock.moko.resources.desc.ResourceFormattedStringDesc
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun SpecialDeckStarrySkyTitle(
    modifier: Modifier = Modifier,
    deckCards: List<SpecialDeckCard>,
    receivedCards: Set<CardId>,
    userSpecialDeckInfo: UserSpecialDeckInfo,
    onCloseClick: () -> Unit,
) {
    Column(modifier = modifier) {
        TopNavigationBar(
            leadingIcon = {
                IconNavigationItem(
                    iconRes = R.drawable.ic_24_close,
                    onClick = onCloseClick
                )
            },
            title = R.string.special_deck_starry_sky_title.strResDesc()
        )

        Text(
            text = ResourceFormattedStringDesc(
                StringResource(R.string.special_deck_starry_sky_subtitle_1),
                listOf(receivedCards.size, deckCards.size)
            ).localizedByLocal(),
            style = CustomTheme.typography.caption.small,
            color = CustomTheme.colors.text.primary,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .fillMaxWidth()
        )
        val subtitle = if (userSpecialDeckInfo.isCanSelectNewCard()) {
            R.string.special_deck_starry_sky_subtitle_2_1.strResDesc().localizedByLocal()
        } else {
            ResourceFormattedStringDesc(
                StringResource(R.string.special_deck_starry_sky_subtitle_2_2),
                listOf(
                    userSpecialDeckInfo.cooldownTime.hours,
                    userSpecialDeckInfo.cooldownTime.minutes
                )
            ).localizedByLocal()
        }
        if (!userSpecialDeckInfo.isCompleted) {
            Text(
                text = subtitle,
                style = CustomTheme.typography.caption.small,
                color = CustomTheme.colors.text.primary,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 6.dp, horizontal = 50.dp)
            )
        }
    }
}