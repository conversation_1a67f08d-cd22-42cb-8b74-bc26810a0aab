package com.metacards.metacards.features.special_deck.ui.card_details.widgets

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.ui.MetaCardDefaults
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.launch
import kotlin.math.PI
import kotlin.math.sin

private const val GLASS_DAMAGE_LEVEL_0_START = 0
private const val GLASS_DAMAGE_LEVEL_0_END = 2
const val GLASS_DAMAGE_LEVEL_1_START = 3
private const val GLASS_DAMAGE_LEVEL_1_END = 5
const val GLASS_DAMAGE_LEVEL_2_START = 6
const val GLASS_DAMAGE_LEVEL_2_END = 9

@Composable
fun SpecialDeckCardDetailsGlassCard(
    normalImage: ImageBitmap?,
    glassDamageLevel: Int,
    onGlassClick: () -> Unit,
) {
    val coroutineScope = rememberCoroutineScope()

    val glassPainterRes = remember(glassDamageLevel) {
        when (glassDamageLevel) {
            in GLASS_DAMAGE_LEVEL_0_START..GLASS_DAMAGE_LEVEL_0_END -> R.drawable.ic_break_glass_level_0
            in GLASS_DAMAGE_LEVEL_1_START..GLASS_DAMAGE_LEVEL_1_END -> R.drawable.ic_break_glass_level_1
            else -> R.drawable.ic_break_glass_level_2
        }
    }
    val interactionSource = remember { MutableInteractionSource() }

    var currentAnimationSinX by remember {
        mutableFloatStateOf(0f)
    }

    val cardOffsetAnimatable = remember {
        Animatable(initialValue = 0f)
    }

    fun launchCardAnimation() {
        if (!cardOffsetAnimatable.isRunning) {
            coroutineScope.launch {
                cardOffsetAnimatable.animateTo(
                    targetValue = currentAnimationSinX + 3 * PI.toFloat(),
                    animationSpec = tween(
                        durationMillis = 300,
                        easing = LinearEasing
                    )
                )
                currentAnimationSinX += 3 * PI.toFloat()
            }
        }
    }

    val cardOffset = sin(cardOffsetAnimatable.value) * 10f

    SpecialDeckCardContainer(
        cardContent = {
            normalImage?.let {
                Image(
                    bitmap = it,
                    contentDescription = null,
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(MetaCardDefaults.shape)
                )
            }

            Image(
                painter = painterResource(id = glassPainterRes),
                contentDescription = null,
                contentScale = ContentScale.FillBounds,
                modifier = Modifier
                    .fillMaxSize()
                    .clip(MetaCardDefaults.shape)
            )
        },
        hintContent = {
            val hintAlpha by animateFloatAsState(
                targetValue = if (normalImage != null) 1f else 0f,
                label = "hintAlpha"
            )
            Text(
                text = R.string.special_deck_details_text_break_glass.strResDesc().localizedByLocal(),
                style = CustomTheme.typography.body.primary,
                color = CustomTheme.colors.text.primary,
                modifier = Modifier
                    .padding(top = 16.dp)
                    .alpha(hintAlpha)
            )
        },
        isLoading = normalImage == null,
        cardModifier = Modifier
            .clickable(
                indication = null,
                interactionSource = interactionSource,
                onClick = {
                    launchCardAnimation()
                    onGlassClick()
                }
            )
            .offset(cardOffset.dp)
    )
}
