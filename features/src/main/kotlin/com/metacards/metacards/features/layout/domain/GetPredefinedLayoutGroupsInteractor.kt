package com.metacards.metacards.features.layout.domain

import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.mapData
import com.metacards.metacards.features.user.domain.UserRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine

class GetPredefinedLayoutGroupsInteractor(
    private val userRepository: UserRepository,
    private val predefinedLayoutRepository: PredefinedLayoutRepository,
) {

    fun execute(): Flow<LoadableState<List<PredefinedLayoutGroupWithAvailable>>> = combine(
        userRepository.user,
        predefinedLayoutRepository.getPredefinedLayoutGroups()
    ) { user, groups ->
        val hasSubscription = user?.subscriptionState is User.SubscriptionState.Ongoing

        val layouts = predefinedLayoutRepository.getPredefinedLayouts().map { layout ->
            PredefinedLayoutWithAvailable(
                layout = layout,
                isAvailable = layout.isFree || hasSubscription
            )
        }

        groups.mapData { list ->
            list?.map { group ->
                PredefinedLayoutGroupWithAvailable(
                    name = group.name,
                    layouts = group.layoutIds
                        .mapNotNull { layoutId ->
                            layouts.find { it.layout.id == layoutId }
                        }
                        .sortedBy { !it.isAvailable }
                )
            }
        }
    }
}
