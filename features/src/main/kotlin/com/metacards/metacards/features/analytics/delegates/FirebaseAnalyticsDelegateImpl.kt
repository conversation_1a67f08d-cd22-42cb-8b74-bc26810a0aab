package com.metacards.metacards.features.analytics.delegates

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.os.Bundle
import com.google.firebase.analytics.FirebaseAnalytics
import com.metacards.metacards.core.localization.data.AppLanguageService
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.features.analytics.AnalyticsEvent

@SuppressLint("MissingPermission")
class FirebaseAnalyticsDelegateImpl(
    private val context: Context,
    private val appLanguageService: AppLanguageService
) : FirebaseAnalyticsDelegate {

    private val firebaseAnalytics by lazy {
        FirebaseAnalytics.getInstance(context)
    }

    override fun sendEvent(analyticsEvent: AnalyticsEvent, user: User?) {
        firebaseAnalytics.logEvent(analyticsEvent.name, createParamsBundle(analyticsEvent, user))
    }

    private fun createParamsBundle(analyticsEvent: AnalyticsEvent, user: User?): Bundle {
        return Bundle().apply {
            analyticsEvent.parameters?.forEach {
                putString(it.key, it.value)
            }

            putString("user_id", user?.userId?.value)
            putString("user_name", user?.name)
            putString("user_gender", user?.gender?.name)
            putString("user_email", user?.email?.value)
            putString("user_phone", user?.phone)
            putString("language", appLanguageService.getLanguage().name)
            putString(
                "device_sdk_version",
                Build.VERSION.SDK_INT.toString() + " (${Build.VERSION.RELEASE})"
            )
            putString("device_vendor", Build.MANUFACTURER)
            putString("device_model", Build.MODEL)
        }
    }
}