package com.metacards.metacards.features.user.ui.subscription

import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.childContext
import com.arkivanov.essenty.lifecycle.doOnDestroy
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.auth_suggestion_dialog.createAuthSuggestion
import com.metacards.metacards.core.createDefaultDialogComponent
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.model.DialogData
import com.metacards.metacards.core.dialog.dialogControl
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.error_handling.safeRun
import com.metacards.metacards.core.message.data.MessageService
import com.metacards.metacards.core.message.domain.Message
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.payments.createPaymentComponent
import com.metacards.metacards.features.payments.ui.PaymentComponent
import com.metacards.metacards.features.user.domain.UserRepository
import dev.icerock.moko.resources.desc.desc
import kotlinx.coroutines.flow.MutableStateFlow

class RealSubscriptionComponent(
    componentContext: ComponentContext,
    componentFactory: ComponentFactory,
    override val baseSubscriptionComponent: BaseSubscriptionComponent,
    userRepository: UserRepository,
    private val errorHandler: ErrorHandler,
    private val analyticsService: AnalyticsService,
    private val messageService: MessageService,
    private val onOutput: (SubscriptionComponent.Output) -> Unit
) : ComponentContext by componentContext, SubscriptionComponent {
    override val subscriptionScreenState = MutableStateFlow(SubscriptionScreenState.START)

    override val paymentComponent: PaymentComponent by lazy {
        componentFactory.createPaymentComponent(
            componentContext.childContext("PaymentComponent"),
            ::onPaymentOutput
        )
    }

    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        dialogControl(dialogComponentFactory = { config, componentContext, dialogControl ->
            componentFactory.createDefaultDialogComponent(
                componentContext,
                config.data,
                dialogControl::dismiss
            )
        })

    private val user = userRepository.user

    private fun onPaymentOutput(output: PaymentComponent.Output) {
        when (output) {
            is PaymentComponent.Output.Canceled -> {
                messageService.showMessage(
                    Message(
                        text = "StringDesc.Resource(R.string.deck_info_payment_canceled)".desc(),
                    )
                )
            }

            is PaymentComponent.Output.Succeed -> {
                messageService.showMessage(
                    Message(
                        text = "StringDesc.Resource(R.string.deck_info_payment_completed)".desc(),
                    )
                )
            }

            is PaymentComponent.Output.WebStoreRequested -> {
                onOutput(SubscriptionComponent.Output.WebViewRequested(output.url, output.title))
            }
        }
    }

    init {
        lifecycle.doOnDestroy {
            analyticsService.logEvent(AnalyticsEvent.SubBackEvent)
        }
    }

    override fun onCompleteClick() {
        if (user.value == null) {
            dialogControl.show(
                DefaultDialogComponent.Config(
                    DialogData.createAuthSuggestion(
                        cancelAction = {
                            dialogControl.dismiss()
                        },
                        acceptAction = {
                            analyticsService.logEvent(AnalyticsEvent.AccountAuthEvent)
                            onOutput(SubscriptionComponent.Output.AuthScreenRequested)
                        }
                    )
                )
            )
        } else {
            safeRun(errorHandler) {
                val purchaseType = baseSubscriptionComponent.getPurchaseType()
                paymentComponent.startPayment(purchaseType)
            }
        }
    }

    override fun onGoToMainClick() {
        when (subscriptionScreenState.value) {
            SubscriptionScreenState.COMPLETED -> {
                if (baseSubscriptionComponent.userSubscriptionState.value !is User.SubscriptionState.Ongoing) {
                    subscriptionScreenState.value = SubscriptionScreenState.START
                } else {
                    onOutput(SubscriptionComponent.Output.MainScreenRequested)
                }
            }

            SubscriptionScreenState.CANCEL -> {
                componentScope.safeLaunch(errorHandler) {
                    subscriptionScreenState.value = SubscriptionScreenState.START
                }
            }

            else -> {
                // Do nothing
            }
        }
    }

    override fun onSuccessBackClick() {
        subscriptionScreenState.value = SubscriptionScreenState.START
    }

    override fun onCancelClick() {
        safeRun(errorHandler) {
            val id = baseSubscriptionComponent.getIdForCancel()
            val isCanceled = paymentComponent.cancelPurchase(id)
            if (isCanceled) {
                subscriptionScreenState.value = SubscriptionScreenState.CANCEL
            }
        }
    }
}