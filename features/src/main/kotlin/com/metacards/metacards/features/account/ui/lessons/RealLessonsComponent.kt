package com.metacards.metacards.features.account.ui.lessons

import android.os.Parcelable
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.stack.ChildStack
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.router.stack.childStack
import com.arkivanov.decompose.router.stack.push
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.utils.toStateFlow
import com.metacards.metacards.features.account.createLessonCategoriesComponent
import com.metacards.metacards.features.account.createLessonDetailsComponent
import com.metacards.metacards.features.account.createLessonListComponent
import com.metacards.metacards.features.account.domain.entity.LessonCategoryId
import com.metacards.metacards.features.account.ui.lessons.categories.LessonCategoriesComponent
import com.metacards.metacards.features.account.ui.lessons.list.LessonListComponent
import com.metacards.metacards.features.deck.domain.entity.Lesson
import kotlinx.coroutines.flow.StateFlow
import kotlinx.parcelize.Parcelize

class RealLessonsComponent(
    componentContext: ComponentContext,
    private val componentFactory: ComponentFactory,
    private val onOutput: (LessonsComponent.Output) -> Unit,
    screen: LessonsComponent.Screen
) : ComponentContext by componentContext, LessonsComponent {

    private val navigation = StackNavigation<ChildConfig>()

    override val childStack: StateFlow<ChildStack<*, LessonsComponent.Child>> =
        childStack(
            source = navigation,
            initialConfiguration = screen.toChildConfig(),
            handleBackButton = true,
            childFactory = ::createChild
        ).toStateFlow(lifecycle)

    private fun createChild(
        childConfig: ChildConfig,
        componentContext: ComponentContext
    ) = when (childConfig) {
        is ChildConfig.Categories -> {
            LessonsComponent.Child.Categories(
                componentFactory.createLessonCategoriesComponent(
                    componentContext,
                    ::onCategoriesOutput
                )
            )
        }

        is ChildConfig.Details -> {
            LessonsComponent.Child.Details(
                componentFactory.createLessonDetailsComponent(
                    componentContext,
                    childConfig.lesson
                )
            )
        }

        is ChildConfig.List -> {
            LessonsComponent.Child.List(
                componentFactory.createLessonListComponent(
                    componentContext,
                    childConfig.lessonCategoryId,
                    ::onListOutput
                )
            )
        }
    }

    private fun onListOutput(output: LessonListComponent.Output) {
        when (output) {
            is LessonListComponent.Output.LessonDetailsRequested -> {
                navigation.push(ChildConfig.Details(output.lesson))
            }
        }
    }

    private fun onCategoriesOutput(output: LessonCategoriesComponent.Output) {
        when (output) {
            is LessonCategoriesComponent.Output.CategorySelected -> {
                navigation.push(ChildConfig.List(output.lessonCategoryId))
            }

            is LessonCategoriesComponent.Output.TutorialRequested -> {
                onOutput(LessonsComponent.Output.TutorialRequested)
            }
        }
    }

    sealed interface ChildConfig : Parcelable {
        @Parcelize
        data object Categories : ChildConfig

        @Parcelize
        data class List(val lessonCategoryId: LessonCategoryId) : ChildConfig

        @Parcelize
        data class Details(val lesson: Lesson) : ChildConfig
    }

    private fun LessonsComponent.Screen.toChildConfig() = when (this) {
        LessonsComponent.Screen.Categories -> ChildConfig.Categories
        is LessonsComponent.Screen.Details -> ChildConfig.List(lessonCategoryId)
        is LessonsComponent.Screen.List -> ChildConfig.Details(lesson)
    }
}
