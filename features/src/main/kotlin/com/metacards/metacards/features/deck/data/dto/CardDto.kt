package com.metacards.metacards.features.deck.data.dto

import androidx.core.net.toUri
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.deck.domain.entity.CardWithComment
import com.metacards.metacards.features.deck.domain.entity.DeckId
import kotlinx.serialization.Serializable

@Serializable
data class CardDto(
    val cardId: String = "",
    val deckId: String = "",
    val image: String = "",
    val texture: String = "",
    val comment: String? = null,
    val arObject: String? = null,
    val animation: String? = null,
    val nameLocalized: Map<String, String?>? = null,
    val helpLocalized: Map<String, String?>? = null,
)

fun CardDto.toDomain(): Card {
    return Card(
        id = CardId(cardId),
        deckId = DeckId(deckId),
        imageUrl = image,
        textureUri = texture.toUri(),
        arObjectUrl = arObject,
        gifUrl = animation,
        name = nameLocalized?.let(::LocalizableString),
        help = helpLocalized?.let(::LocalizableString)
    )
}

fun CardWithComment.toDto(): CardDto {
    return CardDto(
        cardId = card.id.value,
        deckId = card.deckId.value,
        image = card.imageUrl,
        texture = card.textureUri.toString(),
        arObject = card.arObjectUrl,
        comment = comment.ifEmpty { null },
        animation = card.gifUrl
    )
}