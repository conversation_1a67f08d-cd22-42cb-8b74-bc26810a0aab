package com.metacards.metacards.features.user.ui.subscription.subscription_bottom_sheet

import com.metacards.metacards.core.button.ButtonState
import kotlinx.coroutines.flow.MutableStateFlow

class FakeSubscriptionBottomSheetComponent(
    override val isWithAdv: Boolean = true,
    override val isSpecialDeckCardRequested: Boolean = false
) : SubscriptionBottomSheetComponent {

    override val showAdvButtonState = MutableStateFlow(ButtonState.Loading)

    override fun onShowAdvClick() = Unit
    override fun onLookTariffsClick() = Unit
    override fun onCloseClick() = Unit
}