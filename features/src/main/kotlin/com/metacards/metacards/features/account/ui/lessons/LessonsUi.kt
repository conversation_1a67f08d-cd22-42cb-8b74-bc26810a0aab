package com.metacards.metacards.features.account.ui.lessons

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import com.arkivanov.decompose.extensions.compose.jetpack.stack.Children
import com.metacards.metacards.features.account.ui.lessons.categories.LessonCategoriesUi
import com.metacards.metacards.features.account.ui.lessons.details.LessonDetailsUi
import com.metacards.metacards.features.account.ui.lessons.list.LessonListUi

@Composable
fun LessonsUi(
    component: LessonsComponent,
    modifier: Modifier = Modifier
) {
    val childStack by component.childStack.collectAsState()

    Box(modifier = modifier) {
        Children(stack = childStack, modifier = Modifier.fillMaxSize()) { child ->
            when (val instance = child.instance) {
                is LessonsComponent.Child.Categories -> LessonCategoriesUi(
                    instance.component,
                    Modifier.fillMaxSize()
                )
                is LessonsComponent.Child.Details -> LessonDetailsUi(
                    instance.component,
                    Modifier.fillMaxSize()
                )
                is LessonsComponent.Child.List -> LessonListUi(
                    instance.component,
                    Modifier.fillMaxSize()
                )
            }
        }
    }
}