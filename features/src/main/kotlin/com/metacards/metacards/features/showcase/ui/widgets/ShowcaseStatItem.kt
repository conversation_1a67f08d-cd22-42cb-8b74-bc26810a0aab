package com.metacards.metacards.features.showcase.ui.widgets

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun ColumnScope.ShowcaseStatItem(
    onAnalyticsBannerClick: () -> Unit
) {
    ShowcaseItemContainer(
        title = R.string.showcase_stat_title.strResDesc().localizedByLocal(),
        subtitle = R.string.showcase_stat_subtitle.strResDesc().localizedByLocal()
    ) {
        Image(
            painter = painterResource(R.drawable.ic_showcase_stat_bg),
            contentDescription = "showcase_stat_bg",
            contentScale = ContentScale.FillWidth,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .clip(RoundedCornerShape(16.dp))
                .clickable(onClick = onAnalyticsBannerClick)
        )
    }
}