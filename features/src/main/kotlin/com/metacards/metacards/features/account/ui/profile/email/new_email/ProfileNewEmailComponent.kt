package com.metacards.metacards.features.account.ui.profile.email.new_email

import androidx.annotation.StringRes
import com.metacards.metacards.core.button.ButtonState
import kotlinx.coroutines.flow.StateFlow
import ru.mobileup.kmm_form_validation.control.InputControl

interface ProfileNewEmailComponent {
    val inputControl: InputControl
    val buttonState: StateFlow<ButtonState>

    fun onSaveClick()

    sealed interface Output {
        data class EmailVerificationRequested(
            @StringRes val title: Int,
            @StringRes val text: Int,
            val email: String
        ) : Output
    }
}