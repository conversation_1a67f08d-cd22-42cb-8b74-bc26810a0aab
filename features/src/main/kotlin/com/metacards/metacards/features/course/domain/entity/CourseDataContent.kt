package com.metacards.metacards.features.course.domain.entity

import android.os.Parcelable
import com.metacards.metacards.core.localization.ui.LocalizableString
import kotlinx.parcelize.Parcelize

@Parcelize
data class CourseDataContent(
    val contentId: CourseContentId,
    val name: LocalizableString,
    val order: Int,
    val type: CourseContentType,
    val isCompleted: Boolean
) : Parcelable {
    companion object {
        val mock = CourseDataContent(
            contentId = CourseContentId(""),
            name = LocalizableString.createNonLocalizable(""),
            order = 8068,
            type = CourseContentType.LESSON,
            isCompleted = false
        )
    }
}