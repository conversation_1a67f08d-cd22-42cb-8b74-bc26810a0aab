package com.metacards.metacards.features.layout.data

import com.google.firebase.firestore.toObject
import com.metacards.metacards.core.network.firestore.FirestoreService
import com.metacards.metacards.core.network.firestore.getFlowWithLoadable
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.mapLoadable
import com.metacards.metacards.features.layout.data.dto.PredefinedLayoutDto
import com.metacards.metacards.features.layout.data.dto.PredefinedLayoutGroupDto
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.tasks.await

class PredefinedLayoutDataSource(private val firestoreService: FirestoreService) {

    suspend fun getPredefinedLayouts(): List<PredefinedLayoutDto> = firestoreService.db
        .collection(PREDEFINED_LAYOUTS_COLLECTION_PATH)
        .get()
        .await()
        .documents
        .mapNotNull { it.toObject<PredefinedLayoutDto>()?.copy(id = it.id) }

    fun getPredefinedLayoutGroups(): Flow<LoadableState<List<PredefinedLayoutGroupDto>>> =
        firestoreService.db
            .collection(PREDEFINED_LAYOUT_GROUPS_COLLECTION_PATH)
            .getFlowWithLoadable(firestoreService)
            .mapLoadable {
                it?.documents?.mapNotNull { document ->
                    document.toObject<PredefinedLayoutGroupDto>()?.copy(id = document.id)
                }
            }

    companion object {
        private const val PREDEFINED_LAYOUTS_COLLECTION_PATH = "layouts"
        private const val PREDEFINED_LAYOUT_GROUPS_COLLECTION_PATH = "layoutGroups"
    }
}
