package com.metacards.metacards.features.home.ui

import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.features.advbanner.domain.entity.HomeAdvBanner
import com.metacards.metacards.features.deck.domain.entity.DailyCard
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.domain.entity.DeckWithAvailable
import com.metacards.metacards.features.tutorial.data.TutorialStep
import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import kotlinx.coroutines.flow.StateFlow

interface HomeComponent {
    val deckList: StateFlow<List<DeckWithAvailable>>
    val homeAdvBanner: StateFlow<HomeAdvBanner?>
    val dailyCardState: StateFlow<DailyCard?>
    val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent>
    val tutorialMessage: StateFlow<TutorialMessage?>
    val tutorialStep: StateFlow<TutorialStep>

    fun onDeckInfoButtonClick(deckId: DeckId)
    fun onDeckClick(deckId: DeckId)
    fun onAdvBannerClick(homeAdvBanner: HomeAdvBanner)
    fun onCardOfTheDayClick()
    fun onAdvBannerDismiss()
    fun onLayoutBannerClick()

    sealed interface Output {
        data class DeckScreenRequested(val deckId: DeckId) : Output
        data class NewDeckLayoutRequested(val deckId: DeckId) : Output
        data object SubscriptionBottomSheetRequested : Output
        data object DailyCardRequested : Output
        data object SubscriptionScreenRequested : Output
        data object PredefinedLayoutsRequested : Output
        data object AuthScreenRequested : Output
        data object AuthSuggestingRequested : Output
        data object ShowShakerPopup : Output
        data class SpecialDeckRequested(val deckId: DeckId, val forceOnboarding: Boolean) : Output
    }
}
