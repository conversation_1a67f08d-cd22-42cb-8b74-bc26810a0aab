package com.metacards.metacards.features.account.ui.lessons

import android.os.Parcelable
import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.features.account.domain.entity.LessonCategoryId
import com.metacards.metacards.features.account.ui.lessons.categories.LessonCategoriesComponent
import com.metacards.metacards.features.account.ui.lessons.details.LessonDetailsComponent
import com.metacards.metacards.features.account.ui.lessons.list.LessonListComponent
import com.metacards.metacards.features.deck.domain.entity.Lesson
import kotlinx.coroutines.flow.StateFlow
import kotlinx.parcelize.Parcelize

interface LessonsComponent {

    val childStack: StateFlow<ChildStack<*, Child>>

    sealed interface Child {
        class Categories(val component: LessonCategoriesComponent) : Child
        class List(val component: LessonListComponent) : Child
        class Details(val component: LessonDetailsComponent) : Child
    }

    sealed interface Screen : Parcelable {
        @Parcelize
        data object Categories : Screen

        @Parcelize
        data class List(val lesson: Lesson) : Screen

        @Parcelize
        data class Details(val lessonCategoryId: LessonCategoryId) : Screen
    }

    sealed interface Output {
        data object TutorialRequested : Output
    }
}