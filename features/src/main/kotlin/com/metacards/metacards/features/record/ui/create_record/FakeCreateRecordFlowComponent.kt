package com.metacards.metacards.features.record.ui.create_record

import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.core.utils.createFakeChildStackStateFlow
import com.metacards.metacards.features.record.ui.create_record.add_card.FakeAddCardComponent
import kotlinx.coroutines.flow.StateFlow

class FakeCreateRecordFlowComponent : CreateRecordFlowComponent {
    override val childStack: StateFlow<ChildStack<*, CreateRecordFlowComponent.Child>> =
        createFakeChildStackStateFlow(CreateRecordFlowComponent.Child.AddCard(FakeAddCardComponent()))
}
