package com.metacards.metacards.features.course.ui.details

import com.metacards.metacards.core.utils.createFakeChildStackStateFlow
import com.metacards.metacards.features.course.domain.entity.CourseData
import com.metacards.metacards.features.course.ui.page.FakeCoursePageComponent
import kotlinx.coroutines.flow.MutableStateFlow

class FakeCourseDetailsComponent : CourseDetailsComponent {

    override val childStack =
        createFakeChildStackStateFlow(CourseDetailsComponent.Child.CoursePage(FakeCoursePageComponent()))
    override val courseData = MutableStateFlow<CourseData?>(null)
    override val courseQuery: CourseData.Query = CourseData.Query.mock
}