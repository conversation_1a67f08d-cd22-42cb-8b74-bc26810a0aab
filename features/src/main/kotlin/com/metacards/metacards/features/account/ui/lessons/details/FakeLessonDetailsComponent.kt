package com.metacards.metacards.features.account.ui.lessons.details

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.deck.domain.entity.Lesson

class FakeLessonDetailsComponent : LessonDetailsComponent {
    override val lesson: Lesson = Lesson(
        LocalizableString.createNonLocalizable(""),
        0,
        LocalizableString.createNonLocalizable(""),
        LocalizableString.createNonLocalizable(""),
        previewUrl = ""
    )

    override fun onVideoPlay() = Unit
}