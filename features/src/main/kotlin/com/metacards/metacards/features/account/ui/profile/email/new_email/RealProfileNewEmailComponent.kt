package com.metacards.metacards.features.account.ui.profile.email.new_email

import com.arkivanov.decompose.ComponentContext
import com.google.firebase.auth.FirebaseAuthUserCollisionException
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.user.domain.Email
import com.metacards.metacards.core.user.domain.UserProvider
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.features.R
import com.metacards.metacards.features.auth.domain.Password
import com.metacards.metacards.features.auth.domain.email.UpdateEmailInteractor
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import ru.mobileup.kmm_form_validation.control.InputControl
import ru.mobileup.kmm_form_validation.options.ImeAction
import ru.mobileup.kmm_form_validation.options.KeyboardOptions
import ru.mobileup.kmm_form_validation.options.KeyboardType
import ru.mobileup.kmm_form_validation.validation.control.isNotBlank
import ru.mobileup.kmm_form_validation.validation.control.regex
import ru.mobileup.kmm_form_validation.validation.form.FormValidator
import ru.mobileup.kmm_form_validation.validation.form.formValidator

class RealProfileNewEmailComponent(
    componentContext: ComponentContext,
    private val password: String,
    private val errorHandler: ErrorHandler,
    userProvider: UserProvider,
    private val updateEmailInteractor: UpdateEmailInteractor,
    private val onOutput: (ProfileNewEmailComponent.Output) -> Unit
) : ComponentContext by componentContext, ProfileNewEmailComponent {

    companion object {
        private const val EMAIL_REGEX_PATTERN =
            "[a-zA-Z0-9+._%\\-]{1,256}@[a-zA-Z0-9][a-zA-Z0-9\\-]{0,64}(\\.[a-zA-Z0-9][a-zA-Z0-9\\-]{0,25})+"
    }

    private val debounce = Debounce()

    private val userFlow = userProvider.getUser()

    override val inputControl: InputControl = InputControl(
        componentScope,
        keyboardOptions = KeyboardOptions(
            keyboardType = KeyboardType.Email,
            imeAction = ImeAction.Done
        )
    )

    private val formValidator: FormValidator = componentScope.formValidator {
        input(inputControl) {
            isNotBlank(StringDesc.Resource(R.string.email_enter_error))
            regex(
                regex = EMAIL_REGEX_PATTERN.toRegex(),
                errorMessage = StringDesc.Resource(R.string.email_enter_error)
            )
        }
    }

    private val _buttonState = MutableStateFlow(ButtonState.Enabled)

    override val buttonState = computed(_buttonState, inputControl.error) { state, error ->
        if (error != null) ButtonState.Disabled else state
    }

    init {
        with(inputControl) { text.onEach { error.value = null }.launchIn(componentScope) }
    }

    override fun onSaveClick() {
        if (!formValidator.validate().isValid) return
        DebounceClick(debounce, "onNewEmailSave", errorHandler) {
            try {
                _buttonState.value = ButtonState.Loading

                val user = userFlow.value ?: throw IllegalStateException("User not received")
                val newEmail = inputControl.text.value

                if (user.email?.value == newEmail) {
                    inputControl.error.value =
                        StringDesc.Resource(R.string.account_profile_edit_password_error_email_collision)
                    _buttonState.value = ButtonState.Disabled
                    return@DebounceClick
                }

                updateEmailInteractor.execute(Email(newEmail), Password(password))

                onOutput(
                    ProfileNewEmailComponent.Output.EmailVerificationRequested(
                        R.string.reset_password_sent_email_title,
                        R.string.account_profile_edit_email_verify_new_email_text,
                        inputControl.text.value
                    )
                )
            } catch (_: FirebaseAuthUserCollisionException) {
                inputControl.error.value =
                    StringDesc.Resource(R.string.account_profile_edit_password_error_user_collision)
                _buttonState.value = ButtonState.Disabled
            }
        }
    }
}