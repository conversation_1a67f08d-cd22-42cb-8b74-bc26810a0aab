package com.metacards.metacards.features.account.ui

import com.metacards.metacards.core.bottom_sheet.BottomSheetControl
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.localization.domain.AppLanguage
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.features.account.ui.flow.AccountFlowComponent
import com.metacards.metacards.features.account.ui.language.AccountLanguageComponent
import com.metacards.metacards.features.advbanner.domain.entity.AccountAdvBanner
import com.metacards.metacards.features.advbanner.domain.entity.AccountAdvBannerType
import com.metacards.metacards.features.deck.domain.entity.DeckId
import kotlinx.coroutines.flow.StateFlow

interface AccountComponent {
    val user: StateFlow<User?>
    val notificationToggleState: StateFlow<NotificationToggleState>
    val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent>
    val appLanguage: StateFlow<AppLanguage>
    val languageBottomSheetControl: BottomSheetControl<AccountLanguageComponent.Config, AccountLanguageComponent>
    val accountAdvBanner: StateFlow<AccountAdvBanner?>

    fun onPromocodesClick()
    fun onProfileBlockClick()
    fun onAdvBannerClick(type: AccountAdvBannerType)
    fun onSubscriptionClick()
    fun onBuyDecksClick()
    fun onLessonsClick()
    fun onFavouriteCardsClick()
    fun onLanguageClick()
    fun onNotificationToggleClick(isSubscribed: Boolean)
    fun onFeedbackClick()
    fun onRateClick()
    fun onAboutTheAppClick()
    fun onAddYourDeckClick()
    fun onInstagramClick()
    fun onFacebookClick()
    fun onInternetClick()
    fun onSpecialDeckClick()

    sealed interface Output {
        class AccountFlowScreenRequested(val screen: AccountFlowComponent.Screen) : Output
        data object FavoriteCardsRequested : Output
        data object AuthScreenRequested : Output
        data object AddPhysicalDeckRequested : Output
        data object AuthSuggestingScreenRequested : Output
        data object PremiumSuggestingScreenRequested : Output
        data object RecompositionRequested : Output
        data object AuthSuggestingRequested : Output
        data class SpecialDeckRequested(val deckId: DeckId) : Output
    }

    sealed interface NotificationToggleState {
        data object On : NotificationToggleState
        data object Off : NotificationToggleState
        data object Loading : NotificationToggleState
    }
}
