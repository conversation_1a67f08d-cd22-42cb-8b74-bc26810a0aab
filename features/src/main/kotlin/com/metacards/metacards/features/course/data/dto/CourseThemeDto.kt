package com.metacards.metacards.features.course.data.dto

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.course.domain.entity.CourseStatus
import com.metacards.metacards.features.course.domain.entity.CourseTheme
import com.metacards.metacards.features.course.domain.entity.CourseThemeId

data class CourseThemeDto(
    val id: String = "",
    val cover: String = "",
    val descriptionLocalized: Map<String, String?>? = null,
    val nameLocalized: Map<String, String?> = emptyMap(),
    val status: String = "",
    val courses: List<CourseShortDataDto> = emptyList()
) {
    fun toDomain() = CourseTheme(
        themeId = CourseThemeId(id),
        name = LocalizableString(nameLocalized),
        description = descriptionLocalized?.let(::LocalizableString),
        coverUrl = cover,
        status = CourseStatus.fromString(status),
        courses = courses.map { it.toDomain(id) }
    )
}
