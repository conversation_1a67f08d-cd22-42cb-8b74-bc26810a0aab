package com.metacards.metacards.features.record.data.dto

import com.google.firebase.Timestamp
import com.google.firebase.firestore.PropertyName
import com.metacards.metacards.core.utils.TimestampAsStringSerializer
import com.metacards.metacards.features.record.domain.NewRecord
import kotlinx.serialization.Serializable

@Serializable
class NewRecordDto(
    @Serializable(with = TimestampAsStringSerializer::class)
    val creationDate: Timestamp,
    val questions: List<QuestionDto>,
    val comments: List<CommentDto>,
    val moodLevel: Int?,
    val energyLevel: Int?,
    @get:PropertyName("isFavourite") // required for a field that starts with "is". See: https://medium.com/@eeddeellee/boolean-fields-that-start-with-is-in-firebase-firestore-49afb65e3639
    val isFavourite: Boolean,
    @get:PropertyName("isArchived")
    val isArchived: Boolean,
    val totalCards: List<String>,
    @get:PropertyName("isDailyCardLayout")
    val isDailyCardLayout: Boolean,
    @get:PropertyName("isViewedAds")
    val isViewedAds: Boolean,
    val courseId: String?,
    val courseThemeId: String?
)

@Serializable
data class NewRecordResult(
    val result: String? = null,
    val data: String? = null
)

fun NewRecord.toDto() = NewRecordDto(
    creationDate = Timestamp.now(),
    questions = questions.map { it.toDto() },
    comments = comments.map { it.toDto() },
    moodLevel = userFeelings.mood?.value,
    energyLevel = userFeelings.energy?.value,
    isFavourite = isFavourite,
    isArchived = false,
    totalCards = questions.flatMap {
        it.cardsWithComment.map { cardWithComment -> cardWithComment.card.id.value }
    },
    isDailyCardLayout = isDailyCardLayout,
    isViewedAds = isViewedAds,
    courseId = courseId?.value,
    courseThemeId = courseThemeId?.value
)