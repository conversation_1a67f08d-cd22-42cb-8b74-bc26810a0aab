package com.metacards.metacards.features.special_deck.ui.card_opening_cooldown

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.StringResource
import dev.icerock.moko.resources.desc.ResourceFormattedStringDesc
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun SpecialDeckCardOpeningCooldownUi(
    component: SpecialDeckCardOpeningCooldownComponent,
    modifier: Modifier = Modifier
) {

    Column(modifier = modifier) {
        Row(
            modifier = Modifier
                .padding(
                    horizontal = 16.dp,
                    vertical = 24.dp
                ),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = ResourceFormattedStringDesc(
                    StringResource(R.string.special_deck_card_opening_cooldown_title),
                    listOf(
                        component.deckCooldownTime.hours,
                        component.deckCooldownTime.minutes
                    )
                ).localizedByLocal(),
                style = CustomTheme.typography.heading.secondary,
                color = CustomTheme.colors.text.primary,
                modifier = Modifier.weight(1f)
            )

            BackNavigationItem(iconRes = R.drawable.ic_24_close)
        }

        Text(
            text = ResourceFormattedStringDesc(
                StringResource(R.string.special_deck_card_opening_cooldown_description_1),
                listOf(
                    component.deckCooldownTime.hours,
                    component.deckCooldownTime.minutes
                )
            ).localizedByLocal(),
            style = CustomTheme.typography.body.primary,
            color = CustomTheme.colors.text.primary,
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .fillMaxWidth()
        )

        Spacer(modifier = Modifier.height(12.dp))

        Text(
            text = R.string.special_deck_card_opening_cooldown_description_2.strResDesc().localizedByLocal(),
            style = CustomTheme.typography.body.primary,
            color = CustomTheme.colors.text.primary,
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .fillMaxWidth()
        )

        MetaAccentButton(
            text = R.string.special_deck_card_opening_cooldown_button_ok.strResDesc().localizedByLocal(),
            onClick = component::onUnderstoodClick,
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    top = 24.dp,
                    bottom = 8.dp,
                    start = 16.dp,
                    end = 16.dp
                )
        )
    }
}

@Preview
@Composable
private fun SpecialDeckCardOpeningCooldownPreview() {
    AppTheme {
        SpecialDeckCardOpeningCooldownUi(FakeSpecialDeckCardOpeningCooldownComponent())
    }
}