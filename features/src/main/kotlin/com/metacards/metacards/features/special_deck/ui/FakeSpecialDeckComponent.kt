package com.metacards.metacards.features.special_deck.ui

import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.FakeDialogControl
import com.metacards.metacards.core.utils.createFakeChildStackStateFlow
import com.metacards.metacards.features.special_deck.ui.constellation_obtained.FakeSpecialDeckConstellationObtainedComponent
import com.metacards.metacards.features.special_deck.ui.constellation_obtained.SpecialDeckConstellationObtainedComponent
import com.metacards.metacards.features.special_deck.ui.first_card_obtained.FakeSpecialDeckFirstCardObtainedComponent
import com.metacards.metacards.features.special_deck.ui.first_card_obtained.SpecialDeckFirstCardObtainedComponent
import com.metacards.metacards.features.special_deck.ui.card_details.FakeSpecialDeckCardDetailsComponent
import kotlinx.coroutines.flow.StateFlow

class FakeSpecialDeckComponent : SpecialDeckComponent {
    override val childStack: StateFlow<ChildStack<*, SpecialDeckComponent.Child>> = createFakeChildStackStateFlow(
        SpecialDeckComponent.Child.StarDetails(FakeSpecialDeckCardDetailsComponent())
    )
    override val constellationObtainedControl:
        DialogControl<SpecialDeckConstellationObtainedComponent.Config, SpecialDeckConstellationObtainedComponent> =
        FakeDialogControl(FakeSpecialDeckConstellationObtainedComponent())

    override val firstCardObtainedControl:
        DialogControl<SpecialDeckFirstCardObtainedComponent.Config, SpecialDeckFirstCardObtainedComponent> =
        FakeDialogControl(FakeSpecialDeckFirstCardObtainedComponent())
}