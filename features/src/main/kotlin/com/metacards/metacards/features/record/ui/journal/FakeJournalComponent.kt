package com.metacards.metacards.features.record.ui.journal

import com.metacards.metacards.core.utils.createFakeChildStackStateFlow
import com.metacards.metacards.features.record.ui.record_list.FakeRecordListComponent
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.datetime.Instant

class FakeJournalComponent : JournalComponent {

    override val tabsState = MutableStateFlow(TabsState.Normal)

    override val childStack = createFakeChildStackStateFlow(
        JournalComponent.Child.RecordList(FakeRecordListComponent(), JournalComponent.Tab.Layouts)
    )

    override fun onArchiveClick() = Unit

    override fun onCalendarClick() = Unit

    override fun onTabSelected(tab: JournalComponent.Tab) = Unit

    override fun onScrollToTopClick() = Unit
    override fun showJournalRecordsForDate(date: Instant) = Unit
}