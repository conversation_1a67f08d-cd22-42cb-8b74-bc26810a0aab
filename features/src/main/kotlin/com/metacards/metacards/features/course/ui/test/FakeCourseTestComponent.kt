package com.metacards.metacards.features.course.ui.test

import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.FakeDialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.FakeDefaultDialogComponent
import com.metacards.metacards.features.course.domain.entity.CourseQuestion
import com.metacards.metacards.features.course.domain.entity.CourseTest
import kotlinx.coroutines.flow.MutableStateFlow

class FakeCourseTestComponent : CourseTestComponent {

    override val courseName = null
    override val test = MutableStateFlow(CourseTest.Mock)
    override val shouldShowTutorial = MutableStateFlow(false)
    override val closeDialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        FakeDialogControl(FakeDefaultDialogComponent())

    override fun onUserInteracted() = Unit
    override fun onRevertClick(previousQuestion: CourseQuestion) = Unit
    override fun onQuestionAnswered(dragAnchor: CourseTestDragAnchors, question: CourseQuestion) = Unit
    override fun onLastQuestionAnswered() = Unit
    override fun onCloseTutorialClick() = Unit
    override fun onCloseClick() = Unit
}