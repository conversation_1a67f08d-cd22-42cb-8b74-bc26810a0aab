package com.metacards.metacards.features.deck.domain.repository

import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardHint
import com.metacards.metacards.features.deck.domain.entity.DailyCard
import com.metacards.metacards.features.deck.domain.entity.Deck
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.domain.entity.DeckInfo
import kotlinx.coroutines.flow.Flow

interface DecksRepository {
    suspend fun getDecksForPurchase(): List<Deck>
    fun getDecksForPurchaseFlow(): Flow<List<Deck>>
    suspend fun getDecksForSubscription(): List<Deck>
    suspend fun getFreeDecks(): List<Deck>
    fun getCardsByDeckFlow(deckId: DeckId): Flow<LoadableState<List<Card>>>
    suspend fun getCardsByDeck(deckId: DeckId): List<Card>
    fun getDeckById(deckId: DeckId): Flow<LoadableState<Deck>>
    fun getDeckInfoByIdFlow(deckId: DeckId): Flow<LoadableState<DeckInfo>>
    suspend fun getDeckInfoById(deckId: DeckId): DeckInfo?
    suspend fun getCardHints(): List<CardHint>?
    suspend fun getDailyCard(): DailyCard
}