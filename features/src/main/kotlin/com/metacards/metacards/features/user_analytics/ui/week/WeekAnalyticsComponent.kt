package com.metacards.metacards.features.user_analytics.ui.week

import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.PagedData
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.user_analytics.domain.week.WeekAnalyticsInfo
import kotlinx.coroutines.flow.StateFlow

interface WeekAnalyticsComponent {

    val analyticsState: StateFlow<LoadableState<PagedData<WeekAnalyticsInfo>>>
    val isPremiumUser: StateFlow<Boolean>

    fun onLoadMore()

    fun onWeekPrev()

    fun onWeekNext()

    fun onRecordClick(recordId: RecordId)

    sealed interface Output {
        data class RecordDetailsRequested(val recordId: RecordId) : Output
    }
}