package com.metacards.metacards.features.account.ui.profile.email

import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.features.account.ui.profile.email.new_email.ProfileNewEmailComponent
import com.metacards.metacards.features.account.ui.profile.password.confirm.ProfileConfirmPasswordComponent
import com.metacards.metacards.features.auth.ui.auth_type.email.message.SentMessageToEmailComponent
import kotlinx.coroutines.flow.StateFlow

interface ProfileEmailComponent {
    val childStack: StateFlow<ChildStack<*, Child>>

    sealed interface Child {
        class ConfirmPassword(val component: ProfileConfirmPasswordComponent) : Child
        class NewEmail(val component: ProfileNewEmailComponent) : Child
        class VerifyEmail(val component: SentMessageToEmailComponent) : Child
    }

    sealed interface Output {
        object AuthScreenRequested : Output
        object MainScreenRequested : Output
    }
}