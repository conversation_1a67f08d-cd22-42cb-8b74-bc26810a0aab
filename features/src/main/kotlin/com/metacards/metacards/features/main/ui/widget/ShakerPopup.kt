package com.metacards.metacards.features.main.ui.widget

import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.strResDesc

private const val INITIAL_TRANSLATION_VALUE = -50f
private const val TARGET_TRANSLATION_VALUE = 50f
private const val ANIMATION_DURATION_VALUE = 250

@Composable
fun ShakerPopup(
    onDismissButtonClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    var popupControl by remember { mutableStateOf(true) }
    val infiniteTransition = rememberInfiniteTransition(label = "shakerPopupTransitionAnimation")

    val translationX by infiniteTransition.animateFloat(
        initialValue = INITIAL_TRANSLATION_VALUE,
        targetValue = TARGET_TRANSLATION_VALUE,
        animationSpec = infiniteRepeatable(
            animation = tween(ANIMATION_DURATION_VALUE, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ), label = "shakerPopupTranslationXAnimation"
    )

    if (popupControl) {
        Popup(
            alignment = Alignment.BottomCenter,
            properties = PopupProperties(focusable = true),
            onDismissRequest = {
                onDismissButtonClick()
                popupControl = false
            }
        ) {
            Surface(
                shape = RoundedCornerShape(16.dp),
                color = CustomTheme.colors.background.modal,
                modifier = modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .padding(bottom = 8.dp)
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.padding(vertical = 40.dp, horizontal = 24.dp)
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_shaker_popup_phone),
                        contentDescription = null,
                        modifier = Modifier
                            .height(230.dp)
                            .graphicsLayer { this.translationX = translationX }
                            .padding(bottom = 24.dp)
                    )

                    Text(
                        text = R.string.home_shake_popup_title.strResDesc().localizedByLocal(),
                        style = CustomTheme.typography.heading.medium,
                        color = CustomTheme.colors.text.primary,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )

                    Text(
                        text = R.string.home_shake_popup_text.strResDesc().localizedByLocal(),
                        style = CustomTheme.typography.body.primary,
                        color = CustomTheme.colors.text.primary,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )

                    MetaAccentButton(
                        text = R.string.home_shake_popup_button.strResDesc().localizedByLocal(),
                        onClick = onDismissButtonClick,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        }
    }
}
