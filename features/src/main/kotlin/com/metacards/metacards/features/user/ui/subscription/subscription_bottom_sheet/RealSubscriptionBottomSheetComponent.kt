package com.metacards.metacards.features.user.ui.subscription.subscription_bottom_sheet

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.adv.domain.YandexAdvHelper
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn

class RealSubscriptionBottomSheetComponent(
    componentContext: ComponentContext,
    override val isSpecialDeckCardRequested: <PERSON><PERSON>an,
    override val isWithAdv: Boolean,
    private val analyticsService: AnalyticsService,
    private val yandexAdvHelper: YandexAdvHelper,
    private val errorHandler: ErrorHandler,
    private val onOutput: (SubscriptionBottomSheetComponent.Output) -> Unit
) : ComponentContext by componentContext, SubscriptionBottomSheetComponent {

    override val showAdvButtonState = yandexAdvHelper.isAdvLoaded.map { loaded ->
        if (loaded) ButtonState.Enabled else ButtonState.Loading
    }.stateIn(componentScope, SharingStarted.Eagerly, ButtonState.Loading)

    private val debounce = Debounce()

    init { if (isWithAdv) { yandexAdvHelper.init() } }

    override fun onLookTariffsClick() {
        onOutput(SubscriptionBottomSheetComponent.Output.TariffsRequested)
    }

    override fun onShowAdvClick() {
        DebounceClick(debounce, "subscriptionBSHShowAdvClick", errorHandler) {
            val onAdDismissed = {
                if (yandexAdvHelper.isRewarded) {
                    onOutput(SubscriptionBottomSheetComponent.Output.DismissRequested)
                }
            }
            yandexAdvHelper.showAdv(
                onAdDismissed = onAdDismissed,
                onAdFailedToShow = {
                    // этот весь код нужен из-за вот этой проблемы: https://ascnt.atlassian.net/browse/META-1559
                    componentScope.safeLaunch(errorHandler) {
                        yandexAdvHelper.init()
                        // ждем, пока реклама не загрузится
                        yandexAdvHelper.isAdvLoaded.first { it }
                        yandexAdvHelper.showAdv(
                            onAdDismissed = onAdDismissed,
                            onAdFailedToShow = {}
                        )
                    }
                }
            )
        }
    }

    override fun onCloseClick() {
        analyticsService.logEvent(AnalyticsEvent.SubFormCloseEvent)
        onOutput(SubscriptionBottomSheetComponent.Output.DismissRequested)
    }
}