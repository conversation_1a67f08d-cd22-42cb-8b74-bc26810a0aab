package com.metacards.metacards.features.auth.data

import co.touchlab.kermit.Logger
import com.google.firebase.auth.AuthCredential
import com.google.firebase.auth.EmailAuthProvider
import com.google.firebase.auth.FirebaseAuth.AuthStateListener
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.auth.ktx.auth
import com.google.firebase.ktx.Firebase
import com.metacards.metacards.features.auth.domain.AuthUser
import com.metacards.metacards.features.auth.domain.LoginType
import com.metacards.metacards.features.auth.domain.SSOType
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await

class FirebaseAuthService {
    private val auth = Firebase.auth

    private val logger = Logger.withTag("FirebaseAuthService")

    private val _currentUser: MutableSharedFlow<FirebaseUser?> = MutableSharedFlow(
        replay = 1,
        onBufferOverflow = BufferOverflow.DROP_OLDEST,
    )

    val currentUser: MutableSharedFlow<FirebaseUser?>
        get() = _currentUser

    private val authStateListener = AuthStateListener {
        _currentUser.tryEmit(it.currentUser)
    }

    init {
        auth.addAuthStateListener(authStateListener)
    }

    suspend fun awaitAuthUser(timeout: Long): FirebaseUser? {
        var lock = true
        var timer = 0L
        val step = 100L
        var firebaseUser: FirebaseUser? = null

        try {
            coroutineScope {
                launch {
                    logger.i("launching timer with timeout: $timeout")
                    while (true) {
                        logger.i("timer: $timer of $timeout")
                        if (timer >= timeout) {
                            lock = false
                            logger.i("timer lock free")
                            break
                        }
                        delay(step)
                        timer += step
                    }
                }

                while (true) {
                    logger.i("request user")
                    val user = auth.currentUser
                    if (user != null) {
                        logger.i("user received: $user")
                        firebaseUser = user
                        break
                    }
                    if (!lock) {
                        logger.i("user not received")
                        break
                    }
                    delay(step)
                }
            }
            return firebaseUser
        } catch (e: Exception) {
            println(e.stackTrace)
            return null
        }
    }

    fun close() {
        auth.removeAuthStateListener(authStateListener)
    }

    suspend fun hasAccount(email: String): Boolean {
        val result = auth.fetchSignInMethodsForEmail(email).await()
        val hasAccount = result.signInMethods?.any {
            it == EmailAuthProvider.EMAIL_PASSWORD_SIGN_IN_METHOD
        }
        return hasAccount ?: false
    }

    suspend fun authWithEmailPass(email: String, pass: String) {
        val credential = EmailAuthProvider.getCredential(email, pass)
        val userWithThatCredentials = auth.currentUser?.linkWithCredential(credential)

        if (userWithThatCredentials == null) {
            auth.signInWithEmailAndPassword(email, pass).await()
        }
    }

    fun logout() {
        auth.signOut()
    }

    suspend fun resetPasswordByEmail(email: String) {
        auth.sendPasswordResetEmail(email).await()
    }

    suspend fun sendEmailVerifyMessage() {
        auth.currentUser?.sendEmailVerification()?.await()
    }

    suspend fun registerByEmailPass(email: String, pass: String): AuthUser? {
        return auth.createUserWithEmailAndPassword(email, pass).await().user.toAuthUser()
    }

    suspend fun reloadUser() {
        auth.currentUser?.reload()?.await()
        _currentUser.tryEmit(auth.currentUser)
    }

    suspend fun updateEmail(newEmail: String, pass: String) {
        val email = auth.currentUser?.providerData?.first()?.email
        require(email != null)

        val credentials = EmailAuthProvider.getCredential(email, pass)

        auth.currentUser?.reauthenticate(credentials)?.await()
        auth.currentUser?.updateEmail(newEmail)?.await()
        reloadUser()
    }

    suspend fun deleteUser() {
        auth.currentUser?.delete()?.await()
    }

    suspend fun tryPassword(password: String) {
        val email = auth.currentUser?.providerData?.first()?.email
        require(email != null)

        auth.signInWithEmailAndPassword(email, password).await()
    }

    suspend fun updatePassword(oldPassword: String, newPassword: String) {
        val email = auth.currentUser?.providerData?.first()?.email
        require(email != null)

        val credential = EmailAuthProvider.getCredential(email, oldPassword)

        auth.currentUser?.reauthenticate(credential)?.await()
        auth.currentUser?.updatePassword(newPassword)?.await()
    }

    suspend fun signInByGoogleSSO(firebaseCredential: AuthCredential) {
        val authUser = auth.currentUser?.toAuthUser()
        val isUserHasOnlyGoogle = (authUser?.authLoginTypes?.first() as? LoginType.SSO)
            ?.ssoType is SSOType.Google // to reauthenticate

        val userWithSuchCredentials = if (isUserHasOnlyGoogle) {
            null
        } else {
            auth.currentUser?.linkWithCredential(firebaseCredential)?.await()
        }

        if (userWithSuchCredentials == null) {
            auth.signInWithCredential(firebaseCredential).await()
        }
    }

    suspend fun signInByOtherSSO(firebaseToken: String) {
        auth.signInWithCustomToken(firebaseToken).await()
    }
}