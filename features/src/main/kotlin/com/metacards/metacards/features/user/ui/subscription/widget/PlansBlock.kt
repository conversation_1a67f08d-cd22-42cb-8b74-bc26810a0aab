package com.metacards.metacards.features.user.ui.subscription.widget

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.user.domain.SubscriptionType
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.utils.formatWithPattern
import com.metacards.metacards.features.R
import com.metacards.metacards.features.user.ui.subscription.SubscriptionComponent
import com.metacards.metacards.features.user.ui.subscription.subscription_bottom_sheet.widgets.SubscriptionPlan
import com.metacards.metacards.features.user.ui.subscription.subscription_bottom_sheet.widgets.SubscriptionWithDiscountPlan
import dev.icerock.moko.resources.StringResource
import dev.icerock.moko.resources.desc.ResourceFormattedStringDesc
import dev.icerock.moko.resources.desc.strResDesc

private const val DATE_FORMAT_PATTERN = "dd.MM.yyyy"

@Composable
fun PlansBlock(
    component: SubscriptionComponent,
    modifier: Modifier = Modifier
) {
    val userSubscriptionState by component.baseSubscriptionComponent.userSubscriptionState.collectAsState()
    val isTrialAvailable by component.baseSubscriptionComponent.isTrialAvailable.collectAsState()
    val tariffs by component.baseSubscriptionComponent.tariffs.collectAsState()

    Column(modifier = modifier.fillMaxWidth()) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp)
        ) {
            Text(
                text = R.string.account_subscription_plans_header.strResDesc().localizedByLocal(),
                color = CustomTheme.colors.text.primary,
                style = CustomTheme.typography.heading.secondary,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            Text(
                text = when (userSubscriptionState) {
                    is User.SubscriptionState.None -> {
                        if (isTrialAvailable) {
                            R.string.account_subscription_plan_title_trial.strResDesc()
                                .localizedByLocal()
                        } else {
                            R.string.account_subscription_plan_title_payment.strResDesc()
                                .localizedByLocal()
                        }
                    }

                    is User.SubscriptionState.Ongoing -> {
                        val resource =
                            StringResource(R.string.account_subscription_plan_title_payment_changed)
                        val date = (userSubscriptionState as User.SubscriptionState.Ongoing)
                            .expirationDate.formatWithPattern(DATE_FORMAT_PATTERN)
                        ResourceFormattedStringDesc(
                            resource,
                            listOf(date)
                        )
                            .localizedByLocal()
                    }
                },
                color = CustomTheme.colors.text.secondary,
                style = CustomTheme.typography.body.primary
            )

            val subscriptionState = userSubscriptionState
            if (
                subscriptionState is User.SubscriptionState.Ongoing &&
                subscriptionState.subscriptionType != SubscriptionType.SUBSCRIPTION_PLAN_PROMO
            ) {
                Text(
                    text = R.string.account_subscription_plan_cancel.strResDesc()
                        .localizedByLocal(),
                    color = CustomTheme.colors.text.secondary,
                    style = CustomTheme.typography.body.secondaryUnderline,
                    modifier = Modifier.clickable(onClick = component::onCancelClick)
                )
            }
        }

        tariffs.data?.forEach { tariff ->
            val leadingCaptionText = when (tariff.type) {
                SubscriptionType.SUBSCRIPTION_PLAN_YEAR -> {
                    if (userSubscriptionState is User.SubscriptionState.None && tariff.hasTrial) {
                        R.string.account_subscription_plan_seven_days_off.strResDesc()
                            .localizedByLocal()
                    } else {
                        if (tariff.saving > 0) {
                            ResourceFormattedStringDesc(
                                StringResource(R.string.account_subscription_plan_caption_economy),
                                listOf(tariff.saving)
                            ).localizedByLocal()
                        } else {
                            null
                        }
                    }
                }

                SubscriptionType.SUBSCRIPTION_PLAN_HALF_YEAR -> {
                    if (userSubscriptionState is User.SubscriptionState.None && tariff.hasTrial
                    ) {
                        R.string.account_subscription_plan_seven_days_off.strResDesc()
                            .localizedByLocal()
                    } else {
                        if (tariff.saving > 0) {
                            ResourceFormattedStringDesc(
                                StringResource(R.string.account_subscription_plan_caption_economy),
                                listOf(tariff.saving)
                            ).localizedByLocal()
                        } else {
                            null
                        }
                    }
                }

                else -> {
                    if (userSubscriptionState is User.SubscriptionState.None && tariff.hasTrial) {
                        R.string.account_subscription_plan_seven_days_off.strResDesc()
                            .localizedByLocal()
                    } else {
                        if (tariff.saving > 0) {
                            ResourceFormattedStringDesc(
                                StringResource(R.string.account_subscription_plan_caption_economy),
                                listOf(tariff.saving)
                            ).localizedByLocal()
                        } else {
                            null
                        }
                    }
                }
            }

            val monthPaymentString = if (tariff.pricePerMonth != null) {
                ResourceFormattedStringDesc(
                    StringResource(R.string.subscription_per_month),
                    listOf("${tariff.pricePerMonth}")
                ).localizedByLocal()
            } else {
                null
            }

            if (tariff.hasDiscount) {
                SubscriptionWithDiscountPlan(
                    id = tariff.type,
                    titleText = tariff.title,
                    priceWithDiscountString = tariff.priceWithDiscount.toDecimalString(),
                    basePriceString = tariff.price.toDecimalString(),
                    monthPaymentString = monthPaymentString,
                    selectedSubscriptionTypeFlow = component.baseSubscriptionComponent.selectedSubscriptionType,
                    onSelect = component.baseSubscriptionComponent::onSubscriptionTypeSelect,
                    discountPercent = tariff.discountPercent
                )
            } else {
                SubscriptionPlan(
                    id = tariff.type,
                    titleText = tariff.title,
                    fullPaymentString = tariff.price.toDecimalString(),
                    selectedSubscriptionTypeFlow = component.baseSubscriptionComponent.selectedSubscriptionType,
                    onSelect = component.baseSubscriptionComponent::onSubscriptionTypeSelect,
                    monthPaymentString = monthPaymentString,
                    leadingCaptionText = leadingCaptionText,
                )
            }
        }
    }
}