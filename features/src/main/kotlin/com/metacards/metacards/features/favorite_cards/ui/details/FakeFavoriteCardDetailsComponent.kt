package com.metacards.metacards.features.favorite_cards.ui.details

import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.features.favorite_cards.domain.FavoriteCardInfo
import com.metacards.metacards.features.record.domain.RecordId
import kotlinx.coroutines.flow.MutableStateFlow

class FakeFavoriteCardDetailsComponent : FavoriteCardDetailsComponent {

    override val cardInfoState = MutableStateFlow(LoadableState(data = FavoriteCardInfo.MOCK))
    override val isShareLoading = MutableStateFlow(false)

    override fun onFavoriteClick() = Unit
    override fun onToggleShare() = Unit
    override fun onRecordClick(recordId: RecordId) = Unit
}