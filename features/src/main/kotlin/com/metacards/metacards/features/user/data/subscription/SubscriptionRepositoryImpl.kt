package com.metacards.metacards.features.user.data.subscription

import com.metacards.metacards.core.user.domain.SubscriptionTariff
import com.metacards.metacards.core.user.domain.SubscriptionType
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.mapLoadableSuspend
import com.metacards.metacards.features.payments.domain.BillingService
import com.metacards.metacards.features.user.data.subscription.dto.toDomain
import com.metacards.metacards.features.user.domain.subscription.repository.SubscriptionRepository
import kotlinx.coroutines.flow.Flow
import kotlin.math.roundToInt

class SubscriptionRepositoryImpl(
    private val subscriptionsDataSource: SubscriptionsDataSource,
    private val billingService: BillingService,
) : SubscriptionRepository {
    override fun getTariffs(): Flow<LoadableState<List<SubscriptionTariff>>> {
        return subscriptionsDataSource.getSubscriptionTariffsFlow()
            .mapLoadableSuspend {
                val domain = it?.map { subscriptionTariffDto -> subscriptionTariffDto.toDomain() }
                mergeTariffs(domain ?: emptyList())
            }
    }

    private suspend fun mergeTariffs(tariffs: List<SubscriptionTariff>): List<SubscriptionTariff> {
        val list = mutableListOf<SubscriptionTariff>()
        tariffs.forEach { tariff ->
            val subscriptions = billingService.fetchSubscriptions(tariff.storeId, tariff.promotionalOfferId)
            val mergedTariffs = subscriptions.map { plan ->
                val type = SubscriptionType.fromStoreId(plan.id) ?: return@map null
                val perMonthBasePrice = if (plan.discountsAvailable) plan.priceWithDiscount else plan.price
                SubscriptionTariff(
                    id = tariff.id,
                    offerId = plan.promotionalOfferId,
                    order = type.order(),
                    type = type,
                    title = type.displayName(),
                    price = plan.price,
                    priceWithDiscount = plan.priceWithDiscount,
                    saving = 0,
                    discountPercent = (100 - (plan.priceWithDiscount.value / plan.price.value * 100)).roundToInt(),
                    pricePerMonth = type.pricePerMonth(perMonthBasePrice),
                    storeId = plan.id,
                    hasTrial = plan.hasTrial,
                    hasDiscount = plan.discountsAvailable
                )
            }.filterNotNull()
            list += mergedTariffs
        }
        var result: List<SubscriptionTariff> = list
        result.firstOrNull { it.type == SubscriptionType.SUBSCRIPTION_PLAN_THREE_MONTH }
            ?.let { tariff ->
                val price = tariff.pricePerMonth?.value ?: 0.0
                result = result.map { it.updatedSavings(price) }
            }
        return result.sortedBy(SubscriptionTariff::order)
    }
}
