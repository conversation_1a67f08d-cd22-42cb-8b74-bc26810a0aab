package com.metacards.metacards.features.course.ui.lesson

import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.localization.ui.LocalizableString
import kotlinx.coroutines.flow.StateFlow

interface CourseLessonComponent {

    val courseName: LocalizableString?
    val cards: StateFlow<CourseLessonCards?>
    val closeDialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent>
    val lastCardButtonText: String

    fun onToNextContentClick()
    fun onVideoFullScreenClick(position: Long, videoUrl: String)
    fun onCloseClick()

    sealed interface Output {
        data class NextContentRequested(val order: Int) : Output
        data object CloseRequested : Output
    }
}