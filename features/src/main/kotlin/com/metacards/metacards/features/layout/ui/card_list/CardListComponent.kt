package com.metacards.metacards.features.layout.ui.card_list

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardHintWithExpanded
import com.metacards.metacards.features.deck.domain.entity.CardWithComment
import com.metacards.metacards.features.deck.domain.entity.CardWithFavoriteAndComment
import com.metacards.metacards.features.layout.domain.CardListViewData
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import kotlinx.coroutines.flow.StateFlow

interface CardListComponent {
    val canSelectMoreCard: StateFlow<CanSelectCard>
    val selectedCards: StateFlow<List<CardWithFavoriteAndComment>>
    val cardHints: StateFlow<List<CardHintWithExpanded>?>
    val pageCount: StateFlow<Int>
    val cardListViewData: CardListViewData
    val isShareLoading: StateFlow<Boolean>
    val tutorialMessage: StateFlow<TutorialMessage?>

    fun onToggleFavorite(cardIndex: Int, isOn: Boolean)
    fun onToggleShare(cardUrl: String, cardIndex: Int)
    fun onMoreCardClick()
    fun onFinishClick()
    fun onAddEntryClick()
    fun onCardClick(card: Card)
    fun onCardCommentValueChanged(cardIndex: Int, value: String)
    fun onCloseClick()
    fun onHintExpand(page: Int, isExpanded: Boolean)

    enum class CanSelectCard {
        Can, CanLast, Cant
    }

    sealed interface Output {
        data object CloseRequested : Output
        data class OnFinished(val cardsWithComments: List<CardWithComment>) : Output
        data class OnFinishedDaily(
            val cardWithComment: CardWithComment,
            val question: LocalizableString
        ) : Output
        data class OnPredefinedFinished(
            val cardsWithComments: List<CardWithComment>,
            val isLastPredefined: Boolean
        ) : Output

        data class FullScreenCardRequested(val card: Card) : Output
        data object AuthSuggestingScreenRequested : Output
        data class PremiumSuggestingScreenRequested(val withAdv: Boolean = false) : Output
        data class DailyCardRecordRequested(val recordId: RecordId) : Output
    }
}
