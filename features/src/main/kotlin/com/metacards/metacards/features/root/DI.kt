package com.metacards.metacards.features.root

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.localization.data.AppLanguageUpdateListener
import com.metacards.metacards.core.utils.KoinConsts.UpdateUserLanguageAtFirebaseName
import com.metacards.metacards.features.root.data.UpdateUserLanguageAtFirebase
import com.metacards.metacards.features.root.ui.RealRootComponent
import com.metacards.metacards.features.root.ui.RootComponent
import org.koin.core.component.get
import org.koin.core.qualifier.named
import org.koin.dsl.module

val rootModule = module {
    factory<AppLanguageUpdateListener>(named(UpdateUserLanguageAtFirebaseName)) {
        UpdateUserLanguageAtFirebase(get())
    }
}

fun ComponentFactory.createRootComponent(
    componentContext: ComponentContext,
    isActivityRecreated: Boolean
): RootComponent {
    return RealRootComponent(
        componentContext,
        isActivityRecreated,
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(named(UpdateUserLanguageAtFirebaseName)),
        get(),
        get(),
    )
}
