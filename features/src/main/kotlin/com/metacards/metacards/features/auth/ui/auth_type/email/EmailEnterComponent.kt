package com.metacards.metacards.features.auth.ui.auth_type.email

import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.user.domain.Email
import kotlinx.coroutines.flow.StateFlow
import ru.mobileup.kmm_form_validation.control.InputControl

interface EmailEnterComponent {
    val emailInputControl: InputControl
    val showSignInButton: StateFlow<Boolean>
    val showSignUpButton: StateFlow<Boolean>
    val showCheckbox: StateFlow<Boolean>
    val checkboxState: StateFlow<Boolean>
    val nextButtonState: StateFlow<ButtonState>

    fun onNextButtonClick()
    fun onSignUpButtonClick()
    fun onSignInButtonClick()
    fun onCheckboxStateChange(checked: Boolean)
    fun openLink(url: String)
    fun onGoBackClick()
    fun onFocusRequest()

    sealed interface Output {
        data class NextButtonPressed(val email: Email) : Output
        data class WebViewRequested(val url: String) : Output
        data object SignUpRequested : Output
        data object SignInRequested : Output
        data object GoBackRequested : Output
    }
}