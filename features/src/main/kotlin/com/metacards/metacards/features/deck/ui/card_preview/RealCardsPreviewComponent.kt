package com.metacards.metacards.features.deck.ui.card_preview

import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.backhandler.BackCallback
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.createDefaultDialogComponent
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.dialogControl
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.sharing.SharingManager
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.withProgress
import com.metacards.metacards.features.deck.domain.entity.Card
import com.metacards.metacards.features.deck.domain.entity.CardId
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn

class RealCardsPreviewComponent(
    componentContext: ComponentContext,
    override val cards: List<Card>,
    override val shouldShowGif: Boolean,
    initialPosition: Int,
    private val onOutput: (CardsPreviewComponent.Output) -> Unit,
    componentFactory: ComponentFactory,
    private val errorHandler: ErrorHandler,
    private val sharingManager: SharingManager,
) : ComponentContext by componentContext, CardsPreviewComponent {

    private val backCallback = BackCallback {
        onCloseButtonClick()
    }

    override val currentCardIndex: MutableStateFlow<Int> = MutableStateFlow(initialPosition)

    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        dialogControl(dialogComponentFactory = { config, componentContext, dialogControl ->
            componentFactory.createDefaultDialogComponent(
                componentContext,
                config.data,
                dialogControl::dismiss
            )
        })

    override val isShareLoading = MutableStateFlow(false)

    override val isToggleShareEnable = currentCardIndex.map {
        cards.getOrNull(it)?.id != CardId.LOCKED_CARD_ID
    }.stateIn(componentScope, SharingStarted.Eagerly, false)

    init {
        backHandler.register(backCallback)
    }

    override fun onToggleShare() {
        componentScope.safeLaunch(errorHandler) {
            withProgress(isShareLoading) {
                cards.getOrNull(currentCardIndex.value)?.imageUrl?.let {
                    sharingManager.shareCard(cardUrl = it, isDailyCard = false)
                }
            }
        }
    }

    override fun onCloseButtonClick() {
        onOutput(CardsPreviewComponent.Output.CloseRequested(currentCardIndex.value))
    }

    override fun onPageSelected(index: Int) {
        currentCardIndex.value = index
    }
}
