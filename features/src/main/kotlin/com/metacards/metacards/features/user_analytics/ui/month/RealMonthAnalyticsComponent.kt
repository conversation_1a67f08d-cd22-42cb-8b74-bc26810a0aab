package com.metacards.metacards.features.user_analytics.ui.month

import android.os.Build
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.doOnStart
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.paged_loading.PagedLoading
import com.metacards.metacards.core.paged_loading.handleErrors
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.PagedData
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.toLoadableState
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.user.domain.GetUserSubscriptionStateInteractor
import com.metacards.metacards.features.user_analytics.domain.month.MonthAnalyticsInfo
import com.metacards.metacards.features.user_analytics.domain.month.MonthAnalyticsPagedLoading
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn

class RealMonthAnalyticsComponent(
    componentContext: ComponentContext,
    private val monthAnalyticsLoading: MonthAnalyticsPagedLoading,
    private val errorHandler: ErrorHandler,
    private val analyticsService: AnalyticsService,
    getUserSubscriptionStateInteractor: GetUserSubscriptionStateInteractor,
    private val onOutput: (MonthAnalyticsComponent.Output) -> Unit
) : ComponentContext by componentContext, MonthAnalyticsComponent {

    override val isPremiumUser = getUserSubscriptionStateInteractor.execute().map {
        it is User.SubscriptionState.Ongoing
    }.stateIn(componentScope, SharingStarted.Eagerly, false)

    override val analyticsState: StateFlow<LoadableState<PagedData<MonthAnalyticsInfo>>> =
        computed(monthAnalyticsLoading.stateFlow, isPremiumUser) { monthAnalytics, isPremiumUser ->
            val data = if (!isPremiumUser && (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S)) {
                PagedLoading.State.mock(
                    MonthAnalyticsInfo.LIST_MOCK
                )
            } else {
                monthAnalytics
            }
            data.toLoadableState()
        }

    init {
        monthAnalyticsLoading.handleErrors(componentScope) {
            errorHandler.handleError(it.exception)
        }

        lifecycle.doOnStart {
            val pagingState = monthAnalyticsLoading.stateFlow.value
            if (pagingState.data == null && pagingState.loadingStatus == PagedLoading.LoadingStatus.None) {
                monthAnalyticsLoading.loadFirstPage()
            }
        }
    }

    override fun onLoadMore() {
        monthAnalyticsLoading.loadNext()
    }

    override fun onMonthPrev() {
        analyticsService.logEvent(AnalyticsEvent.AnalyticsMonthPrevEvent)
    }

    override fun onMonthNext() {
        analyticsService.logEvent(AnalyticsEvent.AnalyticsMonthNextEvent)
    }

    override fun onRecordClick(recordId: RecordId) {
        analyticsService.logEvent(AnalyticsEvent.AnalyticsMonthRecordTapEvent)
        onOutput(MonthAnalyticsComponent.Output.RecordDetailsRequested(recordId))
    }
}