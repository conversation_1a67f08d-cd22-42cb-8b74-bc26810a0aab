package com.metacards.metacards.features.auth.ui.auth_type.email

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.MetaCheckbox
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.MetaClickableText
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.text_field.DefaultTextFieldFooterError
import com.metacards.metacards.core.widget.text_field.MetaTextField
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.delay

private const val TERMS_FIRST_LINK_TAG = "TERMS_FIRST_LINK_TAG"
private const val TERMS_SECOND_LINK_TAG = "TERMS_SECOND_LINK_TAG"

@Composable
fun EmailEnterUi(
    component: EmailEnterComponent,
    isSignIn: Boolean,
    nextButtonText: String,
) {
    val showSignInButton by component.showSignInButton.collectAsState()
    val showSignUpButton by component.showSignUpButton.collectAsState()
    val showCheckbox by component.showCheckbox.collectAsState()
    val checkboxState by component.checkboxState.collectAsState()
    val nextButtonState by component.nextButtonState.collectAsState()
    val focusManager = LocalFocusManager.current

    val onClick = if (isSignIn) component::onSignUpButtonClick else component::onSignInButtonClick

    // Workaround to fix a weird bug on Samsung devices where pasting text via the keyboard
    // makes it impossible to delete the pasted content without this delay
    LaunchedEffect(Unit) {
        delay(350)
        component.onFocusRequest()
    }

    BackHandler {
        focusManager.clearFocus(true)
        component.onGoBackClick()
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(bottom = 24.dp, top = 8.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceBetween
    ) {
        MetaTextField(
            placeholder = R.string.email_enter_placeholder.strResDesc().localizedByLocal(),
            inputControl = component.emailInputControl,
            footerError = {
                Column {
                    DefaultTextFieldFooterError(errorMessage = it)

                    when {
                        showSignInButton ->
                            Text(
                                modifier = Modifier
                                    .padding(start = 16.dp)
                                    .clickable(onClick = onClick),
                                text = R.string.email_enter_signin.strResDesc().localizedByLocal(),
                                color = CustomTheme.colors.text.caption,
                                style = CustomTheme.typography.caption.small,
                            )

                        showSignUpButton -> Text(
                            modifier = Modifier
                                .padding(start = 16.dp)
                                .clickable(onClick = onClick),
                            text = R.string.email_enter_signup.strResDesc().localizedByLocal(),
                            color = CustomTheme.colors.text.caption,
                            style = CustomTheme.typography.caption.small,
                        )
                    }
                }
            }
        )

        Column(
            modifier = Modifier,
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            if (showCheckbox) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.Top,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {

                    MetaCheckbox(checkboxState, component::onCheckboxStateChange)

                    MetaClickableText(
                        modifier = Modifier
                            .padding(start = 8.dp)
                            .weight(1f),
                        textStyle = CustomTheme.typography.caption.medium.copy(color = CustomTheme.colors.text.secondary),
                        text = R.string.email_enter_terms.strResDesc().localizedByLocal(),
                        annotations = listOf(
                            Pair(
                                TERMS_FIRST_LINK_TAG,
                                R.string.email_enter_terms_link_first
                                    .strResDesc()
                                    .localizedByLocal()
                            ),
                            Pair(
                                TERMS_SECOND_LINK_TAG,
                                R.string.email_enter_terms_link_second
                                    .strResDesc()
                                    .localizedByLocal()
                            )
                        ),
                        onTextClick = {
                            component.openLink(it)
                        }
                    )
                }
            }

            MetaAccentButton(
                modifier = Modifier.fillMaxWidth(),
                text = nextButtonText,
                onClick = component::onNextButtonClick,
                state = nextButtonState
            )
        }
    }
}

@Preview
@Composable
fun EmailEnterUiPreview() {
    val scope = rememberCoroutineScope()

    AppTheme {
        EmailEnterUi(
            component = FakeEmailEnterComponent(scope),
            isSignIn = false,
            nextButtonText = R.string.email_enter_next_button.strResDesc().localizedByLocal()
        )
    }
}