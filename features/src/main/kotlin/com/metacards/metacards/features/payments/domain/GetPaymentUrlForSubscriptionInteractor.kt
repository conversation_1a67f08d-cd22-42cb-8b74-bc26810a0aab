package com.metacards.metacards.features.payments.domain

import com.metacards.metacards.core.error_handling.ServerException
import java.net.URL

class GetPaymentUrlForSubscriptionInteractor(private val repository: PaymentRepository) {
    suspend fun execute(subscriptionId: String): URL? {
        val result = repository.makePaymentForSubscription(subscriptionId)
        if (result.isError) throw ServerException(null, result.data ?: result.toString())
        return result.data?.let { URL(it) }
    }
}