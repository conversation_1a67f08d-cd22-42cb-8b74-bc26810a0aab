package com.metacards.metacards.features.deck.domain.entity

import android.os.Parcelable
import com.metacards.metacards.core.localization.ui.LocalizableString
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.Serializable

@JvmInline
@Parcelize
@Serializable
value class DeckId(val value: String) : Parcelable

data class DeckWithAvailable(
    val deck: Deck,
    val available: <PERSON>olean,
    val isSpecialDeckCompleted: Boolean
) {
    companion object {
        val MOCK = DeckWithAvailable(
            deck = Deck.MOCK,
            available = true,
            isSpecialDeckCompleted = false
        )

        val LIST_MOCK = listOf(
            MOCK,
            MOCK,
            MOCK,
            MOCK
        )
    }
}

fun DeckId.isDeckAvailable(
    deckAvailability: Deck.Availability,
    hasSubscription: Boolean,
    purchased: List<DeckId>
): Boolean {
    return when {
        deckAvailability == Deck.Availability.FREE -> true
        purchased.find { it == this } != null -> true
        deckAvailability == Deck.Availability.SUB && hasSubscription -> true
        else -> false
    }
}

data class Deck(
    val id: DeckId,
    val name: LocalizableString,
    val availability: Availability,
    val coverUrl: String,
    val isSpecial: Boolean = false,
) {

    override fun equals(other: Any?): Boolean {
        return this === other || (other is Deck && id == other.id)
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }

    companion object {
        val MOCK = Deck(
            DeckId("mock"),
            availability = Availability.FREE,
            coverUrl = "",
            name = LocalizableString.createNonLocalizable("Title deck")
        )

        val LIST_MOCK = listOf(
            MOCK,
            MOCK,
            MOCK,
            MOCK
        )

        fun List<Deck>.asDeckWithAvailable(
            hasSubscription: Boolean,
            purchased: List<Deck>
        ): List<DeckWithAvailable> {
            return map { deck ->
                val available =
                    deck.id.isDeckAvailable(deck.availability, hasSubscription, purchased.map { it.id })

                DeckWithAvailable(deck, available, false)
            }
        }
    }

    enum class Availability {
        FREE, SUB, BUY
    }
}

fun getPurchasedDecks(
    purchasedDecksId: List<DeckId?>,
    forPurchaseDecks: List<Deck>
): List<Deck> {
    return forPurchaseDecks.filter { deck -> purchasedDecksId.find { it == deck.id } != null }
}

fun getForPurchaseDecks(purchased: List<Deck>, forPurchase: List<Deck>): List<Deck> {
    return forPurchase - purchased.toSet()
}