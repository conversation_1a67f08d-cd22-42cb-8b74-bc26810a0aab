package com.metacards.metacards.features.user.ui.subscription.subscription_bottom_sheet.widgets

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.user.domain.SubscriptionType
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.StateFlow

@Composable
fun SubscriptionPlan(
    id: SubscriptionType,
    titleText: StringDesc,
    fullPaymentString: String,
    selectedSubscriptionTypeFlow: StateFlow<SubscriptionType>,
    onSelect: (SubscriptionType) -> Unit,
    modifier: Modifier = Modifier,
    monthPaymentString: String? = null,
    leadingCaptionText: String? = null
) {
    val isSelected = id == selectedSubscriptionTypeFlow.collectAsState().value

    Card(
        shape = RoundedCornerShape(16.dp),
        elevation = 0.dp,
        backgroundColor = CustomTheme.colors.background.primary,
        modifier = modifier
            .padding(bottom = 8.dp)
            .clip(RoundedCornerShape(16.dp))
            .clickable { onSelect(id) }
    ) {
        Box(
            modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth()
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.align(Alignment.CenterStart)
            ) {
                Icon(
                    painter = painterResource(
                        id = if (isSelected) {
                            R.drawable.ic_24_radio_button_on
                        } else {
                            R.drawable.ic_24_radio_button_off
                        }
                    ),
                    contentDescription = null,
                    tint = CustomTheme.colors.icons.primary,
                    modifier = Modifier.padding(end = 12.dp)
                )

                Column {
                    Text(
                        text = titleText.localizedByLocal(),
                        style = CustomTheme.typography.heading.small,
                        color = CustomTheme.colors.text.primary
                    )

                    leadingCaptionText?.let { text ->
                        Surface(
                            shape = RoundedCornerShape(4.dp),
                            color = CustomTheme.colors.button.accent,
                            modifier = Modifier.padding(top = 2.dp)
                        ) {
                            Text(
                                text = text,
                                color = CustomTheme.colors.text.caption,
                                style = CustomTheme.typography.caption.bigSemiBold
                            )
                        }
                    }
                }
            }

            Column(
                horizontalAlignment = Alignment.End,
                modifier = Modifier.align(Alignment.CenterEnd)
            ) {
                Text(
                    text = fullPaymentString,
                    style = CustomTheme.typography.heading.small,
                    color = CustomTheme.colors.text.primary
                )

                monthPaymentString?.let {
                    Text(
                        text = it,
                        style = CustomTheme.typography.body.primary,
                        color = CustomTheme.colors.text.secondary
                    )
                }
            }
        }
    }
}