package com.metacards.metacards.features.layout.ui.predefined_layout

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.adv.domain.YandexAdvHelper
import com.metacards.metacards.core.auth_suggestion_dialog.createAuthSuggestion
import com.metacards.metacards.core.createDefaultDialogComponent
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.model.DialogData
import com.metacards.metacards.core.dialog.dialogControl
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.localization.data.AppLanguageService
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.mapData
import com.metacards.metacards.core.utils.stateIn
import com.metacards.metacards.features.R
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.layout.domain.CardSource
import com.metacards.metacards.features.layout.domain.GetPredefinedLayoutGroupsInteractor
import com.metacards.metacards.features.layout.domain.LayoutId
import com.metacards.metacards.features.layout.domain.PredefinedLayoutGroupWithAvailable
import com.metacards.metacards.features.layout.domain.PredefinedLayoutWithAvailable
import com.metacards.metacards.features.user.domain.UserRepository
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch

class RealPredefinedLayoutComponent(
    componentContext: ComponentContext,
    componentFactory: ComponentFactory,
    userRepository: UserRepository,
    getPredefinedLayoutGroupsInteractor: GetPredefinedLayoutGroupsInteractor,
    private val analyticsService: AnalyticsService,
    private val appLanguageService: AppLanguageService,
    private val yandexAdvHelper: YandexAdvHelper,
    private val errorHandler: ErrorHandler,
    private val onOutput: (PredefinedLayoutComponent.Output) -> Unit,
) : ComponentContext by componentContext, PredefinedLayoutComponent {

    private val layoutGroups = getPredefinedLayoutGroupsInteractor
        .execute()
        .catch { e -> errorHandler.handleError(e as Exception) }
        .stateIn(this, LoadableState(loading = true))

    override val groupNames: StateFlow<List<StringDesc>> = computed(layoutGroups) {
        val names = it.data?.map(PredefinedLayoutGroupWithAvailable::name) ?: listOf()
        listOf(R.string.predefined_layout_tab_all.strResDesc()) + names
    }

    override val currentGroupIndex = MutableStateFlow(0)

    override val filteredLayoutGroups = computed(
        layoutGroups,
        currentGroupIndex
    ) { layoutGroups, index ->
        layoutGroups.mapData {
            if (index == 0) it else it?.filter { it.name == groupNames.value[index] } ?: emptyList()
        }
    }

    private val user = userRepository.user

    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        dialogControl(dialogComponentFactory = { config, componentContext, dialogControl ->
            componentFactory.createDefaultDialogComponent(
                componentContext,
                config.data,
                dialogControl::dismiss
            )
        })

    override fun onGroupClick(index: Int) {
        currentGroupIndex.value = index
    }

    override fun onLayoutClick(layoutId: LayoutId) {
        filteredLayoutGroups.value.data?.forEach { group ->
            group.layouts.find { it.layout.id == layoutId }?.let { layout ->
                if (user.value?.subscriptionState?.canLayout == false) {
                    subscribeForAdvReward(layout)
                    onOutput(
                        PredefinedLayoutComponent.Output.SubscriptionBottomsheetRequested(
                            withAdv = true
                        )
                    )
                } else {
                    analyticsService.logEvent(
                        AnalyticsEvent.LayoutChooseEvent(
                            layoutId = layoutId.value,
                            layoutName = layout.layout.name.toString(appLanguageService.getLanguage())
                        )
                    )
                    onOutput(
                        PredefinedLayoutComponent.Output.LayoutDetailsRequested(
                            CardSource.Questions(layout.layout.questions)
                        )
                    )
                }
            }
        }
    }

    override fun onBlockContentClick() {
        if (user.value == null) {
            dialogControl.show(
                DefaultDialogComponent.Config(
                    DialogData.createAuthSuggestion(
                        cancelAction = {
                            dialogControl.dismiss()
                        },
                        acceptAction = {
                            analyticsService.logEvent(AnalyticsEvent.AccountAuthEvent)
                            onOutput(PredefinedLayoutComponent.Output.AuthScreenRequested)
                        }
                    )
                )
            )
        } else {
            onOutput(
                PredefinedLayoutComponent.Output.SubscriptionBottomsheetRequested(
                    withAdv = false
                )
            )
        }
    }

    private fun subscribeForAdvReward(layoutWithAvailable: PredefinedLayoutWithAvailable) {
        yandexAdvHelper.subscribeForReward {
            analyticsService.logEvent(
                AnalyticsEvent.LayoutChooseEvent(
                    layoutId = layoutWithAvailable.layout.id.value,
                    layoutName = layoutWithAvailable.layout.name.toString(
                        appLanguageService.getLanguage()
                    )
                )
            )
            onOutput(
                PredefinedLayoutComponent.Output.LayoutDetailsRequested(
                    CardSource.Questions(layoutWithAvailable.layout.questions)
                )
            )
        }
    }
}
