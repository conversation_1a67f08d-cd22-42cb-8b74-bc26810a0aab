package com.metacards.metacards.features.payments.ui.bottom_sheet

import com.metacards.metacards.core.button.ButtonState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakePaymentBottomSheetComponent : PaymentBottomSheetComponent {

    override val buttonState: StateFlow<ButtonState> = MutableStateFlow(ButtonState.Enabled)
    override fun onWebStoreClick() = Unit
    override fun onDismiss() = Unit
}