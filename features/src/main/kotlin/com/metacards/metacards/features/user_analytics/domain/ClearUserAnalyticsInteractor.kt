package com.metacards.metacards.features.user_analytics.domain

import com.metacards.metacards.features.record.domain.RecordsPagedLoading
import com.metacards.metacards.features.user_analytics.domain.month.MonthAnalyticsPagedLoading
import com.metacards.metacards.features.user_analytics.domain.week.WeekAnalyticsPagedLoading

class ClearUserAnalyticsInteractor(
    private val weekAnalyticsPagedLoading: WeekAnalyticsPagedLoading,
    private val monthAnalyticsPagedLoading: MonthAnalyticsPagedLoading,
    private val layoutAnalyticsPagedLoading: RecordsPagedLoading
) {
    fun execute() {
        weekAnalyticsPagedLoading.cancel(clear = true)
        monthAnalyticsPagedLoading.cancel(clear = true)
        layoutAnalyticsPagedLoading.cancel(clear = true)
    }
}