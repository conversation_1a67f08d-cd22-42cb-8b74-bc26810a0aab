package com.metacards.metacards.features.record.ui.create_record.add_card

import android.Manifest
import androidx.camera.core.ImageCapture
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.doOnCreate
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.permissions.PermissionService
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.record.domain.camera.CardPhoto
import com.metacards.metacards.features.record.domain.camera.TakePhotoInteractor
import com.metacards.metacards.features.record.ui.create_record.add_card.image_tracking.ImageTrackingComponent
import kotlinx.coroutines.flow.MutableStateFlow

private const val CAMERA_PERMISSION = Manifest.permission.CAMERA

class RealAddCardComponent(
    componentContext: ComponentContext,
    imageTrackingComponent: Lazy<ImageTrackingComponent>,
    private val takePhotoInteractor: TakePhotoInteractor,
    private val errorHandler: ErrorHandler,
    private val analyticsService: AnalyticsService,
    private val permissionService: PermissionService,
    private val onOutput: (AddCardComponent.Output) -> Unit
) : ComponentContext by componentContext, AddCardComponent {

    init {
        lifecycle.doOnCreate {
            if (!permissionService.isPermissionGranted(CAMERA_PERMISSION)) {
                onOutput(AddCardComponent.Output.PermissionCameraRequested)
            }
        }
    }

    override val currentState: MutableStateFlow<AddCardComponent.State> =
        MutableStateFlow(AddCardComponent.State.Camera)

    override val imageTrackingComponent by imageTrackingComponent

    override fun onCameraIconClick() {
        currentState.value = AddCardComponent.State.Camera
    }

    override fun onImageTrackingClick() {
        currentState.value = AddCardComponent.State.ImageTracking
    }

    override fun takePhoto(imageCapture: ImageCapture) {
        analyticsService.logEvent(AnalyticsEvent.ManualRecordPhotoTakeEvent)
        componentScope.safeLaunch(errorHandler) {
            takePhotoInteractor.execute(imageCapture) { cardPhoto ->
                onOutput(AddCardComponent.Output.ConfirmPhotoRequested(cardPhoto))
            }
        }
    }

    override fun onContinueClick() {
        val cardPhoto = CardPhoto(assetsPath = "models/512.png")
        onOutput(AddCardComponent.Output.ConfirmPhotoRequested(cardPhoto))
    }
}