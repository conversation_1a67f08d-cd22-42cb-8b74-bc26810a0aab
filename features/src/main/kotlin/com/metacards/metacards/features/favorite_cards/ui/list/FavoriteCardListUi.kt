package com.metacards.metacards.features.favorite_cards.ui.list

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.user.domain.FavoriteCard
import com.metacards.metacards.core.utils.navigationBarsPaddingDp
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.EmptyPlaceholder
import com.metacards.metacards.core.widget.SkeletonElement
import com.metacards.metacards.core.widget.button.MetaSecondaryButton
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.deck.ui.MetaCard
import com.metacards.metacards.features.deck.ui.MetaCardDefaults
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun FavoriteCardListUi(component: FavoriteCardListComponent, modifier: Modifier = Modifier) {

    val cards by component.cards.collectAsState()

    BoxWithFade(modifier.fillMaxSize(), listOfColors = CustomTheme.colors.gradient.backgroundList) {
        Column {
            TopNavigationBar(
                title = R.string.favorite_cards_title.strResDesc(),
                leadingIcon = { BackNavigationItem() }
            )

            if (cards.isEmpty()) {
                EmptyCardsPlaceholder(onGoToMainClick = component::onGoToMainClick)
            } else {
                CardsContent(cards = cards, onCardClick = component::onCardClick)
            }
        }
    }
}

@Composable
private fun CardsContent(
    cards: List<FavoriteCard>,
    onCardClick: (CardId) -> Unit,
    modifier: Modifier = Modifier,
) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(2),
        modifier = modifier,
        contentPadding = PaddingValues(
            start = 16.dp,
            end = 16.dp,
            top = 8.dp,
            bottom = 8.dp + navigationBarsPaddingDp
        ),
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        items(cards, key = { it.id }) { card ->
            MetaCard(
                modifier = Modifier.fillMaxWidth(),
                image = card.imageUrl,
                paddingValues = PaddingValues(0.dp),
                shape = RoundedCornerShape(16.dp),
                onCardClick = { onCardClick(CardId(card.id)) }
            )
        }
    }
}

@Composable
private fun CardsSkeleton(modifier: Modifier = Modifier) {
    LazyVerticalGrid(
        modifier = modifier,
        userScrollEnabled = false,
        columns = GridCells.Fixed(2),
        contentPadding = PaddingValues(start = 16.dp, end = 16.dp, top = 8.dp, bottom = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        items(6) {
            SkeletonElement(
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(MetaCardDefaults.aspectRatio, matchHeightConstraintsFirst = true),
                cornerRadius = 16.dp
            )
        }
    }
}

@Composable
private fun EmptyCardsPlaceholder(onGoToMainClick: () -> Unit, modifier: Modifier = Modifier) {
    EmptyPlaceholder(
        modifier = modifier,
        title = R.string.favorite_cards_empty_title.strResDesc().localizedByLocal(),
        message = R.string.favorite_cards_empty_message.strResDesc().localizedByLocal(),
        buttons = {
            MetaSecondaryButton(
                modifier = Modifier.fillMaxWidth(),
                text = R.string.favorite_cards_go_to_main_button.strResDesc().localizedByLocal(),
                onClick = onGoToMainClick
            )
        }
    )
}

@Preview(showSystemUi = true)
@Composable
fun FavoriteCardListUiPreview() {
    AppTheme {
        FavoriteCardListUi(component = FakeFavoriteCardListComponent())
    }
}