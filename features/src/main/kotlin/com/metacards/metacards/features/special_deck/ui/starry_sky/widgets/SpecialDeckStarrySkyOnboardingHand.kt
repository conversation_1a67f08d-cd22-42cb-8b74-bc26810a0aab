package com.metacards.metacards.features.special_deck.ui.starry_sky.widgets

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.features.R

private const val ONBOARDING_HAND_TRANSLATION_X_START = -150f
private const val ONBOARDING_HAND_TRANSLATION_X_END = 150f
private const val ONBOARDING_HAND_TRANSLATION_DURATION = 1000

@Composable
fun SpecialDeckStarrySkyOnboardingHand(
    isUserStoppedInteraction: Boolean,
    isLoading: Boolean,
    isDeckCompleted: Boolean,
    modifier: Modifier = Modifier
) {
    if (!isLoading && !isDeckCompleted) {
        Box(modifier = modifier) {
            AnimatedVisibility(
                visible = isUserStoppedInteraction,
                modifier = Modifier.align(Alignment.BottomCenter)
            ) {
                val infiniteTransition = rememberInfiniteTransition(label = "onboarding_hand")

                val onboardingHandTranslationX by infiniteTransition.animateFloat(
                    initialValue = ONBOARDING_HAND_TRANSLATION_X_START,
                    targetValue = ONBOARDING_HAND_TRANSLATION_X_END,
                    animationSpec = infiniteRepeatable(
                        animation = tween(
                            ONBOARDING_HAND_TRANSLATION_DURATION,
                            easing = LinearEasing
                        ),
                        repeatMode = RepeatMode.Reverse
                    ),
                    label = "onboardingHandTranslationXAnimation"
                )
                Icon(
                    painter = painterResource(id = R.drawable.ic_special_deck_onboarding_hand),
                    contentDescription = null,
                    tint = CustomTheme.colors.icons.primary,
                    modifier = Modifier
                        .padding(16.dp)
                        .graphicsLayer { translationX = onboardingHandTranslationX }
                )
            }
        }
    }
}