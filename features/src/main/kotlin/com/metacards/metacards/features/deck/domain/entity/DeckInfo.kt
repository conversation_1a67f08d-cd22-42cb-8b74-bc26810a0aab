package com.metacards.metacards.features.deck.domain.entity

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.price.Price
import java.net.URI

data class DeckInfoWithCards(
    val deckInfo: DeckInfo,
    val cards: List<Card>,
    val deckAvailable: Boolean,
    val cardCount: Int
) {
    companion object {
        val mock = DeckInfoWithCards(
            deckInfo = DeckInfo.mock,
            cards = Card.getMockList(),
            deckAvailable = true,
            cardCount = Card.getMockList().size
        )
    }
}

data class DeckInfo(
    val deckId: DeckId,
    val name: LocalizableString,
    val description: LocalizableString,
    val price: Price,
    val availability: Deck.Availability,
    val videoLessons: List<Lesson>,
    val shopUrl: URI?,
    val coverUrl: String,
    val storeId: String,
    val tags: List<LocalizableString>,
    val hasAR: Boolean
) {

    companion object {
        val mock = DeckInfo(
            deckId = DeckId("mock"),
            price = Price(100.0),
            shopUrl = URI(""),
            videoLessons = emptyList(),
            description = LocalizableString.createNonLocalizable(""),
            availability = Deck.Availability.FREE,
            name = LocalizableString.createNonLocalizable("Deck"),
            coverUrl = "",
            tags = listOf(LocalizableString.createNonLocalizable("New")),
            storeId = "",
            hasAR = true
        )
    }
}