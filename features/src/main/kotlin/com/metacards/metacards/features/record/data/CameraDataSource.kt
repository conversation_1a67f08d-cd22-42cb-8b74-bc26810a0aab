package com.metacards.metacards.features.record.data

import android.content.ContentResolver
import android.content.ContentUris
import android.content.ContentValues
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.net.Uri
import android.provider.MediaStore
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageInfo
import androidx.camera.core.ImageProxy
import androidx.core.content.ContextCompat
import co.touchlab.kermit.Logger
import com.metacards.metacards.features.record.domain.camera.CardPhoto
import java.time.LocalDateTime

class CameraDataSource(private val context: Context) {
    fun takePhoto(
        imageCapture: ImageCapture,
        photo: (CardPhoto) -> Unit
    ) {
        val contentValue = ContentValues()
        contentValue.put(MediaStore.MediaColumns.DISPLAY_NAME, "MetaCard ${LocalDateTime.now()}")
        contentValue.put(MediaStore.MediaColumns.MIME_TYPE, "image/jpeg")
        val uri =
            context.contentResolver.insert(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                contentValue
            )

        imageCapture.takePicture(
            ContextCompat.getMainExecutor(context),
            object : ImageCapture.OnImageCapturedCallback() {
                override fun onCaptureSuccess(image: ImageProxy) {
                    val imageInfo = image.imageInfo
                    val bitmap = rotateBitmapIfNeeded(image.toBitmap(), imageInfo).crop()
                    saveBitMap(bitmap, uri, context)
                    photo(CardPhoto(uri = uri))
                    image.close()
                }
            }
        )
    }

    private fun rotateBitmapIfNeeded(source: Bitmap, info: ImageInfo): Bitmap {
        val angle = info.rotationDegrees.toFloat()
        val matrix = Matrix()
        matrix.postRotate(angle)

        return Bitmap.createBitmap(source, 0, 0, source.width, source.height, matrix, true)
    }

    private fun ImageProxy.toBitmap(): Bitmap {
        val planeProxy = planes[0]
        val buffer = planeProxy.buffer
        val bytes = ByteArray(buffer.remaining())
        buffer.get(bytes)

        return BitmapFactory.decodeByteArray(bytes, 0, bytes.size)
    }

    private fun saveBitMap(bitMap: Bitmap, uri: Uri?, context: Context) {
        if (uri != null) {
            val output = context.contentResolver.openOutputStream(uri)
            if (output != null) {
                bitMap.compress(Bitmap.CompressFormat.JPEG, 100, output)
            } else {
                Logger.e("can't open stream for uri: $uri")
            }
        }
    }

    private fun Bitmap.crop(): Bitmap {
        val left = (width * LEFT_CROP_PERCENT).toInt()
        val top = (height * TOP_CROP_PERCENT).toInt()
        val right = (width * RIGHT_CROP_PERCENT).toInt()
        val bottom = (height * BOTTOM_CROP_PERCENT).toInt()

        return Bitmap.createBitmap(this, left, top, right - left, bottom - top)
    }

    fun deletePhoto(uri: Uri) {
        val contentResolver: ContentResolver = context.contentResolver
        val selection = MediaStore.Images.Media._ID + " = ?"
        val id = ContentUris.parseId(uri).toString()
        val selectionArgs = arrayOf(id)
        contentResolver.delete(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
            selection,
            selectionArgs
        )
    }
}

private const val LEFT_CROP_PERCENT = 0.1
private const val TOP_CROP_PERCENT = 0.1
private const val RIGHT_CROP_PERCENT = 0.9
private const val BOTTOM_CROP_PERCENT = 0.75
