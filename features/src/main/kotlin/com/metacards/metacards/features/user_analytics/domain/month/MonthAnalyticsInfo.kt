package com.metacards.metacards.features.user_analytics.domain.month

import com.metacards.metacards.features.record.domain.Record
import java.time.Month
import java.time.YearMonth

data class MonthAnalyticsInfo(
    val month: YearMonth,
    val records: List<Record>
) {

    val id: String by lazy {
        month.toString()
    }

    val recordsWithDescendingDates by lazy {
        records.reversed()
    }

    val energyLevelPercents: Map<Int, Float> by lazy {
        calculateLevelPercents(records) { it.energyLevel }
    }

    val moodLevelPercents: Map<Int, Float> by lazy {
        calculateLevelPercents(records) { it.moodLevel }
    }

    val energyMaxPercent: MaxPercent? by lazy {
        calculateMaxPercent(energyLevelPercents)
    }

    val moodMaxPercent: MaxPercent? by lazy {
        calculateMaxPercent(moodLevelPercents)
    }

    companion object {

        val MOCK = mock(Month.JULY)

        val LIST_MOCK = listOf(
            mock(Month.JULY), mock(Month.JUNE), mock(Month.MAY)
        )

        fun mock(month: Month): MonthAnalyticsInfo {
            return MonthAnalyticsInfo(
                month = YearMonth.of(2023, month),
                Record.LIST_MOCK
            )
        }
    }
}

private fun calculateLevelPercents(
    records: List<Record>,
    levelProvider: (Record) -> Int?
): Map<Int, Float> {
    val levels = (1..Record.MAX_FEELING_LEVEL)
    val recordCount = records.count { levelProvider(it) in levels }
    if (recordCount == 0) return emptyMap()

    return levels.associate { level ->
        val recordWithLevelCount = records.count { levelProvider(it) == level }
        val percent = recordWithLevelCount / recordCount.toFloat() * 100
        (level to percent)
    }
}

private fun calculateMaxPercent(
    levelPercents: Map<Int, Float>
): MaxPercent? {
    if (levelPercents.isEmpty()) return null

    val percent = levelPercents.values.max()
    val level = levelPercents.filter { it.value == percent }.keys.last()

    return MaxPercent(percent, level)
}