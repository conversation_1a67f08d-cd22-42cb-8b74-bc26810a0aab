package com.metacards.metacards.features.user_analytics.ui.week.widgets

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.features.R
import com.metacards.metacards.features.record.domain.LevelType
import com.metacards.metacards.features.user_analytics.domain.week.WeekAnalyticsInfo
import com.metacards.metacards.features.user_analytics.ui.common_widgets.AnalyticsLevelCard
import dev.icerock.moko.resources.desc.strResDesc
import java.text.DecimalFormat
import kotlin.math.roundToInt

@Composable
fun WeekAnalyticsLevels(
    analyticsInfo: WeekAnalyticsInfo,
    modifier: Modifier = Modifier
) {
    val formatter = remember { DecimalFormat("#.0") }

    Row(modifier = modifier.padding(horizontal = 16.dp)) {

        analyticsInfo.averageEnergyLevel?.let { level ->
            AnalyticsLevelCard(
                levelType = LevelType.Energy,
                level = level.roundToInt(),
                text = formatter.format(level),
                description = R.string.analytics_average_energy.strResDesc().localizedByLocal(),
                modifier = Modifier.weight(1f).padding(end = 4.dp)
            )
        }

        analyticsInfo.averageMoodLevel?.let { level ->
            AnalyticsLevelCard(
                levelType = LevelType.Mood,
                level = level.roundToInt(),
                text = formatter.format(level),
                description = R.string.analytics_average_mood.strResDesc().localizedByLocal(),
                modifier = Modifier.weight(1f).padding(end = 4.dp)
            )
        }
    }
}
