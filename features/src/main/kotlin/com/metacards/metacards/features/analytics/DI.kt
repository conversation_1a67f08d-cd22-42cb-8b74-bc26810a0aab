package com.metacards.metacards.features.analytics

import com.metacards.metacards.features.analytics.delegates.FirebaseAnalyticsDelegate
import com.metacards.metacards.features.analytics.delegates.FirebaseAnalyticsDelegateImpl
import com.metacards.metacards.features.analytics.delegates.LoggingDelegate
import com.metacards.metacards.features.analytics.delegates.LoggingDelegateImpl
import org.koin.dsl.module

val analyticsModule = module {
    single<FirebaseAnalyticsDelegate> { FirebaseAnalyticsDelegateImpl(get(), get()) }
    single<LoggingDelegate> { LoggingDelegateImpl() }
    single<AnalyticsService> { AnalyticsServiceImpl(get(), get(), get()) }
}