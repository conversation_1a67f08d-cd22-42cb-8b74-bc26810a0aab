package com.metacards.metacards.features.account.ui.lessons.list

import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.features.account.domain.entity.LessonCategory
import com.metacards.metacards.features.deck.domain.entity.Lesson
import kotlinx.coroutines.flow.StateFlow

interface LessonListComponent {

    val lessonCategoryState: StateFlow<LoadableState<LessonCategory>>

    val lessonsState: StateFlow<LoadableState<List<Lesson>>>

    fun onLessonDetailsClick(lesson: Lesson)

    fun onVideoPlay(lesson: Lesson)

    sealed interface Output {
        data class LessonDetailsRequested(val lesson: Lesson) : Output
    }
}