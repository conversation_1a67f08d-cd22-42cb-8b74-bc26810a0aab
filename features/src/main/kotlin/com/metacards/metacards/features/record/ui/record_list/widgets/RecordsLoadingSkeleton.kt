package com.metacards.metacards.features.record.ui.record_list.widgets

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.SkeletonElement

@Composable
fun RecordsLoadingSkeleton(hasAddButton: <PERSON><PERSON>an, modifier: Modifier = Modifier) {
    Column(
        modifier = modifier
            .padding(horizontal = 16.dp)
            .fillMaxSize()
    ) {

        RecordDateSkeleton(modifier = Modifier.padding(top = 4.dp))

        if (hasAddButton) {
            AddButtonSkeleton(modifier = Modifier.padding(top = 8.dp))
        }

        repeat(2) {
            RecordCardSkeleton(
                modifier = Modifier.padding(top = 8.dp)
            )
        }

        RecordDateSkeleton(modifier = Modifier.padding(top = 8.dp))

        repeat(2) {
            RecordCardSkeleton(
                modifier = Modifier.padding(top = 8.dp)
            )
        }
    }
}

@Composable
private fun AddButtonSkeleton(modifier: Modifier = Modifier) {
    SkeletonElement(
        modifier = modifier
            .fillMaxWidth()
            .height(52.dp),
        cornerRadius = 26.dp
    )
}

@Composable
private fun RecordDateSkeleton(modifier: Modifier = Modifier) {
    SkeletonElement(
        modifier = modifier
            .padding(start = 16.dp)
            .size(70.dp, 16.dp),
        cornerRadius = 8.dp
    )
}

@Composable
private fun RecordCardSkeleton(modifier: Modifier = Modifier) {
    SkeletonElement(
        modifier = modifier
            .fillMaxWidth()
            .height(105.dp),
        cornerRadius = 16.dp
    )
}

@Preview
@Composable
private fun RecordsLoadingSkeletonPreview() {
    AppTheme {
        RecordsLoadingSkeleton(hasAddButton = true)
    }
}