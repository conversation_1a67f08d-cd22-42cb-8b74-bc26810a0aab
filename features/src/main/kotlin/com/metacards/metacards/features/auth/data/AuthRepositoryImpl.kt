package com.metacards.metacards.features.auth.data

import co.touchlab.kermit.Logger
import com.google.firebase.auth.FirebaseUser
import com.metacards.metacards.core.app.LogoutCleaner
import com.metacards.metacards.core.error_handling.errorMessage
import com.metacards.metacards.core.message.data.MessageService
import com.metacards.metacards.core.message.domain.Message
import com.metacards.metacards.core.user.domain.Email
import com.metacards.metacards.core.user.domain.UserId
import com.metacards.metacards.core.utils.e
import com.metacards.metacards.features.auth.domain.AuthUser
import com.metacards.metacards.features.auth.domain.AuthUserProvider
import com.metacards.metacards.features.auth.domain.Password
import com.metacards.metacards.features.auth.domain.email.EmailAuthRepository
import com.metacards.metacards.features.auth.domain.sso.google.GoogleAuthRepository
import com.metacards.metacards.features.user.domain.UserRepository
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.withContext

class AuthRepositoryImpl(
    private val firebaseAuthService: FirebaseAuthService,
    private val googleAuthService: GoogleAuthService,
    coroutineScope: CoroutineScope,
    private val ioDispatcher: CoroutineDispatcher,
    private val userRepository: UserRepository,
    private val logoutCleaner: LogoutCleaner,
    private val messageService: MessageService
) : AuthUserProvider, EmailAuthRepository, GoogleAuthRepository {
    private val logger = Logger.withTag("AuthRepositoryImpl")

    override val authUser: MutableStateFlow<AuthUser?> = MutableStateFlow(null)

    override suspend fun reloadAuthUser(): AuthUser? {
        firebaseAuthService.reloadUser()
        return authUser.value
    }

    init {
        firebaseAuthService.currentUser
            .onEach { handleUserChanges(it) }
            .launchIn(coroutineScope)
    }

    override suspend fun awaitUser(timeout: Long) =
        firebaseAuthService.awaitAuthUser(timeout).toAuthUser()

    override suspend fun hasAccount(email: Email): Boolean = withContext(ioDispatcher) {
        firebaseAuthService.hasAccount(email.value)
    }

    override suspend fun auth(email: Email, password: Password) = withContext(ioDispatcher) {
        firebaseAuthService.authWithEmailPass(email.value, password.value)
    }

    override fun logout() = firebaseAuthService.logout()

    override suspend fun resetPassword(email: Email) = withContext(ioDispatcher) {
        firebaseAuthService.resetPasswordByEmail(email.value)
    }

    override suspend fun sendEmailVerifyMessage() = withContext(ioDispatcher) {
        firebaseAuthService.sendEmailVerifyMessage()
    }

    override suspend fun create(email: Email, password: Password) {
        firebaseAuthService.registerByEmailPass(
            email.value,
            password.value
        )
    }

    override suspend fun deleteUser() = firebaseAuthService.deleteUser()

    override suspend fun updateEmail(email: Email, password: Password) {
        firebaseAuthService.updateEmail(
            email.value,
            password.value
        )
    }

    override suspend fun signInWithGoogle(afterSelection: () -> Unit) =
        googleAuthService.signIn(afterSelection)

    override suspend fun tryAuthWithPassword(password: String) =
        firebaseAuthService.tryPassword(password)

    override suspend fun updatePassword(oldPassword: String, newPassword: String) =
        firebaseAuthService.updatePassword(oldPassword, newPassword)

    private suspend fun handleUserChanges(firebaseUser: FirebaseUser?) {
        try {
            if (firebaseUser != null) {
                val authUser = firebaseUser.toAuthUser() ?: return
                this.authUser.value = authUser
                userRepository.fetchUser(authUser.toAuthUserInfo())
            } else {
                logoutUser(authUser.value?.userId)
                userRepository.fetchUser(null)
            }
        } catch (e: CancellationException) {
            throw e
        } catch (e: Exception) {
            logger.e(e)
            messageService.showMessage(Message(text = e.errorMessage.error))
        }
    }

    @Suppress("NAME_SHADOWING")
    private suspend fun logoutUser(userId: UserId?) {
        logoutCleaner.onLogoutClean()
    }
}
