package com.metacards.metacards.features.deeplink.data

import android.net.Uri
import androidx.core.net.toUri
import co.touchlab.kermit.Logger
import com.metacards.metacards.core.deeplink.DeeplinkAction
import com.metacards.metacards.core.deeplink.DeeplinkService
import com.metacards.metacards.core.error_handling.detailedErrorMessage
import com.metacards.metacards.core.message.data.MessageService
import com.metacards.metacards.core.message.domain.Message
import com.metacards.metacards.core.user.domain.SubscriptionId
import com.metacards.metacards.core.utils.e
import com.metacards.metacards.features.auth.data.SSOAuthService
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deeplink.domain.DeeplinkData
import dev.icerock.moko.resources.desc.desc
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.serialization.SerializationException
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.JsonElement

class DeeplinkServiceImpl(
    private val ssoAuthService: SSOAuthService,
    private val messageService: MessageService,
    private val json: Json
) : DeeplinkService {
    private val logger = Logger.withTag("DeeplinkService")

    override val deeplinkActionFlow: MutableSharedFlow<DeeplinkAction> =
        MutableSharedFlow(
            extraBufferCapacity = Int.MAX_VALUE,
            onBufferOverflow = BufferOverflow.DROP_OLDEST
        )

    override fun tryHandleDeeplink(jsonElement: JsonElement): Boolean {
        try {
            when (val data = json.decodeFromJsonElement(DeeplinkData.serializer(), jsonElement)) {
                DeeplinkData.GetSub,
                DeeplinkData.LookAnalytics,
                DeeplinkData.MakeRecord,
                is DeeplinkData.NewDeck,
                DeeplinkData.SubPurchaseError,
                DeeplinkData.SubPurchaseSoon,
                is DeeplinkData.Deck,
                is DeeplinkData.PaymentCompleted,
                DeeplinkData.CloseAuth,
                DeeplinkData.DailyCard,
                is DeeplinkData.Course -> {
                    logger.i(data.toString())
                    deeplinkActionFlow.tryEmit(data)
                }

                is DeeplinkData.Auth -> {
                    logger.i(data.toString())
                    ssoSignInWithIntentData(data.link.value)
                    deeplinkActionFlow.tryEmit(data)
                }
            }

            return true
        } catch (e: SerializationException) {
            logger.e(e)
            messageService.showMessage(Message(e.detailedErrorMessage.error))
            return false
        } catch (e: IllegalArgumentException) {
            logger.e(e)
            messageService.showMessage(Message(e.detailedErrorMessage.error))
            return false
        }
    }

    override fun tryHandleDeeplink(deeplink: Uri): Boolean {
        return when (deeplink.scheme) {
            DeeplinkData.Deeplink.SCHEME -> {
                when (deeplink.host) {
                    DeeplinkData.Deeplink.DECK_ID -> {
                        val deckId = deeplink.lastPathSegment ?: return false
                        logger.i(deeplink.toString())
                        deeplinkActionFlow.tryEmit(DeeplinkData.NewDeck(DeckId(deckId)))
                    }

                    DeeplinkData.Deeplink.MAIN -> {
                        logger.i(deeplink.toString())
                        deeplinkActionFlow.tryEmit(DeeplinkData.MakeRecord)
                    }

                    DeeplinkData.Deeplink.ANALYTICS_MONTH.substringBefore('/') -> {
                        logger.i(deeplink.toString())
                        deeplinkActionFlow.tryEmit(DeeplinkData.LookAnalytics)
                    }

                    DeeplinkData.Deeplink.SUB -> {
                        logger.i(deeplink.toString())
                        deeplinkActionFlow.tryEmit(DeeplinkData.SubPurchaseSoon)
                    }

                    DeeplinkData.Deeplink.AUTH -> {
                        logger.i(deeplink.toString())
                        ssoSignInWithIntentData(deeplink.toString())
                        true
                    }

                    DeeplinkData.Deeplink.PAYMENT_COMPLETED -> {
                        val deckId = deeplink.getQueryParameter("deckId")
                        val subId = deeplink.getQueryParameter("subId")
                        logger.i(deeplink.toString())
                        if (!deckId.isNullOrEmpty()) {
                            val action = DeeplinkData.PaymentCompleted.Deck(DeckId(deckId))
                            deeplinkActionFlow.tryEmit(action)
                            return true
                        }

                        if (!subId.isNullOrEmpty()) {
                            val action =
                                DeeplinkData.PaymentCompleted.Subscription(SubscriptionId(subId))
                            deeplinkActionFlow.tryEmit(action)
                            return true
                        }

                        false
                    }

                    DeeplinkData.Deeplink.COURSE -> {
                        logger.i(deeplink.toString())
                        val pathSegments = deeplink.pathSegments
                        if (pathSegments.size == 2) {
                            val themeId = pathSegments[0]
                            val courseId = pathSegments[1]
                            deeplinkActionFlow.tryEmit(DeeplinkData.Course(themeId, courseId))
                            return true
                        }
                        false
                    }

                    else -> false
                }
            }

            else -> false
        }
    }

    private fun ssoSignInWithIntentData(link: String) {
        val uri = link.toUri()
        val firebaseTokenParameterName = "firebaseToken"
        val uidParameterName = "uid"
        val authErrorParameterName = "error"

        val hasParams = uri.queryParameterNames.containsAll(
            listOf(
                firebaseTokenParameterName,
                uidParameterName
            )
        ) || uri.queryParameterNames.contains(authErrorParameterName)

        if (!hasParams) {
            val message = "SSO data doesn't have uid & firebase token"
            logger.e(message)
            messageService.showMessage(Message(message.desc()))
            return
        }

        val firebaseToken = uri.getQueryParameter(firebaseTokenParameterName)
        val userId = uri.getQueryParameter(uidParameterName)
        val error = uri.getQueryParameter(authErrorParameterName)

        if (error == null) {
            if (firebaseToken == null || userId == null) {
                val message = "SSO data doesn't have uid & firebase token"
                logger.e(message)
                messageService.showMessage(Message(message.desc()))
                return
            }

            ssoAuthService.signIn(firebaseToken)
        } else {
            deeplinkActionFlow.tryEmit(DeeplinkData.CloseAuth)
        }
    }
}