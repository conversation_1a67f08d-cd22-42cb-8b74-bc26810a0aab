package com.metacards.metacards.features.layout.ui.question_for_layout

import android.os.Parcelable
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.layout.domain.CardSource
import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import kotlinx.coroutines.flow.StateFlow
import kotlinx.parcelize.Parcelize
import ru.mobileup.kmm_form_validation.control.InputControl

interface QuestionForLayoutComponent {
    val maxSymbolsCount: Int
    val currentSymbolsCount: StateFlow<Int>
    val questionInputControl: InputControl
    val tutorialMessage: StateFlow<TutorialMessage?>

    fun onNextButtonClick()
    fun onCloseButtonClick()

    sealed interface Output {
        data class LayoutRequested(val cardSource: CardSource, val question: String) : Output
        data object OnDismiss : Output
    }

    @Parcelize
    data class Config(val deckId: DeckId) : Parcelable
}