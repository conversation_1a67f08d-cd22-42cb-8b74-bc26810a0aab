package com.metacards.metacards.features.account.domain.interactor

import com.metacards.metacards.features.account.domain.repository.FeedbackRepository
import kotlinx.coroutines.flow.Flow

class SendFeedbackInteractor(private val repository: FeedbackRepository) {
    fun execute(subject: String, email: String, question: String): Flow<String> {
        return repository.sendFeedback(subject, email, question)
    }
}
