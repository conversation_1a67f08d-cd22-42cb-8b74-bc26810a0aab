package com.metacards.metacards.features.account.ui.profile.password.confirm

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.core.widget.text_field.MetaTextField
import com.metacards.metacards.features.R
import com.metacards.metacards.features.auth.ui.auth_type.pass.TextVisibilityIcon
import dev.icerock.moko.resources.desc.strResDesc
import ru.mobileup.kmm_form_validation.toCompose

@Composable
fun ProfileConfirmPasswordUi(
    component: ProfileConfirmPasswordComponent,
    modifier: Modifier = Modifier,
) {
    var showPass by remember { mutableStateOf(false) }
    val proceedButtonState by component.proceedButtonState.collectAsState()

    BoxWithFade(
        modifier = modifier.navigationBarsPadding(),
        listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        Column(modifier = Modifier.matchParentSize()) {
            TopNavigationBar(
                title = component.toolbarText,
                leadingIcon = { BackNavigationItem() },
                modifier = Modifier.padding(bottom = 44.dp)
            )

            MetaTextField(
                modifier = Modifier.padding(horizontal = 16.dp),
                inputControl = component.inputControl,
                trailingIcon = { TextVisibilityIcon(showPass) { showPass = !showPass } },
                placeholder = R.string.password_create_placeholder.strResDesc().localizedByLocal(),
                visualTransformation = if (!showPass) component.inputControl.visualTransformation.toCompose() else VisualTransformation.None,
                headerMessage = {
                    Text(
                        text = component.textFieldHeader.localizedByLocal(),
                        color = CustomTheme.colors.text.primary,
                        style = CustomTheme.typography.caption.large,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )

                    component.textFieldCaption?.let { caption ->
                        Text(
                            text = caption.localizedByLocal(),
                            color = CustomTheme.colors.text.secondary,
                            style = CustomTheme.typography.body.primary,
                            modifier = Modifier.padding(bottom = 16.dp)
                        )
                    } ?: Spacer(modifier = Modifier.height(8.dp))
                }
            )
        }

        MetaAccentButton(
            state = proceedButtonState,
            text = component.bottomButtonText.localizedByLocal(),
            onClick = component::onProceedClick,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .padding(bottom = 24.dp)
                .align(Alignment.BottomCenter)
        )
    }
}

@Preview
@Composable
fun ProfileConfirmPasswordUiPreview() {
    AppTheme {
        ProfileConfirmPasswordUi(component = FakeProfileConfirmPasswordComponent())
    }
}