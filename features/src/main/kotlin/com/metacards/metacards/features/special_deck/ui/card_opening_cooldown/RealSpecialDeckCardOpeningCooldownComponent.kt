package com.metacards.metacards.features.special_deck.ui.card_opening_cooldown

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCooldownTime

class RealSpecialDeckCardOpeningCooldownComponent(
    componentContext: ComponentContext,
    override val deckCooldownTime: SpecialDeckCooldownTime,
    private val onOutput: (SpecialDeckCardOpeningCooldownComponent.Output) -> Unit
) : ComponentContext by componentContext, SpecialDeckCardOpeningCooldownComponent {

    override fun onUnderstoodClick() {
        onOutput(SpecialDeckCardOpeningCooldownComponent.Output.DismissRequested)
    }
}