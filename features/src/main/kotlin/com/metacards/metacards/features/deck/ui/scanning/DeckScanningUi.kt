package com.metacards.metacards.features.deck.ui.scanning

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.camera.CameraPreviewWithQrCodeScanner
import com.metacards.metacards.core.camera.CameraScannerOverlay
import com.metacards.metacards.core.dialog.default_component.DefaultDialog
import com.metacards.metacards.core.dialog.default_component.model.DialogData
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.EmptyPlaceholder
import com.metacards.metacards.core.widget.button.MetaSecondaryButton
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun DeckScanningUi(component: DeckScanningComponent, modifier: Modifier = Modifier) {
    val state by component.state.collectAsState()

    Box(
        modifier = modifier
            .fillMaxSize()
    ) {
        CameraPreviewWithQrCodeScanner(
            modifier = Modifier.matchParentSize(),
            qrCodeScannerEnabled = state == DeckScanningComponent.State.Scanning,
            onQrCodeScanned = component::onQrCodeScanned
        )

        DeskScanningContent(
            state = state,
            onGoToSettingClick = component::onGoToSettingsClick,
            onDeckAddingErrorClosed = component::onDeckAddingErrorClosed
        )

        BackNavigationItem(
            paddingValues = PaddingValues(16.dp),
            modifier = Modifier.statusBarsPadding()
        )
    }
}

@Composable
private fun BoxScope.DeskScanningContent(
    state: DeckScanningComponent.State,
    onGoToSettingClick: () -> Unit,
    onDeckAddingErrorClosed: () -> Unit
) {
    when (state) {
        DeckScanningComponent.State.RequestingPermission -> {
            // nothing
        }

        DeckScanningComponent.State.PermissionDenied -> {
            PermissionDeniedPlaceholder(onGoToSettingClick = onGoToSettingClick)
        }

        DeckScanningComponent.State.Scanning, DeckScanningComponent.State.DeckAddingInProgress -> {
            CameraScannerOverlay(
                modifier = Modifier.matchParentSize(),
                text = R.string.deck_scanning_overlay_text.strResDesc().localizedByLocal(),
                hasProgressBar = state == DeckScanningComponent.State.DeckAddingInProgress
            )
        }

        is DeckScanningComponent.State.DeckAddingFailed -> {
            DefaultDialog(
                title = state.errorTitle.localizedByLocal(),
                message = state.errorMessage.localizedByLocal(),
                buttons = listOf(
                    DialogData.Button(
                        title = com.metacards.metacards.core.R.string.common_ok.strResDesc(),
                        action = onDeckAddingErrorClosed
                    )
                ),
                canDismissed = true,
                onDismiss = onDeckAddingErrorClosed
            )
        }
    }
}

@Composable
private fun PermissionDeniedPlaceholder(
    onGoToSettingClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    EmptyPlaceholder(
        modifier = modifier,
        title = R.string.deck_scanning_permission_denied_title.strResDesc().localizedByLocal(),
        message = R.string.deck_scanning_permission_denied_message.strResDesc().localizedByLocal(),
        iconRes = R.drawable.ic_64_photo,
        buttons = {
            MetaSecondaryButton(
                modifier = Modifier.fillMaxWidth(),
                text = R.string.deck_scanning_go_to_setting.strResDesc().localizedByLocal(),
                onClick = onGoToSettingClick
            )
        }
    )
}

@Preview
@Composable
fun DeckScanningUiPreview() {
    AppTheme {
        DeckScanningUi(FakeDeckScanningComponent())
    }
}