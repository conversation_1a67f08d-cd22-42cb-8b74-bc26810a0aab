package com.metacards.metacards.features.account.ui.profile.delete

import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.FakeDialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.FakeDefaultDialogComponent
import com.metacards.metacards.features.auth.domain.LoginType
import kotlinx.coroutines.flow.MutableStateFlow

class FakeProfileDeleteAccountComponent : ProfileDeleteAccountComponent {
    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        FakeDialogControl(FakeDefaultDialogComponent())

    override val userLoginTypes = MutableStateFlow(emptySet<LoginType>())
    override fun startReauthentication(onLoaded: () -> Unit) {}

    override fun onPasswordConfirmed() = Unit
}