package com.metacards.metacards.features.special_deck.ui.first_card_obtained

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.ui.MetaCardDefaults
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun SpecialDeckFirstCardObtainedUi(
    component: SpecialDeckFirstCardObtainedComponent,
    modifier: Modifier = Modifier
) {
    Surface(
        shape = RoundedCornerShape(16.dp),
        color = CustomTheme.colors.background.modal,
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
            .padding(bottom = 8.dp)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.padding(vertical = 40.dp, horizontal = 24.dp)
        ) {
            AsyncImage(
                modifier = Modifier
                    .height(230.dp)
                    .padding(bottom = 24.dp)
                    .clip(RoundedCornerShape(20.dp)),
                model = ImageRequest.Builder(LocalContext.current)
                    .data(component.imageUrl)
                    .placeholder(R.drawable.bg_card_placeholder)
                    .crossfade(MetaCardDefaults.CROSSFADE_DURATION_MILLIS)
                    .build(),
                contentDescription = null,
            )

            Text(
                text = R.string.special_deck_first_card_popup_title.strResDesc().localizedByLocal(),
                style = CustomTheme.typography.heading.medium,
                color = CustomTheme.colors.text.primary,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            Text(
                text = R.string.special_deck_first_card_popup_text.strResDesc().localizedByLocal(),
                style = CustomTheme.typography.body.primary,
                color = CustomTheme.colors.text.primary,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            MetaAccentButton(
                text = R.string.special_deck_first_card_popup_btn_confirm.strResDesc().localizedByLocal(),
                onClick = component::onCoolClick,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}