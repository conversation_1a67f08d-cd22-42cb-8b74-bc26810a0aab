package com.metacards.metacards.features.auth.data

import androidx.activity.ComponentActivity
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import co.touchlab.kermit.Logger
import com.google.android.gms.auth.api.identity.BeginSignInRequest
import com.google.android.gms.auth.api.identity.BeginSignInResult
import com.google.android.gms.auth.api.identity.Identity
import com.google.android.gms.auth.api.identity.SignInClient
import com.google.android.gms.common.api.ApiException
import com.google.firebase.auth.GoogleAuthProvider
import com.metacards.metacards.core.utils.e
import com.metacards.metacards.features.BuildConfig
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resumeWithException

class GoogleAuthServiceImpl(
    private val coroutineScope: CoroutineScope,
    private val firebaseAuthService: FirebaseAuthService
) : GoogleAuthService {
    private val logger = Logger.withTag("GoogleAuthServiceImpl")

    private lateinit var googleActivityResultLauncher: ActivityResultLauncher<IntentSenderRequest>
    private var activity: ComponentActivity? = null
    private var afterSelection: () -> Unit = {}

    override fun attach(activity: ComponentActivity) {
        this.activity = activity
    }

    override fun detach() {
        activity = null
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun registerForGoogleAuthResult() {
        activity?.let {
            val oneTapClient = Identity.getSignInClient(it)

            googleActivityResultLauncher = getGoogleActivityResultLauncher(it, oneTapClient)
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override suspend fun signIn(afterSelection: () -> Unit) {
        activity?.let {
            val oneTapClient = Identity.getSignInClient(it)
            val signInRequest = createGoogleBeginSignInRequest()
            val result = beginSignInGoogleOneTap(it, oneTapClient, signInRequest)

            googleActivityResultLauncher.launch(
                IntentSenderRequest
                    .Builder(result.pendingIntent.intentSender)
                    .build()
            )
            this.afterSelection = afterSelection
        }
    }

    private fun createGoogleBeginSignInRequest(): BeginSignInRequest =
        BeginSignInRequest.builder()
            .setGoogleIdTokenRequestOptions(
                BeginSignInRequest.GoogleIdTokenRequestOptions.builder()
                    .setSupported(true)
                    .setServerClientId(BuildConfig.SSO_CLIENT_ID_GOOGLE)
                    .setFilterByAuthorizedAccounts(false)
                    .build()
            )
            .build()

    @ExperimentalCoroutinesApi
    private fun getGoogleActivityResultLauncher(
        activity: ComponentActivity,
        oneTapClient: SignInClient
    ): ActivityResultLauncher<IntentSenderRequest> = activity.registerForActivityResult(
        ActivityResultContracts.StartIntentSenderForResult()
    ) { activityResult ->
        coroutineScope.launch {
            try {
                val data = activityResult.data
                val credential = oneTapClient.getSignInCredentialFromIntent(data)
                val firebaseCredential = GoogleAuthProvider.getCredential(
                    credential.googleIdToken, null
                )

                firebaseAuthService.signInByGoogleSSO(firebaseCredential)
                afterSelection()
            } catch (e: ApiException) {
                // user dismiss auth dialog or has no google accounts at all
                logger.e(e)
            } catch (e: Exception) {
                logger.e(e)
            }
        }
    }

    @ExperimentalCoroutinesApi
    suspend fun beginSignInGoogleOneTap(
        activity: ComponentActivity,
        oneTapClient: SignInClient,
        signInRequest: BeginSignInRequest
    ): BeginSignInResult =
        suspendCancellableCoroutine { continuation ->
            oneTapClient.beginSignIn(signInRequest)
                .addOnSuccessListener(activity) { result ->
                    if (continuation.isActive) {
                        continuation.resume(result) { throwable ->
                            logger.e("beginSignInGoogleOneTap: ", throwable)
                        }
                    }
                }
                .addOnFailureListener(activity) { e ->
                    if (continuation.isActive) {
                        continuation.resumeWithException(e)
                    }
                }
                .addOnCanceledListener {
                    logger.d("beginSignInGoogleOneTap: cancelled")
                    continuation.cancel()
                }
        }
}