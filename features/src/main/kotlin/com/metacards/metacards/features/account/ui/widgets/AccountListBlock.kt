package com.metacards.metacards.features.account.ui.widgets

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import dev.icerock.moko.resources.desc.StringDesc

@Composable
fun AccountListBlock(
    headerText: StringDesc,
    modifier: Modifier = Modifier,
    backgroundColor: Color = CustomTheme.colors.background.primary,
    background: Painter? = null,
    content: @Composable () -> Unit,
) {
    Column(modifier = modifier) {
        Text(
            text = headerText.localizedByLocal(),
            color = CustomTheme.colors.text.secondary,
            style = CustomTheme.typography.caption.medium,
            modifier = Modifier.padding(start = 16.dp, end = 16.dp, bottom = 8.dp)
        )

        Card(
            shape = RoundedCornerShape(16.dp),
            backgroundColor = backgroundColor,
            elevation = 0.dp,
        ) {
            Box {
                background?.let {
                    Image(
                        painter = background,
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier.matchParentSize()
                    )
                }
                Column(modifier = Modifier) {
                    content()
                }
            }
        }
    }
}