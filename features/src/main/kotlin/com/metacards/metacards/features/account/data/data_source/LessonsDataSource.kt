package com.metacards.metacards.features.account.data.data_source

import com.google.firebase.firestore.toObject
import com.google.firebase.firestore.toObjects
import com.metacards.metacards.core.network.firestore.FirestoreService
import com.metacards.metacards.core.network.firestore.getFlow
import com.metacards.metacards.core.network.firestore.getFlowWithLoadable
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.mapLoadable
import com.metacards.metacards.features.account.data.dto.LessonCategoryDto
import com.metacards.metacards.features.deck.data.dto.LessonDto
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

class LessonsDataSource(private val firestoreService: FirestoreService) {

    fun getAllLessonCategoriesFlow(): Flow<List<LessonCategoryDto>> {
        val flow = firestoreService.db.collection(LESSONS_COLLECTION_PATH)
            .getFlow(firestoreService)

        return flow.map { snapshot ->
            snapshot.mapNotNull {
                it.toObject<LessonCategoryDto>().copy(id = it.id)
            }
        }
    }

    fun getLessonCategoryByIdFlow(categoryId: String): Flow<LoadableState<LessonCategoryDto>> {
        val path = "$LESSONS_COLLECTION_PATH/$categoryId"
        return firestoreService.db.document(path)
            .getFlowWithLoadable(firestoreService)
            .mapLoadable { it?.toObject<LessonCategoryDto>()?.copy(id = categoryId) }
    }

    fun getCategoryLessonsFlow(categoryId: String): Flow<LoadableState<List<LessonDto>>> {
        val path = "$LESSONS_COLLECTION_PATH/$categoryId/videos"
        val flow = firestoreService.db.collection(path)
            .getFlowWithLoadable(firestoreService)

        return flow.mapLoadable { snapshot ->
            snapshot?.toObjects()
        }
    }

    companion object {
        private const val LESSONS_COLLECTION_PATH = "lessons"
    }
}