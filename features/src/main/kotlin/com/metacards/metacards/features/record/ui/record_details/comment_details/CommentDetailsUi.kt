package com.metacards.metacards.features.record.ui.record_details.comment_details

import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.delay

private const val MIN_INNER_SCROLL_BAR_HEIGHT_PX = 30
private const val SCROLL_VISIBILITY_DELAY = 500L

@Composable
fun CommentDetailsUi(
    component: CommentDetailsComponent,
    modifier: Modifier = Modifier
) {
    val textScrollState = rememberScrollState()
    val density = LocalDensity.current
    var outerScrollBarHeightPx by remember { mutableIntStateOf(0) }
    val innerScrollBarHeightPx by remember {
        derivedStateOf {
            val init = outerScrollBarHeightPx - (textScrollState.maxValue.toFloat() / 15f).toInt()
            if (init < MIN_INNER_SCROLL_BAR_HEIGHT_PX) {
                MIN_INNER_SCROLL_BAR_HEIGHT_PX
            } else {
                init
            }
        }
    }
    var isScrollVisible by remember { mutableStateOf(true) }

    LaunchedEffect(key1 = textScrollState.isScrollInProgress) {
        isScrollVisible = if (textScrollState.isScrollInProgress) {
            true
        } else {
            delay(SCROLL_VISIBILITY_DELAY)
            false
        }
    }

    Column(
        modifier = modifier
            .fillMaxWidth()
            .fillMaxHeight(0.5f)
    ) {
        Row(
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 24.dp, horizontal = 16.dp)
        ) {
            Text(
                text = R.string.add_record_comment_label.strResDesc().localizedByLocal(),
                color = CustomTheme.colors.text.primary,
                style = CustomTheme.typography.heading.secondary
            )

            Icon(
                painter = painterResource(id = R.drawable.ic_24_close),
                contentDescription = null,
                tint = CustomTheme.colors.icons.primary,
                modifier = Modifier.clickable { component.onDismiss() }
            )
        }

        component.commentCreationDate?.let {
            Text(
                text = it,
                color = CustomTheme.colors.text.secondary,
                style = CustomTheme.typography.caption.medium,
                modifier = Modifier.padding(start = 16.dp, bottom = 16.dp)
            )
        }

        Box(modifier = Modifier.fillMaxSize()) {
            Column(modifier = Modifier.matchParentSize()) {
                component.cardImageUrl?.let {
                    AsyncImage(
                        model = it,
                        contentScale = ContentScale.Crop,
                        contentDescription = null,
                        modifier = Modifier
                            .align(Alignment.CenterHorizontally)
                            .padding(bottom = 8.dp)
                            .clip(RoundedCornerShape(4.dp))
                            .size(width = 48.dp, height = 72.dp)
                            .border(BorderStroke(width = 0.5.dp, CustomTheme.colors.stroke.secondary))
                            .clickable { component.onCardClick?.invoke() }
                    )
                }

                Text(
                    text = component.commentText,
                    color = CustomTheme.colors.text.primary,
                    style = CustomTheme.typography.body.primary,
                    modifier = Modifier
                        .verticalScroll(textScrollState)
                        .padding(horizontal = 16.dp)
                )
            }

            if (textScrollState.maxValue != 0) {
                // this is workaround of this bug: https://stackoverflow.com/questions/67975569/why-cant-i-use-animatedvisibility-in-a-boxscope
                androidx.compose.animation.AnimatedVisibility(
                    visible = isScrollVisible,
                    enter = fadeIn(),
                    exit = fadeOut(),
                    modifier = Modifier.align(Alignment.CenterEnd)
                ) {
                    BoxWithConstraints(
                        modifier = Modifier
                            .fillMaxHeight()
                            .padding(vertical = 20.dp)
                            .padding(end = 8.dp)
                            .width(4.dp)
                            .background(
                                CustomTheme.colors.background.segmentControl,
                                RoundedCornerShape(4.dp)
                            )
                    ) {
                        outerScrollBarHeightPx =
                            with(density) { <EMAIL>() }

                        var offset by remember { mutableFloatStateOf(0f) }

                        if (textScrollState.maxValue != 0) {
                            offset =
                                textScrollState.value.toFloat() / textScrollState.maxValue.toFloat() *
                                        (outerScrollBarHeightPx - innerScrollBarHeightPx)
                        }

                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(with(density) { innerScrollBarHeightPx.toDp() })
                                .offset(y = with(density) { offset.toDp() })
                                .background(
                                    CustomTheme.colors.icons.primary,
                                    RoundedCornerShape(4.dp)
                                )
                        ) {}
                    }
                }
            }
        }
    }
}

@Preview
@Composable
fun CommentDetailsUiPreview() {
    AppTheme {
        CommentDetailsUi(component = FakeCommentDetailsComponent())
    }
}