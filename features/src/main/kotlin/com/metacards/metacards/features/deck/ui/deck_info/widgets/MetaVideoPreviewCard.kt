package com.metacards.metacards.features.deck.ui.deck_info.widgets

import android.webkit.URLUtil
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.metacards.metacards.core.localization.ui.localizedByLocalNullable
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.domain.entity.Lesson
import com.metacards.metacards.features.deck.ui.MetaCardDefaults

@Composable
fun MetaVideoPreviewCard(
    videoLesson: Lesson,
    modifier: Modifier = Modifier,
    textMaxLines: Int = 3,
    backgroundColor: Color = CustomTheme.colors.background.primary,
    onDetailsClick: ((Lesson) -> Unit)? = null,
    onVideoPlay: () -> Unit = { },
) {
    Surface(
        modifier = modifier,
        color = backgroundColor,
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Box(
                modifier = Modifier
                    .padding(bottom = 8.dp)
                    .clip(RoundedCornerShape(16.dp))
                    .clickable(onClick = onVideoPlay)
            ) {
                if (URLUtil.isValidUrl(videoLesson.previewUrl)) {
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(videoLesson.previewUrl)
                            .crossfade(MetaCardDefaults.CROSSFADE_DURATION_MILLIS)
                            .build(),
                        contentScale = ContentScale.Crop,
                        contentDescription = null,
                        modifier = Modifier
                            .fillMaxSize()
                            .aspectRatio(2.05f)
                            .clip(RoundedCornerShape(16.dp))
                    )
                } else {
                    Image(
                        painter = painterResource(R.drawable.logo_512),
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier
                            .fillMaxSize()
                            .aspectRatio(2.05f)
                            .clip(RoundedCornerShape(16.dp)),
                    )
                }

                Icon(
                    painter = painterResource(R.drawable.ic_32_play),
                    contentDescription = null,
                    tint = CustomTheme.colors.icons.primary,
                    modifier = Modifier
                        .align(Alignment.Center)
                )
            }

            videoLesson.name.localizedByLocalNullable()?.let { name ->
                Text(
                    modifier = Modifier
                        .padding(vertical = 4.dp)
                        .clickable { onDetailsClick?.invoke(videoLesson) },
                    text = name,
                    color = CustomTheme.colors.text.primary,
                    style = CustomTheme.typography.body.secondary
                )
            }

            videoLesson.description.localizedByLocalNullable()?.let { description ->
                Text(
                    modifier = Modifier.clickable { onDetailsClick?.invoke(videoLesson) },
                    text = description,
                    color = CustomTheme.colors.text.secondary,
                    style = CustomTheme.typography.caption.medium,
                    maxLines = textMaxLines,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
    }
}
