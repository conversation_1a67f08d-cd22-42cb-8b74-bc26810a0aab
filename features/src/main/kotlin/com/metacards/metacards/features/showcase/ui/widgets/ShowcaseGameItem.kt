package com.metacards.metacards.features.showcase.ui.widgets

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun ColumnScope.ShowcaseGameItem(
    onGameBannerClick: () -> Unit
) {
    ShowcaseItemContainer(
        title = R.string.showcase_game_title.strResDesc().localizedByLocal(),
        subtitle = R.string.showcase_game_subtitle.strResDesc().localizedByLocal()
    ) {
        Box(
            contentAlignment = Alignment.Center
        ) {
            Image(
                painter = painterResource(R.drawable.ic_showcase_game_bg),
                contentDescription = "showcase_game_bg",
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(onClick = onGameBannerClick)
            )

            Text(
                text = R.string.showcase_game_card_title.strResDesc().localizedByLocal(),
                style = CustomTheme.typography.heading.small,
                color = CustomTheme.colors.text.primary,
                modifier = Modifier
                    .padding(horizontal = 16.dp)
            )
        }
    }
}