package com.metacards.metacards.features.user_analytics.ui.layouts

import android.os.Build
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.doOnStart
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.paged_loading.PagedLoading
import com.metacards.metacards.core.paged_loading.handleErrors
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.PagedData
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.mapData
import com.metacards.metacards.core.utils.toLoadableState
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.record.domain.Record
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.record.domain.RecordsPagedLoading
import com.metacards.metacards.features.record.ui.record_list.ScrollCommand
import com.metacards.metacards.features.user.domain.GetUserSubscriptionStateInteractor
import com.metacards.metacards.features.user_analytics.domain.layout.LayoutRecord
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.datetime.LocalDate

class RealLayoutsAnalyticsComponent(
    componentContext: ComponentContext,
    private val errorHandler: ErrorHandler,
    private val layoutsPagedLoading: RecordsPagedLoading,
    private val analyticsService: AnalyticsService,
    getUserSubscriptionStateInteractor: GetUserSubscriptionStateInteractor,
    private val onOutput: (LayoutsAnalyticsComponent.Output) -> Unit,
) : ComponentContext by componentContext, LayoutsAnalyticsComponent {

    private var prevIndex = START_POSITION

    override val isPremiumUser = getUserSubscriptionStateInteractor.execute().map {
        it is User.SubscriptionState.Ongoing
    }.stateIn(componentScope, SharingStarted.Eagerly, false)

    override val analyticsState: StateFlow<LoadableState<PagedData<LayoutRecord>>> =
        computed(layoutsPagedLoading.stateFlow, isPremiumUser) { analytics, isPremiumUser ->
            val data = if (!isPremiumUser && (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S)) {
                PagedLoading.State.mock(Record.LIST_MOCK.reversed())
            } else {
                analytics
            }

            data.toLoadableState().mapData { analytics -> analytics.mapToPagedLayoutRecord() }
        }

    override val selectedRecord = MutableStateFlow<LayoutRecord?>(null)
    override val scrollToRecordCommand =
        MutableSharedFlow<ScrollCommand?>(
            replay = 1,
            extraBufferCapacity = 0,
            onBufferOverflow = BufferOverflow.DROP_OLDEST
        )

    init {
        layoutsPagedLoading.handleErrors(componentScope) {
            errorHandler.handleError(it.exception)
        }

        lifecycle.doOnStart {
            val pagingState = analyticsState.value

            if (pagingState.data?.list?.isEmpty() == true) {
                layoutsPagedLoading.loadFirstPage()
                scrollToRecordCommand.tryEmit(ScrollCommand.ScrollToIndex(START_POSITION))
            }
        }
    }

    override fun updateSelectedRecord(index: Int?) {
        val layoutRecords = analyticsState.value.data?.list.orEmpty()

        if (index != null && layoutRecords.size > AMOUNT_EMPTY_RECORD_ITEM * 2) {
            when {
                index > prevIndex -> analyticsService.logEvent(AnalyticsEvent.AnalyticsSetupsNextEvent)
                index < prevIndex -> analyticsService.logEvent(AnalyticsEvent.AnalyticsSetupsPrevEvent)
            }
            prevIndex = index
            selectedRecord.update { layoutRecords.getOrNull(index) }
        }
    }

    override fun onRecordClick(recordId: RecordId) {
        analyticsService.logEvent(AnalyticsEvent.AnalyticsSetupsRecordTapEvent)
        onScrollCommandExecuted()
        onOutput(LayoutsAnalyticsComponent.Output.RecordDetailsRequested(recordId))
    }

    override fun onLoadMore() {
        layoutsPagedLoading.loadNext()
    }

    private fun PagedData<Record>?.mapToPagedLayoutRecord(): PagedData<LayoutRecord> {
        val layoutRecords = mutableListOf<LayoutRecord>()

        val filteredList = this?.list
            ?.filter { it.isArchived.not() }
            ?.filter { it.moodLevel?.notZero().orFalse() || it.energyLevel?.notZero().orFalse() }

        if (filteredList?.isNotEmpty() == true) {
            var date: LocalDate? = null

            filteredList
                .reversed()
                .forEach { record ->
                    val layoutRecord = LayoutRecord(
                        date = record.creationDate,
                        record = record,
                        showDate = if (date != record.creationDate) {
                            date = record.creationDate
                            true
                        } else {
                            false
                        }
                    )

                    layoutRecords.add(layoutRecord)
                }

            repeat(AMOUNT_EMPTY_RECORD_ITEM) {
                layoutRecords.add(LayoutRecord.empty())
                layoutRecords.add(0, LayoutRecord.empty())
            }
        }

        return PagedData(
            list = layoutRecords.reversed(),
            hasNextPage = this?.hasNextPage.orFalse(),
            hasPreviousPage = this?.hasPreviousPage.orFalse(),
            loadingNextPage = this?.loadingNextPage.orFalse(),
            loadingPreviousPage = this?.loadingPreviousPage.orFalse()
        )
    }

    private fun onScrollCommandExecuted() {
        scrollToRecordCommand.tryEmit(null)
    }
}

private fun Int.notZero(): Boolean = this > 0
private fun Boolean?.orFalse(): Boolean = this ?: false

private const val AMOUNT_EMPTY_RECORD_ITEM = 3
private const val START_POSITION = 3
