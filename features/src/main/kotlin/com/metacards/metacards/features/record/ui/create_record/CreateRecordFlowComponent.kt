package com.metacards.metacards.features.record.ui.create_record

import android.os.Parcelable
import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.features.record.domain.camera.CardPhoto
import com.metacards.metacards.features.record.ui.create_record.add_card.AddCardComponent
import com.metacards.metacards.features.record.ui.create_record.camera_permission.CameraPermissionComponent
import com.metacards.metacards.features.record.ui.create_record.main.CreateRecordComponent
import com.metacards.metacards.features.record.ui.create_record.photo_confirm.ConfirmPhotoComponent
import kotlinx.coroutines.flow.StateFlow
import kotlinx.parcelize.Parcelize

interface CreateRecordFlowComponent {
    val childStack: StateFlow<ChildStack<*, Child>>

    sealed interface Child {
        class CreateRecord(val component: CreateRecordComponent) : Child
        class PermissionCamera(val component: CameraPermissionComponent) : Child
        class AddCard(val component: AddCardComponent) : Child
        class ConfirmPhoto(val component: ConfirmPhotoComponent) : Child
    }

    sealed interface Screen : Parcelable {
        @Parcelize
        data class CreateRecord(
            val photo: CardPhoto = CardPhoto(),
            val isDailyCard: Boolean = false
        ) : Screen

        @Parcelize
        data object PermissionCamera : Screen

        @Parcelize
        data object Camera : Screen

        @Parcelize
        data class ConfirmPhoto(val cardPhoto: CardPhoto) : Screen
    }

    sealed interface Output {
        data object JournalRequested : Output
        data object SubscriptionSuggestingRequested : Output
        data object AuthSuggestingRequested : Output
    }
}
