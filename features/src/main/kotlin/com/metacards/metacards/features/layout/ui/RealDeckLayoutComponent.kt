package com.metacards.metacards.features.layout.ui

import android.os.Parcelable
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.router.stack.childStack
import com.arkivanov.decompose.router.stack.pop
import com.arkivanov.decompose.router.stack.push
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.createDefaultDialogComponent
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.model.DialogData
import com.metacards.metacards.core.dialog.dialogControl
import com.metacards.metacards.core.utils.toStateFlow
import com.metacards.metacards.features.R
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CourseThemeId
import com.metacards.metacards.features.layout.createBaseCardSelectComponent
import com.metacards.metacards.features.layout.createCardListComponent
import com.metacards.metacards.features.layout.domain.CardListViewData
import com.metacards.metacards.features.layout.domain.CardSource
import com.metacards.metacards.features.layout.domain.PredefinedQuestionsWithCards
import com.metacards.metacards.features.layout.ui.card_list.CardListComponent
import com.metacards.metacards.features.layout.ui.select_card.CardSelectComponent
import com.metacards.metacards.features.record.domain.RecordData
import com.metacards.metacards.features.record.domain.RecordSource
import com.metacards.metacards.features.tutorial.data.TutorialMessageService
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.parcelize.Parcelize

class RealDeckLayoutComponent(
    componentContext: ComponentContext,
    private val componentFactory: ComponentFactory,
    private val cardSource: CardSource,
    questionText: String?,
    private val courseId: CourseId?,
    private val courseThemeId: CourseThemeId?,
    private val tutorialMessageService: TutorialMessageService,
    private val analyticsService: AnalyticsService,
    private val onOutput: (DeckLayoutComponent.Output) -> Unit,
) : ComponentContext by componentContext, DeckLayoutComponent {
    private val navigation = StackNavigation<ChildConfig>()

    private val predefinedQuestionsWithCards = mutableListOf<PredefinedQuestionsWithCards>()
    private var predefinedQuestionsOrder = 0

    override val childStack = childStack(
        source = navigation,
        initialConfiguration = if (cardSource !is CardSource.DailyCardSource) {
            ChildConfig.CardSelect(cardSource, questionText)
        } else {
            ChildConfig.CardList(CardListViewData.forDailyCard(cardSource))
        },
        handleBackButton = true,
        childFactory = ::createChild
    ).toStateFlow(lifecycle)

    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        dialogControl(::createDefaultDialogComponent)

    private fun createDefaultDialogComponent(
        config: DefaultDialogComponent.Config,
        context: ComponentContext,
        control: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent>,
    ): DefaultDialogComponent {
        return componentFactory.createDefaultDialogComponent(
            context,
            config.data,
            control::dismiss
        )
    }

    private fun createChild(
        config: ChildConfig,
        componentContext: ComponentContext,
    ): DeckLayoutComponent.Child = when (config) {
        is ChildConfig.CardSelect -> {
            DeckLayoutComponent.Child.SelectCard(
                componentFactory.createBaseCardSelectComponent(
                    componentContext,
                    config.cardSource,
                    config.questionText,
                    ::onCardSelectOutput
                )
            )
        }

        is ChildConfig.CardList -> {
            DeckLayoutComponent.Child.CardList(
                componentFactory.createCardListComponent(
                    componentContext,
                    config.cardListViewData,
                    ::onCardListOutput
                )
            )
        }
    }

    private fun onCardListOutput(output: CardListComponent.Output) {
        when (output) {
            is CardListComponent.Output.FullScreenCardRequested -> {
                onOutput(
                    DeckLayoutComponent.Output.FullScreenCardRequested(
                        card = output.card,
                        cardSource = cardSource,
                    )
                )
            }

            is CardListComponent.Output.OnFinished -> {
                if (cardSource is CardSource.Deck) {
                    onOutput(
                        DeckLayoutComponent.Output.OnFinished(
                            output.cardsWithComments,
                            recordData = getRecordData(
                                setupType = CardSelectComponent.Tabs.Flat.name,
                                cardsNumber = output.cardsWithComments.size
                            )
                        )
                    )
                } else {
                    navigation.pop()
                }
                onOutput(
                    DeckLayoutComponent.Output.OnFinished(
                        output.cardsWithComments,
                        recordData = getRecordData(
                            setupType = CardSelectComponent.Tabs.Flat.name,
                            cardsNumber = output.cardsWithComments.size
                        )
                    )
                )
            }

            is CardListComponent.Output.OnPredefinedFinished -> {
                (cardSource as? CardSource.Questions)?.list
                    ?.getOrNull(predefinedQuestionsOrder)?.let { question ->
                        predefinedQuestionsWithCards.add(
                            PredefinedQuestionsWithCards(
                                question,
                                output.cardsWithComments
                            )
                        )
                        predefinedQuestionsOrder++
                    }

                if (output.isLastPredefined) {
                    onOutput(
                        DeckLayoutComponent.Output.OnPredefinedFinished(
                            RecordSource.Creation(
                                questions = predefinedQuestionsWithCards.map { it.toQuestion() }
                            ),
                            recordData = getRecordData(
                                setupType = "",
                                cardsNumber = output.cardsWithComments.size
                            ),
                            courseId = courseId,
                            courseThemeId = courseThemeId
                        )
                    )
                }

                navigation.pop()
            }

            CardListComponent.Output.CloseRequested -> onClose(false)

            CardListComponent.Output.AuthSuggestingScreenRequested ->
                onOutput(DeckLayoutComponent.Output.AuthSuggestingRequested)

            is CardListComponent.Output.PremiumSuggestingScreenRequested -> onOutput(
                DeckLayoutComponent.Output.SubscriptionSuggestingRequested(output.withAdv)
            )

            is CardListComponent.Output.DailyCardRecordRequested -> {
                onOutput(DeckLayoutComponent.Output.DailyRecordRequested(output.recordId))
            }

            is CardListComponent.Output.OnFinishedDaily -> {
                onOutput(
                    DeckLayoutComponent.Output.OnFinishedDaily(
                        output.cardWithComment,
                        output.question
                    )
                )
            }
        }
    }

    private fun onCardSelectOutput(output: CardSelectComponent.Output) {
        when (output) {
            is CardSelectComponent.Output.OnCardSelected -> {
                navigation.push(
                    ChildConfig.CardList(
                        CardListViewData(
                            selectedCard = output.card,
                            questionText = output.questionText,
                            availableCards = output.availableCards,
                            isPredefined = cardSource is CardSource.Questions,
                            isLastPredefined = output.isLastPredefined,
                            isDailyCard = false,
                            questionIndex = output.questionIndex,
                            cardHintList = output.cardHints
                        )
                    )
                )
            }

            is CardSelectComponent.Output.OnCloseRequested -> {
                onClose(output.isArCardSelect)
            }

            is CardSelectComponent.Output.OnArFinished -> {
                onOutput(
                    DeckLayoutComponent.Output.OnFinished(
                        output.cardsWithComment,
                        recordData = getRecordData(
                            setupType = CardSelectComponent.Tabs.AR.name,
                            cardsNumber = output.cardsWithComment.size
                        )
                    )
                )
            }

            is CardSelectComponent.Output.OnArPredefinedFinished -> {
                (cardSource as? CardSource.Questions)?.list
                    ?.getOrNull(predefinedQuestionsOrder)?.let { question ->
                        predefinedQuestionsWithCards.add(
                            PredefinedQuestionsWithCards(
                                question,
                                output.cardsWithComment
                            )
                        )
                        predefinedQuestionsOrder++
                    }

                if (output.isLastPredefined) {
                    onOutput(
                        DeckLayoutComponent.Output.OnPredefinedFinished(
                            RecordSource.Creation(
                                questions = predefinedQuestionsWithCards.map { it.toQuestion() }
                            ),
                            recordData = getRecordData(
                                setupType = "",
                                cardsNumber = output.cardsWithComment.size
                            ),
                            courseId = courseId,
                            courseThemeId = courseThemeId
                        )
                    )
                }
            }

            CardSelectComponent.Output.AuthSuggestingScreenRequested ->
                onOutput(DeckLayoutComponent.Output.AuthSuggestingRequested)

            CardSelectComponent.Output.SubscriptionSuggestingScreenRequested ->
                onOutput(DeckLayoutComponent.Output.SubscriptionSuggestingRequested())
        }
    }

    private fun onClose(isArCardSelect: Boolean) {
        val isPredefined = cardSource is CardSource.Questions
        val isDailyCard = cardSource is CardSource.DailyCardSource

        run {
            when {
                isDailyCard -> return@run

                isPredefined -> if (isArCardSelect) {
                    AnalyticsEvent.LayoutArCloseEvent
                } else {
                    AnalyticsEvent.LayoutCloseEvent
                }

                else -> if (isArCardSelect) {
                    AnalyticsEvent.SetupArCloseEvent
                } else {
                    AnalyticsEvent.SetupManualCloseEvent
                }
            }.let(analyticsService::logEvent)
        }

        val cancelButtonText = if (isDailyCard) {
            R.string.random_card_close_dialog_cancel_button.strResDesc()
        } else {
            R.string.deck_layout_exit_dialog_cancel_button.strResDesc()
        }

        val cancelButton = DialogData.Button(cancelButtonText) {
            run {
                when {
                    isDailyCard -> return@run

                    isPredefined -> if (isArCardSelect) {
                        AnalyticsEvent.LayoutArCloseCancelEvent
                    } else {
                        AnalyticsEvent.LayoutCloseCancelEvent
                    }

                    else -> if (isArCardSelect) {
                        AnalyticsEvent.SetupArCloseCancelEvent
                    } else {
                        AnalyticsEvent.SetupManualCloseCancelEvent
                    }
                }.let(analyticsService::logEvent)
            }
            dialogControl.dismiss()
        }

        val exitButtonText = if (isDailyCard) {
            R.string.random_card_close_dialog_skip_button.strResDesc()
        } else {
            R.string.deck_layout_exit_dialog_exit_button.strResDesc()
        }

        val exitButton = DialogData.Button(exitButtonText) {
            run {
                when {
                    isDailyCard -> return@run

                    isPredefined -> if (isArCardSelect) {
                        AnalyticsEvent.LayoutArCloseConfirmEvent
                    } else {
                        AnalyticsEvent.LayoutCloseConfirmEvent
                    }

                    else -> if (isArCardSelect) {
                        AnalyticsEvent.SetupArCloseConfirmEvent
                    } else {
                        AnalyticsEvent.SetupManualCloseConfirmEvent
                    }
                }.let(analyticsService::logEvent)
            }

            onOutput(DeckLayoutComponent.Output.OnCloseRequested)
            tutorialMessageService.processCloseDeckLayoutComponentTutorial()
        }

        val title = if (isDailyCard) {
            R.string.random_card_close_dialog_title.strResDesc()
        } else {
            R.string.deck_layout_exit_dialog_title.strResDesc()
        }

        val dialogData = DialogData(
            title = title,
            message = R.string.deck_layout_exit_dialog_message.strResDesc(),
            buttons = listOf(cancelButton, exitButton)
        )
        dialogControl.show(DefaultDialogComponent.Config(dialogData))
    }

    private fun getRecordData(setupType: String, cardsNumber: Int): RecordData {
        val deckId = (cardSource as? CardSource.Deck)?.deckId?.value
        return RecordData(
            setupType = setupType,
            cardsNumber = cardsNumber,
            deckId = deckId
        )
    }

    sealed interface ChildConfig : Parcelable {

        @Parcelize
        data class CardSelect(
            val cardSource: CardSource,
            val questionText: String? = null,
        ) : ChildConfig

        @Parcelize
        data class CardList(val cardListViewData: CardListViewData) : ChildConfig
    }
}