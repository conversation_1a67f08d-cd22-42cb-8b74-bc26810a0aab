package com.metacards.metacards.features.tutorial.ui

import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.doOnCreate
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.R
import com.metacards.metacards.core.createDefaultDialogComponent
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.model.DialogData
import com.metacards.metacards.core.dialog.dialogControl
import com.metacards.metacards.core.preferences.models.TutorialState
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.tutorial.data.TutorialMessageService
import com.metacards.metacards.features.tutorial.data.TutorialRepository
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

class RealTutorialMessageComponent(
    componentContext: ComponentContext,
    componentFactory: ComponentFactory,
    private val tutorialMessageService: TutorialMessageService,
    private val tutorialRepository: TutorialRepository,
    private val analyticsService: AnalyticsService,
    private val onOutput: (TutorialMessageComponent.Output) -> Unit
) : ComponentContext by componentContext, TutorialMessageComponent {

    override val tutorialInProgress = tutorialRepository.tutorialStateFlow.map {
        it != TutorialState.COMPLETED
    }.stateIn(componentScope, SharingStarted.Eagerly, false)

    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        dialogControl(dialogComponentFactory = { config, componentContext, dialogControl ->
            componentFactory.createDefaultDialogComponent(
                componentContext,
                config.data,
                dialogControl::dismiss
            )
        })

    private val cancelDialogConfig by lazy {
        DefaultDialogComponent.Config(
            DialogData(
                title = StringDesc.Resource(R.string.tutorial_dialog_title),
                message = StringDesc.Resource(R.string.tutorial_dialog_message),
                buttons = listOf(
                    DialogData.Button(
                        title = StringDesc.Resource(R.string.tutorial_dialog_cancel_button),
                        action = {
                            analyticsService.logEvent(AnalyticsEvent.TutorCloseCancelEvent)
                            dialogControl.dismiss()
                        }
                    ),
                    DialogData.Button(
                        title = StringDesc.Resource(R.string.tutorial_dialog_close_button),
                        action = {
                            analyticsService.logEvent(AnalyticsEvent.TutorCloseConfirmEvent)
                            onOutput(TutorialMessageComponent.Output.CancelTutorialRequested)
                            componentScope.launch {
                                tutorialRepository.updateTutorialState(TutorialState.FORCE_FINISH)
                            }
                            dialogControl.dismiss()
                        }
                    )
                )
            )
        )
    }

    init {
        lifecycle.doOnCreate {
            componentScope.launch {
                tutorialMessageService.tutorialCloseRequestFlow.collect {
                    onTutorialCancel()
                }
            }
        }
    }

    override fun onTutorialCancel() {
        analyticsService.logEvent(AnalyticsEvent.TutorCloseEvent(tutorialMessageService.tutorialStepFlow.value.step))
        dialogControl.show(cancelDialogConfig)
    }
}