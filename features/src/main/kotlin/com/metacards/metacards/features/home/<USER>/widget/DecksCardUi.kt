package com.metacards.metacards.features.home.ui.widget

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.EaseInOut
import androidx.compose.animation.core.animateDp
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.updateTransition
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredHeight
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.FractionalThreshold
import androidx.compose.material.Icon
import androidx.compose.material.Surface
import androidx.compose.material.SwipeableState
import androidx.compose.material.Text
import androidx.compose.material.rememberSwipeableState
import androidx.compose.material.swipeable
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.placeholder.PlaceholderHighlight
import com.metacards.metacards.core.widget.placeholder.placeholder
import com.metacards.metacards.core.widget.placeholder.shimmer
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.domain.entity.Deck
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.domain.entity.DeckWithAvailable
import com.metacards.metacards.features.deck.ui.MetaCardDefaults
import com.metacards.metacards.features.home.ui.FakeHomeComponent
import com.metacards.metacards.features.home.ui.HomeComponent
import dev.icerock.moko.resources.desc.strResDesc
import java.util.ArrayDeque
import java.util.Deque

private const val TRANSITION_DURATION_MILLIS = 1000

private fun <T> cardMoveTransitionSpec() = tween<T>(
    durationMillis = 300,
    delayMillis = 0,
    easing = EaseInOut
)

@Composable
fun DecksCard(
    deckList: List<DeckWithAvailable>,
    parentWidth: Dp,
    component: HomeComponent,
    availableHeight: Dp,
    isTutorial: Boolean = false
) {
    val decksLayoutParams = DecksLayoutParams(parentWidth, availableHeight)

    AnimatedContent(
        targetState = deckList.isEmpty(),
        transitionSpec = {
            fadeIn(animationSpec = tween(TRANSITION_DURATION_MILLIS)) togetherWith
                fadeOut(animationSpec = tween(TRANSITION_DURATION_MILLIS))
        },
        label = "DecksCard"
    ) {
        if (it) {
            DecksSkeleton(decksLayoutParams)
        } else {
            DecksLayout(
                decks = deckList,
                decksLayoutParams = decksLayoutParams,
                isTutorial = isTutorial,
                onInfoButtonClick = component::onDeckInfoButtonClick,
                onDeckClick = component::onDeckClick
            )
        }
    }
}

data class DecksLayoutParams(
    val parentWidth: Dp,
    val availableHeight: Dp,
    val cardTopGap: Dp = 12.dp,
    val visibleCount: Int = 3,
    val scaleGap: Float = 0.1f
) {
    val cardHeight: Dp = parentWidth / MetaCardDefaults.aspectRatio
    val heightDelta: Dp = (cardHeight - availableHeight).takeIf { it > 0.dp } ?: 0.dp
    fun createListOfDeckValues(size: Int): List<DeckValues> {
        return List(size) {
            val multiplier = maxOf(visibleCount - it - 1, 0)
            val zIndex = multiplier.toFloat()
            val scale = 1 - it * 0.1f
            val scaleTopGap = cardHeight * (scale - 1f) / 2f
            val topGap = scaleTopGap + cardTopGap * multiplier

            DeckValues(topGap, zIndex, scale)
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun DecksLayout(
    decks: List<DeckWithAvailable>,
    decksLayoutParams: DecksLayoutParams,
    isTutorial: Boolean,
    onInfoButtonClick: (DeckId) -> Unit,
    onDeckClick: (DeckId) -> Unit
) {
    val visibleCount = decksLayoutParams.visibleCount
    val topCardIndex = remember { mutableIntStateOf(0) }
    val sizePx = with(LocalDensity.current) { decksLayoutParams.cardHeight.toPx() * 0.8f }
    val anchors = mapOf(-sizePx to -1, 0f to 0, sizePx to 1)

    var isFirstLaunch by remember {
        mutableStateOf(true)
    }

    val valuesDeque: Deque<DeckValues> =
        remember { ArrayDeque(decksLayoutParams.createListOfDeckValues(decks.size)) }
    val swipeableState = rememberSwipeableState(0)
    val swipeableStateSecond = rememberSwipeableState(0)
    val swipeableStateThird = rememberSwipeableState(0)
    val stateDequeue: Deque<SwipeableState<Int>> = remember {
        ArrayDeque(listOf(swipeableState, swipeableStateSecond, swipeableStateThird))
    }

    val currentState = stateDequeue.peek()!!

    val swipeOffset = currentState.offset.value.dp
    val values: Deque<DeckValues> = ArrayDeque(valuesDeque)

    var visibleItems by remember {
        mutableStateOf(listOf(0, 1, 2))
    }

    var flag by remember {
        mutableStateOf(false)
    }

    LaunchedEffect(key1 = currentState.currentValue, key2 = isFirstLaunch) {
        val value = currentState.currentValue

        when {
            isFirstLaunch -> isFirstLaunch = false

            value == 1 || value == -1 -> {
                flag = true

                if (topCardIndex.intValue + 1 == decks.size) {
                    topCardIndex.intValue = 0
                } else {
                    topCardIndex.intValue = topCardIndex.intValue + 1
                }

                val state = stateDequeue.poll()!!
                stateDequeue.offer(state)

                val lastIndex = topCardIndex.intValue + visibleCount - 1
                val newIndex = if (lastIndex >= decks.size) {
                    lastIndex - decks.size
                } else {
                    lastIndex
                }
                visibleItems = visibleItems + newIndex

                val lastElement = valuesDeque.pollLast()
                valuesDeque.offerFirst(lastElement)
            }

            value == 0 && flag -> {
                val temp = (visibleItems).toMutableList()
                temp.removeFirst()
                visibleItems = temp
            }
        }
    }

    LaunchedEffect(key1 = flag) {
        if (flag) {
            stateDequeue.peekLast()!!.snapTo(0)
            flag = false
        }
    }

    decks.forEachIndexed { index, deck ->
        if (values.isEmpty()) return@forEachIndexed
        val value = values.pop()
        val swipeOffsetY = if (index == topCardIndex.intValue) swipeOffset.value else 0f
        val transition = updateTransition(targetState = value, label = "cardMove")
        val scale by transition.animateFloat(
            label = "scale",
            transitionSpec = { cardMoveTransitionSpec() }
        ) { it.scale }
        val offsetY by transition.animateDp(
            label = "offsetY",
            transitionSpec = { cardMoveTransitionSpec() }
        ) {
            (it.topGap) + swipeOffsetY.dp / 2f
        }

        val translationY = with(LocalDensity.current) {
            offsetY.toPx()
        }

        AnimatedVisibility(
            modifier = Modifier.zIndex(value.zIndex),
            visible = visibleItems.contains(index),
            enter = slideInVertically(initialOffsetY = { fullHeight -> fullHeight }),
            exit = slideOutVertically(targetOffsetY = { fullHeight -> fullHeight })
        ) {
            val swipeableModifier = if (index == topCardIndex.intValue && !isTutorial) {
                Modifier.swipeable(
                    state = currentState,
                    anchors = anchors,
                    thresholds = { _, _ -> FractionalThreshold(0.4f) },
                    orientation = Orientation.Vertical,
                    velocityThreshold = 800.dp,
                )
            } else {
                Modifier
            }

            DeckCard(
                deck = deck,
                onInfoButtonClick = {
                    if (!isTutorial) {
                        onInfoButtonClick(it)
                    }
                },
                modifier = Modifier
                    .padding(top = decksLayoutParams.heightDelta)
                    .requiredHeight(decksLayoutParams.cardHeight)
                    .graphicsLayer {
                        scaleX = scale
                        scaleY = scale
                        this.translationY = translationY
                    }
                    .then(swipeableModifier)
                    .clip(MetaCardDefaults.shape)
                    .clickable { onDeckClick(deck.deck.id) }
            )
        }
    }
}

@Composable
fun DecksSkeleton(decksLayoutParams: DecksLayoutParams) {
    val deckValuesList = decksLayoutParams.createListOfDeckValues(decksLayoutParams.visibleCount)
    for (index in 0 until decksLayoutParams.visibleCount) {
        val values = deckValuesList[index]
        val translationY = with(LocalDensity.current) {
            values.topGap.toPx()
        }

        DeckWithShimmer(
            decksLayoutParams = decksLayoutParams,
            scale = values.scale,
            translationY = translationY,
            zIndex = values.zIndex,
        )
    }
}

@Composable
fun DeckWithShimmer(
    decksLayoutParams: DecksLayoutParams,
    scale: Float,
    translationY: Float,
    zIndex: Float,
) {
    Surface(
        modifier = Modifier
            .padding(top = decksLayoutParams.heightDelta)
            .requiredHeight(decksLayoutParams.cardHeight)
            .graphicsLayer(
                scaleX = scale,
                scaleY = scale,
                translationY = translationY
            )
            .zIndex(zIndex)
            .fillMaxWidth(),
        color = CustomTheme.colors.background.modal,
        shape = MetaCardDefaults.shape,
        border = MetaCardDefaults.borderStroke
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .placeholder(
                    visible = true,
                    color = CustomTheme.colors.background.placeholder,
                    shape = MetaCardDefaults.shape,
                    highlight = PlaceholderHighlight.shimmer(
                        highlightColor = CustomTheme.colors.system.invert.copy(alpha = 0.6f)
                    ),
                    placeholderFadeTransitionSpec = { tween() },
                    contentFadeTransitionSpec = { tween() }
                )
        )
    }
}

@Composable
fun DeckCard(
    deck: DeckWithAvailable,
    onInfoButtonClick: (DeckId) -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier,
        color = CustomTheme.colors.background.modal,
        shape = MetaCardDefaults.shape,
        border = MetaCardDefaults.borderStroke,
    ) {
        val gradientColors = listOf(
            CustomTheme.colors.gradient.backgroundEnd.copy(alpha = 0.35f),
            CustomTheme.colors.gradient.backgroundEnd.copy(alpha = 0.0f)
        )

        BoxWithFade(
            modifier = Modifier.fillMaxWidth(), listOfColors = gradientColors,
            behindContent = {
                AsyncImage(
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(MetaCardDefaults.aspectRatio),
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(deck.deck.coverUrl)
                        .placeholder(R.drawable.bg_card_placeholder)
                        .crossfade(MetaCardDefaults.CROSSFADE_DURATION_MILLIS)
                        .build(),
                    contentDescription = null,
                    contentScale = ContentScale.FillBounds
                )
            }
        ) {
            Content(deck, onInfoButtonClick)
        }
    }
}

@Composable
private fun Content(
    deck: DeckWithAvailable,
    onInfoButtonClick: (DeckId) -> Unit,
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Header(deck.deck, onInfoButtonClick)

        val toStrRes = when {
            deck.deck.isSpecial -> {
                if (deck.isSpecialDeckCompleted) {
                    R.string.home_deck_available_action_title
                } else {
                    R.string.home_deck_special_not_completed_action_title
                }
            }
            deck.available -> R.string.home_deck_available_action_title
            else -> R.string.home_deck_unavailable_action_title
        }.strResDesc()

        ActionButton(toStrRes)
    }
}

@Composable
private fun Header(
    deck: Deck,
    onInfoButtonClick: (DeckId) -> Unit,
) {
    Row(
        modifier = Modifier.padding(top = 16.dp, bottom = 12.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        InfoIcon(visible = false)

        Text(
            modifier = Modifier.weight(1f), text = deck.name.localizedByLocal(),
            textAlign = TextAlign.Center,
            style = CustomTheme.typography.heading.medium,
            color = CustomTheme.colors.text.caption
        )

        InfoIcon(onClick = { onInfoButtonClick(deck.id) })
    }
}

@Composable
private fun InfoIcon(visible: Boolean = true, onClick: () -> Unit = {}) {
    val alpha = if (visible) 1f else 0f

    Box(
        Modifier
            .padding(horizontal = 16.dp)
            .alpha(alpha)
            .clickable(boundedRipple = false, onClick = onClick)
    ) {
        Icon(
            painter = painterResource(id = R.drawable.ic_info),
            contentDescription = null,
            tint = CustomTheme.colors.system.unspecified
        )
    }
}

data class DeckValues(
    val topGap: Dp,
    val zIndex: Float,
    val scale: Float
)

@Preview(showSystemUi = true)
@Composable
fun TestDeckPreview() {
    AppTheme {
        val component = FakeHomeComponent()
        val decks by component.deckList.collectAsState()

        DecksCard(decks, 400.dp, component, 500.dp)
    }
}
