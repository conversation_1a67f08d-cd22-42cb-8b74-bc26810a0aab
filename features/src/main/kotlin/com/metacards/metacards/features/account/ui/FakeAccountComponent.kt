package com.metacards.metacards.features.account.ui

import com.metacards.metacards.core.bottom_sheet.BottomSheetControl
import com.metacards.metacards.core.bottom_sheet.FakeBottomSheetControl
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.FakeDialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.FakeDefaultDialogComponent
import com.metacards.metacards.core.localization.domain.AppLanguage
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.features.account.ui.language.AccountLanguageComponent
import com.metacards.metacards.features.account.ui.language.FakeAccountLanguageComponent
import com.metacards.metacards.features.advbanner.domain.entity.AccountAdvBannerType
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakeAccountComponent : AccountComponent {
    override val user: StateFlow<User?> = MutableStateFlow(null)
    override val notificationToggleState: StateFlow<AccountComponent.NotificationToggleState> =
        MutableStateFlow(AccountComponent.NotificationToggleState.On)
    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        FakeDialogControl(FakeDefaultDialogComponent())
    override val languageBottomSheetControl:
        BottomSheetControl<AccountLanguageComponent.Config, AccountLanguageComponent> =
        FakeBottomSheetControl(FakeAccountLanguageComponent())

    override val appLanguage: StateFlow<AppLanguage> = MutableStateFlow(AppLanguage.ESP)
    override val accountAdvBanner = MutableStateFlow(null)

    override fun onProfileBlockClick() = Unit
    override fun onPromocodesClick() = Unit
    override fun onAdvBannerClick(type: AccountAdvBannerType) = Unit
    override fun onSubscriptionClick() = Unit
    override fun onBuyDecksClick() = Unit
    override fun onLessonsClick() = Unit
    override fun onFavouriteCardsClick() = Unit
    override fun onNotificationToggleClick(isSubscribed: Boolean) = Unit
    override fun onFeedbackClick() = Unit
    override fun onLanguageClick() = Unit
    override fun onRateClick() = Unit
    override fun onAboutTheAppClick() = Unit
    override fun onAddYourDeckClick() = Unit
    override fun onInstagramClick() = Unit
    override fun onFacebookClick() = Unit
    override fun onInternetClick() = Unit
    override fun onSpecialDeckClick() = Unit
}