package com.metacards.metacards.features.deeplink

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.deeplink.DeeplinkService
import com.metacards.metacards.core.deeplink.LinkGenerator
import com.metacards.metacards.core.network.createDefaultJson
import com.metacards.metacards.core.utils.KoinConsts
import com.metacards.metacards.features.deck.domain.interactor.GetCardWithFavoriteInteractor
import com.metacards.metacards.features.deck.domain.interactor.IsCardFavoriteInteractor
import com.metacards.metacards.features.deeplink.data.BranchIOLinkGenerator
import com.metacards.metacards.features.deeplink.data.DeeplinkServiceImpl
import com.metacards.metacards.features.deeplink.ui.DeeplinkNavigationComponent
import com.metacards.metacards.features.deeplink.ui.RealDeeplinkNavigationComponent
import org.koin.core.component.get
import org.koin.core.qualifier.named
import org.koin.dsl.module

val deeplinkModule = module {
    single<DeeplinkService> {
        DeeplinkServiceImpl(
            get(),
            get(),
            get(named(KoinConsts.PushJsonName))
        )
    }
    single<LinkGenerator> { BranchIOLinkGenerator(get(), get()) }
    single(named(KoinConsts.PushJsonName)) { createDefaultJson() }
    factory { IsCardFavoriteInteractor(get()) }
    factory { GetCardWithFavoriteInteractor(get()) }
}

fun ComponentFactory.createDeeplinkComponent(
    componentContext: ComponentContext,
    onOutput: (DeeplinkNavigationComponent.Output) -> Unit
): DeeplinkNavigationComponent {
    return RealDeeplinkNavigationComponent(componentContext, get(), get(), get(), get(), onOutput)
}