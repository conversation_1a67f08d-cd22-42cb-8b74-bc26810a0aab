package com.metacards.metacards.features.record.domain

import com.metacards.metacards.features.course.domain.repository.CourseRepository
import kotlinx.coroutines.flow.map

class GetUserPassedTestsInteractor(
    private val courseRepository: CourseRepository
) {
    fun execute() = courseRepository.getUserPassedTestsFlow().map { list ->
        list.sortedByDescending { test -> test.resultDate }
    }
}