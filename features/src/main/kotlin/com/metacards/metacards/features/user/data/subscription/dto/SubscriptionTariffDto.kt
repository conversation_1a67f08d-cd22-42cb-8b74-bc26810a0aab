package com.metacards.metacards.features.user.data.subscription.dto

import com.metacards.metacards.core.R
import com.metacards.metacards.core.price.Price
import com.metacards.metacards.core.user.domain.SubscriptionId
import com.metacards.metacards.core.user.domain.SubscriptionTariff
import com.metacards.metacards.core.user.domain.SubscriptionType
import com.metacards.metacards.core.utils.Resource
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.serialization.Serializable

@Serializable
class SubscriptionTariffDto(
    val id: String = "",
    val subscriptionType: String = "",
    val duration: Int = 0,
    val price: Int = 0,
    val storeId: String = "",
    @field:JvmField
    val isActive: Boolean = false,
    val promotionalOfferId: String = ""
) {
    fun copy(
        id: String = this.id,
        subscriptionType: String = this.subscriptionType,
        duration: Int = this.duration,
        price: Int = this.price,
        storeId: String = this.storeId,
        isActive: Boolean = this.isActive,
        promotionalOfferId: String = this.promotionalOfferId
    ): SubscriptionTariffDto =
        SubscriptionTariffDto(id, subscriptionType, duration, price, storeId, isActive, promotionalOfferId)
}

fun SubscriptionTariffDto.toDomain(): SubscriptionTariff {
    val type =
        SubscriptionType.fromString(subscriptionType) ?: SubscriptionType.SUBSCRIPTION_PLAN_THREE_MONTH
    return SubscriptionTariff(
        id = SubscriptionId(id),
        type = type,
        title = StringDesc.Resource(
            when (type) {
                SubscriptionType.SUBSCRIPTION_PLAN_YEAR -> R.string.subscription_plan_year
                SubscriptionType.SUBSCRIPTION_PLAN_HALF_YEAR -> R.string.subscription_plan_half_year
                SubscriptionType.SUBSCRIPTION_PLAN_THREE_MONTH -> R.string.subscription_plan_month
                SubscriptionType.SUBSCRIPTION_PLAN_PROMO -> R.string.subscription_plan_month
            }
        ),
        price = Price(price.toDouble()),
        storeId = storeId,
        pricePerMonth = null,
        priceWithDiscount = Price(price.toDouble()),
        discountPercent = 0,
        order = type.order(),
        offerId = null,
        promotionalOfferId = promotionalOfferId.ifEmpty { null }
    )
}