package com.metacards.metacards.features.account.ui.profile.delete

import androidx.annotation.StringRes
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.features.auth.domain.LoginType
import kotlinx.coroutines.flow.StateFlow

interface ProfileDeleteAccountComponent {
    val userLoginTypes: StateFlow<Set<LoginType>>
    val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent>

    fun onPasswordConfirmed()
    fun startReauthentication(onLoaded: () -> Unit)

    sealed interface Output {
        data object AuthScreenRequested : Output
        data class PasswordConfirmationRequested(
            @StringRes val toolbarTextRes: Int,
            @StringRes val textFieldHeaderRes: Int
        ) : Output

        data class SignInViaWebViewRequested(val url: String, @StringRes val title: Int) : Output
        data object WebViewDismissRequested : Output
    }
}