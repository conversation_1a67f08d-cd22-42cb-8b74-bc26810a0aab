package com.metacards.metacards.features.special_deck.ui

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import com.arkivanov.decompose.extensions.compose.jetpack.stack.Children
import com.metacards.metacards.core.dialog.popup.MetaPopup
import com.metacards.metacards.features.special_deck.ui.card_details.SpecialDeckCardDetailsUi
import com.metacards.metacards.features.special_deck.ui.constellation_obtained.SpecialDeckConstellationObtainedUi
import com.metacards.metacards.features.special_deck.ui.deck_obtained.SpecialDeckDeckObtainedUi
import com.metacards.metacards.features.special_deck.ui.first_card_obtained.SpecialDeckFirstCardObtainedUi
import com.metacards.metacards.features.special_deck.ui.starry_sky.SpecialDeckStarrySkyUi
import com.metacards.metacards.features.special_deck.ui.tutorial.SpecialDeckOnboardingUi

@Composable
fun SpecialDeckUi(
    component: SpecialDeckComponent,
    modifier: Modifier = Modifier
) {
    val childStack by component.childStack.collectAsState()

    Children(
        stack = childStack,
        modifier = modifier
    ) { child ->
        when (val instance = child.instance) {
            is SpecialDeckComponent.Child.StarDetails -> SpecialDeckCardDetailsUi(instance.component)
            is SpecialDeckComponent.Child.Onboarding -> SpecialDeckOnboardingUi(instance.component)
            is SpecialDeckComponent.Child.StarrySky -> SpecialDeckStarrySkyUi(instance.component)
            is SpecialDeckComponent.Child.DeckObtained -> SpecialDeckDeckObtainedUi(instance.component)
        }
    }

    MetaPopup(dialogControl = component.constellationObtainedControl) {
        SpecialDeckConstellationObtainedUi(it)
    }

    MetaPopup(dialogControl = component.firstCardObtainedControl) {
        SpecialDeckFirstCardObtainedUi(it)
    }
}