package com.metacards.metacards.features.auth.data

import com.google.firebase.auth.EmailAuthProvider
import com.google.firebase.auth.FirebaseAuthProvider
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.auth.GoogleAuthProvider
import com.metacards.metacards.core.user.domain.AuthUserInfo
import com.metacards.metacards.core.user.domain.Email
import com.metacards.metacards.core.user.domain.UserId
import com.metacards.metacards.features.auth.domain.AuthUser
import com.metacards.metacards.features.auth.domain.LoginType
import com.metacards.metacards.features.auth.domain.SSOType

private const val VK_UID_PREFIX = "vk_"
private const val OK_UID_PREFIX = "ok_"
private const val YANDEX_UID_PREFIX = "ya_"

fun FirebaseUser?.toAuthUser(): AuthUser? {
    if (this == null) return null

    return AuthUser(
        userId = UserId(uid),
        isEmailVerified = isEmailVerified,
        authLoginTypes = providerData.mapNotNull { info ->
            when (info.providerId) {
                EmailAuthProvider.PROVIDER_ID -> {
                    info.email?.let { LoginType.Email(it) }
                }

                GoogleAuthProvider.PROVIDER_ID -> {
                    LoginType.SSO(SSOType.Google, email)
                }

                FirebaseAuthProvider.PROVIDER_ID -> {
                    when (info.uid.substring(VK_UID_PREFIX.indices)) {
                        VK_UID_PREFIX -> LoginType.SSO(SSOType.VK)
                        OK_UID_PREFIX -> LoginType.SSO(SSOType.OK)
                        YANDEX_UID_PREFIX -> LoginType.SSO(SSOType.Yandex)
                        else -> null
                    }
                }

                else -> null
            }
        }.toSet()
    )
}

fun AuthUser?.toAuthUserInfo(): AuthUserInfo? {
    if (this == null) return null
    val email = getLoginByType<LoginType.Email>()?.value
    val emailBySSO = getLoginsByType<LoginType.SSO>().find { it.email != null }?.value
    val resultEmail = email ?: emailBySSO
    return AuthUserInfo(
        userId = userId,
        email = resultEmail?.let { Email(it) },
    )
}