package com.metacards.metacards.features.auth.ui.auth_type.email

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.user.domain.Email
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.features.R
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.auth.domain.email.HasAccountByEmailInteractor
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import ru.mobileup.kmm_form_validation.control.InputControl
import ru.mobileup.kmm_form_validation.options.ImeAction
import ru.mobileup.kmm_form_validation.options.KeyboardOptions
import ru.mobileup.kmm_form_validation.options.KeyboardType
import ru.mobileup.kmm_form_validation.validation.control.isNotBlank
import ru.mobileup.kmm_form_validation.validation.control.regex
import ru.mobileup.kmm_form_validation.validation.form.FormValidator
import ru.mobileup.kmm_form_validation.validation.form.formValidator

class RealEmailEnterComponent(
    componentContext: ComponentContext,
    private val isSignIn: Boolean,
    private val isResetPassword: Boolean,
    private val errorHandler: ErrorHandler,
    private val hasAccountByEmailInteractor: HasAccountByEmailInteractor,
    private val analyticsService: AnalyticsService,
    private val onOutput: (EmailEnterComponent.Output) -> Unit,
) : ComponentContext by componentContext, EmailEnterComponent {

    companion object {
        private const val EMAIL_REGEX_PATTERN =
            "[a-zA-Z0-9+._%\\-]{1,256}@[a-zA-Z0-9][a-zA-Z0-9\\-]{0,64}(\\.[a-zA-Z0-9][a-zA-Z0-9\\-]{0,25})+"
    }

    private val isLoading: MutableStateFlow<Boolean> = MutableStateFlow(false)

    private val debounce = Debounce()

    override val emailInputControl: InputControl = InputControl(
        coroutineScope = componentScope,
        keyboardOptions = KeyboardOptions(
            autoCorrect = false,
            keyboardType = KeyboardType.Email,
            imeAction = ImeAction.Done
        )
    )

    private val formValidator: FormValidator = componentScope.formValidator {
        input(emailInputControl, required = true) {
            isNotBlank(StringDesc.Resource(R.string.email_enter_error))
            regex(
                regex = EMAIL_REGEX_PATTERN.toRegex(),
                errorMessage = StringDesc.Resource(R.string.email_enter_error)
            )
        }
    }

    override val showCheckbox: MutableStateFlow<Boolean> = MutableStateFlow(!isSignIn)

    override val checkboxState: MutableStateFlow<Boolean> = MutableStateFlow(false)

    private val isNextButtonEnabled = computed(
        checkboxState,
        showCheckbox,
        emailInputControl.error
    ) { checkbox, showCheckbox, hasError ->
        hasError == null && (checkbox || !showCheckbox)
    }

    override val nextButtonState: StateFlow<ButtonState> =
        computed(isNextButtonEnabled, isLoading) { enabled, loading ->
            ButtonState.create(!enabled, loading)
        }

    override val showSignInButton: MutableStateFlow<Boolean> = MutableStateFlow(false)

    override val showSignUpButton: MutableStateFlow<Boolean> = MutableStateFlow(false)

    init {
        with(emailInputControl) { text.onEach { error.value = null }.launchIn(componentScope) }
    }

    private suspend fun nextButtonAction() {
        if (!formValidator.validate().isValid) return

        analyticsService.logEvent(
            if (isSignIn) {
                if (isResetPassword) {
                    AnalyticsEvent.AuthMailForgetPasswordSend
                } else {
                    AnalyticsEvent.AuthMailNextEvent
                }
            } else {
                AnalyticsEvent.RegistrationMailNextEvent
            }
        )

        val email = Email(emailInputControl.text.value)
        val hasAccount = hasAccountByEmailInteractor.execute(email)

        if (isSignIn) {
            if (!hasAccount) {
                emailInputControl.error.value =
                    StringDesc.Resource(R.string.email_enter_has_account_error)
                showSignUpButton.value = true
            } else {
                onOutput(EmailEnterComponent.Output.NextButtonPressed(email))
                showSignUpButton.value = false
            }
        } else {
            if (hasAccount) {
                emailInputControl.error.value =
                    StringDesc.Resource(R.string.email_enter_no_has_account_error)
                showSignInButton.value = true
            } else {
                onOutput(EmailEnterComponent.Output.NextButtonPressed(email))
                showSignInButton.value = false
            }
        }
    }

    override fun onNextButtonClick() {
        DebounceClick(debounce, "onNextButtonClick", errorHandler, isLoading) {
            nextButtonAction()
        }
    }

    override fun onSignUpButtonClick() {
        analyticsService.logEvent(
            if (isResetPassword) {
                AnalyticsEvent.AuthMailForgetPasswordRegEvent
            } else {
                AnalyticsEvent.AuthMailRegEvent
            }
        )
        onOutput(EmailEnterComponent.Output.SignUpRequested)
    }

    override fun onSignInButtonClick() {
        analyticsService.logEvent(AnalyticsEvent.RegistrationMailAuthEvent)
        onOutput(EmailEnterComponent.Output.SignInRequested)
    }

    override fun onCheckboxStateChange(checked: Boolean) {
        checkboxState.value = checked
    }

    override fun openLink(url: String) {
        onOutput(EmailEnterComponent.Output.WebViewRequested(url))
    }

    override fun onGoBackClick() {
        onOutput(EmailEnterComponent.Output.GoBackRequested)
    }

    override fun onFocusRequest() = emailInputControl.requestFocus()
}
