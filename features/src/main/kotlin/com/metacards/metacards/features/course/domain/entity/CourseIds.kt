package com.metacards.metacards.features.course.domain.entity

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
@JvmInline
value class CourseId(val value: String) : Parcelable

@Parcelize
@JvmInline
value class CourseContentId(val value: String) : Parcelable

@Parcelize
@JvmInline
value class CourseThemeId(val value: String) : Parcelable

@Parcelize
@JvmInline
value class CourseTestDocumentId(val value: String) : Parcelable