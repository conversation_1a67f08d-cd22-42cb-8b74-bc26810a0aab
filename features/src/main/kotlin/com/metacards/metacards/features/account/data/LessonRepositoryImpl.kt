package com.metacards.metacards.features.account.data

import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.mapLoadable
import com.metacards.metacards.features.account.data.data_source.LessonsDataSource
import com.metacards.metacards.features.account.data.dto.toDomain
import com.metacards.metacards.features.account.domain.entity.LessonCategory
import com.metacards.metacards.features.account.domain.entity.LessonCategoryId
import com.metacards.metacards.features.account.domain.repository.LessonRepository
import com.metacards.metacards.features.deck.data.dto.toDomain
import com.metacards.metacards.features.deck.domain.entity.Lesson
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

class LessonRepositoryImpl(
    private val lessonsDataSource: LessonsDataSource
) : LessonRepository {

    override fun getAllLessonCategoriesFlow(): Flow<List<LessonCategory>> {
        return lessonsDataSource.getAllLessonCategoriesFlow()
            .map {
                it.map { lessonCategoryDto -> lessonCategoryDto.toDomain() }
            }
    }

    override fun getCategoryLessonsFlow(categoryId: LessonCategoryId): Flow<LoadableState<List<Lesson>>> {
        return lessonsDataSource.getCategoryLessonsFlow(categoryId.value)
            .mapLoadable {
                it?.map { lessonDto -> lessonDto.toDomain() }
            }
    }

    override fun getLessonCategoryByIdFlow(categoryId: LessonCategoryId): Flow<LoadableState<LessonCategory>> {
        return lessonsDataSource.getLessonCategoryByIdFlow(categoryId.value)
            .mapLoadable {
                it?.toDomain()
            }
    }
}