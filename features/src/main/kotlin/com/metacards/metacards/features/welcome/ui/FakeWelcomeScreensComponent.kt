package com.metacards.metacards.features.welcome.ui

import dev.icerock.moko.resources.desc.Raw
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.MutableSharedFlow

class FakeWelcomeScreensComponent : WelcomeScreensComponent {
    override val pages: List<WelcomeScreenPage> = listOf(
        WelcomeScreenPage(
            title = StringDesc.Raw("Что такое МАК"),
            caption = StringDesc.Raw("Это \"Вкусно и точка\" здорового человека")
        )
    )

    override val pageToOpen = MutableSharedFlow<Int>()

    override fun onPageChanged(page: Int) = Unit
    override fun onProceedClick(pageToOpen: Int) = Unit
    override fun onAuthClick() = Unit
    override fun onAuthLaterClick() = Unit
}