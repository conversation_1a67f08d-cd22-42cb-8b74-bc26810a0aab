package com.metacards.metacards.features.special_deck.ui

import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.special_deck.ui.constellation_obtained.SpecialDeckConstellationObtainedComponent
import com.metacards.metacards.features.special_deck.ui.deck_obtained.SpecialDeckDeckObtainedComponent
import com.metacards.metacards.features.special_deck.ui.first_card_obtained.SpecialDeckFirstCardObtainedComponent
import com.metacards.metacards.features.special_deck.ui.card_details.SpecialDeckCardDetailsComponent
import com.metacards.metacards.features.special_deck.ui.starry_sky.SpecialDeckStarrySkyComponent
import com.metacards.metacards.features.special_deck.ui.tutorial.SpecialDeckOnboardingComponent
import kotlinx.coroutines.flow.StateFlow

interface SpecialDeckComponent {

    val childStack: StateFlow<ChildStack<*, Child>>

    val constellationObtainedControl: DialogControl<*, SpecialDeckConstellationObtainedComponent>
    val firstCardObtainedControl: DialogControl<*, SpecialDeckFirstCardObtainedComponent>

    sealed interface Child {
        class StarDetails(val component: SpecialDeckCardDetailsComponent) : Child
        class Onboarding(val component: SpecialDeckOnboardingComponent) : Child
        class StarrySky(val component: SpecialDeckStarrySkyComponent) : Child
        class DeckObtained(val component: SpecialDeckDeckObtainedComponent) : Child
    }

    sealed interface Output {
        data object CloseRequested : Output
        data class LayoutRequested(val deckId: DeckId) : Output
        data class SubscriptionBottomSheetRequested(val isSpecialDeckCardRequested: Boolean) : Output
    }
}
