package com.metacards.metacards.features.special_deck.ui.starry_sky.widgets

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCard
import dev.icerock.moko.resources.desc.strResDesc
import kotlin.random.Random

private const val STAR_PULSE_ANIMATION_DURATION_MIN = 1000
private const val STAR_PULSE_ANIMATION_DURATION_MAX = 2000

@Composable
fun SpecialDeckStarrySkyCards(
    modifier: Modifier = Modifier,
    cards: List<SpecialDeckCard>,
    receivedCards: Set<CardId>,
    scaleCoefficient: Float,
    isCanSelectNewStar: Boolean,
    onCardClick: (SpecialDeckCard) -> Unit
) {
    val userHasNotCards = receivedCards.isEmpty()

    cards.forEach { card ->
        val isFirstCard = card.id == cards.firstOrNull()?.id
        val starTint = when {
            card.id in receivedCards -> CustomTheme.colors.star.open
            else -> CustomTheme.colors.star.disable
        }

        Box(
            modifier = modifier
                .offset {
                    IntOffset(
                        x = card.coordinates.x.times(scaleCoefficient).toInt(),
                        y = card.coordinates.y.times(scaleCoefficient).toInt()
                    )
                }
        ) {
            when {
                userHasNotCards -> {
                    if (isFirstCard) {
                        PulsingStar(
                            card = card,
                            onClick = { onCardClick(card) }
                        )
                    } else {
                        NormalStar(
                            onClick = { onCardClick(card) },
                            card = card,
                            tint = starTint
                        )
                    }
                }

                isCanSelectNewStar -> {
                    if (card.id in receivedCards) {
                        NormalStar(
                            onClick = { onCardClick(card) },
                            card = card,
                            tint = starTint
                        )
                    } else {
                        PulsingStar(
                            card = card,
                            onClick = { onCardClick(card) }
                        )
                    }
                }

                else -> {
                    NormalStar(
                        onClick = { onCardClick(card) },
                        card = card,
                        tint = starTint
                    )
                }
            }
        }
    }

    if (userHasNotCards) {
        cards.firstOrNull()?.let {
            FirstCardTooltip(card = it, scaleCoefficient = scaleCoefficient)
        }
    }
}

@Composable
private fun FirstCardTooltip(
    card: SpecialDeckCard,
    scaleCoefficient: Float
) {
    Box(
        modifier = Modifier
            .offset {
                IntOffset(
                    x = card.coordinates.x
                        .plus(25)
                        .times(scaleCoefficient)
                        .toInt(),
                    y = card.coordinates.y
                        .minus(20)
                        .times(scaleCoefficient)
                        .toInt()
                )
            }
            .background(CustomTheme.colors.background.hint, RoundedCornerShape(10.dp))
            .drawWithContent {
                drawContent()
                val path = Path().apply {
                    moveTo(15 * density, size.height)
                    lineTo(15 * density, size.height + (10 * density))
                    lineTo(25 * density, size.height)
                }
                drawPath(path, CustomTheme.colors.background.hint)
            },
    ) {
        Text(
            text = R.string.special_deck_starry_star_press_me_tooltip.strResDesc().localizedByLocal(),
            style = CustomTheme.typography.caption.small,
            color = CustomTheme.colors.text.caption,
            modifier = Modifier
                .padding(horizontal = 4.dp, vertical = 8.dp)
        )
    }
}

@Composable
private fun NormalStar(
    onClick: () -> Unit,
    card: SpecialDeckCard,
    tint: Color,
) {
    IconButton(
        onClick = onClick,
    ) {
        Icon(
            painter = painterResource(card.size.getDrawableRes()),
            contentDescription = null,
            tint = tint
        )
    }
}

@Composable
private fun PulsingStar(
    onClick: () -> Unit,
    card: SpecialDeckCard
) {
    val infiniteTransition = rememberInfiniteTransition(label = "card_pulse_transition_${card.id}")
    val pulseCardSize by infiniteTransition.animateFloat(
        initialValue = 1f,
        targetValue = 32f,
        animationSpec = infiniteRepeatable(
            animation = tween(
                Random.nextInt(STAR_PULSE_ANIMATION_DURATION_MIN, STAR_PULSE_ANIMATION_DURATION_MAX),
                easing = LinearEasing
            ),
            repeatMode = RepeatMode.Reverse
        ),
        label = "card_pulse_label_${card.id}"
    )
    Box(
        modifier = Modifier
            .clip(CircleShape)
            .clickable(onClick = onClick),
        contentAlignment = Alignment.Center
    ) {
        Icon(
            painter = painterResource(R.drawable.ic_star_bg),
            contentDescription = null,
        )
        Icon(
            painter = painterResource(card.size.getDrawableRes()),
            contentDescription = null,
            tint = CustomTheme.colors.star.active,
            modifier = Modifier
                .size(pulseCardSize.dp)
        )
    }
}