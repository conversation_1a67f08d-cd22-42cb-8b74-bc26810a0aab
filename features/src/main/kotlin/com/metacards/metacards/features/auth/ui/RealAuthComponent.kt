package com.metacards.metacards.features.auth.ui

import android.os.Parcelable
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.router.stack.bringToFront
import com.arkivanov.decompose.router.stack.childStack
import com.arkivanov.decompose.router.stack.pop
import com.arkivanov.decompose.router.stack.replaceAll
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.preferences.PreferencesService
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.toStateFlow
import com.metacards.metacards.features.R
import com.metacards.metacards.features.auth.createMainAuthComponent
import com.metacards.metacards.features.auth.createSignInComponent
import com.metacards.metacards.features.auth.createSignUpComponent
import com.metacards.metacards.features.auth.ui.main_auth.MainAuthComponent
import com.metacards.metacards.features.auth.ui.sign_in.SignInComponent
import com.metacards.metacards.features.auth.ui.sign_up.SignUpComponent
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize

class RealAuthComponent(
    componentContext: ComponentContext,
    startCommand: AuthComponent.StartCommand?,
    preferencesService: PreferencesService,
    private val onOutput: (AuthComponent.Output) -> Unit,
    private val componentFactory: ComponentFactory
) : ComponentContext by componentContext, AuthComponent {
    private val navigation = StackNavigation<ChildConfig>()
    private val initialConfiguration: ChildConfig = when (startCommand) {
        is AuthComponent.StartCommand.OpenEmailVerification -> ChildConfig.SignUp(
            SignUpComponent.StartCommand.OpenEmailVerification(startCommand.email)
        )

        AuthComponent.StartCommand.OpenWithAnimation -> ChildConfig.MainAuth(true)
        null -> ChildConfig.MainAuth(false)
    }

    override val childStack = childStack(
        source = navigation,
        initialConfiguration = initialConfiguration,
        handleBackButton = true,
        childFactory = ::createChild
    ).toStateFlow(lifecycle)

    init {
        componentScope.launch { preferencesService.disableAuthScreenLaunchFlag() }
    }

    private fun createChild(
        config: ChildConfig,
        componentContext: ComponentContext
    ): AuthComponent.Child = when (config) {
        is ChildConfig.SignIn -> {
            AuthComponent.Child.SignIn(
                componentFactory.createSignInComponent(
                    componentContext,
                    ::onSignInOutput,
                )
            )
        }

        is ChildConfig.SignUp -> {
            AuthComponent.Child.SignUp(
                componentFactory.createSignUpComponent(
                    componentContext,
                    ::onSignUpOutput,
                    config.startCommand
                )
            )
        }

        is ChildConfig.MainAuth -> {
            AuthComponent.Child.MainAuth(
                componentFactory.createMainAuthComponent(
                    componentContext,
                    config.shouldAnimate,
                    ::onMainAuthOutput
                )
            )
        }
    }

    private fun onMainAuthOutput(output: MainAuthComponent.Output) {
        when (output) {
            MainAuthComponent.Output.MainPageRequested -> {
                onOutput(AuthComponent.Output.MainPageRequested)
            }

            is MainAuthComponent.Output.SignInRequested -> {
                navigation.bringToFront(ChildConfig.SignIn)
            }

            is MainAuthComponent.Output.SignUpRequested -> {
                navigation.bringToFront(ChildConfig.SignUp())
            }

            is MainAuthComponent.Output.SignInViaWebViewRequested -> {
                onOutput(AuthComponent.Output.WebViewRequested(output.url, output.title))
            }

            is MainAuthComponent.Output.EmailVerificationRequested -> {
                navigation.bringToFront(
                    ChildConfig.SignUp(
                        SignUpComponent.StartCommand.OpenEmailVerification(output.email)
                    )
                )
            }
        }
    }

    private fun onSignUpOutput(output: SignUpComponent.Output) {
        when (output) {
            is SignUpComponent.Output.SignInRequested -> {
                navigation.bringToFront(ChildConfig.SignIn)
            }

            SignUpComponent.Output.MainScreenRequested -> {
                onOutput(AuthComponent.Output.MainPageRequested)
            }

            SignUpComponent.Output.AuthScreenRequested -> {
                navigation.replaceAll(ChildConfig.MainAuth(false))
            }

            is SignUpComponent.Output.WebViewRequested -> {
                onOutput(AuthComponent.Output.WebViewRequested(output.url, R.string.empty))
            }

            SignUpComponent.Output.GoBackRequested -> navigation.pop()
        }
    }

    private fun onSignInOutput(output: SignInComponent.Output) {
        when (output) {
            is SignInComponent.Output.SignUpRequested -> {
                navigation.bringToFront(ChildConfig.SignUp())
            }

            SignInComponent.Output.MainScreenRequested -> onOutput(AuthComponent.Output.MainPageRequested)

            is SignInComponent.Output.EmailVerificationRequested -> {
                navigation.bringToFront(
                    ChildConfig.SignUp(
                        SignUpComponent.StartCommand.OpenEmailVerification(output.email)
                    )
                )
            }

            is SignInComponent.Output.WebViewRequested -> {
                onOutput(AuthComponent.Output.WebViewRequested(output.url, R.string.empty))
            }

            SignInComponent.Output.GoBackRequested -> navigation.pop()
        }
    }

    sealed interface ChildConfig : Parcelable {
        @Parcelize
        data class SignUp(
            val startCommand: SignUpComponent.StartCommand? = null
        ) : ChildConfig

        @Parcelize
        data object SignIn : ChildConfig

        @Parcelize
        data class MainAuth(val shouldAnimate: Boolean) : ChildConfig
    }
}
