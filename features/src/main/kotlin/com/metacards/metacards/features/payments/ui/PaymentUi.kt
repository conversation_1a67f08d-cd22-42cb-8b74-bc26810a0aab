package com.metacards.metacards.features.payments.ui

import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.metacards.metacards.core.bottom_sheet.ModalBottomSheet
import com.metacards.metacards.features.payments.ui.bottom_sheet.PaymentBottomSheetUi

@Composable
fun PaymentComponent(modifier: Modifier = Modifier, component: PaymentComponent) {
    Box(modifier = modifier) {
        when (component) {
            is InternationalPaymentComponent -> {
                // TODO: disable user interactions when payment is not completed
            }

            is RussianPaymentComponent -> {
                ModalBottomSheet(
                    control = component.paymentBottomSheetControl,
                    sheetBackgroundColor = Color.Transparent
                ) {
                    PaymentBottomSheetUi(component = it)
                }
            }
        }
    }
}
