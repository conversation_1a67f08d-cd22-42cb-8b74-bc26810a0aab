package com.metacards.metacards.features.payments

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.features.payments.data.BillingServiceImpl
import com.metacards.metacards.features.payments.data.PaymentRepositoryImpl
import com.metacards.metacards.features.payments.domain.BillingService
import com.metacards.metacards.features.payments.domain.GetPaymentUrlForDeckInteractor
import com.metacards.metacards.features.payments.domain.GetPaymentUrlForSubscriptionInteractor
import com.metacards.metacards.features.payments.domain.PaymentRepository
import com.metacards.metacards.features.payments.ui.InternationalPaymentComponent
import com.metacards.metacards.features.payments.ui.PaymentComponent
import com.metacards.metacards.features.payments.ui.RussianPaymentComponent
import com.metacards.metacards.features.payments.ui.bottom_sheet.PaymentBottomSheetComponent
import com.metacards.metacards.features.payments.ui.bottom_sheet.RealPaymentBottomSheetComponent
import org.koin.core.component.get
import org.koin.dsl.module

val paymentModule = module {
    single<BillingService> { BillingServiceImpl(get(), get(), get()) }
    single<PaymentRepository> { PaymentRepositoryImpl(get()) }
    factory { GetPaymentUrlForDeckInteractor(get()) }
    factory { GetPaymentUrlForSubscriptionInteractor(get()) }
}

fun ComponentFactory.createPaymentBottomSheetComponent(
    componentContext: ComponentContext,
    config: PaymentBottomSheetComponent.Config,
    onOutput: (PaymentBottomSheetComponent.Output) -> Unit
): PaymentBottomSheetComponent {
    return RealPaymentBottomSheetComponent(
        componentContext,
        config,
        get(),
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createPaymentComponent(
    componentContext: ComponentContext,
    onOutput: (PaymentComponent.Output) -> Unit
): PaymentComponent {
    return createInternationalPaymentComponent(componentContext, onOutput)
}

fun ComponentFactory.createInternationalPaymentComponent(
    componentContext: ComponentContext,
    onOutput: (PaymentComponent.Output) -> Unit
): PaymentComponent {
    return InternationalPaymentComponent(
        componentContext,
        get(),
        get(),
        get(),
        get(),
        onOutput
    )
}

fun ComponentFactory.createRussianPaymentComponent(
    componentContext: ComponentContext,
    onOutput: (PaymentComponent.Output) -> Unit
): PaymentComponent {
    return RussianPaymentComponent(
        componentContext,
        this,
        onOutput
    )
}