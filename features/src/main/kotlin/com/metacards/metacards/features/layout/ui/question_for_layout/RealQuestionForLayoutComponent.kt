package com.metacards.metacards.features.layout.ui.question_for_layout

import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.doOnDestroy
import com.arkivanov.essenty.lifecycle.doOnStart
import com.metacards.metacards.core.utils.UserInputTransformation
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.layout.domain.CardSource
import com.metacards.metacards.features.tutorial.data.TutorialMessageService
import com.metacards.metacards.features.tutorial.data.TutorialStep
import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import ru.mobileup.kmm_form_validation.control.InputControl
import ru.mobileup.kmm_form_validation.options.ImeAction
import ru.mobileup.kmm_form_validation.options.KeyboardCapitalization
import ru.mobileup.kmm_form_validation.options.KeyboardOptions
import ru.mobileup.kmm_form_validation.options.KeyboardType

class RealQuestionForLayoutComponent(
    componentContext: ComponentContext,
    private val deckId: DeckId,
    private val tutorialMessageService: TutorialMessageService,
    private val analyticsService: AnalyticsService,
    private val onOutput: (QuestionForLayoutComponent.Output) -> Unit,
    override val maxSymbolsCount: Int = MAX_SYMBOLS_COUNT,
) : ComponentContext by componentContext, QuestionForLayoutComponent {
    companion object {
        private const val MAX_SYMBOLS_COUNT = 72
    }

    private var onNextClicked = false

    override val questionInputControl: InputControl = InputControl(
        coroutineScope = componentScope,
        singleLine = false,
        keyboardOptions = KeyboardOptions(
            capitalization = KeyboardCapitalization.Sentences,
            autoCorrect = true,
            keyboardType = KeyboardType.Text,
            imeAction = ImeAction.Default
        ),
        maxLength = maxSymbolsCount,
        textTransformation = UserInputTransformation
    ).apply { requestFocus() }

    override val currentSymbolsCount: StateFlow<Int> = computed(questionInputControl.text) {
        it.length
    }

    val msg = tutorialMessageService.tutorialMessageFlow
        .stateIn(componentScope, SharingStarted.Eagerly, null)

    override val tutorialMessage: StateFlow<TutorialMessage?> = combine(
        tutorialMessageService.tutorialStepFlow, tutorialMessageService.tutorialMessageFlow
    ) { step, message ->
        if (step == TutorialStep.QUESTION) message else null
    }.stateIn(componentScope, SharingStarted.Lazily, null)

    init {
        lifecycle.doOnStart {
            tutorialMessageService.handleQuestionForLayoutComponentTutorial(componentScope)
        }
        lifecycle.doOnDestroy {
            if (!onNextClicked) {
                tutorialMessageService.processCloseQuestionForLayoutComponentTutorial()
            }
        }
    }

    override fun onNextButtonClick() {
        onNextClicked = true
        analyticsService.logEvent(
            AnalyticsEvent.SetupQuestionNextEvent(
                isQuestionInput = questionInputControl.text.value.isNotEmpty()
            )
        )
        onOutput(
            QuestionForLayoutComponent.Output.LayoutRequested(
                CardSource.Deck(deckId),
                questionInputControl.text.value
            )
        )
    }

    override fun onCloseButtonClick() {
        onOutput(QuestionForLayoutComponent.Output.OnDismiss)
    }
}