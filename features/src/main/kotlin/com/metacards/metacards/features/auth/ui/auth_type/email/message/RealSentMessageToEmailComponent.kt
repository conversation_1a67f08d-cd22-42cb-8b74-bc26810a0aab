package com.metacards.metacards.features.auth.ui.auth_type.email.message

import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.doOnCreate
import com.arkivanov.essenty.lifecycle.doOnResume
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.createDefaultDialogComponent
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.model.DialogData
import com.metacards.metacards.core.dialog.dialogControl
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.features.R
import com.metacards.metacards.features.auth.domain.AuthUser
import com.metacards.metacards.features.auth.domain.GetAuthUserInteractor
import com.metacards.metacards.features.auth.domain.LogoutInteractor
import com.metacards.metacards.features.auth.domain.ReloadAuthUserInteractor
import com.metacards.metacards.features.auth.domain.email.SendEmailVerifyMessageInteractor
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class RealSentMessageToEmailComponent(
    componentContext: ComponentContext,
    componentFactory: ComponentFactory,
    title: StringDesc,
    text: StringDesc,
    shouldAutoClose: Boolean,
    private val onOutput: (SentMessageToEmailComponent.Output) -> Unit,
    private val reloadAuthUserInteractor: ReloadAuthUserInteractor,
    private val errorHandler: ErrorHandler,
    getAuthUserInteractor: GetAuthUserInteractor,
    sendEmailVerifyMessageInteractor: SendEmailVerifyMessageInteractor,
    logoutInteractor: LogoutInteractor
) : ComponentContext by componentContext, SentMessageToEmailComponent {
    private val authUser: StateFlow<AuthUser?> = getAuthUserInteractor.execute()

    override val title: MutableStateFlow<StringDesc> = MutableStateFlow(title)
    override val text: MutableStateFlow<StringDesc> = MutableStateFlow(text)

    init {
        lifecycle.doOnCreate {
            componentScope.safeLaunch(errorHandler, showError = false) {
                sendEmailVerifyMessageInteractor.execute()
            }
        }

        if (shouldAutoClose) {
            lifecycle.doOnResume {
                componentScope.safeLaunch(errorHandler, showError = false) {
                    authUser.collect { user ->
                        if (user != null && !user.isEmailVerified) {
                            reloadAuthUserInteractor.execute()
                        }
                    }
                }
            }

            componentScope.safeLaunch(errorHandler, showError = false) {
                authUser.collect {
                    if (it?.isEmailVerified == true) {
                        onOutput(SentMessageToEmailComponent.Output.MainScreenRequested)
                    }
                }
            }
        }
    }

    override val dismissDialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        dialogControl(dialogComponentFactory = { config, componentContext, dialogControl ->
            componentFactory.createDefaultDialogComponent(
                componentContext,
                config.data,
                dialogControl::dismiss
            )
        })

    private val dismissDialogConfig by lazy {
        DefaultDialogComponent.Config(
            DialogData(
                title = StringDesc.Resource(R.string.reset_password_dismiss_dialog_title),
                message = StringDesc.Resource(R.string.reset_password_dismiss_dialog_text),
                buttons = listOf(
                    DialogData.Button(
                        title = StringDesc.Resource(R.string.reset_password_dismiss_dialog_cancel),
                        action = dismissDialogControl::dismiss
                    ),
                    DialogData.Button(
                        title = StringDesc.Resource(R.string.reset_password_dismiss_dialog_confirm),
                        action = {
                            logoutInteractor.execute()
                            onOutput(SentMessageToEmailComponent.Output.AuthScreenRequested)
                        }
                    )
                )
            )
        )
    }

    override fun onCloseButtonClick() {
        dismissDialogControl.show(dismissDialogConfig)
    }
}