package com.metacards.metacards.features.course.ui.lesson

import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.FakeDialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.FakeDefaultDialogComponent
import kotlinx.coroutines.flow.MutableStateFlow

class FakeCourseLessonComponent : CourseLessonComponent {

    override val courseName = null
    override val cards = MutableStateFlow(CourseLessonCards.mock)
    override val closeDialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        FakeDialogControl(FakeDefaultDialogComponent())
    override val lastCardButtonText: String = ""

    override fun onToNextContentClick() = Unit
    override fun onVideoFullScreenClick(position: Long, videoUrl: String) = Unit
    override fun onCloseClick() = Unit
}