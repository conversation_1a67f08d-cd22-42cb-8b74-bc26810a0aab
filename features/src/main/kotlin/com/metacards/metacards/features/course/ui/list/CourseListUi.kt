package com.metacards.metacards.features.course.ui.list

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.utils.dispatchOnBackPressed
import com.metacards.metacards.core.widget.button.MetaButton
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.course.domain.entity.CourseAvailability
import com.metacards.metacards.features.course.domain.entity.CourseShortData
import com.metacards.metacards.features.course.domain.entity.CourseStatus
import com.metacards.metacards.features.course.domain.entity.CourseTheme
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun CourseListUi(
    component: CourseListComponent,
) {
    val isPremiumUser by component.isPremiumUser.collectAsState()
    val isActiveTheme = component.theme.status == CourseStatus.ACTIVE

    val verticalScrollModifier = if (isActiveTheme) Modifier.verticalScroll(rememberScrollState()) else Modifier

    Column(
        modifier = Modifier
            .safeDrawingPadding()
    ) {
        TopNavigationBar(
            modifier = Modifier.fillMaxWidth(),
            title = component.theme.name,
            leadingIcon = { BackNavigationItem() },
        )

        Spacer(modifier = Modifier.height(16.dp))

        Column(verticalScrollModifier) {
            if (isActiveTheme) {
                ActiveContent(
                    theme = component.theme,
                    isPremiumUser = isPremiumUser,
                    onCourseClick = component::onCourseClick,
                )
            } else {
                SoonContent(component.theme)
            }
        }
    }
}

@Composable
private fun ColumnScope.SoonContent(
    theme: CourseTheme,
) {
    val context = LocalContext.current
    CourseItem(
        coverUrl = theme.coverUrl,
        status = CourseStatus.SOON,
        name = theme.name.localizedByLocal(),
        description = theme.description?.localizedByLocal(),
        shouldBlur = false,
        shouldShowPremiumIcon = false,
        isCompleted = false
    )

    Spacer(modifier = Modifier.height(16.dp))

    Text(
        text = R.string.course_list_soon_content_description
            .strResDesc()
            .localizedByLocal(),
        style = CustomTheme.typography.body.primary,
        color = CustomTheme.colors.text.primary,
        modifier = Modifier
            .padding(horizontal = 32.dp)
    )

    Spacer(modifier = Modifier.weight(1f))

    MetaButton(
        text = R.string.course_list_soon_content_button_view_other_courses
            .strResDesc()
            .localizedByLocal(),
        onClick = { dispatchOnBackPressed(context) },
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp)
    )
}
@Composable
private fun ActiveContent(
    theme: CourseTheme,
    isPremiumUser: Boolean,
    onCourseClick: (CourseShortData) -> Unit,
) {

    theme.description?.let {
        Text(
            text = R.string.course_list_content_header_description
                .strResDesc()
                .localizedByLocal(),
            style = CustomTheme.typography.heading.medium,
            color = CustomTheme.colors.text.primary,
            modifier = Modifier.padding(horizontal = 16.dp)
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = it.localizedByLocal(),
            style = CustomTheme.typography.body.primary,
            color = CustomTheme.colors.text.secondary,
            modifier = Modifier.padding(horizontal = 16.dp)
        )
    }

    Spacer(modifier = Modifier.height(24.dp))

    Text(
        text = R.string.course_list_content_header_other_courses_for_theme
            .strResDesc()
            .localizedByLocal(),
        style = CustomTheme.typography.heading.medium,
        color = CustomTheme.colors.text.primary,
        modifier = Modifier.padding(horizontal = 16.dp)
    )

    Spacer(modifier = Modifier.height(8.dp))

    theme.courses.forEach { course ->
        val shouldBlur = course.availability == CourseAvailability.SUB &&
            !isPremiumUser &&
            course.status == CourseStatus.ACTIVE
        CourseItem(
            coverUrl = course.coverUrl,
            name = course.name.localizedByLocal(),
            status = course.status,
            description = course.shortDescription?.localizedByLocal(),
            shouldBlur = shouldBlur,
            shouldShowPremiumIcon = shouldBlur,
            isCompleted = course.isCompleted,
            onClick = { onCourseClick(course) }
        )
    }
}

@Composable
private fun CourseItem(
    coverUrl: String,
    status: CourseStatus,
    name: String,
    description: String?,
    shouldBlur: Boolean,
    shouldShowPremiumIcon: Boolean,
    isCompleted: Boolean,
    onClick: (() -> Unit)? = null,
) {
    val blurModifier = if (shouldBlur) Modifier.blur(4.dp) else Modifier
    val clickModifier = onClick?.let { Modifier.clickable { it.invoke() } } ?: Modifier

    Box {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .clip(RoundedCornerShape(16.dp))
                .background(CustomTheme.colors.background.primary)
                .then(clickModifier)
                .then(blurModifier)
        ) {
            Spacer(modifier = Modifier.height(16.dp))

            Box(
                modifier = Modifier
                    .padding(horizontal = 16.dp)
            ) {
                AsyncImage(
                    model = coverUrl,
                    contentDescription = null,
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(2.05f)
                        .clip(RoundedCornerShape(16.dp)),
                    contentScale = ContentScale.Crop
                )

                val badgeTextRes = when {
                    status == CourseStatus.SOON -> R.string.course_list_course_item_badge_coming_soon
                    isCompleted -> R.string.course_list_course_item_badge_completed
                    else -> null
                }

                badgeTextRes?.let {
                    Box(
                        modifier = Modifier
                            .padding(16.dp)
                            .background(CustomTheme.colors.button.small, RoundedCornerShape(30.dp)),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = badgeTextRes.strResDesc()
                                .localizedByLocal(),
                            style = CustomTheme.typography.button.small,
                            fontSize = 13.sp,
                            color = CustomTheme.colors.text.caption,
                            modifier = Modifier
                                .padding(vertical = 8.dp, horizontal = 24.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(12.dp))

            Text(
                text = name,
                style = CustomTheme.typography.heading.medium,
                color = CustomTheme.colors.text.primary,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier
                    .padding(horizontal = 16.dp)
            )

            description?.let {
                Spacer(modifier = Modifier.height(8.dp))

                Text(
                    text = it,
                    style = CustomTheme.typography.caption.medium,
                    color = CustomTheme.colors.text.secondary,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier
                        .padding(horizontal = 16.dp)
                        .heightIn(min = 34.dp)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))
        }

        if (shouldShowPremiumIcon) {
            PremiumBadge(
                modifier = Modifier
                    .align(Alignment.Center)
                    .padding(bottom = 72.dp)
            )
        }
    }

    Spacer(modifier = Modifier.height(8.dp))
}

@Composable
fun PremiumBadge(modifier: Modifier = Modifier) {
    Row(
        modifier = modifier
            .background(CustomTheme.colors.button.small, CircleShape)
            .border(
                width = 0.5.dp,
                color = CustomTheme.colors.stroke.secondary,
                shape = CircleShape
            )
            .padding(vertical = 8.dp, horizontal = 20.dp),
        horizontalArrangement = Arrangement.spacedBy(4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            painter = painterResource(id = R.drawable.ic_24_locked),
            contentDescription = null,
            tint = CustomTheme.colors.icons.primary
        )

        Text(
            text = R.string.course_list_course_item_badge_premium
                .strResDesc()
                .localizedByLocal(),
            style = CustomTheme.typography.button.small,
            color = CustomTheme.colors.text.primary
        )
    }
}