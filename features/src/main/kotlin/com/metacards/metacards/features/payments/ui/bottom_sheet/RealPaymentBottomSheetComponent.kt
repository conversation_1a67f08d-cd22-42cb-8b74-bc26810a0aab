package com.metacards.metacards.features.payments.ui.bottom_sheet

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.payments.domain.GetPaymentUrlForDeckInteractor
import com.metacards.metacards.features.payments.domain.GetPaymentUrlForSubscriptionInteractor
import kotlinx.coroutines.flow.MutableStateFlow
import java.net.URL

class RealPaymentBottomSheetComponent(
    componentContext: ComponentContext,
    private val config: PaymentBottomSheetComponent.Config,
    getPaymentUrlForDeckInteractor: GetPaymentUrlForDeckInteractor,
    getPaymentUrlForSubscriptionInteractor: GetPaymentUrlForSubscriptionInteractor,
    errorHandler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    private val analyticsService: AnalyticsService,
    private val onOutput: (PaymentBottomSheetComponent.Output) -> Unit
) : ComponentContext by componentContext, PaymentBottomSheetComponent {

    private val paymentUrl = MutableStateFlow<URL?>(null)

    override val buttonState = MutableStateFlow(ButtonState.Loading)

    init {
        componentScope.safeLaunch(errorHandler) {
            val url = when (config) {
                is PaymentBottomSheetComponent.Config.Deck -> {
                    getPaymentUrlForDeckInteractor.execute(config.deckId)
                }

                is PaymentBottomSheetComponent.Config.Subscription -> {
                    getPaymentUrlForSubscriptionInteractor.execute(config.id)
                }
            }

            if (url != null) {
                paymentUrl.value = url
                buttonState.value = ButtonState.Enabled
            } else {
                buttonState.value = ButtonState.Disabled
            }
        }
    }

    override fun onWebStoreClick() {
        val event = when (config) {
            is PaymentBottomSheetComponent.Config.Subscription -> AnalyticsEvent.SubSiteEvent
            is PaymentBottomSheetComponent.Config.Deck -> AnalyticsEvent.ShopDeckSiteEvent(
                deckId = config.deckId,
                deckName = config.deckName
            )
        }
        analyticsService.logEvent(event)
        paymentUrl.value?.let {
            onOutput(PaymentBottomSheetComponent.Output.WebStoreRequested(it))
        }
    }

    override fun onDismiss() {
        onOutput(PaymentBottomSheetComponent.Output.DismissRequested)
    }
}