package com.metacards.metacards.features.record.data

import android.net.Uri
import com.metacards.metacards.core.user.domain.UserId
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.record.domain.NewRecord
import com.metacards.metacards.features.record.domain.Record
import com.metacards.metacards.features.record.domain.RecordFilter
import com.metacards.metacards.features.record.domain.RecordId
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDate

interface RecordRepository {

    suspend fun getCardPhotoUri(byte: ByteArray): Uri?

    fun getRecordById(userId: UserId, recordId: RecordId): Flow<LoadableState<Record>>

    suspend fun getNewestRecords(filter: RecordFilter, limit: Int): List<Record>

    suspend fun getRecordsOlderThan(
        time: Instant,
        filter: RecordFilter,
        limit: Int,
        inclusive: Boolean = false
    ): List<Record>

    suspend fun getRecordsNewerThan(
        time: Instant,
        filter: RecordFilter,
        limit: Int
    ): List<Record>

    suspend fun getRecordsBetweenTimes(
        startTime: Instant,
        endTime: Instant,
        filter: RecordFilter
    ): List<Record>

    suspend fun getRecordsBetweenDates(
        startDate: LocalDate,
        endDate: LocalDate,
        filter: RecordFilter
    ): List<Record>

    fun getRecordsByCardId(cardId: CardId): Flow<LoadableState<List<Record>>>

    suspend fun createRecord(newRecord: NewRecord)

    suspend fun updateRecord(userId: UserId, updatedRecord: Record)

    suspend fun getDailyCardRecordId(userId: UserId): RecordId?
}