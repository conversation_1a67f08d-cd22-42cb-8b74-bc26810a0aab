@file:UnstableApi

package com.metacards.metacards.features.video_player

import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.webkit.URLUtil
import androidx.activity.ComponentActivity
import androidx.activity.OnBackPressedCallback
import androidx.activity.compose.setContent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Icon
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.features.R
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent

class VideoPlayerActivity : ComponentActivity(), KoinComponent {
    companion object {
        private const val VIDEO_URL_KEY = "video_url_key"
        private const val SHOULD_RESUME_PLAY_KEY = "should_resume_play_key"
        private const val PLAY_ON_START = "play_on_start"
        private const val START_WITH_LANDSCAPE = "start_with_landscape"
        private const val INITIAL_POSITION = "initial_position"

        // prevent recreating when config changes
        private var exoPlayer: ExoPlayer? = null

        fun start(
            videoUrl: String,
            context: Context,
            startWithLandscape: Boolean = false,
            playOnStart: Boolean = false,
            position: Long = 0
        ) {
            Intent(context, VideoPlayerActivity::class.java).apply {
                putExtra(VIDEO_URL_KEY, videoUrl)
                putExtra(START_WITH_LANDSCAPE, startWithLandscape)
                putExtra(INITIAL_POSITION, position)
                putExtra(PLAY_ON_START, playOnStart)
            }.let(context::startActivity)
        }

        val finishPositionCallBack = MutableSharedFlow<Pair<String, Long>>(extraBufferCapacity = 1)
    }

    private var isPlaying = false
    private var shouldResumePlay = false
    private val debounce = Debounce()

    private val playerEventListener = object : Player.Listener {
        override fun onRenderedFirstFrame() {
            super.onRenderedFirstFrame()
            if (intent.getBooleanExtra(PLAY_ON_START, false)) exoPlayer?.play()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (savedInstanceState == null) {
            if (exoPlayer != null) {
                exoPlayer?.release() // if for some reason exoplayer wasn't released onDestroy
                exoPlayer = null
            }
            val startWithLandscape = intent.getBooleanExtra(START_WITH_LANDSCAPE, false)
            if (startWithLandscape) requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        } else {
            shouldResumePlay = savedInstanceState.getBoolean(SHOULD_RESUME_PLAY_KEY)
        }
        WindowCompat.getInsetsController(window, window.decorView)
            .run { hide(WindowInsetsCompat.Type.systemBars()) }
        val videoUrl = intent.getStringExtra(VIDEO_URL_KEY) ?: ""
        if (exoPlayer == null) createPlayer(videoUrl)

        setContent {
            var isControllerVisible by remember { mutableStateOf(false) }
            Box {
                AndroidView(
                    modifier = Modifier
                        .fillMaxSize(),
                    factory = {
                        PlayerView(this@VideoPlayerActivity).apply {
                            player = exoPlayer
                            setShowNextButton(false)
                            setShowPreviousButton(false)
                            setShowBuffering(PlayerView.SHOW_BUFFERING_ALWAYS)
                            setControllerVisibilityListener(
                                PlayerView.ControllerVisibilityListener {
                                    isControllerVisible = it == View.VISIBLE
                                }
                            )
                        }
                    }
                )
                AnimatedVisibility(isControllerVisible) {
                    Icon(
                        painter = painterResource(R.drawable.ic_24_close),
                        contentDescription = null,
                        tint = Color.White,
                        modifier = Modifier
                            .padding(16.dp)
                            .clickable(onClick = ::onCloseClick)
                    )
                }
            }
        }
        if (shouldResumePlay) exoPlayer?.play()

        onBackPressedDispatcher.addCallback(object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                finishPositionCallBack.tryEmit(videoUrl to (exoPlayer?.currentPosition ?: 0L))
                finish()
            }
        })
    }

    override fun onPause() {
        super.onPause()
        isPlaying = exoPlayer?.isPlaying ?: false
        exoPlayer?.pause()
    }

    override fun onDestroy() {
        super.onDestroy()
        if (isFinishing) {
            exoPlayer?.release()
            exoPlayer = null
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putBoolean(SHOULD_RESUME_PLAY_KEY, isPlaying)
    }

    private fun createPlayer(videoUrl: String) {
        if (!URLUtil.isValidUrl(videoUrl)) finish()
        exoPlayer = ExoPlayer.Builder(this).build().apply {
            setMediaItem(MediaItem.fromUri(Uri.parse(videoUrl)))
            val initialPosition = intent.getLongExtra(INITIAL_POSITION, 0L)
            seekTo(initialPosition)
            addListener(playerEventListener)
            prepare()
        }
    }

    private fun onCloseClick() {
        lifecycleScope.launch {
            debounce.click(id = "VideoPlayerClose", block = onBackPressedDispatcher::onBackPressed)
        }
    }
}
