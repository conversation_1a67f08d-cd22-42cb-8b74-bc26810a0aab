package com.metacards.metacards.features.record.ui.create_record.add_card.image_tracking

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalInspectionMode
import com.metacards.metacards.features.record.ui.create_record.add_card.image_tracking.widgets.ArSceneImageTracking

@Composable
fun ImageTrackingUi(modifier: Modifier = Modifier, content: @Composable (Boolean) -> Unit) {
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        val isTracking = remember { mutableStateOf(false) }

        if (LocalInspectionMode.current) {
            ImageTrackingUiPreview(Modifier.matchParentSize())
        } else {
            ArSceneImageTracking(Modifier.matchParentSize()) {
                isTracking.value = true
            }
        }

        content(isTracking.value)
    }
}

@Composable
private fun ImageTrackingUiPreview(modifier: Modifier) {
    Box(
        modifier = modifier
            .background(Color.DarkGray)
    )
}