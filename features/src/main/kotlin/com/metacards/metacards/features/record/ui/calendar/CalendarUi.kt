package com.metacards.metacards.features.record.ui.calendar

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.VerticalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.kizitonwose.calendar.compose.VerticalCalendar
import com.kizitonwose.calendar.compose.rememberCalendarState
import com.kizitonwose.calendar.core.CalendarDay
import com.kizitonwose.calendar.core.CalendarMonth
import com.kizitonwose.calendar.core.DayPosition
import com.kizitonwose.calendar.core.daysOfWeek
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.utils.endToInstant
import com.metacards.metacards.core.utils.getDisplayName
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.SkeletonElement
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.datetime.DayOfWeek
import kotlinx.datetime.Instant
import java.time.LocalDate
import java.time.format.TextStyle
import java.util.Locale

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun CalendarUi(
    component: CalendarComponent,
    modifier: Modifier = Modifier
) {
    val startMonth by component.startMonth.collectAsState()
    val endMonth by component.endMonth.collectAsState()
    val isDataLoaded by component.isDataLoaded.collectAsState()
    val daysWithRecords by component.daysWithRecords.collectAsState()
    val locale = LocalContext.current.resources.configuration.locales[0]

    val pagerState = rememberPagerState(
        initialPage = 0,
        initialPageOffsetFraction = 0f
    ) {
        (
            (endMonth.year - startMonth.year) * 12 +
                (endMonth.monthValue - startMonth.monthValue + 1)
            ) / 2
    }

    LaunchedEffect(pagerState) {
        snapshotFlow { pagerState.settledPage }.collect { page ->
            component.onCalendarPageSettled(page)
        }
    }

    BoxWithFade(
        modifier = modifier.safeDrawingPadding(),
        listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        Column(modifier = Modifier.fillMaxSize()) {
            TopNavigationBar(
                title = R.string.calendar_header.strResDesc(),
                leadingIcon = {
                    BackNavigationItem()
                }
            )

            DaysOfWeekTitle(
                daysOfWeek = daysOfWeek(),
                locale = locale,
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            VerticalPager(
                state = pagerState,
                reverseLayout = true,
                userScrollEnabled = isDataLoaded
            ) { page ->
                VerticalCalendar(
                    modifier = Modifier.padding(horizontal = 16.dp),
                    state = rememberCalendarState(
                        startMonth = endMonth.minusMonths((page * 2 + 1).toLong()),
                        endMonth = endMonth.minusMonths((page * 2).toLong())
                    ),
                    dayContent = {
                        if (it.position == DayPosition.MonthDate) {
                            DayContent(
                                day = it,
                                hasRecord = daysWithRecords.contains(
                                    CalendarDay(it.date, it.position)
                                ),
                                onDateClick = component::onDateClick,
                                isDataLoaded = isDataLoaded && page == pagerState.settledPage
                            )
                        }
                    },
                    monthHeader = { month ->
                        MonthHeader(
                            month,
                            locale,
                            isDataLoaded && page == pagerState.settledPage
                        )
                    },
                    userScrollEnabled = false
                )
            }
        }
    }
}

@Composable
private fun MonthHeader(
    month: CalendarMonth,
    locale: Locale,
    isDataLoaded: Boolean,
    modifier: Modifier = Modifier
) {
    Row(
        horizontalArrangement = Arrangement.Center,
        modifier = modifier.fillMaxWidth()
    ) {
        AnimatedContent(targetState = isDataLoaded) { isLoaded ->
            if (isLoaded) {
                Text(
                    text = month.yearMonth.getDisplayName(locale).localizedByLocal(),
                    color = CustomTheme.colors.text.primary,
                    style = CustomTheme.typography.heading.medium,
                    modifier = Modifier.padding(all = 10.dp)
                )
            } else {
                SkeletonElement(
                    cornerRadius = 8.dp,
                    modifier = Modifier
                        .padding(all = 10.dp)
                        .size(70.dp, 16.dp)
                )
            }
        }
    }
}

@Composable
private fun DaysOfWeekTitle(
    daysOfWeek: List<DayOfWeek>,
    locale: Locale,
    modifier: Modifier = Modifier
) {
    Row(modifier = modifier.fillMaxWidth()) {
        for (dayOfWeek in daysOfWeek) {
            Text(
                text = dayOfWeek.getDisplayName(TextStyle.SHORT, locale).uppercase(),
                color = CustomTheme.colors.text.primary,
                style = CustomTheme.typography.body.primary,
                textAlign = TextAlign.Center,
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@OptIn(ExperimentalAnimationApi::class)
@Composable
private fun DayContent(
    day: CalendarDay,
    hasRecord: Boolean,
    isDataLoaded: Boolean,
    onDateClick: (Instant) -> Unit
) {
    AnimatedContent(targetState = isDataLoaded) { isDaysLoaded ->
        if (isDaysLoaded) {
            Day(day = day, hasRecord = hasRecord, onDateClick = onDateClick)
        } else {
            DaySkeleton()
        }
    }
}

@Composable
private fun Day(
    day: CalendarDay,
    hasRecord: Boolean,
    onDateClick: (Instant) -> Unit
) {
    Box(
        modifier = Modifier
            .aspectRatio(1f)
            .clickable(
                enabled = hasRecord,
                onClick = { onDateClick(day.endToInstant()) }
            ),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(2.dp, Alignment.Top),
            modifier = Modifier
                .background(
                    if (day.date.toEpochDay() == LocalDate.now().toEpochDay()) {
                        CustomTheme.colors.background.primary
                    } else {
                        Color.Transparent
                    },
                    RoundedCornerShape(8.dp)
                )
        ) {
            Text(
                text = day.date.dayOfMonth.toString(),
                color = if (hasRecord) {
                    CustomTheme.colors.text.primary
                } else {
                    CustomTheme.colors.text.secondary
                },
                style = CustomTheme.typography.body.primary,
                modifier = Modifier.padding(start = 8.dp, top = 8.dp, end = 8.dp)
            )

            if (hasRecord) {
                Surface(
                    color = CustomTheme.colors.button.accent,
                    shape = CircleShape,
                    modifier = Modifier
                        .padding(bottom = 8.dp)
                        .size(4.dp),
                    content = {}
                )
            } else {
                Spacer(
                    modifier = Modifier
                        .padding(bottom = 8.dp)
                        .height(4.dp)
                )
            }
        }
    }
}

@Composable
fun DaySkeleton() {
    Box(
        modifier = Modifier.aspectRatio(1f),
        contentAlignment = Alignment.Center
    ) {
        Surface(
            color = CustomTheme.colors.background.placeholder,
            shape = CircleShape,
            modifier = Modifier.size(17.dp),
            content = {}
        )
    }
}

@Preview
@Composable
fun CalendarUiPreview() {
    AppTheme {
        CalendarUi(component = FakeCalendarComponent())
    }
}