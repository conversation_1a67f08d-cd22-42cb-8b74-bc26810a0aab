package com.metacards.metacards.features.auth.ui.auth_type.pass.create_pass

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.text_field.DefaultTextFieldFooterMessage
import com.metacards.metacards.core.widget.text_field.MetaTextField
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.strResDesc
import ru.mobileup.kmm_form_validation.toCompose

@Composable
fun CreatePasswordUi(component: CreatePasswordComponent) {
    val enterButtonState by component.enterButtonState.collectAsState()

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(bottom = 24.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        var showPass by remember { mutableStateOf(false) }
        var showRetryPass by remember { mutableStateOf(false) }

        Text(
            text = R.string.password_create_title.strResDesc().localizedByLocal(),
            style = CustomTheme.typography.caption.large,
            color = CustomTheme.colors.text.primary,
            modifier = Modifier.padding(top = 60.dp)
        )

        MetaTextField(
            inputControl = component.passwordInputControl,
            placeholder = R.string.password_create_placeholder.strResDesc().localizedByLocal(),
            footerMessage = {
                DefaultTextFieldFooterMessage(message = R.string.password_create_hint.strResDesc())
            },
            trailingIcon = { TextVisibilityIcon(showPass) { showPass = !showPass } },
            visualTransformation = if (!showPass) component.passwordInputControl.visualTransformation.toCompose() else VisualTransformation.None
        )

        Text(
            text = R.string.password_create_confirm_title.strResDesc().localizedByLocal(),
            style = CustomTheme.typography.caption.large,
            color = CustomTheme.colors.text.primary
        )

        MetaTextField(
            inputControl = component.confirmPasswordInputControl,
            placeholder = R.string.password_create_placeholder.strResDesc().localizedByLocal(),
            trailingIcon = {
                TextVisibilityIcon(showRetryPass) {
                    showRetryPass = !showRetryPass
                }
            },
            visualTransformation = if (!showRetryPass) component.confirmPasswordInputControl.visualTransformation.toCompose() else VisualTransformation.None
        )

        Spacer(modifier = Modifier.weight(1f))

        MetaAccentButton(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 4.dp),
            text = R.string.password_create_signup.strResDesc().localizedByLocal(),
            onClick = component::onEnterButtonClick,
            state = enterButtonState
        )
    }
}

@Composable
fun TextVisibilityIcon(showPass: Boolean, onClick: () -> Unit) {
    val icon = if (showPass) R.drawable.ic_24_visible else R.drawable.ic_24_invsible

    Icon(
        modifier = Modifier
            .padding(start = 8.dp)
            .clickable(false, onClick = onClick),
        tint = CustomTheme.colors.icons.primary,
        painter = painterResource(id = icon),
        contentDescription = null
    )
}

@Preview
@Composable
fun CreatePasswordUiPreview() {
    val scope = rememberCoroutineScope()

    AppTheme {
        CreatePasswordUi(component = FakeCreatePasswordComponent(scope))
    }
}