package com.metacards.metacards.features.showcase.ui.widgets

import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.placeholder.PlaceholderHighlight
import com.metacards.metacards.core.widget.placeholder.placeholder
import com.metacards.metacards.core.widget.placeholder.shimmer
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.domain.entity.DeckInfoWithCards
import com.metacards.metacards.features.deck.ui.MetaCardDefaults
import dev.icerock.moko.resources.PluralsResource
import dev.icerock.moko.resources.desc.PluralFormatted
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.strResDesc

private const val CARD_WIDTH = 211
private const val IMAGE_ASPECT_RATIO = 0.88f

@Composable
fun ColumnScope.ShowcaseShopItem(
    decksForPurchaseInfoWithCards: List<DeckInfoWithCards>,
    onDeckClick: (DeckInfoWithCards) -> Unit,
) {

    ShowcaseItemContainer(
        title = R.string.showcase_shop_title.strResDesc().localizedByLocal(),
        subtitle = R.string.showcase_shop_subtitle.strResDesc().localizedByLocal()
    ) {
        if (decksForPurchaseInfoWithCards.isEmpty()) {
            CardLoader()
        } else {
            val deckModifier = Modifier
                .width(CARD_WIDTH.dp)
                .clip(RoundedCornerShape(20.dp))
                .background(CustomTheme.colors.background.primary)
            LazyRow(
                modifier = Modifier
                    .fillMaxWidth(),
                contentPadding = PaddingValues(horizontal = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(decksForPurchaseInfoWithCards) { deck ->
                    DeckItem(
                        deck = deck,
                        onDeckClick = { onDeckClick(deck) },
                        modifier = deckModifier
                    )
                }
            }
        }
    }
}

@Composable
fun CardLoader() {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .horizontalScroll(rememberScrollState())
            .padding(horizontal = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        repeat(5) {
            Box(
                modifier = Modifier
                    .size(width = CARD_WIDTH.dp, height = 316.dp)
                    .placeholder(
                        visible = true,
                        color = CustomTheme.colors.background.placeholder,
                        shape = MetaCardDefaults.shape,
                        highlight = PlaceholderHighlight.shimmer(
                            highlightColor = CustomTheme.colors.system.invert.copy(alpha = 0.6f)
                        ),
                        placeholderFadeTransitionSpec = { tween() },
                        contentFadeTransitionSpec = { tween() }
                    )
                    .clip(RoundedCornerShape(16.dp))
            )
        }
    }
}

@Composable
private fun DeckItem(
    deck: DeckInfoWithCards,
    onDeckClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .clickable(onClick = onDeckClick)
    ) {
        Box {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(deck.deckInfo.coverUrl)
                    .placeholder(R.drawable.bg_card_placeholder)
                    .crossfade(MetaCardDefaults.CROSSFADE_DURATION_MILLIS)
                    .build(),
                contentScale = ContentScale.FillBounds,
                contentDescription = null,
                modifier = Modifier
                    .aspectRatio(IMAGE_ASPECT_RATIO)
            )

            if (deck.deckInfo.hasAR) {
                Box(
                    modifier = Modifier
                        .padding(14.dp)
                        .align(Alignment.TopEnd)
                        .background(CustomTheme.colors.button.small, CircleShape),
                ) {
                    Text(
                        modifier = Modifier.padding(4.dp),
                        text = R.string.account_shop_deck_has_ar.strResDesc()
                            .localizedByLocal(),
                        textAlign = TextAlign.Center,
                        style = CustomTheme.typography.button.small,
                        color = CustomTheme.colors.text.caption
                    )
                }
            }
            deck.deckInfo.tags.firstOrNull()?.let { tag ->
                Box(
                    modifier = Modifier
                        .padding(12.dp)
                        .background(CustomTheme.colors.button.small, CircleShape),
                ) {
                    Text(
                        modifier = Modifier.padding(horizontal = 24.dp, vertical = 8.dp),
                        text = tag.localizedByLocal(),
                        textAlign = TextAlign.Center,
                        style = CustomTheme.typography.button.small,
                        color = CustomTheme.colors.text.caption
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(20.dp))

        Text(
            text = deck.deckInfo.name.localizedByLocal(),
            color = CustomTheme.colors.text.caption,
            style = CustomTheme.typography.caption.bigSemiBold,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier
                .padding(horizontal = 20.dp)
        )

        Spacer(modifier = Modifier.height(4.dp))

        val cardDescription = StringDesc.PluralFormatted(
            PluralsResource(R.plurals.account_shop_cards_in_deck),
            deck.cardCount,
            deck.cardCount
        ).localizedByLocal() +
            " • " +
            if (deck.deckAvailable) {
                R.string.account_shop_deck_is_available.strResDesc()
                    .localizedByLocal()
            } else {
                deck.deckInfo.price.toDecimalString()
            }
        Text(
            text = cardDescription,
            color = CustomTheme.colors.text.caption,
            style = CustomTheme.typography.caption.small,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier
                .padding(horizontal = 20.dp)
        )

        Spacer(modifier = Modifier.height(20.dp))
    }
}