package com.metacards.metacards.features.layout.domain.ar

class Grid<T : Any>(sizeX: Int, sizeZ: Int) {
    private val grid: MutableList<MutableList<T?>> =
        MutableList(sizeX) { MutableList(sizeZ) { null } }

    fun put(element: T, position2D: Position2D) {
        grid[position2D.x][position2D.z] = element
    }

    fun get(position2D: Position2D) = grid[position2D.x][position2D.z]

    fun remove(position2D: Position2D): T? {
        val element = grid[position2D.x][position2D.z]
        grid[position2D.x][position2D.z] = null
        return element
    }

    fun remove(element: T): Boolean {
        grid.find { row -> row.find { it == element } != null } ?: return false
        var columnIndex: Int = -1
        val rowIndex = grid.indexOfFirst { row ->
            columnIndex = row.indexOfFirst { it == element }
            columnIndex != -1
        }

        grid[rowIndex][columnIndex] = null
        return true
    }

    fun getEmptySlots(): MutableList<Position2D> {
        val emptySlotsId = mutableListOf<Position2D>()

        grid.forEachIndexed { rowIndex, row ->
            row.forEachIndexed { columnIndex, element ->
                if (element == null) {
                    emptySlotsId.add(Position2D(rowIndex, columnIndex))
                }
            }
        }

        return emptySlotsId
    }
}