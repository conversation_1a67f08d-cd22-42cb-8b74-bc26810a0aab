package com.metacards.metacards.features.payments.domain

import com.metacards.metacards.core.error_handling.ServerException
import java.net.URL

class GetPaymentUrlForDeckInteractor(private val repository: PaymentRepository) {
    suspend fun execute(deckId: String): URL? {
        val result = repository.makePaymentForDeck(deckId)
        if (result.isError) throw ServerException(null, result.data ?: result.toString())
        return result.data?.let { URL(it) }
    }
}