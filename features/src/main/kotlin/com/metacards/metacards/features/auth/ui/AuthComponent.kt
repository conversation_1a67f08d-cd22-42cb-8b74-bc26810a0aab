package com.metacards.metacards.features.auth.ui

import android.os.Parcelable
import androidx.annotation.StringRes
import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.core.user.domain.Email
import com.metacards.metacards.features.auth.ui.main_auth.MainAuthComponent
import com.metacards.metacards.features.auth.ui.sign_in.SignInComponent
import com.metacards.metacards.features.auth.ui.sign_up.SignUpComponent
import kotlinx.coroutines.flow.StateFlow
import kotlinx.parcelize.Parcelize

interface AuthComponent {
    val childStack: StateFlow<ChildStack<*, Child>>

    sealed interface Child {
        class SignUp(val component: SignUpComponent) : Child
        class SignIn(val component: SignInComponent) : Child
        class MainAuth(val component: MainAuthComponent) : Child
    }

    sealed interface Output {
        data object MainPageRequested : Output
        data class WebViewRequested(val url: String, @StringRes val title: Int) : Output
    }

    sealed interface StartCommand : Parcelable {
        @Parcelize
        data class OpenEmailVerification(val email: Email) : StartCommand

        @Parcelize
        data object OpenWithAnimation : StartCommand
    }
}