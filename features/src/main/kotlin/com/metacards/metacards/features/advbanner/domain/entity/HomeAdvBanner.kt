package com.metacards.metacards.features.advbanner.domain.entity

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.deck.domain.entity.DeckId

data class HomeAdvBannerState(
    val isShow: Boolean,
    val homeAdvBanner: HomeAdvBanner
) {
    companion object {
        val MOCK: HomeAdvBannerState = HomeAdvBannerState(isShow = true, homeAdvBanner = HomeAdvBanner.MOCK)
    }
}

data class HomeAdvBanner(
    val id: String,
    val title: LocalizableString,
    val description: LocalizableString?,
    val coverUrl: String,
    val type: HomeBannerType,
    val deckId: DeckId?,
    val priority: Int,
    val style: HomeBannerStyle
) {
    companion object {
        val MOCK = HomeAdvBanner(
            id = "",
            title = LocalizableString.createNonLocalizable("Title"),
            description = LocalizableString.createNonLocalizable("Description"),
            coverUrl = "ridiculus",
            type = HomeBannerType.SUB,
            deckId = null,
            priority = -1,
            style = HomeBannerStyle.PROMO
        )
    }
}

enum class HomeBannerType {
    SUB, DECK;

    companion object {
        fun fromString(value: String) = if (value == "SUB") SUB else DECK
    }
}

enum class HomeBannerStyle {
    PROMO, DEFAULT;

    companion object {
        fun fromString(value: String) = if (value == DEFAULT.name) DEFAULT else PROMO
    }
}
