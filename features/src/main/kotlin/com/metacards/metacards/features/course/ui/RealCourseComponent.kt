package com.metacards.metacards.features.course.ui

import android.os.Parcelable
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.stack.ChildStack
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.router.stack.childStack
import com.arkivanov.decompose.router.stack.push
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.utils.toStateFlow
import com.metacards.metacards.features.course.createCourseDetails
import com.metacards.metacards.features.course.createCourseListComponent
import com.metacards.metacards.features.course.domain.entity.CourseTheme
import com.metacards.metacards.features.course.ui.details.CourseDetailsComponent
import com.metacards.metacards.features.course.ui.list.CourseListComponent
import kotlinx.coroutines.flow.StateFlow
import kotlinx.parcelize.Parcelize

class RealCourseComponent(
    componentContext: ComponentContext,
    private val componentFactory: ComponentFactory,
    private val onOutput: (CourseComponent.Output) -> Unit,
    screen: CourseComponent.Screen,
) : ComponentContext by componentContext, CourseComponent {

    private val navigation = StackNavigation<ChildConfig>()

    override val childStack: StateFlow<ChildStack<*, CourseComponent.Child>> = childStack(
        source = navigation,
        initialConfiguration = screen.toChildConfig(),
        handleBackButton = true,
        childFactory = ::createChild
    ).toStateFlow(lifecycle)

    private fun createChild(
        config: ChildConfig,
        componentContext: ComponentContext
    ): CourseComponent.Child = when (config) {

        is ChildConfig.CourseList -> CourseComponent.Child.CourseList(
            componentFactory.createCourseListComponent(
                componentContext,
                ::onCourseListOutput,
                config.theme
            )
        )

        is ChildConfig.Details -> CourseComponent.Child.Details(
            componentFactory.createCourseDetails(
                componentContext,
                ::onCourseDetailsOutput,
                config.screen
            )
        )
    }

    private fun onCourseDetailsOutput(output: CourseDetailsComponent.Output) {
        when (output) {
            CourseDetailsComponent.Output.CloseRequested -> onOutput(CourseComponent.Output.CloseRequested)
            is CourseDetailsComponent.Output.SubscriptionBottomSheetRequested ->
                onOutput(CourseComponent.Output.SubscriptionBottomSheetRequested(output.withAdv))

            CourseDetailsComponent.Output.AuthScreenRequested ->
                onOutput(CourseComponent.Output.AuthScreenRequested)
            is CourseDetailsComponent.Output.LayoutDetailsRequested ->
                onOutput(
                    CourseComponent.Output.LayoutDetailsRequested(
                        output.cardSource,
                        output.courseId,
                        output.themeId
                    )
                )
        }
    }

    private fun onCourseListOutput(output: CourseListComponent.Output) {
        when (output) {
            is CourseListComponent.Output.CoursePageRequested ->
                navigation.push(
                    ChildConfig.Details(
                        CourseDetailsComponent.Screen.Course(output.courseQuery)
                    )
                )

            CourseListComponent.Output.SubscriptionBottomSheetRequested ->
                onOutput(CourseComponent.Output.SubscriptionBottomSheetRequested(false))

            CourseListComponent.Output.AuthSuggestingRequested ->
                onOutput(CourseComponent.Output.AuthSuggestingRequested)
        }
    }

    private sealed interface ChildConfig : Parcelable {

        @Parcelize
        data class CourseList(val theme: CourseTheme) : ChildConfig

        @Parcelize
        data class Details(val screen: CourseDetailsComponent.Screen) : ChildConfig
    }

    private fun CourseComponent.Screen.toChildConfig(): ChildConfig = when (this) {
        is CourseComponent.Screen.Details -> ChildConfig.Details(screen)
        is CourseComponent.Screen.CourseList -> ChildConfig.CourseList(theme)
    }
}