package com.metacards.metacards.features.record.data.dto

import com.google.firebase.Timestamp
import com.metacards.metacards.core.utils.TimestampAsStringSerializer
import com.metacards.metacards.core.utils.toKotlinInstant
import com.metacards.metacards.core.utils.toTimestamp
import com.metacards.metacards.features.record.domain.Comment
import kotlinx.serialization.Serializable

@Serializable
class CommentDto(
    @Serializable(with = TimestampAsStringSerializer::class)
    val creationDate: Timestamp = Timestamp.now(),
    val comment: String = ""
)

fun CommentDto.toDomain(): Comment {
    return Comment(
        creationTime = creationDate.toKotlinInstant(),
        text = comment
    )
}

fun Comment.toDto(): CommentDto {
    return CommentDto(
        creationDate = creationTime.toTimestamp(),
        comment = text
    )
}