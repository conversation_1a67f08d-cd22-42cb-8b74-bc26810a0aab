package com.metacards.metacards.features.special_deck.ui.constellation_obtained

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.localization.ui.LocalizableString

class RealSpecialDeckConstellationObtainedComponent(
    componentContext: ComponentContext,
    override val imageUrl: String,
    override val header: LocalizableString,
    override val description: LocalizableString,
    private val onOutput: (SpecialDeckConstellationObtainedComponent.Output) -> Unit,
) : ComponentContext by componentContext, SpecialDeckConstellationObtainedComponent {

    override fun onWowClick() {
        onOutput(SpecialDeckConstellationObtainedComponent.Output.DismissRequested)
    }
}