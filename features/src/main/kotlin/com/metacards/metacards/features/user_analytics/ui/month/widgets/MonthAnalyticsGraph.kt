package com.metacards.metacards.features.user_analytics.ui.month.widgets

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.Divider
import androidx.compose.material.Slider
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.getDisplayName
import com.metacards.metacards.features.record.domain.LevelType
import com.metacards.metacards.features.record.ui.utils.feelingLevelToColor
import com.metacards.metacards.features.user_analytics.domain.month.MonthAnalyticsInfo
import java.time.Month
import java.time.YearMonth
import kotlin.math.cos
import kotlin.math.roundToInt
import kotlin.math.sin

@Composable
fun MonthAnalyticsGraphs(
    analyticsInfo: MonthAnalyticsInfo,
    animationRatio: Float,
    modifier: Modifier = Modifier
) {
    Column(modifier) {
        Row(
            Modifier
                .padding(horizontal = 16.dp, vertical = 12.dp)
                .fillMaxWidth()
        ) {
            MonthGraph(
                modifier = Modifier
                    .weight(1.0f)
                    .aspectRatio(1.0f),
                levelPercents = analyticsInfo.energyLevelPercents,
                levelType = LevelType.Energy,
                animationRatio = animationRatio
            )

            Spacer(modifier = Modifier.width(16.dp))

            MonthGraph(
                modifier = Modifier
                    .weight(1.0f)
                    .aspectRatio(1.0f),
                levelPercents = analyticsInfo.moodLevelPercents,
                levelType = LevelType.Mood,
                animationRatio = animationRatio
            )
        }

        Divider(
            modifier = Modifier.padding(top = 12.dp),
            color = CustomTheme.colors.background.primary,
            thickness = 2.dp
        )

        val currentMonth = analyticsInfo.month
        val previousMonth = currentMonth.minusMonths(1)
        val nextMonth = currentMonth.plusMonths(1)

        Row(
            Modifier
                .padding(horizontal = 16.dp, vertical = 8.dp)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            MonthText(month = previousMonth, active = false)
            MonthText(month = currentMonth, active = true)
            MonthText(month = nextMonth, active = false)
        }
    }
}

@Composable
private fun MonthGraph(
    levelPercents: Map<Int, Float>,
    levelType: LevelType,
    animationRatio: Float,
    modifier: Modifier = Modifier,
) {
    if (levelPercents.isEmpty()) {
        EmptyMonthGraph(levelType, modifier)
    } else {
        FilledMonthGraph(levelType, levelPercents, animationRatio, modifier)
    }
}

@Composable
private fun EmptyMonthGraph(levelType: LevelType, modifier: Modifier) {
    val strokeWidth = with(LocalDensity.current) { 2.dp.toPx() }
    val style = Stroke(strokeWidth)
    val color = CustomTheme.colors.button.secondary

    val lineAngles = remember(levelType) {
        val additionalAngle = when (levelType) {
            LevelType.Energy -> 45
            LevelType.Mood -> 225
        }
        listOf(0, 90, 180, 270, additionalAngle)
    }

    Canvas(modifier) {
        val outerRadius = size.minDimension / 2
        val innerRadius = outerRadius / 3

        drawCircle(
            radius = outerRadius,
            color = color,
            style = style,
        )

        drawCircle(
            radius = innerRadius,
            color = color,
            style = style
        )

        for (angle in lineAngles) {
            val a = Math.toRadians(angle - 90.0).toFloat()
            drawLine(
                start = Offset(
                    center.x + innerRadius * cos(a),
                    center.y + innerRadius * sin(a)
                ),
                end = Offset(
                    center.x + outerRadius * cos(a),
                    center.y + outerRadius * sin(a)
                ),
                color = color,
                strokeWidth = strokeWidth
            )
        }
    }
}

@Composable
private fun FilledMonthGraph(
    levelType: LevelType,
    levelPercents: Map<Int, Float>,
    animationRatio: Float,
    modifier: Modifier
) {

    val orderedLevelPercents = remember(levelPercents) {
        levelPercents.entries.sortedByDescending { it.key }
    }
    val textMeasurer = rememberTextMeasurer()

    Canvas(modifier) {
        if (animationRatio == 0.0f) return@Canvas

        // shape settings
        val outerRadius = size.minDimension / 2
        val innerRadius = outerRadius / 3
        val center = Offset(size.width / 2, size.height / 2)

        // animation settings
        val strokeAnimationDelay = 0.1f
        val strokeAnimationSpeed = 2.5f
        val strokeAlphaAnimationStart = 0.3f
        val strokeAlphaAnimationSpeed = 1.5f
        val textAnimationDelay = 0.6f
        val textAnimationSpeed = 5.0f

        var accumulatedAngle = 0.0f
        var arcIndex = 0

        for ((level, percent) in orderedLevelPercents) {
            if (percent == 0.0f) continue

            val angle = percent / 100.0f * 360

            // сегменты анимируются с задержкой относительно друг друга, поэтому используем arcIndex в формуле
            val strokeAnimationRatio =
                ((animationRatio - arcIndex * strokeAnimationDelay) * strokeAnimationSpeed)
                    .coerceIn(0.0f, 1.0f)
            val strokeWidth = (outerRadius - innerRadius) * strokeAnimationRatio
            val strokeAlpha =
                (strokeAlphaAnimationStart + strokeAnimationRatio * strokeAlphaAnimationSpeed)
                    .coerceIn(0.0f, 1.0f)

            drawArc(
                color = feelingLevelToColor(levelType, level),
                startAngle = accumulatedAngle - 90,
                sweepAngle = angle,
                useCenter = false,
                topLeft = Offset(
                    center.x - innerRadius - strokeWidth / 2,
                    center.y - innerRadius - strokeWidth / 2
                ),
                size = Size(
                    innerRadius * 2 + strokeWidth,
                    innerRadius * 2 + strokeWidth
                ),
                style = Stroke(strokeWidth),
                alpha = strokeAlpha
            )
            accumulatedAngle += angle
            arcIndex += 1
        }

        val textAnimationRatio = ((animationRatio - textAnimationDelay) * textAnimationSpeed)
            .coerceIn(0.0f, 1.0f)

        val middleRadius = (innerRadius + outerRadius) / 2
        accumulatedAngle = 0.0f

        for ((_, percent) in orderedLevelPercents) {
            val angle = percent / 100.0f * 360

            if (percent < 5) {
                accumulatedAngle += angle
                continue
            }

            val measuredText = textMeasurer.measure(
                text = percent.roundToInt().let { if (it > 5) "$it %" else "" },
                style = CustomTheme.typography.caption.medium
            )

            val textAngleRadians = Math.toRadians(accumulatedAngle + angle / 2.0 - 90).toFloat()

            val textCenter = Offset(
                x = center.x + middleRadius * cos(textAngleRadians),
                y = center.y + middleRadius * sin(textAngleRadians)
            )

            val textTopLeft = Offset(
                x = textCenter.x - measuredText.size.width / 2,
                y = textCenter.y - measuredText.size.height / 2
            )

            drawText(
                textLayoutResult = measuredText,
                color = CustomTheme.colors.text.inverted,
                alpha = textAnimationRatio,
                topLeft = textTopLeft
            )

            accumulatedAngle += angle
        }
    }
}

@Composable
fun MonthText(month: YearMonth, active: Boolean, modifier: Modifier = Modifier) {
    val locale = LocalContext.current.resources.configuration.locales[0]
    Text(
        modifier = modifier,
        text = month.getDisplayName(locale).localizedByLocal(),
        color = if (active) CustomTheme.colors.text.primary else CustomTheme.colors.text.secondary,
        style = CustomTheme.typography.caption.small
    )
}

@Composable
@Preview
fun MonthAnalyticsGraphsPreview() {
    AppTheme {
        MonthAnalyticsGraphs(
            analyticsInfo = MonthAnalyticsInfo.MOCK,
            animationRatio = 1.0f,
            modifier = Modifier.width(300.dp)
        )
    }
}

@Composable
@Preview
fun EmptyMonthGraphPreview() {
    AppTheme {
        MonthAnalyticsGraphs(
            analyticsInfo = MonthAnalyticsInfo(YearMonth.of(2023, Month.JULY), emptyList()),
            animationRatio = 1.0f,
            modifier = Modifier.width(300.dp)
        )
    }
}

@Composable
@Preview
fun FilledMonthGraphPreview() {
    AppTheme {
        FilledMonthGraph(
            levelType = LevelType.Mood,
            levelPercents = mapOf(1 to 1.0f, 2 to 3.0f, 3 to 5.0f, 4 to 7.0f, 5 to 84.0f),
            animationRatio = 1.0f,
            modifier = Modifier.size(200.dp)
        )
    }
}

// For interactive mode
@Composable
@Preview
fun FilledMonthGraphAnimationRatioPreview() {
    var animationRatio by remember { mutableFloatStateOf(1.0f) }

    AppTheme {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            FilledMonthGraph(
                levelType = LevelType.Energy,
                levelPercents = mapOf(1 to 15.0f, 2 to 11.0f, 3 to 7.0f, 4 to 30.0f, 5 to 37.0f),
                animationRatio = animationRatio,
                modifier = Modifier.size(200.dp)
            )

            Spacer(Modifier.height(20.dp))

            Slider(
                value = animationRatio,
                onValueChange = {
                    animationRatio = it
                }
            )
        }
    }
}