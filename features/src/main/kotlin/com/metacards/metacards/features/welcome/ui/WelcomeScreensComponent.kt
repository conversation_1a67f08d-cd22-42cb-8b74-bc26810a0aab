package com.metacards.metacards.features.welcome.ui

import kotlinx.coroutines.flow.SharedFlow

interface WelcomeScreensComponent {
    val pages: List<WelcomeScreenPage>
    val pageToOpen: SharedFlow<Int>

    fun onPageChanged(page: Int)

    fun onProceedClick(pageToOpen: Int)
    fun onAuthClick()
    fun onAuthLaterClick()

    sealed interface Output {
        object AuthRequested : Output
        object ContinueWithoutAuthRequested : Output
    }
}