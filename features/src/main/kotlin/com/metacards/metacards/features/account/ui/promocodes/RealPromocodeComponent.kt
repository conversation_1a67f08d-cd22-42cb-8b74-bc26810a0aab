package com.metacards.metacards.features.account.ui.promocodes

import com.ark<PERSON>nov.decompose.ComponentContext
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.error_handling.BusinessLogicException
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.withProgress
import com.metacards.metacards.features.R
import com.metacards.metacards.features.account.domain.entity.Promocode
import com.metacards.metacards.features.account.domain.interactor.promocodes.ActivatePromocodeInteractor
import dev.icerock.moko.resources.StringResource
import dev.icerock.moko.resources.desc.Raw
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.desc
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import ru.mobileup.kmm_form_validation.control.InputControl
import ru.mobileup.kmm_form_validation.options.KeyboardCapitalization
import ru.mobileup.kmm_form_validation.options.KeyboardOptions
import ru.mobileup.kmm_form_validation.validation.control.isNotBlank
import ru.mobileup.kmm_form_validation.validation.form.RevalidateOnValueChanged
import ru.mobileup.kmm_form_validation.validation.form.dynamicValidationResult
import ru.mobileup.kmm_form_validation.validation.form.formValidator

class RealPromocodeComponent(
    componentContext: ComponentContext,
    private val activatePromocodeInteractor: ActivatePromocodeInteractor,
    private val errorHandler: ErrorHandler,
    private val onOutput: (PromocodeComponent.Output) -> Unit
) : ComponentContext by componentContext, PromocodeComponent {

    companion object {
        private const val MAX_LENGTH = 30
    }

    private val debounce = Debounce()

    override val promocodeInputControl: InputControl = InputControl(
        componentScope,
        maxLength = MAX_LENGTH,
        keyboardOptions = KeyboardOptions(
            capitalization = KeyboardCapitalization.Characters,
            autoCorrect = false
        ),
        textTransformation = { it.uppercase() }
    )

    private val formValidator = componentScope.formValidator {
        features = listOf(RevalidateOnValueChanged)

        input(promocodeInputControl) {
            isNotBlank(StringDesc.Raw(""))
        }
    }

    private val dynamicResult = componentScope.dynamicValidationResult(formValidator)

    private val isLoading = MutableStateFlow(false)

    override val buttonState =
        computed(
            dynamicResult,
            promocodeInputControl.error,
            isLoading
        ) { result, error, isLoading ->
            val isEnabled = error == null && result.isValid
            ButtonState.create(disabled = !isEnabled, isLoading)
        }

    override fun onActivateButtonClick() {
        if (!buttonState.value.isEnabled()) return

        DebounceClick(debounce, "onActivateButtonClick") {
            try {
                withProgress(isLoading) {
                    val promocode = Promocode(promocodeInputControl.value.value.trim())
                    activatePromocodeInteractor.execute(promocode)

                    // Без этого открывается клавиатура при уходе с экрана (не понятно, почему)
                    promocodeInputControl.hasFocus.value = false
                    delay(30)

                    val message = StringResource(R.string.promocode_success).desc()
                    onOutput(PromocodeComponent.Output.CloseScreenRequested(message))
                }
            } catch (e: Exception) {
                when (e) {
                    is BusinessLogicException -> promocodeInputControl.error.value = e.messageDesc
                    is CancellationException -> throw e
                    else -> errorHandler.handleError(e)
                }
            }
        }
    }

    override fun onCloseButtonClick() {
        onOutput(PromocodeComponent.Output.CloseScreenRequested())
    }
}