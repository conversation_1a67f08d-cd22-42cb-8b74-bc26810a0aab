package com.metacards.metacards.features.account.ui.profile.password

import android.os.Parcelable
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.router.stack.childStack
import com.arkivanov.decompose.router.stack.push
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.toStateFlow
import com.metacards.metacards.features.R
import com.metacards.metacards.features.account.createProfileConfirmPasswordComponent
import com.metacards.metacards.features.account.createProfileNewPasswordComponent
import com.metacards.metacards.features.account.ui.profile.password.confirm.ProfileConfirmPasswordComponent
import com.metacards.metacards.features.account.ui.profile.password.create_new.ProfileNewPasswordComponent
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.parcelize.Parcelize
import com.metacards.metacards.core.R as CoreR

class RealProfilePasswordComponent(
    componentContext: ComponentContext,
    private val componentFactory: ComponentFactory,
    private val onOutput: (ProfilePasswordComponent.Output) -> Unit
) : ComponentContext by componentContext, ProfilePasswordComponent {
    private val navigation = StackNavigation<ChildConfig>()

    override val childStack = childStack(
        source = navigation,
        initialConfiguration = ChildConfig.Confirm,
        handleBackButton = true,
        childFactory = ::createChild
    ).toStateFlow(lifecycle)

    private fun createChild(
        childConfig: ChildConfig,
        componentContext: ComponentContext
    ): ProfilePasswordComponent.Child = when (childConfig) {
        is ChildConfig.Confirm -> ProfilePasswordComponent.Child.ConfirmPassword(
            componentFactory.createProfileConfirmPasswordComponent(
                componentContext,
                StringDesc.Resource(R.string.account_profile_edit_password_title),
                StringDesc.Resource(R.string.account_profile_edit_password_header),
                null,
                StringDesc.Resource(CoreR.string.common_proceed),
                ::onProfileConfirmOutput
            )
        )
        is ChildConfig.New -> ProfilePasswordComponent.Child.NewPassword(
            componentFactory.createProfileNewPasswordComponent(
                componentContext,
                childConfig.oldPassword,
                ::onProfileNewPasswordOutput
            )
        )
    }

    private fun onProfileNewPasswordOutput(output: ProfileNewPasswordComponent.Output) {
        when (output) {
            is ProfileNewPasswordComponent.Output.ProfileScreenRequested -> {
                onOutput(ProfilePasswordComponent.Output.DismissRequested)
            }
        }
    }

    private fun onProfileConfirmOutput(output: ProfileConfirmPasswordComponent.Output) {
        when (output) {
            is ProfileConfirmPasswordComponent.Output.PasswordConfirmed -> {
                navigation.push(ChildConfig.New(output.password))
            }
        }
    }

    sealed interface ChildConfig : Parcelable {
        @Parcelize
        object Confirm : ChildConfig

        @Parcelize
        data class New(val oldPassword: String) : ChildConfig
    }
}