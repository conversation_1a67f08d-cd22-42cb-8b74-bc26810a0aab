package com.metacards.metacards.features.layout.ui.select_card.ar.sceneview

import co.touchlab.kermit.Logger
import io.github.sceneview.node.ModelNode
import io.github.sceneview.node.Node
import io.github.sceneview.node.NodeParent

fun NodeParent.addChildSaveTransform(child: Node) {
    val worldTransform = child.worldTransform
    addChild(child)
    val localTransform = child.worldToParent * worldTransform
    child.transform(localTransform)
}

fun NodeParent.removeChildSaveTransform(child: Node) {
    val worldTransform = child.worldTransform
    removeChild(child)
    val localTransform = child.worldToParent * worldTransform
    child.transform(localTransform)
}

fun ModelNode.logPosition(logger: Logger) {
    logger.d("${this::class.simpleName}\n position = $position \n worldPosition = $worldPosition ")
}