package com.metacards.metacards.features.deck.domain.interactor

import com.metacards.metacards.features.deck.domain.entity.Deck
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.deck.domain.repository.DecksRepository
import com.metacards.metacards.features.user.domain.UserRepository

private const val MESSAGE_FAIL_ALREADY_ADDED = "Already added"
private const val MESSAGE_FAIL_INVALID_ID = "Invalid deck id"

sealed interface AddDeckToPurchasedResult {
    class Added(val deck: Deck) : AddDeckToPurchasedResult
    class AlreadyAdded(val message: String) : AddDeckToPurchasedResult
    class InvalidDeckId(val message: String) : AddDeckToPurchasedResult
}

class AddDeckToPurchasedInteractor(
    private val decksRepository: DecksRepository,
    private val userRepository: UserRepository
) {

    suspend fun execute(deckId: DeckId): AddDeckToPurchasedResult {
        val decksForPurchase = decksRepository.getDecksForPurchase() + decksRepository.getDecksForSubscription()
        val freeDecks = decksRepository.getFreeDecks()
        val purchasedDecks = userRepository.getPurchasedDecksId()

        val deckForPurchase = decksForPurchase.find { it.id == deckId }

        return if (purchasedDecks.any { it == deckId.value } || freeDecks.any { it.id == deckId }) {
            AddDeckToPurchasedResult.AlreadyAdded(MESSAGE_FAIL_ALREADY_ADDED)
        } else if (deckForPurchase != null) {
            userRepository.addDeckToPurchased(deckId.value)
            AddDeckToPurchasedResult.Added(deckForPurchase)
        } else {
            AddDeckToPurchasedResult.InvalidDeckId(MESSAGE_FAIL_INVALID_ID)
        }
    }
}