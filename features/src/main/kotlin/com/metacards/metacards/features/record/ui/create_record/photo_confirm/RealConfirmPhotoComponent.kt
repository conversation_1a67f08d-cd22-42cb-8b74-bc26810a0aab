package com.metacards.metacards.features.record.ui.create_record.photo_confirm

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.record.domain.camera.CardPhoto
import com.metacards.metacards.features.record.domain.camera.DeletePhotoFromGalleryInteractor
import kotlinx.coroutines.flow.StateFlow

class RealConfirmPhotoComponent(
    componentContext: ComponentContext,
    override val cardPhoto: StateFlow<CardPhoto>,
    private val deletePhotoFromGalleryInteractor: DeletePhotoFromGalleryInteractor,
    private val errorHandler: <PERSON>rror<PERSON>and<PERSON>,
    private val analyticsService: AnalyticsService,
    private val onOutput: (ConfirmPhotoComponent.Output) -> Unit,
) : ComponentContext by componentContext, ConfirmPhotoComponent {

    override fun savePhoto() {
        analyticsService.logEvent(AnalyticsEvent.ManualRecordPhotoConfirmEvent)
        onOutput(ConfirmPhotoComponent.Output.CreateRecordRequested(cardPhoto.value))
    }

    override fun retakePhoto() {
        analyticsService.logEvent(AnalyticsEvent.ManualRecordPhotoRetakeEvent)
        componentScope.safeLaunch(errorHandler) {
            onOutput(ConfirmPhotoComponent.Output.CameraRequested)

            val uri = cardPhoto.value.uri
            if (uri != null) {
                deletePhotoFromGalleryInteractor.execute(uri)
            }
        }
    }
}
