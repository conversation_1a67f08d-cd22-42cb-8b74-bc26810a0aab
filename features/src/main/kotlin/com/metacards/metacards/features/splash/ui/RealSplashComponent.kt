package com.metacards.metacards.features.splash.ui

import co.touchlab.kermit.Logger
import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.adv.domain.AdvUserConsentState
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.preferences.PreferencesService
import com.metacards.metacards.core.utils.componentScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicBoolean

class RealSplashComponent(
    componentContext: ComponentContext,
    private val onOutput: (SplashComponent.Output) -> Unit,
    private val preferencesService: PreferencesService,
    errorHandler: <PERSON>rrorHandler,
) : ComponentContext by componentContext, SplashComponent {
    val logger = Logger.withTag("SplashComponent")

    private val isAnimationEndFlow = MutableStateFlow(false)
    private var pendingOutput: SplashComponent.Output? = null
    private var goForwardCalled = AtomicBoolean(false)

    init {
        componentScope.safeLaunch(errorHandler) {
            val shouldShowWelcomeScreen = preferencesService.getWelcomeScreensLaunchFlag()
            val shouldShowLoginScreen = preferencesService.getAuthScreenLaunchFlag()
            val shouldShowAdvUserConsentScreen =
                preferencesService.getAdvUserConsentState() == AdvUserConsentState.Unknown

            pendingOutput = when {
                shouldShowWelcomeScreen -> {
                    preferencesService.disableWelcomeScreensLaunchFlag()
                    SplashComponent.Output.WelcomeScreenRequested
                }

                shouldShowLoginScreen -> {
                    SplashComponent.Output.AuthScreenRequested
                }

                shouldShowAdvUserConsentScreen -> {
                    SplashComponent.Output.AdvUserConsentScreenRequested
                }

                else -> SplashComponent.Output.MainScreenRequested
            }
        }

        isAnimationEndFlow
            .onEach { isFinished -> if (isFinished) goForward() }
            .launchIn(componentScope)

        componentScope.launch {
            delay(DOUBLE_CHECK_DELAY)
            if (!goForwardCalled.get()) goForward()
        }
    }

    override fun onAnimationEnd() {
        isAnimationEndFlow.value = true
    }

    private fun goForward() {
        goForwardCalled.set(true)
        onOutput((pendingOutput ?: SplashComponent.Output.MainScreenRequested))
    }

    private companion object {
        const val DOUBLE_CHECK_DELAY = 3500L
    }
}
