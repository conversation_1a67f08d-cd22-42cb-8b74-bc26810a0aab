package com.metacards.metacards.features.user.ui.subscription

import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.features.payments.ui.PaymentComponent
import kotlinx.coroutines.flow.StateFlow
import java.net.URL

interface SubscriptionComponent {
    val baseSubscriptionComponent: BaseSubscriptionComponent
    val subscriptionScreenState: StateFlow<SubscriptionScreenState>
    val paymentComponent: PaymentComponent
    val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent>

    fun onCompleteClick()

    fun onGoToMainClick()

    fun onSuccessBackClick()

    fun onCancelClick()

    sealed interface Output {
        data class WebViewRequested(val url: URL, val title: Int) : Output

        data object MainScreenRequested : Output
        data object AuthScreenRequested : Output
    }
}