package com.metacards.metacards.features.record.ui.create_record.add_card

import androidx.camera.core.ImageCapture
import com.metacards.metacards.features.record.ui.create_record.add_card.image_tracking.FakeImageTrackingComponent
import kotlinx.coroutines.flow.MutableStateFlow

class FakeAddCardComponent : AddCardComponent {
    override val currentState = MutableStateFlow(AddCardComponent.State.Camera)
    override val imageTrackingComponent = FakeImageTrackingComponent()

    override fun onCameraIconClick() = Unit

    override fun onImageTrackingClick() = Unit

    override fun takePhoto(imageCapture: ImageCapture) = Unit

    override fun onContinueClick() = Unit
}