package com.metacards.metacards.features.layout.ui.select_card.widget

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.features.layout.ui.select_card.CardSelectComponent

@Composable
fun TabsLayout(
    tabs: List<CardSelectComponent.Tabs>,
    selectedTab: CardSelectComponent.Tabs,
    paddingValues: PaddingValues,
    innerPaddingValues: PaddingValues,
    onChange: (CardSelectComponent.Tabs) -> Unit,
    modifier: Modifier = Modifier
) {
    BoxWithConstraints(modifier.padding(paddingValues)) {
        val tabWidth = maxWidth / tabs.size

        Box(
            Modifier
                .height(IntrinsicSize.Max)
                .clip(RoundedCornerShape(8.dp))
                .background(CustomTheme.colors.background.segmentControl)
        ) {
            TabIndicator(
                selectedTabIndex = tabs.indexOf(selectedTab),
                tabWidth = tabWidth,
                paddingValues = innerPaddingValues
            )

            Tabs(
                modifier = Modifier,
                tabs = tabs,
                paddingValues = innerPaddingValues,
                onChange = onChange
            )
        }
    }
}

@Composable
private fun TabIndicator(
    selectedTabIndex: Int,
    tabWidth: Dp,
    paddingValues: PaddingValues
) {
    val startOffset by animateDpAsState(targetValue = tabWidth * selectedTabIndex)

    Box(
        modifier = Modifier
            .fillMaxHeight()
            .width(tabWidth)
            .padding(2.dp)
            .offset(x = startOffset)
            .clip(RoundedCornerShape(6.dp))
            .background(Color(0xFF82849C)) // TODO: change color
            .padding(paddingValues)
    )
}

@Composable
private fun Tabs(
    modifier: Modifier,
    tabs: List<CardSelectComponent.Tabs>,
    paddingValues: PaddingValues,
    onChange: (CardSelectComponent.Tabs) -> Unit
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceEvenly,
        verticalAlignment = Alignment.CenterVertically
    ) {
        tabs.forEach { tab ->
            Box(
                modifier = Modifier
                    .weight(1f)
                    .padding(paddingValues)
                    .clickable(false) { onChange(tab) },
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = tab.toStringDesc().localizedByLocal(),
                    style = CustomTheme.typography.caption.bigSemiBold,
                    color = CustomTheme.colors.text.primary
                )
            }
        }
    }
}