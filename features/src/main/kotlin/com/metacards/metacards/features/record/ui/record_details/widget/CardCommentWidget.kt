package com.metacards.metacards.features.record.ui.record_details.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.clickable

private const val CARD_COMMENT_MAX_LINES = 3

@Composable
fun CardCommentWidget(
    cardImageUrl: String,
    commentText: String,
    onClick: (String, String) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .clip(RoundedCornerShape(16.dp))
            .fillMaxWidth()
            .background(CustomTheme.colors.background.primary)
            .clickable { onClick(cardImageUrl, commentText) }
    ) {
        Row(
            modifier = Modifier.padding(16.dp)
        ) {
            AsyncImage(
                model = cardImageUrl,
                contentScale = ContentScale.Crop,
                contentDescription = null,
                modifier = Modifier
                    .padding(end = 12.dp)
                    .clip(RoundedCornerShape(4.dp))
                    .size(width = 24.dp, height = 36.dp)
            )

            Text(
                text = commentText,
                color = CustomTheme.colors.text.primary,
                style = CustomTheme.typography.body.primary,
                overflow = TextOverflow.Ellipsis,
                maxLines = CARD_COMMENT_MAX_LINES
            )
        }
    }
}
