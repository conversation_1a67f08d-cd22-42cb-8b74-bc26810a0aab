package com.metacards.metacards.features.showcase.ui

import com.metacards.metacards.core.user.domain.FavoriteCard
import com.metacards.metacards.features.account.domain.entity.LessonCategory
import com.metacards.metacards.features.course.domain.entity.CourseShortData
import com.metacards.metacards.features.course.domain.entity.CourseTheme
import com.metacards.metacards.features.deck.domain.entity.DeckInfoWithCards
import com.metacards.metacards.features.layout.domain.PredefinedLayoutWithAvailable
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakeShowcaseComponent : ShowcaseComponent {
    override val decksForPurchaseInfoWithCards: StateFlow<List<DeckInfoWithCards>> =
        MutableStateFlow(emptyList())
    override val predefinedLayouts: StateFlow<List<PredefinedLayoutWithAvailable>> =
        MutableStateFlow(emptyList())
    override val videoLessonCategories: StateFlow<List<LessonCategory>> =
        MutableStateFlow(emptyList())
    override val favoriteCards: StateFlow<List<FavoriteCard>> =
        MutableStateFlow(emptyList())
    override val courses: StateFlow<List<CourseTheme>> =
        MutableStateFlow(emptyList())
    override val isPremiumUser: StateFlow<Boolean> = MutableStateFlow(false)

    override fun onGameBannerClick() = Unit
    override fun onShopDeckClick(deck: DeckInfoWithCards) = Unit
    override fun onPredefinedLayoutClick(layout: PredefinedLayoutWithAvailable) = Unit
    override fun onPredefinedLayoutBlockContentClick() = Unit
    override fun onVideoLessonCategoryClick(category: LessonCategory) = Unit
    override fun onAnalyticsBannerClick() = Unit
    override fun onFavoriteCardClick(favoriteCard: FavoriteCard) = Unit
    override fun onCourseThemeClick(courseTheme: CourseTheme) = Unit
    override fun onCourseClick(course: CourseShortData) = Unit
}