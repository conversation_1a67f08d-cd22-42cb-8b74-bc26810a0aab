package com.metacards.metacards.features.user.ui.subscription

import com.metacards.metacards.core.user.domain.SubscriptionTariff
import com.metacards.metacards.core.user.domain.SubscriptionType
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.features.payments.domain.PurchaseType
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakeBaseSubscriptionComponent : BaseSubscriptionComponent {
    override val isTrialAvailable: StateFlow<Boolean> = MutableStateFlow(false)
    override val userSubscriptionState: StateFlow<User.SubscriptionState> =
        MutableStateFlow(User.SubscriptionState.None(false, false))
    override val selectedSubscriptionType: MutableStateFlow<SubscriptionType> =
        MutableStateFlow(SubscriptionType.SUBSCRIPTION_PLAN_THREE_MONTH)
    override val tariffs: StateFlow<LoadableState<List<SubscriptionTariff>>> =
        MutableStateFlow(LoadableState())

    override fun onSubscriptionTypeSelect(subscriptionType: SubscriptionType) = Unit
    override fun getPurchaseType(): PurchaseType.Subscription = PurchaseType.Subscription.New("id", null, "id")
    override fun getIdForCancel(): String = "id"
}