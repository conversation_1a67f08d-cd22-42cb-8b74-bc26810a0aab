package com.metacards.metacards.features.account.ui.language

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.domain.AppLanguage
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.features.R
import com.metacards.metacards.features.account.ui.widgets.BottomSheetListItem
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun AccountLanguageUi(
    component: AccountLanguageComponent,
    modifier: Modifier = Modifier
) {
    val selectedLanguage by component.languageFlow.collectAsState()

    LazyColumn(modifier = modifier, contentPadding = PaddingValues(bottom = 8.dp)) {
        item {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .background(CustomTheme.colors.background.modal)
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_24_close),
                    contentDescription = null,
                    tint = CustomTheme.colors.icons.primary,
                    modifier = Modifier
                        .padding(start = 8.dp, top = 8.dp, bottom = 8.dp)
                        .clip(CircleShape)
                        .clickable { component.onDismiss() }
                        .padding(8.dp)
                )

                Text(
                    text = R.string.account_main_settings_language.strResDesc().localizedByLocal(),
                    color = CustomTheme.colors.text.primary,
                    style = CustomTheme.typography.heading.medium,
                    modifier = Modifier.padding(start = 24.dp)
                )

                Spacer(modifier = Modifier.weight(1f))

                Box(
                    contentAlignment = Alignment.Center,
                    modifier = Modifier
                        .height(36.dp)
                        .padding(end = 16.dp)
                        .clip(CircleShape)
                        .clickable { component.onSaveClick() }
                ) {
                    Text(
                        text = com.metacards.metacards.core.R.string.common_ok.strResDesc()
                            .localizedByLocal(),
                        color = CustomTheme.colors.text.primary,
                        style = CustomTheme.typography.caption.medium,
                        modifier = Modifier.padding(horizontal = 10.dp)
                    )
                }
            }
        }

        item {
            BottomSheetListItem(
                text = R.string.account_main_language_russian.strResDesc().localizedByLocal(),
                isSelected = selectedLanguage == AppLanguage.RUS,
                onClick = { component.onLanguageClick(AppLanguage.RUS) }
            )
        }

        item {
            BottomSheetListItem(
                text = R.string.account_main_language_english.strResDesc().localizedByLocal(),
                isSelected = selectedLanguage == AppLanguage.ENG,
                onClick = { component.onLanguageClick(AppLanguage.ENG) }
            )
        }

        item {
            BottomSheetListItem(
                text = R.string.account_main_language_spanish.strResDesc().localizedByLocal(),
                isSelected = selectedLanguage == AppLanguage.ESP,
                onClick = { component.onLanguageClick(AppLanguage.ESP) }
            )
        }

        item {
            BottomSheetListItem(
                text = R.string.account_main_language_ukrainian.strResDesc().localizedByLocal(),
                isSelected = selectedLanguage == AppLanguage.UKR,
                onClick = { component.onLanguageClick(AppLanguage.UKR) }
            )
        }

        item {
            BottomSheetListItem(
                text = R.string.account_main_language_kazakh.strResDesc().localizedByLocal(),
                isSelected = selectedLanguage == AppLanguage.KAZ,
                onClick = { component.onLanguageClick(AppLanguage.KAZ) }
            )
        }

        item {
            BottomSheetListItem(
                text = R.string.account_main_language_french.strResDesc().localizedByLocal(),
                isSelected = selectedLanguage == AppLanguage.FRA,
                onClick = { component.onLanguageClick(AppLanguage.FRA) }
            )
        }

        item {
            BottomSheetListItem(
                text = R.string.account_main_language_arab.strResDesc().localizedByLocal(),
                isSelected = selectedLanguage == AppLanguage.ARB,
                onClick = { component.onLanguageClick(AppLanguage.ARB) }
            )
        }
    }
}

@Preview
@Composable
fun AccountLanguageUiPreview() {
    AppTheme {
        AccountLanguageUi(component = FakeAccountLanguageComponent())
    }
}
