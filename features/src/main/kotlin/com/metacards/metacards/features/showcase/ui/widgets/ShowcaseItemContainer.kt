package com.metacards.metacards.features.showcase.ui.widgets

import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme

@Composable
fun ColumnScope.ShowcaseItemContainer(
    title: String,
    subtitle: String?,
    content: @Composable ColumnScope.() -> Unit
) {
    Text(
        text = title,
        style = CustomTheme.typography.heading.medium,
        color = CustomTheme.colors.text.primary,
        modifier = Modifier
            .padding(horizontal = 16.dp)
    )

    subtitle?.let {
        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = it,
            style = CustomTheme.typography.body.primary,
            color = CustomTheme.colors.text.secondary,
            modifier = Modifier
                .padding(horizontal = 16.dp)
        )
    }

    Spacer(modifier = Modifier.height(16.dp))

    content()

    Spacer(modifier = Modifier.height(24.dp))
}