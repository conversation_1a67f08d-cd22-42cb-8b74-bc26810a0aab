package com.metacards.metacards.features.special_deck.data.dto

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckConstellation

data class SpecialDeckConstellationDto(
    val bannerDescLocalized: Map<String, String?> = emptyMap(),
    val bannerImg: String = "",
    val bannerTitleLocalized: Map<String, String?> = emptyMap(),
    val cardIds: List<String> = emptyList(),
    val nameAngle: Int = 0,
    val nameAxisX: Int = 0,
    val nameAxisY: Int = 0,
    val nameLocalized: Map<String, String?> = emptyMap(),
) {
    fun toDomain() = SpecialDeckConstellation(
        bannerDescLocalized = LocalizableString(bannerDescLocalized),
        bannerImg = bannerImg,
        bannerTitleLocalized = LocalizableString(bannerTitleLocalized),
        cardIds = cardIds.map(::CardId),
        nameAngle = nameAngle,
        nameAxisX = nameAxisX,
        nameAxisY = nameAxisY,
        name = LocalizableString(nameLocalized)
    )
}