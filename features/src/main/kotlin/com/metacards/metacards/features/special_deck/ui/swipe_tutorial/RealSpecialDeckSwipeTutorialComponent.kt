package com.metacards.metacards.features.special_deck.ui.swipe_tutorial

import com.arkivanov.decompose.ComponentContext

class RealSpecialDeckSwipeTutorialComponent(
    componentContext: ComponentContext,
    private val onOutput: (SpecialDeckSwipeTutorialComponent.Output) -> Unit
) : ComponentContext by componentContext, SpecialDeckSwipeTutorialComponent {

    override fun onWowClick() {
        onOutput(SpecialDeckSwipeTutorialComponent.Output.DismissRequested)
    }
}