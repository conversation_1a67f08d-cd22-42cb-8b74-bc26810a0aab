package com.metacards.metacards.features.course.domain.entity

import com.metacards.metacards.features.course.data.dto.CourseCompletedContentDto
import com.metacards.metacards.features.course.data.dto.CoursePassedCoursesDto

data class CoursePassedCourse(
    val courseId: CourseId,
    val isCompleted: <PERSON><PERSON><PERSON>,
    val completedContent: List<CourseCompletedContent>
) {
    companion object {
        fun createEmpty(courseId: CourseId) = CoursePassedCourse(
            courseId = courseId,
            isCompleted = false,
            completedContent = emptyList()
        )

        fun fromDomain(entity: CoursePassedCourse) = CoursePassedCoursesDto(
            courseId = entity.courseId.value,
            isCompleted = entity.isCompleted,
            completedContent = entity.completedContent.map { CourseCompletedContentDto.fromDomain(it) }
        )
    }
}