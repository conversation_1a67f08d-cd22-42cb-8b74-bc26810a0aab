package com.metacards.metacards.features.course.data.dto

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.course.domain.entity.CourseContentId
import com.metacards.metacards.features.course.domain.entity.CourseQuestion
import com.metacards.metacards.features.course.domain.entity.CourseResult
import com.metacards.metacards.features.course.domain.entity.CourseTest
import com.metacards.metacards.features.course.domain.entity.LinkedVideo
import com.metacards.metacards.features.layout.domain.LayoutId

data class CourseTestDto(
    val id: String? = null,
    val name: Map<String, String> = emptyMap(),
    val questions: List<CourseQuestionDto> = emptyList(),
    val results: List<CourseResultDto> = emptyList()
) {
    class Request(
        val themeId: String,
        val courseId: String,
        val testId: String,
    ) {
        companion object {
            fun fromDomain(query: CourseTest.Query) = Request(
                themeId = query.themeId.value,
                courseId = query.courseId.value,
                testId = query.testId.value
            )
        }
    }
}

class CourseQuestionDto(
    val nameLocalized: Map<String, String> = emptyMap(),
    val image: String? = null,
    val order: Int = 0,
    val yesScore: Int = 0,
    val noScore: Int = 0
)

class CourseResultDto(
    val cover: String? = null,
    val video: Map<String, String>? = null,
    val title: Map<String, String> = emptyMap(),
    val subtitle: Map<String, String>? = null,
    val description: Map<String, String>? = null,
    val minScore: Int = 0,
    val maxScore: Int = 0,
    val linkedLayouts: List<String>? = null,
    val linkedVideos: List<LinkedVideoDto>? = null
)

class LinkedVideoDto(
    val cover: String? = null,
    val videoLocalized: Map<String, String?>? = null,
) {
    companion object {
        fun fromDomain(entity: LinkedVideo) = LinkedVideoDto(
            cover = entity.cover,
            videoLocalized = entity.video.toMap()
        )
    }
}

fun CourseTestDto.toDomain() = CourseTest(
    testId = CourseContentId(id ?: ""),
    name = LocalizableString(name),
    questions = questions.map { it.toDomain() },
    results = results.map { it.toDomain() },
)

fun CourseQuestionDto.toDomain() = CourseQuestion(
    name = LocalizableString(nameLocalized),
    image = image,
    order = order,
    yesScore = yesScore,
    noScore = noScore
)

fun CourseResultDto.toDomain() = CourseResult(
    cover = cover,
    video = video?.let(::LocalizableString),
    title = LocalizableString(title),
    subtitle = subtitle?.let(::LocalizableString),
    description = description?.let(::LocalizableString),
    minScore = minScore,
    maxScore = maxScore,
    linkedLayouts = linkedLayouts?.map(::LayoutId),
    linkedVideos = linkedVideos?.mapNotNull { it.toDomain() }
)

fun LinkedVideoDto.toDomain(): LinkedVideo? {
    return LinkedVideo(
        cover = cover,
        video = videoLocalized?.let(::LocalizableString) ?: return null
    )
}