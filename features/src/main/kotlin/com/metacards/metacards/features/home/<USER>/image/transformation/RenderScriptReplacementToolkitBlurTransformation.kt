package com.metacards.metacards.features.home.ui.image.transformation

import android.graphics.Bitmap
import coil.size.Size
import coil.transform.Transformation
import com.google.android.renderscript.Toolkit

class RenderScriptReplacementToolkitBlurTransformation(private val radius: Int = 25) : Transformation {

    override val cacheKey: String = "${javaClass.name}-$radius"

    override suspend fun transform(input: Bitmap, size: Size): Bitmap {
        return Toolkit.blur(input, radius)
    }
}
