package com.metacards.metacards.features.account.domain.repository

import com.metacards.metacards.core.functions.FunctionResult
import com.metacards.metacards.features.account.domain.entity.Promocode
import com.metacards.metacards.features.account.domain.entity.PromocodeInfo

interface PromocodesRepository {
    suspend fun getPromocodes(): List<PromocodeInfo>
    suspend fun activatePromocode(promocode: Promocode): FunctionResult<Map<String, Any>>
}