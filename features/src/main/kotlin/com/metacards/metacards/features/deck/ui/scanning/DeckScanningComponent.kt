package com.metacards.metacards.features.deck.ui.scanning

import com.metacards.metacards.core.camera.QrCode
import com.metacards.metacards.features.deck.domain.entity.DeckId
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.StateFlow

interface DeckScanningComponent {

    sealed interface State {
        object RequestingPermission : State
        object PermissionDenied : State
        object Scanning : State
        object DeckAddingInProgress : State
        data class DeckAddingFailed(
            val errorTitle: StringDesc,
            val errorMessage: StringDesc
        ) : State
    }

    val state: StateFlow<State>

    fun onQrCodeScanned(qrCode: QrCode)

    fun onGoToSettingsClick()

    fun onDeckAddingErrorClosed()

    sealed interface Output {
        data class DeckAdded(val deckId: DeckId) : Output
        object RejectedScanning : Output
    }
}