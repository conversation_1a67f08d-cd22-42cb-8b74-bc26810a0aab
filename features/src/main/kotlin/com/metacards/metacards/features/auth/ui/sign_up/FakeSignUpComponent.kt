package com.metacards.metacards.features.auth.ui.sign_up

import com.metacards.metacards.core.utils.createFakeChildStackStateFlow
import com.metacards.metacards.features.auth.ui.auth_type.email.FakeEmailEnterComponent
import kotlinx.coroutines.CoroutineScope

class FakeSignUpComponent(coroutineScope: CoroutineScope) : SignUpComponent {
    override val childStack = createFakeChildStackStateFlow(
        SignUpComponent.Child.EmailEnter(FakeEmailEnterComponent(coroutineScope))
    )
    override fun onCloseButtonClick() = Unit
}