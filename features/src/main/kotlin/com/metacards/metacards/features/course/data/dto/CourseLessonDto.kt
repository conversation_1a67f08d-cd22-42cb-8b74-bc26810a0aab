package com.metacards.metacards.features.course.data.dto

import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.course.domain.entity.CourseLesson

data class CourseLessonDto(
    val name: Map<String, String?> = emptyMap(),
    val cards: List<CourseLessonCardDto> = emptyList()
) {
    data class Request(
        val themeId: String,
        val courseId: String,
        val lessonId: String
    ) {
        companion object {
            fun fromDomain(
                domain: CourseLesson.Query
            ) = Request(
                domain.themeId.value,
                domain.courseId.value,
                domain.lessonId.value
            )
        }
    }

    fun toDomain() = CourseLesson(
        name = LocalizableString(name),
        cards = cards.map { it.toDomain() }
    )
}