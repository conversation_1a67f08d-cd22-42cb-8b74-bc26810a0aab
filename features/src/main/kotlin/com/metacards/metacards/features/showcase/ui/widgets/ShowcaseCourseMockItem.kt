package com.metacards.metacards.features.showcase.ui.widgets

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.strResDesc

private const val ROW_WIDTH = 312
private const val CARD_ASPECT_RATIO = 1.84f

@Composable
fun ColumnScope.ShowcaseCourseMockItem() {
    ShowcaseItemContainer(
        title = R.string.showcase_programs_title.strResDesc().localizedByLocal(),
        subtitle = R.string.showcase_programs_subtitle.strResDesc().localizedByLocal()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .horizontalScroll(rememberScrollState())
        ) {
            val itemModifier = Modifier
                .width(ROW_WIDTH.dp)
                .background(CustomTheme.colors.background.primary, RoundedCornerShape(16.dp))

            Spacer(modifier = Modifier.width(16.dp))
            ShowcaseMockItem(
                bg = R.drawable.ic_showcase_program_mock_bg_1,
                title = R.string.showcase_programs_mock_item_1_title,
                subtitle = R.string.showcase_programs_mock_item_1_subtitle,
                modifier = itemModifier
            )

            Spacer(modifier = Modifier.width(8.dp))

            ShowcaseMockItem(
                bg = R.drawable.ic_account_banner_bg_premium,
                title = R.string.showcase_programs_mock_item_2_title,
                subtitle = R.string.showcase_programs_mock_item_2_subtitle,
                modifier = itemModifier
            )

            Spacer(modifier = Modifier.width(16.dp))
        }
    }
}
@Composable
private fun ShowcaseMockItem(
    @DrawableRes bg: Int,
    @StringRes title: Int,
    @StringRes subtitle: Int,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
    ) {
        Spacer(modifier = Modifier.height(16.dp))

        Box {
            Image(
                painter = painterResource(bg),
                contentDescription = null,
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(CARD_ASPECT_RATIO)
                    .padding(horizontal = 16.dp)
                    .clip(RoundedCornerShape(16.dp)),
                contentScale = ContentScale.FillBounds
            )

            Box(
                modifier = Modifier
                    .padding(horizontal = 32.dp, vertical = 16.dp)
                    .background(CustomTheme.colors.button.small, RoundedCornerShape(30.dp)),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = R.string.showcase_programs_mock_item_hint.strResDesc().localizedByLocal(),
                    style = CustomTheme.typography.button.small,
                    fontSize = 13.sp,
                    color = CustomTheme.colors.text.caption,
                    modifier = Modifier
                        .padding(vertical = 8.dp, horizontal = 24.dp)
                )
            }
        }

        Spacer(modifier = Modifier.height(12.dp))

        Text(
            text = title.strResDesc().localizedByLocal(),
            style = CustomTheme.typography.heading.medium,
            color = CustomTheme.colors.text.primary,
            modifier = Modifier
                .padding(horizontal = 16.dp)
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = subtitle.strResDesc().localizedByLocal(),
            style = CustomTheme.typography.caption.medium,
            color = CustomTheme.colors.text.secondary,
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .heightIn(min = 34.dp)
        )

        Spacer(modifier = Modifier.height(16.dp))
    }
}