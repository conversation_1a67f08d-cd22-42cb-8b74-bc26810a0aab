package com.metacards.metacards.features.auth.ui.auth_type.email

import com.metacards.metacards.core.button.ButtonState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import ru.mobileup.kmm_form_validation.control.InputControl

class FakeEmailEnterComponent(coroutineScope: CoroutineScope) : EmailEnterComponent {
    override val emailInputControl: InputControl = InputControl(coroutineScope)
    override val showSignInButton: StateFlow<Boolean> = MutableStateFlow(false)
    override val showSignUpButton: StateFlow<Boolean> = MutableStateFlow(false)
    override val showCheckbox: StateFlow<Boolean> = MutableStateFlow(true)
    override val checkboxState: StateFlow<Boolean> = MutableStateFlow(true)
    override val nextButtonState: StateFlow<ButtonState> = MutableStateFlow(ButtonState.Enabled)

    override fun onNextButtonClick() = Unit
    override fun onSignUpButtonClick() = Unit
    override fun onSignInButtonClick() = Unit
    override fun onCheckboxStateChange(checked: Boolean) = Unit
    override fun openLink(url: String) = Unit
    override fun onGoBackClick() = Unit
    override fun onFocusRequest() = Unit
}