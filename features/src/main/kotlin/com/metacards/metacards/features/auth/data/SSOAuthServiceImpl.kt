package com.metacards.metacards.features.auth.data

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

class SSOAuthServiceImpl(
    private val coroutineScope: CoroutineScope,
    private val firebaseAuthService: FirebaseAuthService
) : SSOAuthService {

    private var onSignInSuccessfullyDismiss = {}

    override fun signIn(firebaseToken: String) {
        coroutineScope.launch {
            firebaseAuthService.signInByOtherSSO(firebaseToken)
            onSignInSuccessfullyDismiss()
        }
    }

    override fun setOnSignInSuccessfullyDismiss(onDismiss: () -> Unit) {
        onSignInSuccessfullyDismiss = onDismiss
    }
}