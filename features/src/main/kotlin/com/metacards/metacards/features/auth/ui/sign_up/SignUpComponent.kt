package com.metacards.metacards.features.auth.ui.sign_up

import android.os.Parcelable
import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.core.user.domain.Email
import com.metacards.metacards.features.auth.ui.auth_type.email.EmailEnterComponent
import com.metacards.metacards.features.auth.ui.auth_type.email.message.SentMessageToEmailComponent
import com.metacards.metacards.features.auth.ui.auth_type.pass.create_pass.CreatePasswordComponent
import kotlinx.coroutines.flow.StateFlow
import kotlinx.parcelize.Parcelize

interface SignUpComponent {
    val childStack: StateFlow<ChildStack<*, Child>>

    fun onCloseButtonClick()

    sealed interface Child {
        class EmailEnter(val component: EmailEnterComponent) : Child
        class CreatePassword(val component: CreatePasswordComponent) : Child
        class SentMessageToEmail(val component: SentMessageToEmailComponent) : Child
    }

    sealed interface Output {
        data object SignInRequested : Output
        data object MainScreenRequested : Output
        data object AuthScreenRequested : Output
        data class WebViewRequested(val url: String) : Output
        data object GoBackRequested : Output
    }

    sealed interface StartCommand : Parcelable {
        @Parcelize
        data class OpenEmailVerification(val email: Email) : StartCommand
    }
}