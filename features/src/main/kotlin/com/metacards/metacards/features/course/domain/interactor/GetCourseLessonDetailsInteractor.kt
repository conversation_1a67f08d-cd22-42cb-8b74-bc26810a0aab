package com.metacards.metacards.features.course.domain.interactor

import android.webkit.URLUtil
import com.metacards.metacards.core.localization.data.AppLanguageService
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.course.domain.entity.CourseLesson
import com.metacards.metacards.features.course.domain.entity.CourseLessonCardType
import com.metacards.metacards.features.course.domain.entity.CourseLessonContent
import com.metacards.metacards.features.course.domain.repository.CourseRepository
import com.metacards.metacards.features.course.ui.lesson.CourseLessonCards

class GetCourseLessonDetailsInteractor(
    val repository: CourseRepository,
    private val appLanguageService: AppLanguageService,
) {
    suspend fun execute(query: CourseLesson.Query): CourseLessonCards {
        val data = repository.getCourseLessonDetails(query)
        val language = appLanguageService.getLanguage()
        val filteredCards = data?.cards?.mapNotNull {
            when (it.cardType) {
                CourseLessonCardType.VIDEO -> {
                    val url = it.videoUrl?.toString(language)
                    val coverUrl = it.videoCoverUrl
                    val valid = URLUtil.isValidUrl(url) && URLUtil.isValidUrl(coverUrl)
                    // if any url is null it can't be valid
                    if (valid) {
                        CourseLessonContent.Video(
                            videoCoverUrl = coverUrl!!,
                            url = url!!
                        )
                    } else {
                        null
                    }
                }

                CourseLessonCardType.IMAGE -> {
                    val url = it.imageUrl?.toString(language)
                    val valid = URLUtil.isValidUrl(url)
                    if (valid) CourseLessonContent.Image(url!!) else null
                }
            }
        } ?: emptyList()
        return CourseLessonCards(
            name = data?.name ?: LocalizableString.createNonLocalizable(""),
            cards = filteredCards
        )
    }
}