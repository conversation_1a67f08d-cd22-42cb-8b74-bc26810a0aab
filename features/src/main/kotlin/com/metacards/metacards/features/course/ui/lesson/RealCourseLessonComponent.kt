package com.metacards.metacards.features.course.ui.lesson

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.activity.ActivityProvider
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.createDefaultDialogComponent
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.model.DialogData
import com.metacards.metacards.core.dialog.dialogControl
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.features.R
import com.metacards.metacards.features.course.domain.entity.CourseContentType
import com.metacards.metacards.features.course.domain.entity.CourseLesson
import com.metacards.metacards.features.course.domain.interactor.GetCourseLessonDetailsInteractor
import com.metacards.metacards.features.course.domain.interactor.UpdateUserPassedContentInteractor
import com.metacards.metacards.features.video_player.VideoPlayerActivity
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

class RealCourseLessonComponent(
    componentContext: ComponentContext,
    componentFactory: ComponentFactory,
    private val onOutput: (CourseLessonComponent.Output) -> Unit,
    private val query: CourseLesson.Query,
    override val lastCardButtonText: String,
    private val order: Int,
    override val courseName: LocalizableString?,
    private val activityProvider: ActivityProvider,
    getCourseLessonDetailsInteractor: GetCourseLessonDetailsInteractor,
    private val updateUserPassedContentInteractor: UpdateUserPassedContentInteractor,
    private val errorHandler: ErrorHandler
) : ComponentContext by componentContext, CourseLessonComponent {

    override val cards = MutableStateFlow<CourseLessonCards?>(null)
    override val closeDialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        dialogControl(dialogComponentFactory = { config, componentContext, dialogControl ->
            componentFactory.createDefaultDialogComponent(
                componentContext,
                config.data,
                dialogControl::dismiss
            )
        })
    private val debounce = Debounce()

    private val closeDialogConfig by lazy {
        DefaultDialogComponent.Config(
            DialogData(
                title = StringDesc.Resource(R.string.course_close_dialog_title),
                message = StringDesc.Resource(R.string.course_lesson_close_dialog_text),
                buttons = listOf(
                    DialogData.Button(
                        title = StringDesc.Resource(R.string.course_lesson_close_dialog_cancel_button),
                        action = closeDialogControl::dismiss
                    ),
                    DialogData.Button(
                        title = StringDesc.Resource(R.string.course_lesson_close_dialog_close_button),
                        action = {
                            closeDialogControl.dismiss()
                            onOutput(CourseLessonComponent.Output.CloseRequested)
                        }
                    )
                )
            )
        )
    }

    init {
        componentScope.safeLaunch(errorHandler) {
            cards.value = getCourseLessonDetailsInteractor.execute(query)
        }
    }

    override fun onToNextContentClick() {
        DebounceClick(debounce = debounce, "onToNextContentClick", errorHandler) {
            updateUserPassedContentInteractor.execute(
                themeId = query.themeId,
                courseId = query.courseId,
                contentId = query.lessonId,
                type = CourseContentType.LESSON
            )
            onOutput(CourseLessonComponent.Output.NextContentRequested(order))
        }
    }

    override fun onVideoFullScreenClick(position: Long, videoUrl: String) {
        componentScope.launch {
            activityProvider.awaitActivity().run {
                VideoPlayerActivity.start(
                    videoUrl = videoUrl,
                    context = this,
                    startWithLandscape = true,
                    playOnStart = true,
                    position = position
                )
            }
        }
    }

    override fun onCloseClick() {
        closeDialogControl.show(closeDialogConfig)
    }
}