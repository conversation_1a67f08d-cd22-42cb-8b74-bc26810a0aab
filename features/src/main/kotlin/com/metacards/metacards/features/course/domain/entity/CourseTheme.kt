package com.metacards.metacards.features.course.domain.entity

import android.os.Parcelable
import com.metacards.metacards.core.localization.ui.LocalizableString
import kotlinx.parcelize.Parcelize

@Parcelize
data class CourseTheme(
    val themeId: CourseThemeId,
    val name: LocalizableString,
    val description: LocalizableString?,
    val status: CourseStatus,
    val coverUrl: String,
    val courses: List<CourseShortData>
) : Parcelable {
    companion object {
        val mock = CourseTheme(
            themeId = CourseThemeId(""),
            name = LocalizableString.createNonLocalizable(""),
            description = null,
            coverUrl = "",
            status = CourseStatus.ACTIVE,
            courses = emptyList()
        )
    }
}