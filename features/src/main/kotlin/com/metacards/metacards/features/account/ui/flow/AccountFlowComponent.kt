package com.metacards.metacards.features.account.ui.flow

import android.os.Parcelable
import androidx.annotation.StringRes
import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.core.message.domain.Message
import com.metacards.metacards.core.widget.navigation_bar.NavigationPage
import com.metacards.metacards.features.account.ui.about_the_app.AboutTheAppComponent
import com.metacards.metacards.features.account.ui.about_the_app.about_us.AboutUsComponent
import com.metacards.metacards.features.account.ui.feedback.FeedbackComponent
import com.metacards.metacards.features.account.ui.lessons.LessonsComponent
import com.metacards.metacards.features.account.ui.profile.ProfileFlowComponent
import com.metacards.metacards.features.account.ui.promocodes.PromocodeComponent
import com.metacards.metacards.features.account.ui.shop.ShopComponent
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.user.ui.subscription.SubscriptionComponent
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.StateFlow
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue

interface AccountFlowComponent {

    val childStack: StateFlow<ChildStack<*, Child>>

    sealed interface Child {
        class Promocodes(val component: PromocodeComponent) : Child
        class Subscription(val component: SubscriptionComponent) : Child
        class Lessons(val component: LessonsComponent) : Child
        class AboutTheApp(val component: AboutTheAppComponent) : Child
        class AboutUs(val component: AboutUsComponent) : Child
        class Shop(val component: ShopComponent) : Child
        class ProfileFlow(val component: ProfileFlowComponent) : Child
        class Feedback(val component: FeedbackComponent) : Child
    }

    sealed interface Screen : Parcelable {
        @Parcelize
        data object Promocodes : Screen

        @Parcelize
        object Subscription : Screen

        @Parcelize
        data class Lessons(val screen: LessonsComponent.Screen) : Screen

        @Parcelize
        object AboutTheApp : Screen

        @Parcelize
        object Shop : Screen

        @Parcelize
        object ProfileFlow : Screen

        @Parcelize
        object Feedback : Screen
    }

    sealed interface Output {
        data class MainScreenRequested(val navigationPage: NavigationPage) : Output
        data class WebViewRequestedResource(val url: @RawValue StringDesc, @StringRes val title: Int) : Output
        data class WebViewRequested(val url: String, @StringRes val title: Int) : Output
        data class DeckDetailsRequested(val deckId: DeckId) : Output
        object AuthScreenRequested : Output
        data class ShowMessageRequested(val message: Message) : Output
        data class SignInViaWebViewRequested(val url: String, @StringRes val title: Int) : Output
        object WebViewDismissRequested : Output
    }
}