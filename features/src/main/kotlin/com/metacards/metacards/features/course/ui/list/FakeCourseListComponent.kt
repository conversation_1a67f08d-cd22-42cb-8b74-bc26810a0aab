package com.metacards.metacards.features.course.ui.list

import com.metacards.metacards.features.course.domain.entity.CourseShortData
import com.metacards.metacards.features.course.domain.entity.CourseTheme
import kotlinx.coroutines.flow.MutableStateFlow

class FakeCourseListComponent : CourseListComponent {
    override val theme: CourseTheme = CourseTheme.mock
    override val isPremiumUser = MutableStateFlow(false)
    override fun onCourseClick(courseShortData: CourseShortData) = Unit
}