package com.metacards.metacards.features.user_analytics.domain.week

import com.metacards.metacards.core.utils.TOTAL_DAYS_IN_WEEK
import com.metacards.metacards.core.utils.toLocalDate
import com.metacards.metacards.features.record.domain.Record
import kotlinx.datetime.Clock
import kotlinx.datetime.DatePeriod
import kotlinx.datetime.LocalDate
import kotlinx.datetime.plus

data class WeekAnalyticsInfo(
    val firstDayOfWeek: LocalDate,
    val records: List<Record>
) {

    val id: String by lazy {
        firstDayOfWeek.toString()
    }

    val weekDays: List<LocalDate> by lazy {
        (0 until TOTAL_DAYS_IN_WEEK).map { day ->
            firstDayOfWeek + DatePeriod(days = day)
        }
    }

    val recordsWithDescendingDates by lazy {
        records.reversed()
    }

    val averageMoodLevel: Float? by lazy {
        calculateAverageLevel(records) { it.moodLevel }
    }

    val averageEnergyLevel: Float? by lazy {
        calculateAverageLevel(records) { it.energyLevel }
    }

    companion object {
        private val now = Clock.System.now().toLocalDate()
        val MOCK = mock(now)

        fun mock(date: LocalDate): WeekAnalyticsInfo {
            return WeekAnalyticsInfo(
                firstDayOfWeek = date,
                records = Record.LIST_MOCK
            )
        }
    }
}

private fun calculateAverageLevel(
    records: List<Record>,
    levelProvider: (Record) -> Int?
): Float? {
    val recordCount = records.count { levelProvider(it) != null }
    if (recordCount == 0) return null

    val levelSum = records.mapNotNull(levelProvider).sum()
    return levelSum / recordCount.toFloat()
}