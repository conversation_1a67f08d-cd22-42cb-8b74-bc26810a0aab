package com.metacards.metacards.features.layout.ui.predefined_layout

import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.FakeDialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.FakeDefaultDialogComponent
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.features.layout.domain.LayoutId
import com.metacards.metacards.features.layout.domain.PredefinedLayoutGroupWithAvailable
import kotlinx.coroutines.flow.MutableStateFlow

class FakePredefinedLayoutComponent : PredefinedLayoutComponent {
    override val filteredLayoutGroups = MutableStateFlow(
        LoadableState(data = listOf(PredefinedLayoutGroupWithAvailable.MOCK))
    )
    override val groupNames = MutableStateFlow(
        listOf(LocalizableString.createNonLocalizable("Some Group"))
    )
    override val currentGroupIndex = MutableStateFlow(0)
    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        FakeDialogControl(FakeDefaultDialogComponent())

    override fun onLayoutClick(layoutId: LayoutId) = Unit
    override fun onBlockContentClick() = Unit
    override fun onGroupClick(index: Int) = Unit
}