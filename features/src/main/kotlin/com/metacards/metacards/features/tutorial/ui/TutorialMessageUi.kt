package com.metacards.metacards.features.tutorial.ui

import androidx.compose.animation.core.animate
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import com.metacards.metacards.core.R
import com.metacards.metacards.core.dialog.default_component.DefaultDialog
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.features.tutorial.domain.MessageAlignment
import com.metacards.metacards.features.tutorial.domain.TutorialMessage
import com.metacards.metacards.features.tutorial.domain.TutorialMessageArrow
import dev.icerock.moko.resources.compose.localized

private const val ANIMATION_DURATION_MILLS = 800

/**
 * Displays a [TutorialMessage] as a popup.
 */
@Composable
fun TutorialMessageUi(
    component: TutorialMessageComponent,
    modifier: Modifier = Modifier
) {
    val tutorialInProgress by component.tutorialInProgress.collectAsState()

    if (tutorialInProgress) {
        Box(
            modifier = modifier
                .fillMaxSize()
                .statusBarsPadding()
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_32_close),
                contentDescription = null,
                tint = CustomTheme.colors.system.unspecified,
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(end = 16.dp, top = 8.dp)
                    .clickable(
                        boundedRipple = false,
                        onClick = component::onTutorialCancel
                    )
            )

            DefaultDialog(component.dialogControl)
        }
    }
}

@Composable
fun MessagePopupContent(
    tutorialMessage: TutorialMessage,
    modifier: Modifier = Modifier
) {
    val content = tutorialMessage.content ?: return
    var alpha by remember { mutableFloatStateOf(1f) }

    LaunchedEffect(key1 = tutorialMessage) {
        animate(0f, 1f, animationSpec = tween(ANIMATION_DURATION_MILLS)) { value, _ ->
            alpha = value
        }
    }
    CompositionLocalProvider(LocalLayoutDirection provides LayoutDirection.Ltr) {
        ConstraintLayout(
            modifier = modifier.alpha(alpha)
        ) {
            val (card, topArrow, bottomArrow) = createRefs()

            Card(
                shape = RoundedCornerShape(16.dp),
                backgroundColor = CustomTheme.colors.background.alert,
                elevation = 0.dp,
                modifier = Modifier
                    .width(272.dp)
                    .border(
                        width = 0.5.dp,
                        color = CustomTheme.colors.stroke.secondary,
                        shape = RoundedCornerShape(16.dp)
                    )
                    .wrapContentSize()
                    .constrainAs(card) {
                        top.linkTo(parent.top)
                        start.linkTo(parent.start)
                    }
            ) {
                Column(
                    modifier = Modifier
                        .padding(horizontal = 24.dp, vertical = 16.dp)
                        .fillMaxWidth()
                ) {
                    content.iconRes?.let {
                        Icon(
                            painter = painterResource(it),
                            tint = Color.Unspecified,
                            contentDescription = null,
                            modifier = Modifier
                                .align(Alignment.CenterHorizontally)
                                .padding(bottom = 12.dp)
                        )
                    }
                    Text(
                        text = content.text.localized(),
                        color = CustomTheme.colors.text.caption,
                        style = CustomTheme.typography.body.primary
                    )
                    content.actionTitle?.let {
                        ActionButton(
                            text = it.localized(),
                            onClick = { content.action?.invoke() },
                            modifier = Modifier
                                .align(Alignment.End)
                                .padding(top = 12.dp)
                        )
                    }
                }
            }

            when (tutorialMessage.arrow) {
                TutorialMessageArrow.TOP, TutorialMessageArrow.TOP_LEFT -> {
                    val isTopCenterArrow =
                        tutorialMessage.arrow == TutorialMessageArrow.TOP
                    val arrowPaddingStart =
                        if (isTopCenterArrow) 0.dp else 16.dp
                    Arrow(
                        isInverted = false,
                        modifier = Modifier
                            .constrainAs(topArrow) {
                                bottom.linkTo(card.top)
                                start.linkTo(card.start)
                                if (isTopCenterArrow) end.linkTo(card.end)
                            }
                            .padding(start = arrowPaddingStart)
                    )
                }

                TutorialMessageArrow.BOTTOM, TutorialMessageArrow.BOTTOM_RIGHT -> {
                    val isBottomCenterArrow =
                        tutorialMessage.arrow == TutorialMessageArrow.BOTTOM
                    Arrow(
                        isInverted = true,
                        modifier = Modifier
                            .constrainAs(bottomArrow) {
                                top.linkTo(card.bottom)
                                if (isBottomCenterArrow) {
                                    start.linkTo(card.start)
                                }
                                end.linkTo(card.end)
                            }
                            .padding(
                                end = if (isBottomCenterArrow) {
                                    0.dp
                                } else {
                                    28.dp
                                }
                            )
                    )
                }

                else -> {
                    // Do nothing
                }
            }
        }
    }
}

@Composable
private fun ActionButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Text(
        text = text.uppercase(),
        color = CustomTheme.colors.text.caption,
        style = CustomTheme.typography.caption.medium,
        modifier = modifier.clickable { onClick() }
    )
}

@Composable
private fun Arrow(
    isInverted: Boolean,
    modifier: Modifier = Modifier
) {
    val offset = if (isInverted) {
        (-1).dp
    } else {
        1.dp
    }
    Canvas(
        modifier = modifier
            .size(15.dp)
            .offset(y = offset)
    ) {
        val trianglePath = Path().apply {
            if (isInverted) {
                moveTo(0f, 0f)
                lineTo(size.width / 2f, size.height * 2 / 3)
                lineTo(size.width, 0f)
            } else {
                moveTo(0f, size.height)
                lineTo(size.width / 2f, size.height * 1 / 3)
                lineTo(size.width, size.height)
            }
        }
        drawPath(
            path = trianglePath,
            color = Color.Black,
        )
        drawPath(
            path = trianglePath,
            color = CustomTheme.colors.stroke.secondary,
            style = Stroke(width = 0.5.dp.toPx(), cap = StrokeCap.Round)
        )
    }
}

private fun MessageAlignment.toComposeAlignment() = when (this) {
    MessageAlignment.CENTER -> Alignment.Center
    MessageAlignment.TOP -> Alignment.TopCenter
    MessageAlignment.BOTTOM -> Alignment.BottomCenter
    MessageAlignment.BOTTOM_START -> Alignment.BottomStart
}

@Preview(showSystemUi = true)
@Composable
fun TutorialMessageUiPreview() {
    AppTheme {
        TutorialMessageUi(FakeTutorialMessageComponent(), Modifier)
    }
}