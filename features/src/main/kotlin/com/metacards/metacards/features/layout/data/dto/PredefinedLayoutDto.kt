package com.metacards.metacards.features.layout.data.dto

import com.google.firebase.firestore.PropertyName
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.layout.domain.LayoutId
import com.metacards.metacards.features.layout.domain.PredefinedLayout
import com.metacards.metacards.features.layout.domain.PredefinedQuestion

class PredefinedLayoutDto(
    val id: String = "",
    val nameLocalized: Map<String, String?> = emptyMap(),
    val cover: String = "",
    @get:PropertyName("isFree") // required for a field that starts with "is". See: https://medium.com/@eeddeellee/boolean-fields-that-start-with-is-in-firebase-firestore-49afb65e3639
    val isFree: Boolean = false,
    val questions: List<PredefinedQuestionDto> = listOf()
) {
    class PredefinedQuestionDto(
        val order: Int = 0,
        val questionLocalized: Map<String, String?> = emptyMap(),
        val deckId: String = ""
    )

    fun copy(
        id: String = this.id,
        name: Map<String, String?> = this.nameLocalized,
        cover: String = this.cover,
        isFree: Boolean = this.isFree,
        questions: List<PredefinedQuestionDto> = this.questions
    ): PredefinedLayoutDto {
        return PredefinedLayoutDto(id, name, cover, isFree, questions)
    }
}

fun PredefinedLayoutDto.toDomain(): PredefinedLayout {
    return PredefinedLayout(
        id = LayoutId(id),
        name = LocalizableString(nameLocalized),
        coverUrl = cover,
        isFree = isFree,
        questions = questions.map { it.toDomain() }
    )
}

fun PredefinedLayoutDto.PredefinedQuestionDto.toDomain(): PredefinedQuestion {
    return PredefinedQuestion(
        order = order,
        text = LocalizableString(questionLocalized),
        deckId = DeckId(deckId)
    )
}