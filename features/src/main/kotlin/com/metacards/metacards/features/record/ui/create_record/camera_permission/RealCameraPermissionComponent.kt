package com.metacards.metacards.features.record.ui.create_record.camera_permission

import android.Manifest
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.doOnStart
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.error_handling.safeRun
import com.metacards.metacards.core.external_apps.ExternalAppService
import com.metacards.metacards.core.permissions.PermissionService
import com.metacards.metacards.core.permissions.SinglePermissionResult
import com.metacards.metacards.core.preferences.PreferencesService
import com.metacards.metacards.core.utils.componentScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update

class RealCameraPermissionComponent(
    componentContext: ComponentContext,
    private val errorHandler: ErrorHandler,
    private val permissionService: PermissionService,
    private val preferencesService: PreferencesService,
    private val externalAppService: ExternalAppService,
    private val onOutput: (CameraPermissionComponent.Output) -> Unit
) : ComponentContext by componentContext, CameraPermissionComponent {
    override val permissionDialogViewed = MutableStateFlow(false)

    init {
        componentScope.safeLaunch(errorHandler) {
            permissionDialogViewed.update { preferencesService.getCameraPermissionDialogViewedFlag() }
        }

        lifecycle.doOnStart {
            safeRun(errorHandler) {
                if (permissionService.isPermissionGranted(CAMERA_PERMISSION)) {
                    onOutput(CameraPermissionComponent.Output.CameraRequested)
                }
            }
        }
    }

    override fun confirmDialog() {
        componentScope.safeLaunch(errorHandler) {
            when (permissionService.requestPermission(CAMERA_PERMISSION)) {
                is SinglePermissionResult.Denied -> {
                    onOutput(CameraPermissionComponent.Output.CreateRecordRequested)
                }

                is SinglePermissionResult.Granted -> {
                    onOutput(CameraPermissionComponent.Output.CameraRequested)
                }
            }
            preferencesService.setCameraPermissionDialogViewedFlag()
        }
    }

    override fun dismissDialog() {
        componentScope.safeLaunch(errorHandler) {
            preferencesService.setCameraPermissionDialogViewedFlag()
        }
        onOutput(CameraPermissionComponent.Output.CreateRecordRequested)
    }

    override fun openSettings() {
        safeRun(errorHandler) {
            externalAppService.openAppSettings()
        }
    }
}

private const val CAMERA_PERMISSION = Manifest.permission.CAMERA
