package com.metacards.metacards.features.account.data.data_source

import com.google.firebase.firestore.toObject
import com.metacards.metacards.core.functions.CloudFunctionsService
import com.metacards.metacards.core.functions.FunctionResult
import com.metacards.metacards.core.network.firestore.FirestoreService
import com.metacards.metacards.features.account.data.dto.PromocodeDto
import kotlinx.coroutines.tasks.await

class PromocodesDataSource(
    private val firestoreService: FirestoreService,
    private val functions: CloudFunctionsService
) {
    suspend fun getPromocodes(): List<PromocodeDto> {
        val result =
            firestoreService.db.collection(PROMOCODES_COLLECTION_PATH).get().await().documents

        return result.mapNotNull { it.toObject<PromocodeDto>()?.copy(id = it.id) }
    }

    suspend fun activatePromocode(promocode: String): FunctionResult<Map<String, Any>> {
        return functions.call(
            PROMOCODES_APPLY_FUNCTION_NAME,
            mapOf(PROMOCODES_APPLY_FUNCTION_PARAM to promocode)
        )
    }

    companion object {
        private const val PROMOCODES_COLLECTION_PATH = "promocodes"
        private const val PROMOCODES_APPLY_FUNCTION_NAME = "applyPromocode"
        private const val PROMOCODES_APPLY_FUNCTION_PARAM = "promocodeId"
    }
}