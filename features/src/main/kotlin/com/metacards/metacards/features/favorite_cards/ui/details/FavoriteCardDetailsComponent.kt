package com.metacards.metacards.features.favorite_cards.ui.details

import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.features.favorite_cards.domain.FavoriteCardInfo
import com.metacards.metacards.features.record.domain.RecordId
import kotlinx.coroutines.flow.StateFlow

interface FavoriteCardDetailsComponent {

    val cardInfoState: StateFlow<LoadableState<FavoriteCardInfo>>
    val isShareLoading: StateFlow<Boolean>

    fun onFavoriteClick()

    fun onRecordClick(recordId: RecordId)

    fun onToggleShare()

    sealed interface Output {
        data class RecordDetailsRequested(val recordId: RecordId) : Output
        data object RemovedFromFavorite : Output
    }
}