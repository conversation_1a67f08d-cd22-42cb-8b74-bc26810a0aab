package com.metacards.metacards.features.account.ui.widgets

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.features.R

@Composable
fun BottomSheetListItem(
    text: String,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() }
    ) {
        Icon(
            painter = painterResource(
                id = if (isSelected) {
                    R.drawable.ic_24_radio_button_on
                } else {
                    R.drawable.ic_24_radio_button_off
                }
            ),
            contentDescription = null,
            tint = CustomTheme.colors.icons.primary,
            modifier = Modifier
                .padding(vertical = 12.dp)
                .padding(start = 16.dp, end = 32.dp)
        )

        Text(
            text = text,
            color = CustomTheme.colors.text.primary,
            style = CustomTheme.typography.body.primary
        )
    }
}