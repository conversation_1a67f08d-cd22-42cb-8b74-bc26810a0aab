package com.metacards.metacards.features.account.ui.lessons.details

import androidx.annotation.OptIn
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.media3.common.util.UnstableApi
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.deck.ui.deck_info.widgets.MetaVideoPreviewCard
import dev.icerock.moko.resources.desc.strResDesc

@OptIn(UnstableApi::class)
@Composable
fun LessonDetailsUi(
    component: LessonDetailsComponent,
    modifier: Modifier = Modifier,
) {
    BoxWithFade(modifier, listOfColors = CustomTheme.colors.gradient.backgroundList) {
        Column(modifier = Modifier.matchParentSize()) {
            TopNavigationBar(
                title = R.string.account_lesson_details_title.strResDesc(),
                leadingIcon = { BackNavigationItem() }
            )
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .verticalScroll(rememberScrollState())
                    .navigationBarsPadding()
            ) {
                MetaVideoPreviewCard(
                    videoLesson = component.lesson,
                    textMaxLines = Int.MAX_VALUE,
                    backgroundColor = Color.Transparent,
                    onVideoPlay = component::onVideoPlay
                )
            }
        }
    }
}

@Preview
@Composable
private fun LessonDetailsUiPreview() {
    AppTheme {
        LessonDetailsUi(component = FakeLessonDetailsComponent())
    }
}