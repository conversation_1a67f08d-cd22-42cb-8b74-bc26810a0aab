package com.metacards.metacards.features.payments.ui.bottom_sheet

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.features.R
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun PaymentBottomSheetUi(
    component: PaymentBottomSheetComponent,
    modifier: Modifier = Modifier
) {
    val actionButtonState by component.buttonState.collectAsState()

    Card(
        backgroundColor = CustomTheme.colors.background.modal,
        elevation = 0.dp,
        shape = RoundedCornerShape(16.dp),
        modifier = modifier
            .padding(horizontal = 16.dp)
            .padding(bottom = 12.dp)
    ) {
        Column(modifier = Modifier.fillMaxWidth()) {
            Icon(
                painter = painterResource(id = R.drawable.ic_20_close),
                contentDescription = null,
                tint = CustomTheme.colors.system.unspecified,
                modifier = Modifier
                    .clickable { component.onDismiss() }
                    .padding(top = 16.dp, end = 16.dp, bottom = 4.dp)
                    .align(Alignment.End)
            )

            Image(
                painter = painterResource(id = R.drawable.bg_modal_payment),
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.CenterHorizontally)
                    .padding(bottom = 24.dp)
            )

            Text(
                text = R.string.deck_info_payment_ru_title.strResDesc().localizedByLocal(),
                color = CustomTheme.colors.text.primary,
                style = CustomTheme.typography.heading.medium,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .align(Alignment.CenterHorizontally)
                    .padding(horizontal = 48.dp)
                    .padding(bottom = 24.dp)
            )

            Text(
                text = R.string.deck_info_payment_ru_text.strResDesc().localizedByLocal(),
                color = CustomTheme.colors.text.primary,
                style = CustomTheme.typography.body.primary,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .align(Alignment.CenterHorizontally)
                    .padding(horizontal = 16.dp)
                    .padding(bottom = 24.dp)
            )

            MetaAccentButton(
                text = R.string.deck_info_payment_ru_button.strResDesc().localizedByLocal(),
                onClick = component::onWebStoreClick,
                state = actionButtonState,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .padding(bottom = 40.dp)
            )
        }
    }
}

@Preview
@Composable
fun PaymentBottomSheetUiPreview() {
    AppTheme {
        PaymentBottomSheetUi(component = FakePaymentBottomSheetComponent())
    }
}