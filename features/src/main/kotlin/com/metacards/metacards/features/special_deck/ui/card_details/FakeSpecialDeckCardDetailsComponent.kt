package com.metacards.metacards.features.special_deck.ui.card_details

import androidx.compose.ui.graphics.ImageBitmap
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.FakeDialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.FakeDefaultDialogComponent
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCard
import com.metacards.metacards.features.special_deck.domain.entity.UserSpecialDeckInfo
import kotlinx.coroutines.flow.MutableStateFlow

class FakeSpecialDeckCardDetailsComponent : SpecialDeckCardDetailsComponent {

    override val isCardAlreadyReceived: Boolean = false
    override val userSpecialDeckInfo: UserSpecialDeckInfo = UserSpecialDeckInfo.mock()
    override val card: SpecialDeckCard = SpecialDeckCard.mock
    override val cardEffectType: SpecialDeckCardDetailsComponent.CardEffectType =
        SpecialDeckCardDetailsComponent.CardEffectType.Glass
    override val blurredImage = MutableStateFlow<ImageBitmap?>(null)
    override val normalImage = MutableStateFlow<ImageBitmap?>(null)
    override val mirrorImage = MutableStateFlow<ImageBitmap?>(null)
    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        FakeDialogControl(FakeDefaultDialogComponent())

    override fun onReadyClick() = Unit
    override fun onCloseClick(isCardReleased: Boolean) = Unit
    override fun onCardReleased() = Unit
}