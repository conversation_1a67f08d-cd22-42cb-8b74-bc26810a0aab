package com.metacards.metacards.features.layout.ui

import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.arkivanov.decompose.extensions.compose.jetpack.stack.Children
import com.metacards.metacards.core.dialog.default_component.DefaultDialog
import com.metacards.metacards.features.layout.ui.card_list.CardListUi
import com.metacards.metacards.features.layout.ui.select_card.CardSelectUi

@Composable
fun DeckLayoutUi(
    component: DeckLayoutComponent,
    modifier: Modifier = Modifier
) {
    val childStack by component.childStack.collectAsState()

    Children(
        stack = childStack,
        modifier = modifier
    ) { child ->
        when (val instance = child.instance) {
            is DeckLayoutComponent.Child.CardList -> CardListUi(
                component = instance.component,
                modifier = modifier
                    .safeDrawingPadding()
                    .imePadding()
            )
            is DeckLayoutComponent.Child.SelectCard -> CardSelectUi(
                component = instance.component,
                modifier = modifier
            )
        }
    }

    DefaultDialog(component.dialogControl)
}

@Preview
@Composable
fun DeckLayoutUiPreview() {
}