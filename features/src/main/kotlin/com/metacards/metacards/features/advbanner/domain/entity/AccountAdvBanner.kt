package com.metacards.metacards.features.advbanner.domain.entity

import com.metacards.metacards.core.localization.ui.LocalizableString

data class AccountAdvBanner(
    val title: LocalizableString,
    val description: LocalizableString?,
    val buttonText: LocalizableString,
    val type: AccountAdvBannerType,
    val priority: Int,
    val style: AccountAdvBannerStyle
) {
    companion object {
        val MOCK = AccountAdvBanner(
            title = LocalizableString.createNonLocalizable(""),
            description = null,
            buttonText = LocalizableString.createNonLocalizable(""),
            type = AccountAdvBannerType.SUB,
            priority = -1,
            style = AccountAdvBannerStyle.CIRCLES,
        )
    }
}

enum class AccountAdvBannerType {
    SUB;
    companion object {
        fun fromString(value: String) = SUB
    }
}

enum class AccountAdvBannerStyle {
    STARS, CIRCLES;

    companion object {
        fun fromString(value: String) = if (value == STARS.name) STARS else CIRCLES
    }
}