package com.metacards.metacards.features.layout.ui.select_card.ar.sceneview

import android.content.ContentResolver
import android.content.Context
import android.content.res.Resources
import android.content.res.Resources.NotFoundException
import android.net.Uri
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import co.touchlab.kermit.Logger
import com.google.android.filament.Engine
import com.google.android.filament.Material
import com.google.android.filament.Texture
import com.metacards.metacards.features.R
import io.github.sceneview.material.MaterialLoader
import io.github.sceneview.texture.TextureLoader

class ArResourceLoader {
    private val logger = Logger.withTag("ArResourceLoader")

    var cardMaterial: Material? = null
        private set
    var planeMaterial: Material? = null
        private set
    var cardsTexture: List<Texture>? = null
        private set
    var planeNotFoundTexture: Texture? = null
        private set
    var planeFoundTexture: Texture? = null
        private set
    var selectedCardBackgroundTexture: Texture? = null
        private set

    suspend fun loadAllResources(context: Context, cardsTextureUri: List<Uri>) {
        loadCardMaterial(context)
        loadPlaneMaterial(context)
        loadCardsTexture(context, cardsTextureUri)
        loadPlaneNotFoundTexture(context)
        loadPlaneFoundTexture(context)
        loadSelectedCardBackgroundTexture(context)
    }

    private suspend fun loadCardMaterial(context: Context) {
        if (cardMaterial == null) {
            cardMaterial = MaterialLoader.loadMaterial(context, "models/image_texture.filamat")
        } else {
            logger.d("cardMaterial already loaded")
        }
    }

    private suspend fun loadPlaneMaterial(context: Context) {
        if (planeMaterial == null) {
            planeMaterial =
                MaterialLoader.loadMaterial(context, "models/transparent_textured.filamat")
        } else {
            logger.d("planeMaterial already loaded")
        }
    }

    private suspend fun loadCardsTexture(context: Context, uris: List<Uri>) {
        if (cardsTexture == null) {
            cardsTexture = uris.map { loadTexture(context, it) }
        } else {
            logger.d("cardsTexture already loaded")
        }
    }

    private suspend fun loadPlaneNotFoundTexture(context: Context) {
        if (planeNotFoundTexture == null) {
            val resourceId = com.metacards.metacards.core.R.drawable.tx_ar_plane_not_detected
            val uri = context.resources.getResourceUri(resourceId)
            planeNotFoundTexture = loadTexture(context, uri)
        } else {
            logger.d("planeNotFoundTexture already loaded")
        }
    }

    private suspend fun loadPlaneFoundTexture(context: Context) {
        if (planeFoundTexture == null) {
            val resourceId = R.drawable.tx_ar_plane_detected
            val uri = context.resources.getResourceUri(resourceId)
            planeFoundTexture = loadTexture(context, uri)
        } else {
            logger.d("planeFoundTexture already loaded")
        }
    }

    private suspend fun loadSelectedCardBackgroundTexture(context: Context) {
        if (selectedCardBackgroundTexture == null) {

            val resourceId = R.drawable.tx_ar_selected_card_background
            val uri = context.resources.getResourceUri(resourceId)
            selectedCardBackgroundTexture = loadTexture(context, uri)
        } else {
            logger.d("selectedCardBackgroundTexture already loaded")
        }
    }

    fun destroy(engine: Engine) {
        cardMaterial?.let { runCatching { engine.destroyMaterial(it) } }
        planeMaterial?.let { runCatching { engine.destroyMaterial(it) } }
        cardsTexture?.forEach { runCatching { engine.destroyTexture(it) } }
        planeFoundTexture?.let { runCatching { engine.destroyTexture(it) } }
        planeNotFoundTexture?.let { runCatching { engine.destroyTexture(it) } }
        selectedCardBackgroundTexture?.let { runCatching { engine.destroyTexture(it) } }
    }

    private fun Resources.getResourceUri(resourceId: Int) = Uri.Builder()
        .scheme(ContentResolver.SCHEME_ANDROID_RESOURCE)
        .authority(getResourcePackageName(resourceId))
        .appendPath(getResourceTypeName(resourceId))
        .appendPath(resourceId.toString())
        .build()

    private suspend fun loadTexture(context: Context, uri: Uri) =
        TextureLoader.loadImageTexture(
            context,
            uri.toString(),
            TextureLoader.TextureType.COLOR
        ) ?: throw NotFoundException("Can't load texture with uri: $uri")
}

@Composable
fun rememberArResourceLoader(engine: Engine): ArResourceLoader {
    val resourceLoader = remember { ArResourceLoader() }
    DisposableEffect(resourceLoader) {
        onDispose {
            resourceLoader.destroy(engine)
        }
    }
    return resourceLoader
}
