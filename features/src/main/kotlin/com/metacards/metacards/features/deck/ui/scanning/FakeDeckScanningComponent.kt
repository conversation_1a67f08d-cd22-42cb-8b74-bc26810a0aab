package com.metacards.metacards.features.deck.ui.scanning

import com.metacards.metacards.core.camera.QrCode
import kotlinx.coroutines.flow.MutableStateFlow

class FakeDeckScanningComponent : DeckScanningComponent {

    override val state = MutableStateFlow(DeckScanningComponent.State.Scanning)

    override fun onQrCodeScanned(qrCode: QrCode) = Unit

    override fun onGoToSettingsClick() = Unit

    override fun onDeckAddingErrorClosed() = Unit
}