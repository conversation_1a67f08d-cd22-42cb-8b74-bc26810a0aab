package com.metacards.metacards.features.auth.ui.sign_in.reset_password

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.arkivanov.decompose.extensions.compose.jetpack.stack.Children
import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.auth.ui.auth_type.email.EmailEnterComponent
import com.metacards.metacards.features.auth.ui.auth_type.email.EmailEnterUi
import com.metacards.metacards.features.auth.ui.auth_type.email.FakeEmailEnterComponent
import com.metacards.metacards.features.auth.ui.auth_type.email.message.SentMessageToEmailUi
import com.metacards.metacards.features.auth.ui.auth_type.pass.create_pass.CreatePasswordUi
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun ResetPasswordUi(
    component: ResetPasswordComponent
) {
    val childStack by component.childStack.collectAsState()

    val icon = getToolbarIcon(childStack, component)

    Column(horizontalAlignment = Alignment.Start) {
        TopNavigationBar(
            title = R.string.reset_password_toolbar_text.strResDesc(),
            leadingIcon = { icon() },
            contentPadding = PaddingValues(vertical = 16.dp)
        )

        Children(stack = childStack) { child ->
            when (val instance = child.instance) {
                is ResetPasswordComponent.Child.CreatePassword -> CreatePasswordUi(component = instance.component)
                is ResetPasswordComponent.Child.EmailEnter -> EmailEnterContent(component = instance.component)
                is ResetPasswordComponent.Child.SentMessageToEmail ->
                    SentMessageToEmailUi(component = instance.component)
            }
        }
    }
}

@Composable
private fun getToolbarIcon(
    childStack: ChildStack<*, ResetPasswordComponent.Child>,
    component: ResetPasswordComponent
): @Composable (RowScope.() -> Unit) {
    val icon: @Composable RowScope.() -> Unit = when (childStack.active.instance) {
        is ResetPasswordComponent.Child.SentMessageToEmail -> {
            {
                BackNavigationItem(
                    iconRes = R.drawable.ic_24_close,
                    onClick = component::onCloseButtonClick
                )
            }
        }

        else -> @Composable {
            { BackNavigationItem() }
        }
    }
    return icon
}

@Composable
private fun EmailEnterContent(component: EmailEnterComponent) {
    Column {
        Text(
            modifier = Modifier.padding(bottom = 16.dp, top = 68.dp),
            text = R.string.reset_password_title.strResDesc().localizedByLocal(),
            style = CustomTheme.typography.caption.large,
            color = CustomTheme.colors.text.primary
        )

        Text(
            modifier = Modifier.padding(bottom = 16.dp),
            text = R.string.reset_password_text.strResDesc().localizedByLocal(),
            style = CustomTheme.typography.body.primary,
            color = CustomTheme.colors.text.secondary
        )

        EmailEnterUi(
            component = component,
            isSignIn = false,
            nextButtonText = R.string.email_enter_next_button.strResDesc().localizedByLocal()
        )
    }
}

@Preview
@Composable
fun ResetPasswordUiPreview() {
    val coroutineScope = rememberCoroutineScope()

    AppTheme {
        ResetPasswordUi(
            component = FakeResetPasswordComponent(
                ResetPasswordComponent.Child.EmailEnter(
                    FakeEmailEnterComponent(coroutineScope)
                )
            )
        )
    }
}