package com.metacards.metacards.features.special_deck.ui.starry_sky

import com.metacards.metacards.core.bottom_sheet.BottomSheetControl
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeck
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCard
import com.metacards.metacards.features.special_deck.domain.entity.UserSpecialDeckInfo
import com.metacards.metacards.features.special_deck.ui.card_opening_cooldown.SpecialDeckCardOpeningCooldownComponent
import com.metacards.metacards.features.special_deck.ui.swipe_tutorial.SpecialDeckSwipeTutorialComponent
import kotlinx.coroutines.flow.StateFlow

interface SpecialDeckStarrySkyComponent {
    val deckInfo: StateFlow<SpecialDeck?>
    val deckCards: StateFlow<List<SpecialDeckCard>>
    val userSpecialDeckInfo: StateFlow<UserSpecialDeckInfo>
    val cooldownBottomSheetControl: BottomSheetControl<
        SpecialDeckCardOpeningCooldownComponent.Config,
        SpecialDeckCardOpeningCooldownComponent
        >
    val isUserStoppedInteraction: StateFlow<Boolean>

    val swipeTutorialControl: DialogControl<*, SpecialDeckSwipeTutorialComponent>

    fun onCardClick(card: SpecialDeckCard)
    fun onUserInteracted()
    fun onCloseClick()
    fun onLayoutClick()

    sealed interface Output {
        data class SpecialDeckCardDetailsRequested(
            val card: SpecialDeckCard,
            val userSpecialDeckInfo: UserSpecialDeckInfo
        ) : Output
        data object CloseRequested : Output
        data object LayoutRequested : Output
        data class SubscriptionBottomSheetRequested(val isSpecialDeckCardRequested: Boolean) : Output
    }
}