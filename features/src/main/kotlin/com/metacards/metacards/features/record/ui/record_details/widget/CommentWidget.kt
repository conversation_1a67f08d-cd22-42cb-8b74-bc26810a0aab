package com.metacards.metacards.features.record.ui.record_details.widget

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Card
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.getDisplayName
import com.metacards.metacards.core.widget.text_field.MetaTextFieldDefaults
import com.metacards.metacards.features.record.domain.Comment

@Composable
fun CommentWidget(
    comment: Comment,
    modifier: Modifier = Modifier
) {
    Card(
        shape = MetaTextFieldDefaults.Shape,
        backgroundColor = CustomTheme.colors.background.primary,
        elevation = 0.dp,
        modifier = modifier
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(16.dp),
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Text(
                text = comment.text,
                color = CustomTheme.colors.text.primary,
                style = CustomTheme.typography.body.primary,
                maxLines = 3,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.fillMaxWidth()
            )

            Text(
                text = comment.creationTime.getDisplayName(),
                color = CustomTheme.colors.text.secondary,
                style = CustomTheme.typography.caption.medium,
                textAlign = TextAlign.End,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}