package com.metacards.metacards.features.account.ui.lessons.details

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.activity.ActivityProvider
import com.metacards.metacards.core.localization.data.AppLanguageService
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.deck.domain.entity.Lesson
import com.metacards.metacards.features.video_player.VideoPlayerActivity
import kotlinx.coroutines.launch

class RealLessonDetailsComponent(
    componentContext: ComponentContext,
    override val lesson: Lesson,
    private val analyticsService: AnalyticsService,
    private val activityProvider: ActivityProvider,
    private val appLanguageService: AppLanguageService,
) : ComponentContext by componentContext, LessonDetailsComponent {
    override fun onVideoPlay() {
        componentScope.launch {
            analyticsService.logEvent(
                AnalyticsEvent.EduVideoEvent()
            )
            activityProvider.awaitActivity().run {
                val url = lesson.videoUrl.toString(appLanguageService.getLanguage())
                VideoPlayerActivity.start(url, this)
            }
        }
    }
}