package com.metacards.metacards.features.course.ui.test_result

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.activity.ActivityProvider
import com.metacards.metacards.core.adv.domain.YandexAdvHelper
import com.metacards.metacards.core.auth_suggestion_dialog.createAuthSuggestion
import com.metacards.metacards.core.createDefaultDialogComponent
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.model.DialogData
import com.metacards.metacards.core.dialog.dialogControl
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.localization.data.AppLanguageService
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.sharing.SharingManager
import com.metacards.metacards.core.user.domain.UserProvider
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.withProgress
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.course.domain.entity.CoursePassedTest
import com.metacards.metacards.features.course.domain.entity.CourseResult
import com.metacards.metacards.features.course.domain.interactor.UpdateUserPassedTestInteractor
import com.metacards.metacards.features.layout.domain.CardSource
import com.metacards.metacards.features.layout.domain.GetPredefinedLayoutsInteractor
import com.metacards.metacards.features.layout.domain.LayoutId
import com.metacards.metacards.features.layout.domain.PredefinedLayoutWithAvailable
import com.metacards.metacards.features.video_player.VideoPlayerActivity
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

class RealCourseTestResultComponent(
    componentContext: ComponentContext,
    componentFactory: ComponentFactory,
    private val onOutput: (CourseTestResultComponent.Output) -> Unit,
    override val courseResult: CourseResult,
    private val dataToSave: CoursePassedTest,
    private val order: Int,
    override val bottomButtonText: String,
    override val courseName: LocalizableString?,
    updateUserPassedTestInteractor: UpdateUserPassedTestInteractor,
    getPredefinedLayoutsInteractor: GetPredefinedLayoutsInteractor,
    private val userProvider: UserProvider,
    private val analyticsService: AnalyticsService,
    private val yandexAdvHelper: YandexAdvHelper,
    private val errorHandler: ErrorHandler,
    private val sharingManager: SharingManager,
    private val appLanguageService: AppLanguageService,
    private val activityProvider: ActivityProvider
) : ComponentContext by componentContext, CourseTestResultComponent {

    override val isCourseResultSaved = MutableStateFlow(false)

    override val predefinedLayouts: StateFlow<List<PredefinedLayoutWithAvailable>> =
        getPredefinedLayoutsInteractor.execute().map { list ->
            list.filter { courseResult.linkedLayouts?.contains(it.layout.id) == true }
        }.stateIn(componentScope, SharingStarted.Eagerly, emptyList())

    override val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent> =
        dialogControl(dialogComponentFactory = { config, componentContext, dialogControl ->
            componentFactory.createDefaultDialogComponent(
                componentContext,
                config.data,
                dialogControl::dismiss
            )
        })

    private var clickedLayoutId: LayoutId? = null

    override val isShareInProgress = MutableStateFlow(false)
    override val title: LocalizableString = dataToSave.name

    init {
        componentScope.safeLaunch(errorHandler) {
            updateUserPassedTestInteractor.execute(result = dataToSave)
            isCourseResultSaved.value = true
        }
    }

    override fun onGoForwardClick() {
        onOutput(CourseTestResultComponent.Output.GoForwardRequested(order))
    }

    override fun onCloseClick() {
        onOutput(CourseTestResultComponent.Output.CloseRequested)
    }

    override fun onShareClick() {
        val cover = courseResult.cover
        val title = courseResult.subtitle ?: return
        val description = courseResult.description ?: return
        val appLanguage = appLanguageService.currentAppLanguage.value
        componentScope.safeLaunch(errorHandler) {
            withProgress(isShareInProgress) {
                sharingManager.shareCourseTestResult(
                    imageUrl = cover,
                    title = title.toString(appLanguage),
                    text = description.toString(appLanguage)
                )
            }
        }
    }

    override fun onVideoFullScreenClick(position: Long, videoUrl: String) {
        componentScope.launch {
            activityProvider.awaitActivity().run {
                VideoPlayerActivity.start(
                    videoUrl = videoUrl,
                    context = this,
                    startWithLandscape = true,
                    playOnStart = true,
                    position = position
                )
            }
        }
    }

    override fun onLayoutClick(layoutId: LayoutId) {
        predefinedLayouts.value.find { it.layout.id == layoutId }?.let {
            if (userProvider.getUser().value?.subscriptionState?.canLayout == false) {
                clickedLayoutId = layoutId
                subscribeForAdvReward()
                onOutput(CourseTestResultComponent.Output.SubscriptionBottomSheetRequested(withAdv = true))
            } else {
                analyticsService.logEvent(
                    AnalyticsEvent.LayoutChooseEvent(
                        layoutId = layoutId.value,
                        layoutName = it.layout.name.toString(appLanguageService.getLanguage())
                    )
                )
                onOutput(
                    CourseTestResultComponent.Output.LayoutDetailsRequested(
                        CardSource.Questions(it.layout.questions),
                        dataToSave.courseId,
                        dataToSave.themeId
                    )
                )
            }
        }
    }

    override fun onBlockContentClick() {
        if (userProvider.getUser().value == null) {
            dialogControl.show(
                DefaultDialogComponent.Config(
                    DialogData.createAuthSuggestion(
                        cancelAction = dialogControl::dismiss,
                        acceptAction = {
                            analyticsService.logEvent(AnalyticsEvent.AccountAuthEvent)
                            onOutput(CourseTestResultComponent.Output.AuthScreenRequested)
                        }
                    )
                )
            )
        } else {
            onOutput(CourseTestResultComponent.Output.SubscriptionBottomSheetRequested(withAdv = false))
        }
    }

    private fun subscribeForAdvReward() {
        yandexAdvHelper.subscribeForReward {
            predefinedLayouts.value.find { it.layout.id == clickedLayoutId }?.let {
                analyticsService.logEvent(
                    AnalyticsEvent.LayoutChooseEvent(
                        layoutId = it.layout.id.value,
                        layoutName = it.layout.name.toString(appLanguageService.getLanguage())
                    )
                )
                onOutput(
                    CourseTestResultComponent.Output.LayoutDetailsRequested(
                        CardSource.Questions(it.layout.questions),
                        dataToSave.courseId,
                        dataToSave.themeId
                    )
                )
                clickedLayoutId = null
            }
        }
    }
}