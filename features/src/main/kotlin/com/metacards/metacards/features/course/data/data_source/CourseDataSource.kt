package com.metacards.metacards.features.course.data.data_source

import com.metacards.metacards.core.network.firestore.FirestoreService
import com.metacards.metacards.core.network.firestore.getFlow
import com.metacards.metacards.features.course.data.dto.CourseDataDto
import com.metacards.metacards.features.course.data.dto.CourseLessonDto
import com.metacards.metacards.features.course.data.dto.CoursePassedCoursesDto
import com.metacards.metacards.features.course.data.dto.CoursePassedTestDto
import com.metacards.metacards.features.course.data.dto.CourseTestDto
import com.metacards.metacards.features.course.data.dto.CourseThemeDto
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.tasks.await

class CourseDataSource(
    private val firestoreService: FirestoreService
) {
    fun getCoursesListFlow(): Flow<List<CourseThemeDto>> {
        val flow = firestoreService.db.collection(COURSE_COLLECTION_PATH).getFlow(firestoreService)
        return flow.map { snapshot ->
            snapshot.documents.mapNotNull {
                it.toObject(CourseThemeDto::class.java)?.copy(id = it.id)
            }
        }
    }

    fun getCourseDataFlow(themeId: String, courseId: String): Flow<CourseDataDto?> =
        firestoreService.db.collection(COURSE_COLLECTION_PATH)
            .document(themeId)
            .collection(COURSE_COLLECTION_PATH)
            .document(courseId)
            .getFlow(firestoreService)
            .map { it.toObject(CourseDataDto::class.java) }

    fun getUserPassedCoursesFlow(
        userId: String
    ): Flow<List<CoursePassedCoursesDto>> = firestoreService.db.collection(getUserPassedCoursesCollectionPath(userId))
        .getFlow(firestoreService)
        .map { it.toObjects(CoursePassedCoursesDto::class.java) }

    fun getUserPassedContentFlow(
        userId: String,
        courseId: String
    ): Flow<CoursePassedCoursesDto?> = firestoreService.db.collection(getUserPassedCoursesCollectionPath(userId))
        .document(courseId)
        .getFlow(firestoreService)
        .map { it.toObject(CoursePassedCoursesDto::class.java) }

    fun getUserPassedTestsFlow(
        userId: String,
    ): Flow<List<CoursePassedTestDto>> = firestoreService.db.collection(getUserPassedTestsCollectionPath(userId))
        .getFlow(firestoreService)
        .mapNotNull { snapshot -> snapshot.map { it.toObject(CoursePassedTestDto::class.java).copy(id = it.id) } }

    suspend fun getCourseLessonDetails(
        request: CourseLessonDto.Request
    ): CourseLessonDto? =
        firestoreService.db.collection(COURSE_COLLECTION_PATH)
            .document(request.themeId)
            .collection(COURSE_COLLECTION_PATH)
            .document(request.courseId)
            .collection(LESSON_COLLECTION_PATH)
            .document(request.lessonId)
            .get()
            .await()
            .toObject(CourseLessonDto::class.java)

    suspend fun getUserPassedContent(
        userId: String,
        courseId: String
    ): CoursePassedCoursesDto? = firestoreService.db.collection(getUserPassedCoursesCollectionPath(userId))
        .document(courseId)
        .get()
        .await()
        .toObject(CoursePassedCoursesDto::class.java)

    suspend fun updateUserPassedContent(
        userId: String,
        courseId: String,
        info: CoursePassedCoursesDto
    ) {
        firestoreService.db.collection(getUserPassedCoursesCollectionPath(userId))
            .document(courseId)
            .set(info)
            .await()
    }

    suspend fun getCourseData(themeId: String, courseId: String): CourseDataDto? =
        firestoreService.db.collection(COURSE_COLLECTION_PATH)
            .document(themeId)
            .collection(COURSE_COLLECTION_PATH)
            .document(courseId)
            .get()
            .await()
            .toObject(CourseDataDto::class.java)

    suspend fun getUserPassedTests(
        userId: String,
    ): List<CoursePassedTestDto> = firestoreService.db.collection(getUserPassedTestsCollectionPath(userId))
        .get()
        .await()
        .map { it.toObject(CoursePassedTestDto::class.java) }

    suspend fun getUserPassedTestById(
        userId: String,
        testResultId: String
    ): CoursePassedTestDto? = firestoreService.db.collection(USERS_COLLECTION_PATH)
        .document(userId)
        .collection(PASSED_TESTS_COLLECTION_PATH)
        .document(testResultId)
        .get()
        .await()
        .toObject(CoursePassedTestDto::class.java)

    suspend fun addUserPassedTest(
        userId: String,
        info: CoursePassedTestDto
    ) {
        firestoreService.db.collection(getUserPassedTestsCollectionPath(userId))
            .document()
            .set(info)
            .await()
    }

    private fun getUserPassedCoursesCollectionPath(
        userId: String,
    ) = USERS_COLLECTION_PATH + SLASH + userId + SLASH + PASSED_COURSES_COLLECTION_PATH

    private fun getUserPassedTestsCollectionPath(
        userId: String,
    ) = USERS_COLLECTION_PATH + SLASH + userId + SLASH + PASSED_TESTS_COLLECTION_PATH

    suspend fun getCourseTestDetails(
        request: CourseTestDto.Request
    ): CourseTestDto? {
        val document = firestoreService.db.collection(COURSE_COLLECTION_PATH)
            .document(request.themeId)
            .collection(COURSE_COLLECTION_PATH)
            .document(request.courseId)
            .collection(TESTS_COLLECTION_PATH)
            .document(request.testId)
            .get()
            .await()
        return document.toObject(CourseTestDto::class.java)?.copy(id = document.id)
    }

    companion object {
        private const val COURSE_COLLECTION_PATH = "courses"
        private const val LESSON_COLLECTION_PATH = "lesson"
        private const val USERS_COLLECTION_PATH = "users"
        private const val PASSED_COURSES_COLLECTION_PATH = "passedCourses"
        private const val PASSED_TESTS_COLLECTION_PATH = "passedTests"
        private const val SLASH = "/"
        private const val TESTS_COLLECTION_PATH = "tests"
    }
}