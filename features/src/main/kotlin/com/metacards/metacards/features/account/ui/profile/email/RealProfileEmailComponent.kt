package com.metacards.metacards.features.account.ui.profile.email

import android.os.Parcelable
import androidx.annotation.StringRes
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.stack.StackNavigation
import com.arkivanov.decompose.router.stack.childStack
import com.arkivanov.decompose.router.stack.push
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.ResourceFormatted
import com.metacards.metacards.core.utils.toStateFlow
import com.metacards.metacards.features.R
import com.metacards.metacards.features.account.createProfileConfirmPasswordComponent
import com.metacards.metacards.features.account.createProfileNewEmailComponent
import com.metacards.metacards.features.account.ui.profile.email.new_email.ProfileNewEmailComponent
import com.metacards.metacards.features.account.ui.profile.password.confirm.ProfileConfirmPasswordComponent
import com.metacards.metacards.features.auth.createSentMessageToEmailComponent
import com.metacards.metacards.features.auth.ui.auth_type.email.message.SentMessageToEmailComponent
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.parcelize.Parcelize
import com.metacards.metacards.core.R as CoreR

class RealProfileEmailComponent(
    componentContext: ComponentContext,
    private val componentFactory: ComponentFactory,
    private val onOutput: (ProfileEmailComponent.Output) -> Unit
) : ComponentContext by componentContext, ProfileEmailComponent {

    private val navigation = StackNavigation<ChildConfig>()

    override val childStack = childStack(
        source = navigation,
        initialConfiguration = ChildConfig.ConfirmPassword,
        handleBackButton = true,
        childFactory = ::createChild
    ).toStateFlow(lifecycle)

    private fun createChild(
        childConfig: ChildConfig,
        componentContext: ComponentContext
    ): ProfileEmailComponent.Child = when (childConfig) {
        is ChildConfig.ConfirmPassword -> {
            ProfileEmailComponent.Child.ConfirmPassword(
                componentFactory.createProfileConfirmPasswordComponent(
                    componentContext,
                    StringDesc.Resource(R.string.account_profile_edit_title),
                    StringDesc.Resource(R.string.account_profile_edit_password_header),
                    StringDesc.Resource(R.string.account_profile_edit_email_verify_password_header),
                    StringDesc.Resource(CoreR.string.common_proceed),
                    ::onProfileConfirmPasswordOutput
                )
            )
        }

        is ChildConfig.NewEmail -> {
            ProfileEmailComponent.Child.NewEmail(
                componentFactory.createProfileNewEmailComponent(
                    componentContext,
                    childConfig.password,
                    ::onProfileNewEmailOutput
                )
            )
        }

        is ChildConfig.VerifyEmail -> {
            ProfileEmailComponent.Child.VerifyEmail(
                componentFactory.createSentMessageToEmailComponent(
                    componentContext,
                    StringDesc.Resource(childConfig.title),
                    StringDesc.ResourceFormatted(childConfig.text, childConfig.email),
                    shouldAutoClose = true,
                    ::onVerifyEmailOutput
                )
            )
        }
    }

    private fun onVerifyEmailOutput(output: SentMessageToEmailComponent.Output) {
        when (output) {
            is SentMessageToEmailComponent.Output.AuthScreenRequested -> {
                onOutput(ProfileEmailComponent.Output.AuthScreenRequested)
            }
            is SentMessageToEmailComponent.Output.MainScreenRequested -> {
                onOutput(ProfileEmailComponent.Output.MainScreenRequested)
            }
        }
    }

    private fun onProfileNewEmailOutput(output: ProfileNewEmailComponent.Output) {
        when (output) {
            is ProfileNewEmailComponent.Output.EmailVerificationRequested ->
                navigation.push(ChildConfig.VerifyEmail(output.title, output.text, output.email))
        }
    }

    private fun onProfileConfirmPasswordOutput(output: ProfileConfirmPasswordComponent.Output) {
        when (output) {
            is ProfileConfirmPasswordComponent.Output.PasswordConfirmed -> {
                navigation.push(ChildConfig.NewEmail(output.password))
            }
        }
    }

    sealed interface ChildConfig : Parcelable {
        @Parcelize
        object ConfirmPassword : ChildConfig

        @Parcelize
        data class NewEmail(val password: String) : ChildConfig

        @Parcelize
        data class VerifyEmail(
            @StringRes val title: Int,
            @StringRes val text: Int,
            val email: String
        ) : ChildConfig
    }
}