package com.metacards.metacards.features.record.ui.record_list

import com.arkivanov.decompose.ComponentContext
import com.arkivanov.essenty.lifecycle.doOnCreate
import com.arkivanov.essenty.lifecycle.doOnResume
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.button.DebounceClick
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.message.data.MessageService
import com.metacards.metacards.core.message.domain.Message
import com.metacards.metacards.core.paged_loading.PagedLoading
import com.metacards.metacards.core.paged_loading.handleErrors
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.PagedData
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.core.utils.getTodayLocalDate
import com.metacards.metacards.core.utils.toLoadableState
import com.metacards.metacards.core.utils.toLocalDate
import com.metacards.metacards.features.R
import com.metacards.metacards.features.analytics.AnalyticsEvent
import com.metacards.metacards.features.analytics.AnalyticsService
import com.metacards.metacards.features.record.domain.Record
import com.metacards.metacards.features.record.domain.RecordId
import com.metacards.metacards.features.record.domain.RecordListType
import com.metacards.metacards.features.record.domain.RecordsPagedLoading
import com.metacards.metacards.features.record.domain.UpdateRecordInteractor
import com.metacards.metacards.features.record.ui.record_details.RecordDetailsComponent
import com.metacards.metacards.features.user.domain.GetUserSubscriptionStateInteractor
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.stateIn
import kotlinx.datetime.Instant

class RealRecordListComponent(
    componentContext: ComponentContext,
    private val messageService: MessageService,
    override val listType: RecordListType,
    private val recordsLoading: RecordsPagedLoading,
    private val errorHandler: ErrorHandler,
    private val updateRecordInteractor: UpdateRecordInteractor,
    private val analyticsService: AnalyticsService,
    getUserSubscriptionStateInteractor: GetUserSubscriptionStateInteractor,
    private val onOutput: (RecordListComponent.Output) -> Unit
) : ComponentContext by componentContext, RecordListComponent {

    private val debounce = Debounce()

    private val subscriptionState = getUserSubscriptionStateInteractor.execute()
        .stateIn(componentScope, SharingStarted.Eagerly, null)

    override val hasAddButton: Boolean
        get() = listType == RecordListType.All

    override val recordsState: StateFlow<LoadableState<PagedData<Record>>> =
        computed(recordsLoading.stateFlow) {
            it.toLoadableState()
        }

    override val visibleRecordsState: MutableStateFlow<LoadableState<PagedData<Record>>> =
        computed(recordsLoading.stateFlow) {
            it.toLoadableState()
        } as MutableStateFlow<LoadableState<PagedData<Record>>>

    // Используется StateFlow, а SharedFlow, потому что в момент, когда нужно вызвать команду, LazyColumn мог еще не создаться
    override val pendingScrollCommand = MutableStateFlow<ScrollCommand?>(null)

    init {
        lifecycle.doOnCreate {
            recordsLoading.loadFirstPage()
        }

        lifecycle.doOnResume {
            recordsLoading.reloadPage()
        }

        recordsLoading.eventFlow
            .onEach { event ->
                when {
                    recordsLoading.startTime != null && event is PagedLoading.Event.FirstPageLoaded -> {
                        scrollToStartTime()
                    }

                    listType == RecordListType.Favourite && event is PagedLoading.Event.PageReloaded -> {
                        pendingScrollCommand.value = ScrollCommand.ScrollToTop
                    }
                }
            }
            .launchIn(componentScope)

        recordsLoading.handleErrors(componentScope) {
            errorHandler.handleError(it.exception)
        }
    }

    override fun onRefresh() {
        recordsLoading.startTime = null
        recordsLoading.loadFirstPage()
    }

    override fun onRetryClick() {
        recordsLoading.loadFirstPage()
    }

    override fun onAddClick() {
        analyticsService.logEvent(AnalyticsEvent.ManualRecordTapEvent)
        onOutput(RecordListComponent.Output.CreateRecordFlowRequested)
    }

    override fun onLoadNext() {
        recordsLoading.loadNext()
    }

    override fun onLoadPrevious() {
        recordsLoading.loadPrevious()
    }

    override fun onScrollToTopClick() {
        if (recordsState.value.data?.hasPreviousPage == true) {
            recordsLoading.startTime = null
            recordsLoading.loadFirstPage(clear = true)
        } else {
            pendingScrollCommand.value = ScrollCommand.ScrollToTop
        }
    }

    override fun onStartTimeChosen(time: Instant) {
        recordsLoading.startTime = if (time.toLocalDate() == getTodayLocalDate()) {
            onScrollToTopClick()
            null
        } else {
            time
        }
        recordsLoading.loadFirstPage()
    }

    override fun onRecordClick(recordId: RecordId) {
        componentScope.safeLaunch(errorHandler) {
            val record = recordsState.value.data?.list?.find { it.id == recordId }
                ?: throw IllegalArgumentException("Record with ID ${recordId.value} was not found.")

            val recordType = if (record.isArchived) {
                analyticsService.logEvent(AnalyticsEvent.ArchiveRecordTapEvent)
                RecordDetailsComponent.RecordType.Archived
            } else {
                analyticsService.logEvent(AnalyticsEvent.RecordTapEvent)
                RecordDetailsComponent.RecordType.Common
            }
            onOutput(
                RecordListComponent.Output.RecordDetailsRequested(
                    recordId = recordId,
                    recordType = recordType,
                    isDailyCardLayout = record.isDailyCardLayout,
                    courseId = record.courseId,
                    courseThemeId = record.courseThemeId
                )
            )
        }
    }

    override fun onArchiveClick(recordId: RecordId) {
        DebounceClick(debounce, "onArchiveClick", errorHandler) {
            when (subscriptionState.value) {
                null, is User.SubscriptionState.None -> onOutput(RecordListComponent.Output.PremiumSuggestingRequested)
                is User.SubscriptionState.Ongoing -> archiveRecord(recordId)
            }
        }
    }

    private suspend fun archiveRecord(recordId: RecordId) {
        val record = recordsState.value.data?.list?.find { it.id == recordId }
            ?.copy(isArchived = listType != RecordListType.Archived)
            ?: throw IllegalArgumentException("Record with ID ${recordId.value} was not found.")

        updateRecordInteractor.execute(record)

        visibleRecordsState.value = visibleRecordsState.value.copy(
            data = visibleRecordsState.value.data?.copy(
                list = visibleRecordsState.value.data?.list?.filterNot { it.id == recordId }
                    ?: return
            )
        )

        if (listType == RecordListType.Archived) {
            showPopup(R.string.record_list_popup_restored_from_archive)
        } else {
            analyticsService.logEvent(AnalyticsEvent.JournalRecordAddArchiveEvent)
            showPopup(R.string.record_list_popup_moved_to_archive)
        }
    }

    override fun onFavouriteClick(recordId: RecordId) {
        DebounceClick(debounce, "onFavouriteClick", errorHandler) {
            when (subscriptionState.value) {
                null, is User.SubscriptionState.None -> onOutput(RecordListComponent.Output.PremiumSuggestingRequested)
                is User.SubscriptionState.Ongoing -> favouriteRecord(recordId)
            }
        }
    }

    private suspend fun favouriteRecord(recordId: RecordId) {
        val record = recordsState.value.data?.list?.find { it.id == recordId }
            ?.let { it.copy(isFavourite = !it.isFavourite) }
            ?: throw IllegalArgumentException("Record with ID ${recordId.value} was not found.")

        updateRecordInteractor.execute(record)

        recordsLoading.loadFirstPage()

        if (record.isFavourite) {
            showPopup(R.string.record_list_popup_moved_to_favourite)
        } else {
            showPopup(R.string.record_list_popup_restored_from_favourite)
        }
    }

    override fun onScrollCommandExecuted() {
        pendingScrollCommand.value = null
    }

    // После того как выбрали в календаре startTime, загрузятся записи в обе стороны от нее, но нужно проскролить так, чтоб
    // было видно только записи старее чем startTime
    private fun scrollToStartTime() {
        val startTime = recordsLoading.startTime ?: return
        val records = recordsLoading.stateFlow.value.data?.list ?: return

        val targetRecord = records.firstOrNull { it.creationTime < startTime }
            ?: records.lastOrNull() ?: return

        pendingScrollCommand.value = ScrollCommand.ScrollToRecord(targetRecord.id)
    }

    private fun showPopup(stringResId: Int) {
        messageService.showMessage(Message(text = stringResId.strResDesc(), isNotification = true))
    }
}