package com.metacards.metacards.features.auth.ui.auth_type.pass.create_pass

import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.user.domain.Email
import kotlinx.coroutines.flow.StateFlow
import ru.mobileup.kmm_form_validation.control.InputControl

interface CreatePasswordComponent {
    val passwordInputControl: InputControl
    val confirmPasswordInputControl: InputControl
    val enterButtonState: StateFlow<ButtonState>

    fun onEnterButtonClick()

    sealed interface Output {
        data class EnterButtonPressed(val email: Email) : Output
    }
}