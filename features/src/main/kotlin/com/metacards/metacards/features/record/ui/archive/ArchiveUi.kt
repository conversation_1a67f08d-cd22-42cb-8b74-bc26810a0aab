package com.metacards.metacards.features.record.ui.archive

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.arkivanov.decompose.extensions.compose.jetpack.stack.Children
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.record.ui.record_list.RecordListUi
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun ArchiveUi(
    component: ArchiveComponent,
    modifier: Modifier = Modifier
) {
    val childStack by component.childStack.collectAsState()

    BoxWithFade(
        modifier = modifier
            .safeDrawingPadding()
            .fillMaxSize(),
        listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        Column(Modifier.matchParentSize()) {
            TopNavigationBar(
                title = R.string.archive_title.strResDesc(),
                leadingIcon = {
                    BackNavigationItem()
                }
            )

            Children(stack = childStack) { child ->
                when (val instance = child.instance) {
                    is ArchiveComponent.Child.ArchivedRecordList -> RecordListUi(
                        modifier = modifier
                            .fillMaxWidth()
                            .weight(1f),
                        component = instance.component
                    )
                }
            }
        }
    }
}

@Preview
@Composable
fun ArchivePreview() {
    AppTheme {
        ArchiveUi(component = FakeArchiveComponent())
    }
}