package com.metacards.metacards.features.showcase.ui

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.adv.domain.YandexAdvHelper
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.user.domain.FavoriteCard
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.utils.componentScope
import com.metacards.metacards.core.utils.computed
import com.metacards.metacards.features.account.domain.entity.LessonCategory
import com.metacards.metacards.features.account.domain.interactor.GetAllLessonCategoriesInteractor
import com.metacards.metacards.features.account.domain.interactor.GetDecksForPurchaseWithCardsInteractor
import com.metacards.metacards.features.course.domain.entity.CourseAvailability
import com.metacards.metacards.features.course.domain.entity.CourseData
import com.metacards.metacards.features.course.domain.entity.CourseShortData
import com.metacards.metacards.features.course.domain.entity.CourseTheme
import com.metacards.metacards.features.course.domain.interactor.GetCourseThemesInteractor
import com.metacards.metacards.features.course.ui.CourseComponent
import com.metacards.metacards.features.course.ui.details.CourseDetailsComponent
import com.metacards.metacards.features.deck.domain.entity.CardId
import com.metacards.metacards.features.deck.domain.entity.DeckInfoWithCards
import com.metacards.metacards.features.favorite_cards.domain.GetFavoriteCardsInteractor
import com.metacards.metacards.features.layout.domain.CardSource
import com.metacards.metacards.features.layout.domain.GetPredefinedLayoutsInteractor
import com.metacards.metacards.features.layout.domain.PredefinedLayoutWithAvailable
import com.metacards.metacards.features.special_deck.domain.repository.SpecialDeckRepository
import com.metacards.metacards.features.user.domain.UserRepository
import kotlinx.coroutines.flow.StateFlow

class RealShowcaseComponent(
    componentContext: ComponentContext,
    private val onOutput: (ShowcaseComponent.Output) -> Unit,
    private val specialDeckRepository: SpecialDeckRepository,
    userRepository: UserRepository,
    private val errorHandler: ErrorHandler,
    getDecksForPurchaseWithCardsInteractor: GetDecksForPurchaseWithCardsInteractor,
    getPredefinedLayoutsInteractor: GetPredefinedLayoutsInteractor,
    getAllLessonCategoriesInteractor: GetAllLessonCategoriesInteractor,
    getFavoriteCardsInteractor: GetFavoriteCardsInteractor,
    getCourseThemesInteractor: GetCourseThemesInteractor,
    private val yandexAdvHelper: YandexAdvHelper
) : ComponentContext by componentContext, ShowcaseComponent {

    override val decksForPurchaseInfoWithCards = getDecksForPurchaseWithCardsInteractor.execute()
    override val predefinedLayouts = getPredefinedLayoutsInteractor.execute()
    override val videoLessonCategories = getAllLessonCategoriesInteractor.execute()
    override val favoriteCards = getFavoriteCardsInteractor.execute()
    override val courses: StateFlow<List<CourseTheme>> = getCourseThemesInteractor.execute()
    private val user = userRepository.user

    override val isPremiumUser: StateFlow<Boolean> = computed(user) {
        it?.subscriptionState is User.SubscriptionState.Ongoing
    }
    private var clickedPredefinedLayout: PredefinedLayoutWithAvailable? = null

    override fun onGameBannerClick() {
        if (!isUserIsLoggedIn()) return
        componentScope.safeLaunch(errorHandler) {
            specialDeckRepository.getSpecialDecks().firstOrNull()?.let {
                onOutput(ShowcaseComponent.Output.SpecialDeckRequested(it.id))
            }
        }
    }

    override fun onShopDeckClick(deck: DeckInfoWithCards) {
        onOutput(ShowcaseComponent.Output.ShopDeckDetailsRequested(deck.deckInfo.deckId))
    }

    override fun onPredefinedLayoutClick(layout: PredefinedLayoutWithAvailable) {
        if (user.value?.subscriptionState?.canLayout == false) {
            clickedPredefinedLayout = layout
            subscribeForAdvReward()
            onOutput(ShowcaseComponent.Output.SubscriptionBottomSheetRequested(withAdv = true))
        } else {
            onOutput(
                ShowcaseComponent.Output.PredefinedLayoutDetailsRequested(
                    CardSource.Questions(layout.layout.questions)
                )
            )
        }
    }

    override fun onPredefinedLayoutBlockContentClick() {
        if (!isUserIsLoggedIn()) return
        onOutput(ShowcaseComponent.Output.SubscriptionBottomSheetRequested(false))
    }

    override fun onVideoLessonCategoryClick(category: LessonCategory) {
        onOutput(ShowcaseComponent.Output.VideoCategorySelected(category.id))
    }

    override fun onAnalyticsBannerClick() {
        onOutput(ShowcaseComponent.Output.AnalyticsRequested)
    }

    override fun onFavoriteCardClick(favoriteCard: FavoriteCard) {
        if (user.value?.subscriptionState is User.SubscriptionState.Ongoing) {
            onOutput(ShowcaseComponent.Output.FavoriteCardDetailsRequested(CardId(favoriteCard.id)))
        } else {
            onOutput(ShowcaseComponent.Output.SubscriptionBottomSheetRequested(withAdv = false))
        }
    }

    override fun onCourseThemeClick(courseTheme: CourseTheme) {
        onOutput(ShowcaseComponent.Output.CourseRequested(CourseComponent.Screen.CourseList(courseTheme)))
    }

    override fun onCourseClick(course: CourseShortData) {
        if (!isUserIsLoggedIn()) return
        if (user.value?.subscriptionState is User.SubscriptionState.Ongoing ||
            course.availability == CourseAvailability.FREE
        ) {
            val query = CourseData.Query(themeId = course.themeId, courseId = course.courseId)
            onOutput(
                ShowcaseComponent.Output.CourseRequested(
                    CourseComponent.Screen.Details(CourseDetailsComponent.Screen.Course(query))
                )
            )
        } else {
            onOutput(ShowcaseComponent.Output.SubscriptionBottomSheetRequested(withAdv = false))
        }
    }

    private fun isUserIsLoggedIn(): Boolean =
        if (user.value == null) {
            onOutput(ShowcaseComponent.Output.AuthSuggestingRequested)
            false
        } else {
            true
        }

    private fun subscribeForAdvReward() {
        yandexAdvHelper.subscribeForReward {
            clickedPredefinedLayout?.let {
                onOutput(
                    ShowcaseComponent.Output.PredefinedLayoutDetailsRequested(
                        CardSource.Questions(it.layout.questions)
                    )
                )
                clickedPredefinedLayout = null
            }
        }
    }
}