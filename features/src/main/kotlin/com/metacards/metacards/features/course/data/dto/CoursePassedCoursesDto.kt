package com.metacards.metacards.features.course.data.dto

import com.google.firebase.firestore.PropertyName
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CoursePassedCourse

data class CoursePassedCoursesDto(
    val courseId: String = "",
    @get:PropertyName("isCompleted")
    val isCompleted: Boolean = false,
    val completedContent: List<CourseCompletedContentDto> = emptyList()
) {
    fun toDomain() = CoursePassedCourse(
        courseId = CourseId(courseId),
        isCompleted = isCompleted,
        completedContent = completedContent.map { it.toDomain() }
    )

    companion object {
        fun fromDomain(entity: CoursePassedCourse) = CoursePassedCoursesDto(
            courseId = entity.courseId.value,
            isCompleted = entity.isCompleted,
            completedContent = entity.completedContent.map { CourseCompletedContentDto.fromDomain(it) }
        )
    }
}