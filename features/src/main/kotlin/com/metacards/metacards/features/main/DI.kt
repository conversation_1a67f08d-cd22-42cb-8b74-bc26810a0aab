package com.metacards.metacards.features.main

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.ComponentFactory
import com.metacards.metacards.features.home.domain.GetHomeScreenDecksInteractor
import com.metacards.metacards.features.main.ui.MainComponent
import com.metacards.metacards.features.main.ui.RealMainComponent
import org.koin.core.component.get
import org.koin.dsl.module

val mainModule = module {
    factory { GetHomeScreenDecksInteractor(get(), get(), get(), get()) }
}

fun ComponentFactory.createMainComponent(
    componentContext: ComponentContext,
    onOutput: (MainComponent.Output) -> Unit
): MainComponent {
    return RealMainComponent(
        componentContext,
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        get(),
        onOutput
    )
}
