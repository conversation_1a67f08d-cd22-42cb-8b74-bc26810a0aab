package com.metacards.metacards.features.account.ui.profile.email

import com.metacards.metacards.core.utils.createFakeChildStackStateFlow
import com.metacards.metacards.features.account.ui.profile.password.confirm.FakeProfileConfirmPasswordComponent

class FakeProfileEmailComponent : ProfileEmailComponent {
    override val childStack = createFakeChildStackStateFlow(
        ProfileEmailComponent.Child.ConfirmPassword(FakeProfileConfirmPasswordComponent())
    )
}