package com.metacards.metacards.features.special_deck.data.dto

import android.text.format.DateUtils
import com.google.firebase.firestore.PropertyName
import com.metacards.metacards.features.deck.domain.entity.DeckId
import com.metacards.metacards.features.special_deck.domain.entity.SpecialDeckCooldownTime
import com.metacards.metacards.features.special_deck.domain.entity.UserSpecialDeckInfo
import kotlinx.datetime.plus
import java.util.Calendar
import java.util.Date

data class UserSpecialDeckInfoDto(
    val specialDeckId: String = "",
    @get:PropertyName("isCompleted")
    val isCompleted: Boolean = false,
    val cards: List<UserSpecialDeckCardInfoDto> = emptyList()
) {
    fun toDomain(): UserSpecialDeckInfo {
        val todayCards = cards.filter {
            DateUtils.isToday(it.gettingDate.toDate().time)
        }
        val calendar = Calendar.getInstance().apply {
            time = Date()
        }
        val hours = (23 - calendar.get(Calendar.HOUR_OF_DAY))
        val min = (60 - calendar.get(Calendar.MINUTE))
        val cooldownTime = SpecialDeckCooldownTime(hours, min)

        return UserSpecialDeckInfo(
            specialDeckId = DeckId(specialDeckId),
            isCompleted = isCompleted,
            cards = cards.map { it.toDomain() },
            cooldownTime = cooldownTime,
            todayReceivedCardsCount = todayCards.size
        )
    }

    companion object {
        fun fromDomain(entity: UserSpecialDeckInfo) = UserSpecialDeckInfoDto(
            specialDeckId = entity.specialDeckId.value,
            isCompleted = entity.isCompleted,
            cards = entity.cards.map { UserSpecialDeckCardInfoDto.fromDomain(it) }
        )
    }
}