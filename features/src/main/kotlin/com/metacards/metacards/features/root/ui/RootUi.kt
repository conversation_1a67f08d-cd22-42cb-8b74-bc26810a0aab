package com.metacards.metacards.features.root.ui

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.imePadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.arkivanov.decompose.extensions.compose.jetpack.stack.Children
import com.metacards.metacards.core.adv.ui.AdvUserConsentUi
import com.metacards.metacards.core.bottom_sheet.ModalBottomSheet
import com.metacards.metacards.core.dialog.default_component.DefaultDialog
import com.metacards.metacards.core.localization.ui.LocalizedContent
import com.metacards.metacards.core.message.ui.MessageUi
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.ConfigureSystemBars
import com.metacards.metacards.core.utils.LocalSystemBarsSettings
import com.metacards.metacards.core.utils.accumulate
import com.metacards.metacards.core.utils.defaultTransitionAnimation
import com.metacards.metacards.core.utils.keyboardAsState
import com.metacards.metacards.core.web_view.WebViewUi
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.features.account.ui.flow.AccountFlowUi
import com.metacards.metacards.features.auth.ui.AuthUi
import com.metacards.metacards.features.course.ui.CourseUi
import com.metacards.metacards.features.deck.ui.card_preview.CardsPreviewUi
import com.metacards.metacards.features.deck.ui.deck_info.DeckInfoUi
import com.metacards.metacards.features.deck.ui.scanning.DeckScanningUi
import com.metacards.metacards.features.favorite_cards.ui.FavoriteCardsUi
import com.metacards.metacards.features.layout.ui.DeckLayoutUi
import com.metacards.metacards.features.layout.ui.predefined_layout.PredefinedLayoutUi
import com.metacards.metacards.features.layout.ui.question_for_layout.QuestionForLayoutUI
import com.metacards.metacards.features.main.ui.MainUi
import com.metacards.metacards.features.payments.ui.PaymentComponent
import com.metacards.metacards.features.record.ui.archive.ArchiveUi
import com.metacards.metacards.features.record.ui.calendar.CalendarUi
import com.metacards.metacards.features.record.ui.create_record.CreateRecordFlowUi
import com.metacards.metacards.features.record.ui.record_details.RecordDetailsUi
import com.metacards.metacards.features.special_deck.ui.SpecialDeckUi
import com.metacards.metacards.features.splash.ui.SplashUi
import com.metacards.metacards.features.tutorial.ui.TutorialMessageUi
import com.metacards.metacards.features.user.ui.subscription.subscription_bottom_sheet.SubscriptionBottomSheetUi
import com.metacards.metacards.features.user_analytics.ui.UserAnalyticsUi
import com.metacards.metacards.features.welcome.ui.WelcomeScreensUi

@Composable
fun RootUi(
    component: RootComponent,
    modifier: Modifier = Modifier
) {
    val childStack by component.childStack.collectAsState()
    val appLanguage by component.appLanguage.collectAsState()
    val isKeyboardOpen by keyboardAsState()
    val isShouldBecomeInvisible by component.isShouldBecomeInvisible.collectAsState()
    val rootAlpha by animateFloatAsState(
        targetValue = if (isShouldBecomeInvisible) 0f else 1f, label = "root alpha"
    )

    LocalizedContent(appLanguage) {
        BoxWithFade(
            modifier = modifier
                .alpha(rootAlpha)
                .fillMaxSize()
                .background(CustomTheme.colors.background.primary),
            listOfColors = CustomTheme.colors.gradient.backgroundList
        ) {
            Box(
                modifier = Modifier
                    .matchParentSize()
            ) {
                Children(
                    childStack,
                    Modifier.matchParentSize(),
                    defaultTransitionAnimation()
                ) { child ->
                    when (val instance = child.instance) {
                        is RootComponent.Child.MainAuth -> AuthUi(instance.component)
                        is RootComponent.Child.Splash -> SplashUi(instance.component)
                        is RootComponent.Child.Main -> MainUi(instance.component)
                        is RootComponent.Child.DeckLayout -> DeckLayoutUi(instance.component)
                        is RootComponent.Child.JournalRecordDetails -> RecordDetailsUi(instance.component)
                        is RootComponent.Child.DeckInfo -> DeckInfoUi(instance.component)
                        is RootComponent.Child.DeckScanning -> DeckScanningUi(instance.component)
                        is RootComponent.Child.CardPreview -> CardsPreviewUi(instance.component)
                        is RootComponent.Child.PredefinedLayouts -> PredefinedLayoutUi(instance.component)
                        is RootComponent.Child.Calendar -> CalendarUi(instance.component)
                        is RootComponent.Child.JournalArchive -> ArchiveUi(instance.component)
                        is RootComponent.Child.AccountFlow -> AccountFlowUi(instance.component)
                        is RootComponent.Child.FavoriteCards -> FavoriteCardsUi(instance.component)
                        is RootComponent.Child.WebView -> WebViewUi(instance.component)
                        is RootComponent.Child.WelcomeScreens -> WelcomeScreensUi(instance.component)
                        is RootComponent.Child.CreateRecordFlow -> CreateRecordFlowUi(instance.component)
                        is RootComponent.Child.AdvUserConsent -> AdvUserConsentUi(instance.component)
                        is RootComponent.Child.SpecialDeck -> SpecialDeckUi(instance.component)
                        is RootComponent.Child.UserAnalytics -> UserAnalyticsUi(instance.component)
                        is RootComponent.Child.Course -> CourseUi(instance.component)
                    }
                }

                Box(modifier = Modifier.imePadding()) {
                    ModalBottomSheet(
                        control = component.bottomSheetControl,
                        addNavigationBarPadding = !isKeyboardOpen,
                        sheetBackgroundColor = Color.Transparent
                    ) {
                        QuestionForLayoutUI(component = it)
                    }

                    MessageUi(
                        component = component.messageComponent,
                        modifier = modifier,
                        bottomPadding = 8.dp
                    )

                    if (childStack.items.any { it.instance is RootComponent.Child.Main }) {
                        TutorialMessageUi(
                            component = component.tutorialMessageComponent,
                            modifier = modifier
                        )
                    }

                    ModalBottomSheet(
                        control = component.subscriptionBottomSheetControl
                    ) {
                        SubscriptionBottomSheetUi(component = it)
                    }

                    PaymentComponent(
                        component = component.paymentComponent,
                        modifier = Modifier.fillMaxSize()
                    )

                    DefaultDialog(component.dialogControl)
                }
            }
        }
    }

    ConfigureSystemBars(
        settings = LocalSystemBarsSettings.current.accumulate(),
        defaultStatusBarColor = Color.Transparent,
        defaultNavigationBarColor = CustomTheme.colors.system.navigationBarColor
    )
}

@Preview(showSystemUi = true)
@Composable
fun RootUiPreview() {
    val scope = rememberCoroutineScope()

    AppTheme {
        RootUi(FakeRootComponent(scope))
    }
}