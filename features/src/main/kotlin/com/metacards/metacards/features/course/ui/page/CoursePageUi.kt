package com.metacards.metacards.features.course.ui.page

import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.IconNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.course.domain.entity.CourseContentType
import com.metacards.metacards.features.course.domain.entity.CourseData
import com.metacards.metacards.features.course.domain.entity.CourseDataContent
import com.metacards.metacards.features.course.domain.entity.CourseProfit
import com.metacards.metacards.features.course.domain.entity.CourseStatus
import com.metacards.metacards.features.course.ui.widgets.CourseLoaderSkeleton
import com.metacards.metacards.features.home.ui.widget.ActionButton
import dev.icerock.moko.resources.StringResource
import dev.icerock.moko.resources.desc.ResourceFormattedStringDesc
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun CoursePageUi(
    component: CoursePageComponent,
) {
    val courseData by component.courseData.collectAsState()
    val isShouldBlur by component.shouldBlur.collectAsState()
    val bottomButtonText by component.bottomButtonText.collectAsState()

    var topBarHeight by remember { mutableStateOf(0.dp) }

    val localDensity = LocalDensity.current

    Box(
        modifier = Modifier
            .fillMaxSize()
            .statusBarsPadding()
    ) {
        Box {
            courseData?.let { course ->
                TopNavigationBar(
                    modifier = Modifier
                        .fillMaxWidth()
                        .onSizeChanged { size ->
                            with(localDensity) { topBarHeight = size.height.toDp() }
                        },
                    title = course.name,
                    leadingIcon = { BackNavigationItem() },
                    trailingContent = {
                        IconNavigationItem(
                            iconRes = R.drawable.ic_24_share_outlined,
                            onClick = component::onShareClick
                        )
                    }
                )
                Content(
                    courseData = course,
                    onContentClick = component::onContentClick,
                    onTestResultClick = component::onTestResultClick,
                    bottomButtonText = bottomButtonText.localizedByLocal(),
                    onStartClick = component::onStartClick,
                    onToOtherCoursesClick = component::onToOtherCoursesClick,
                    localDensity = localDensity,
                    isShouldBlur = isShouldBlur,
                    modifier = Modifier
                        .padding(top = topBarHeight)
                )
            }
        }

        if (isShouldBlur) PremiumButton(component::onBlockContentClick, topBarHeight)

        if (courseData == null) CourseLoaderSkeleton()
    }
}

@Composable
private fun PremiumButton(
    onBlockContentClick: () -> Unit,
    navBarHeight: Dp
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(top = navBarHeight)
            .clickable(onClick = onBlockContentClick)
    ) {
        ActionButton(
            modifier = Modifier
                .align(Alignment.Center)
                .padding(bottom = navBarHeight)
                .clickable(onClick = onBlockContentClick),
            text = StringDesc.Resource(R.string.add_record_premium_action_button),
            borderStroke = BorderStroke(
                0.5.dp,
                CustomTheme.colors.stroke.secondary
            ),
            leadingIcon = {
                IconNavigationItem(iconRes = R.drawable.ic_24_locked)
            }
        )
    }
}

@Composable
private fun SoonContent(courseData: CourseData) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
            .clip(RoundedCornerShape(16.dp))
            .background(CustomTheme.colors.background.primary)
    ) {
        Spacer(modifier = Modifier.height(16.dp))

        Box(
            modifier = Modifier
                .padding(horizontal = 16.dp)
        ) {
            AsyncImage(
                model = courseData.coverUrl,
                contentDescription = null,
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(2.05f)
                    .clip(RoundedCornerShape(16.dp)),
                contentScale = ContentScale.Crop
            )

            Box(
                modifier = Modifier
                    .padding(16.dp)
                    .background(CustomTheme.colors.button.small, RoundedCornerShape(30.dp)),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = R.string.showcase_programs_mock_item_hint.strResDesc()
                        .localizedByLocal(),
                    style = CustomTheme.typography.button.small,
                    fontSize = 13.sp,
                    color = CustomTheme.colors.text.caption,
                    modifier = Modifier
                        .padding(vertical = 8.dp, horizontal = 24.dp)
                )
            }
        }

        Spacer(modifier = Modifier.height(12.dp))

        Text(
            text = courseData.name.localizedByLocal(),
            style = CustomTheme.typography.heading.medium,
            color = CustomTheme.colors.text.primary,
            modifier = Modifier
                .padding(horizontal = 16.dp)
        )

        courseData.shortDescription?.localizedByLocal()?.let {
            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = it,
                style = CustomTheme.typography.caption.medium,
                color = CustomTheme.colors.text.secondary,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .heightIn(min = 34.dp)
            )
        }

        Spacer(modifier = Modifier.height(16.dp))
    }

    Spacer(modifier = Modifier.height(8.dp))

    Spacer(modifier = Modifier.height(16.dp))

    Text(
        text = R.string.course_page_soon_content_description
            .strResDesc()
            .localizedByLocal(),
        style = CustomTheme.typography.body.primary,
        color = CustomTheme.colors.text.primary,
        modifier = Modifier
            .padding(horizontal = 32.dp)
    )
}

@Composable
private fun BoxScope.Content(
    courseData: CourseData,
    onContentClick: (CourseDataContent) -> Unit,
    onTestResultClick: (CourseDataContent) -> Unit,
    bottomButtonText: String,
    onStartClick: () -> Unit,
    onToOtherCoursesClick: () -> Unit,
    localDensity: Density,
    isShouldBlur: Boolean,
    modifier: Modifier = Modifier
) {
    var buttonHeightDp by remember { mutableStateOf(0.dp) }
    val blurModifier = if (isShouldBlur) Modifier.blur(4.dp) else Modifier
    val isActiveCourse = courseData.status == CourseStatus.ACTIVE
    Column(
        modifier = modifier
            .fillMaxSize()
            .then(blurModifier)
            .then(if (isActiveCourse) Modifier.verticalScroll(rememberScrollState()) else Modifier)
    ) {

        Spacer(modifier = Modifier.height(16.dp))

        if (isActiveCourse) {
            HeaderWithDuration(courseData)

            Spacer(modifier = Modifier.height(16.dp))

            courseData.profits?.let { ProfitsSection(it) }

            Spacer(modifier = Modifier.height(16.dp))

            courseData.description?.let { ExpandableDescription(it) }

            Spacer(modifier = Modifier.height(24.dp))

            CourseContent(
                content = courseData.content,
                onContentClick = onContentClick,
                onTestResultClick = onTestResultClick
            )
        } else {
            SoonContent(courseData = courseData)
        }

        Spacer(
            modifier = Modifier
                .navigationBarsPadding()
                .height(buttonHeightDp + 16.dp)
        )
    }

    val buttonModifier = Modifier
        .fillMaxWidth()
        .align(Alignment.BottomCenter)
        .background(
            brush = Brush.verticalGradient(
                listOf(Color.Transparent, Color.Black)
            )
        )
        .navigationBarsPadding()
        .padding(horizontal = 16.dp)
        .padding(bottom = 4.dp)
        .onGloballyPositioned { coordinates ->
            buttonHeightDp = with(localDensity) {
                coordinates.size.height.toDp()
            }
        }.then(blurModifier)

    if (isActiveCourse) {
        MetaAccentButton(
            modifier = buttonModifier,
            text = bottomButtonText,
            onClick = onStartClick,
        )
    } else {
        MetaAccentButton(
            modifier = buttonModifier,
            text = R.string.course_page_soon_content_button_view_other_courses
                .strResDesc()
                .localizedByLocal(),
            onClick = onToOtherCoursesClick,
        )
    }
}

@Composable
private fun HeaderWithDuration(
    courseData: CourseData
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
            .background(CustomTheme.colors.background.primary, RoundedCornerShape(16.dp))
    ) {
        Spacer(modifier = Modifier.height(16.dp))

        AsyncImage(
            model = courseData.coverUrl,
            contentDescription = null,
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .aspectRatio(2.04f)
                .clip(RoundedCornerShape(16.dp))
        )

        Spacer(modifier = Modifier.height(12.dp))

        Text(
            text = courseData.name.localizedByLocal(),
            style = CustomTheme.typography.heading.medium,
            color = CustomTheme.colors.text.primary,
            modifier = Modifier
                .padding(horizontal = 16.dp)
        )

        courseData.description?.let {
            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = it.localizedByLocal(),
                style = CustomTheme.typography.caption.medium,
                color = CustomTheme.colors.text.secondary,
                modifier = Modifier
                    .padding(horizontal = 16.dp)
            )
        }

        Spacer(modifier = Modifier.height(12.dp))

        Row(
            modifier = Modifier
                .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {

            courseData.lessonsAmount?.let {
                Text(
                    text = ResourceFormattedStringDesc(
                        StringResource(R.string.course_page_header_lessons_count),
                        listOf(it)
                    )
                        .localizedByLocal(),
                    style = CustomTheme.typography.caption.medium,
                    color = CustomTheme.colors.text.secondary,
                )
            }

            if (courseData.lessonsAmount != null && courseData.testsAmount != null) {
                Icon(
                    painter = painterResource(R.drawable.ic_16_tetragon_rounded),
                    contentDescription = null,
                    tint = CustomTheme.colors.text.secondary
                )
            }

            courseData.testsAmount?.let {
                Text(
                    text = ResourceFormattedStringDesc(
                        StringResource(R.string.course_page_header_tests_count),
                        listOf(it)
                    )
                        .localizedByLocal(),
                    style = CustomTheme.typography.caption.medium,
                    color = CustomTheme.colors.text.secondary,
                )
            }

            Spacer(modifier = Modifier.weight(1f))

            courseData.duration?.let {
                Icon(
                    painter = painterResource(R.drawable.ic_16_course_clock),
                    contentDescription = null,
                    tint = CustomTheme.colors.text.secondary
                )

                Spacer(modifier = Modifier.width(2.dp))

                Text(
                    text = ResourceFormattedStringDesc(
                        StringResource(R.string.course_page_header_duraration),
                        listOf(it)
                    )
                        .localizedByLocal(),
                    style = CustomTheme.typography.caption.medium,
                    color = CustomTheme.colors.text.secondary,
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))
    }
}

@Composable
private fun ExpandableDescription(
    description: LocalizableString
) {
    var isExpanded by remember { mutableStateOf(false) }
    val rotate by animateFloatAsState(targetValue = if (isExpanded) 180f else 0f, label = "")
    val maxLines by remember { derivedStateOf { if (isExpanded) Int.MAX_VALUE else 2 } }
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .padding(horizontal = 16.dp)
    ) {
        Text(
            text = R.string.course_page_expanded_description_header
                .strResDesc()
                .localizedByLocal(),
            style = CustomTheme.typography.caption.medium,
            color = CustomTheme.colors.text.secondary,
            modifier = Modifier
                .padding(horizontal = 16.dp)
        )

        Spacer(modifier = Modifier.weight(1f))

        Icon(
            painter = painterResource(id = R.drawable.ic_16_down),
            contentDescription = null,
            tint = CustomTheme.colors.text.secondary,
            modifier = Modifier
                .clip(CircleShape)
                .rotate(rotate)
                .padding(horizontal = 16.dp)
                .clickable { isExpanded = !isExpanded }

        )
    }

    Spacer(modifier = Modifier.height(16.dp))

    Box(
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .clip(RoundedCornerShape(16.dp))
            .background(
                CustomTheme.colors.background.primary,
                RoundedCornerShape(16.dp)
            )
            .animateContentSize()
            .clickable { isExpanded = !isExpanded }
    ) {
        Text(
            text = description.localizedByLocal(),
            style = CustomTheme.typography.body.primary,
            color = CustomTheme.colors.text.primary,
            maxLines = maxLines,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier
                .padding(16.dp),
        )
    }
}

@Composable
private fun ProfitsSection(
    profits: List<CourseProfit>
) {
    Text(
        text = R.string.course_page_profits_section_header
            .strResDesc()
            .localizedByLocal(),
        style = CustomTheme.typography.caption.medium,
        color = CustomTheme.colors.text.secondary,
        modifier = Modifier.padding(start = 32.dp, end = 16.dp)
    )

    Spacer(modifier = Modifier.height(8.dp))

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
            .background(CustomTheme.colors.background.primary, RoundedCornerShape(16.dp))
    ) {

        Spacer(modifier = Modifier.height(16.dp))

        profits.forEach { profit ->
            ProfitSectionItem(profit)
        }

        Spacer(modifier = Modifier.height(16.dp))
    }
}

@Composable
private fun ProfitSectionItem(
    profit: CourseProfit
) {
    Row(
        modifier = Modifier
            .padding(horizontal = 16.dp, vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        AsyncImage(
            model = profit.iconUrl,
            contentDescription = null,
            modifier = Modifier
                .size(40.dp)
        )

        Spacer(modifier = Modifier.width(12.dp))

        Text(
            text = profit.description.localizedByLocal(),
            style = CustomTheme.typography.body.primary,
            color = CustomTheme.colors.text.primary,
        )
    }
}

@Composable
private fun CourseContent(
    content: List<CourseDataContent>,
    onContentClick: (CourseDataContent) -> Unit,
    onTestResultClick: (CourseDataContent) -> Unit
) {
    Text(
        text = R.string.course_page_course_content_header
            .strResDesc()
            .localizedByLocal(),
        style = CustomTheme.typography.caption.medium,
        color = CustomTheme.colors.text.secondary,
        modifier = Modifier
            .padding(horizontal = 32.dp)
    )

    Spacer(modifier = Modifier.height(8.dp))

    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp),
        modifier = Modifier.padding(horizontal = 16.dp)
    ) {
        content.forEachIndexed { i, data ->
            val prevCompleted = if (i == 0) true else content.getOrNull(i - 1)?.isCompleted ?: false
            ContentItem(
                content = data,
                prevCompleted = prevCompleted,
                onClick = { onContentClick(data) },
                onTestResultClick = { onTestResultClick(data) },
            )
        }
    }
}

@Composable
private fun ContentItem(
    content: CourseDataContent,
    prevCompleted: Boolean,
    onClick: () -> Unit,
    onTestResultClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val shouldHighlight = content.isCompleted || prevCompleted
    Column(
        modifier = modifier
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(16.dp))
                .background(CustomTheme.colors.background.primary)
                .clickable(onClick = onClick),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Spacer(modifier = Modifier.width(16.dp))

            Box(
                modifier = Modifier
                    .padding(vertical = 16.dp)
                    .size(32.dp)
                    .background(CustomTheme.colors.icons.disabled, CircleShape),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = content.order.toString(),
                    style = CustomTheme.typography.heading.medium,
                    color = if (shouldHighlight) {
                        CustomTheme.colors.text.caption
                    } else {
                        CustomTheme.colors.text.secondary
                    },
                )
            }

            Spacer(modifier = Modifier.width(12.dp))

            Text(
                text = content.name.localizedByLocal(),
                style = CustomTheme.typography.heading.small,
                color = if (shouldHighlight) CustomTheme.colors.text.primary else CustomTheme.colors.text.disabled,
                modifier = Modifier
                    .weight(1f)
                    .padding(end = 12.dp)
            )
        }

        if (content.type == CourseContentType.TEST) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 4.dp)
                    .clip(RoundedCornerShape(16.dp))
                    .background(CustomTheme.colors.background.primary)
                    .clickable(onClick = onTestResultClick),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = R.string.course_page_content_item_test_results_text
                        .strResDesc()
                        .localizedByLocal(),
                    style = CustomTheme.typography.heading.small,
                    color = if (content.isCompleted) CustomTheme.colors.text.primary else CustomTheme.colors.text.disabled,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier
                        .weight(1f)
                        .padding(
                            horizontal = 16.dp,
                            vertical = 24.dp
                        )
                )
            }
        }
    }
}

@Preview(showSystemUi = true)
@Composable
private fun Preview() {
    AppTheme {
        CoursePageUi(component = FakeCoursePageComponent())
    }
}