package com.metacards.metacards.features.account.ui.profile

import com.arkivanov.decompose.router.stack.ChildStack
import com.metacards.metacards.core.utils.createFakeChildStackStateFlow
import com.metacards.metacards.features.account.ui.profile.main.FakeProfileMainComponent
import kotlinx.coroutines.flow.StateFlow

class FakeProfileFlowComponent : ProfileFlowComponent {
    override val childStack: StateFlow<ChildStack<*, ProfileFlowComponent.Child>> =
        createFakeChildStackStateFlow(ProfileFlowComponent.Child.Main(FakeProfileMainComponent()))
}