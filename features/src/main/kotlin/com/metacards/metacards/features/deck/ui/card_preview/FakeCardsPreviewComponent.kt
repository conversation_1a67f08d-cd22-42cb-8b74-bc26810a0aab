package com.metacards.metacards.features.deck.ui.card_preview

import com.metacards.metacards.core.dialog.FakeDialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.FakeDefaultDialogComponent
import com.metacards.metacards.features.deck.domain.entity.Card
import kotlinx.coroutines.flow.MutableStateFlow

class FakeCardsPreviewComponent() : CardsPreviewComponent {
    override val cards = emptyList<Card>()
    override val currentCardIndex = MutableStateFlow(1)
    override val isShareLoading = MutableStateFlow(false)
    override val isToggleShareEnable = MutableStateFlow(false)
    override val shouldShowGif = true
    override val dialogControl =
        FakeDialogControl<DefaultDialogComponent.Config, DefaultDialogComponent>(
            FakeDefaultDialogComponent()
        )

    override fun onCloseButtonClick() = Unit
    override fun onPageSelected(index: Int) = Unit
    override fun onToggleShare() = Unit
}