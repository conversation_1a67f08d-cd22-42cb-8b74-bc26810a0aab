package com.metacards.metacards.features.account.ui

import androidx.compose.animation.AnimatedContent
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Icon
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.bottom_sheet.ModalBottomSheet
import com.metacards.metacards.core.dialog.default_component.DefaultDialog
import com.metacards.metacards.core.localization.domain.AppLanguage
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.utils.mirrorForRtl
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.MetaToggle
import com.metacards.metacards.core.widget.button.MetaButtonDefaults
import com.metacards.metacards.core.widget.button.MetaButtonIconWidget
import com.metacards.metacards.core.widget.button.MetaSecondaryButton
import com.metacards.metacards.features.R
import com.metacards.metacards.features.account.ui.language.AccountLanguageUi
import com.metacards.metacards.features.account.ui.widgets.AccountListBlock
import com.metacards.metacards.features.account.ui.widgets.AccountListItem
import com.metacards.metacards.features.account.ui.widgets.AccountListItemTrailing
import com.metacards.metacards.features.advbanner.ui.AccountAdvBannerUi
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun AccountUi(
    component: AccountComponent,
    navBarHeight: Dp,
    modifier: Modifier = Modifier,
) {
    val notificationToggleState by component.notificationToggleState.collectAsState()
    val appLanguage by component.appLanguage.collectAsState()
    val accountAdvBanner by component.accountAdvBanner.collectAsState()

    BoxWithFade(modifier, listOfColors = CustomTheme.colors.gradient.backgroundList) {
        Column {
            key(appLanguage) {
                Text(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 24.dp, start = 16.dp),
                    text = R.string.account_main_header.strResDesc().localizedByLocal(),
                    style = CustomTheme.typography.heading.primary,
                    color = CustomTheme.colors.text.primary
                )
            }
            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(24.dp),
                contentPadding = PaddingValues(start = 16.dp, end = 16.dp, top = 24.dp),
                modifier = Modifier.fillMaxWidth()
            ) {

                item { NameBlock(component) }

                accountAdvBanner?.let {
                    item {
                        AccountAdvBannerUi(
                            accountAdvBanner = it,
                            onClick = component::onAdvBannerClick,
                        )
                    }
                }

                item {
                    AccountListBlock(
                        headerText = R.string.account_banner_star_deck_header.strResDesc(),
                        backgroundColor = Color.Transparent,
                        background = painterResource(id = R.drawable.bg_star_deck),
                    ) {
                        AccountListItem(
                            leadingText = R.string.account_banner_star_deck_item.strResDesc(),
                            onClick = component::onSpecialDeckClick,
                            contentPadding = PaddingValues(16.dp)
                        )
                    }
                }

                item {
                    AccountListBlock(headerText = R.string.account_main_additional_header.strResDesc()) {
                        AccountListItem(
                            leadingText = R.string.account_main_additional_subscription.strResDesc(),
                            onClick = component::onSubscriptionClick,
                            contentPadding = PaddingValues(
                                start = 16.dp,
                                end = 16.dp,
                                top = 16.dp,
                                bottom = 8.dp
                            )
                        )
                        AccountListItem(
                            leadingText = R.string.account_main_additional_buy_decks.strResDesc(),
                            onClick = component::onBuyDecksClick
                        )

                        AccountListItem(
                            leadingText = R.string.account_main_additional_promocodes.strResDesc(),
                            onClick = component::onPromocodesClick
                        )

                        AccountListItem(
                            leadingText = R.string.account_main_additional_tutorial.strResDesc(),
                            onClick = component::onLessonsClick
                        )
                        AccountListItem(
                            leadingText = R.string.account_main_additional_favourite.strResDesc(),
                            onClick = component::onFavouriteCardsClick,
                            contentPadding = PaddingValues(
                                start = 16.dp,
                                end = 16.dp,
                                top = 8.dp,
                                bottom = 16.dp
                            )
                        )
                    }
                }

                item {
                    AccountListBlock(headerText = R.string.account_main_settings_header.strResDesc()) {
                        AccountListItem(
                            leadingText = R.string.account_main_settings_language.strResDesc(),
                            onClick = component::onLanguageClick,
                            contentPadding = PaddingValues(
                                start = 16.dp,
                                end = 16.dp,
                                top = 16.dp,
                                bottom = 8.dp
                            ),
                            trailingContent = {
                                Row(verticalAlignment = Alignment.CenterVertically) {
                                    Text(
                                        text = when (appLanguage) {
                                            AppLanguage.RUS -> R.string.account_main_language_russian
                                            AppLanguage.ENG -> R.string.account_main_language_english
                                            AppLanguage.ESP -> R.string.account_main_language_spanish
                                            AppLanguage.UKR -> R.string.account_main_language_ukrainian
                                            AppLanguage.KAZ -> R.string.account_main_language_kazakh
                                            AppLanguage.FRA -> R.string.account_main_language_french
                                            AppLanguage.ARB -> R.string.account_main_language_arab
                                        }.strResDesc().localizedByLocal(),
                                        color = CustomTheme.colors.text.secondary,
                                        style = CustomTheme.typography.heading.small,
                                    )

                                    AccountListItemTrailing()
                                }
                            }
                        )
                        AccountListItem(
                            leadingText = R.string.account_main_settings_notifications.strResDesc(),
                            onClick = {
                                component.onNotificationToggleClick(
                                    // Off потому что onCheckedChange отдает значение В КОТОРОЕ юзер хочет его поставить
                                    notificationToggleState is AccountComponent.NotificationToggleState.Off
                                )
                            },
                            contentPadding = PaddingValues(
                                start = 16.dp,
                                end = 16.dp,
                                top = 8.dp,
                                bottom = 16.dp
                            ),
                            trailingContent = {
                                AnimatedContent(
                                    targetState = notificationToggleState,
                                    label = "NotificationItem"
                                ) {
                                    if (it is AccountComponent.NotificationToggleState.Loading) {
                                        CircularProgressIndicator(
                                            modifier = Modifier.size(MetaButtonDefaults.bigButton.minContentSize),
                                            color = CustomTheme.colors.icons.primary
                                        )
                                    } else {
                                        MetaToggle(
                                            checked = notificationToggleState is
                                                    AccountComponent.NotificationToggleState.On,
                                            onCheckedChange = component::onNotificationToggleClick,
                                            modifier = Modifier.height(24.dp)
                                        )
                                    }
                                }
                            },
                        )
                    }
                }

                item {
                    AccountListBlock(headerText = R.string.account_main_about_app_header.strResDesc()) {
                        AccountListItem(
                            leadingText = R.string.account_main_about_app_help.strResDesc(),
                            onClick = component::onFeedbackClick,
                            contentPadding = PaddingValues(
                                start = 16.dp,
                                end = 16.dp,
                                top = 16.dp,
                                bottom = 8.dp
                            ),
                        )
                        AccountListItem(
                            leadingText = R.string.account_main_about_app_rate.strResDesc(),
                            onClick = component::onRateClick
                        )
                        AccountListItem(
                            leadingText = R.string.account_main_about_app_text.strResDesc(),
                            onClick = component::onAboutTheAppClick,
                            contentPadding = PaddingValues(
                                start = 16.dp,
                                end = 16.dp,
                                top = 8.dp,
                                bottom = 16.dp
                            ),
                        )
                    }
                }

                item {
                    MetaSecondaryButton(
                        text = R.string.account_main_button_add_your_deck.strResDesc()
                            .localizedByLocal(),
                        leadingWidget = {
                            MetaButtonIconWidget(
                                icon = R.drawable.ic_24_photo,
                                modifier = Modifier.padding(end = 8.dp)
                            )
                        },
                        onClick = component::onAddYourDeckClick,
                        modifier = Modifier.fillMaxWidth()
                    )
                }

                item {
                    Spacer(modifier = Modifier.height(navBarHeight))
                }
            }
        }
    }

    ModalBottomSheet(
        control = component.languageBottomSheetControl,
        sheetShape = RectangleShape
    ) {
        AccountLanguageUi(component = it)
    }

    DefaultDialog(component.dialogControl)
}

@Composable
private fun NameBlock(
    component: AccountComponent,
    modifier: Modifier = Modifier,
) {
    val user by component.user.collectAsState()
    val nameBlockText = if (user == null) {
        R.string.account_main_name_without_user.strResDesc().localizedByLocal()
    } else {
        if (user?.name != null) {
            user?.name ?: ""
        } else {
            R.string.account_main_name_unspecified.strResDesc().localizedByLocal()
        }
    }

    Card(
        modifier = modifier
            .clip(RoundedCornerShape(16.dp))
            .clickable(onClick = component::onProfileBlockClick),
        elevation = 0.dp,
        backgroundColor = CustomTheme.colors.background.primary
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(all = 16.dp)
        ) {
            Column(modifier = Modifier.align(Alignment.CenterStart)) {
                Row {
                    Text(
                        text = nameBlockText,
                        color = CustomTheme.colors.text.primary,
                        style = CustomTheme.typography.heading.small
                    )
                    if (user?.name == null) {
                        Surface(
                            shape = CircleShape,
                            color = CustomTheme.colors.background.notification,
                            modifier = Modifier
                                .align(Alignment.Top)
                                .padding(start = 1.dp)
                                .size(8.dp),
                            content = {}
                        )
                    }
                }

                Text(
                    text = R.string.account_main_name_caption.strResDesc().localizedByLocal(),
                    color = CustomTheme.colors.text.secondary,
                    style = CustomTheme.typography.caption.bigSemiBold,
                    modifier = Modifier.padding(top = 4.dp)
                )
            }

            Icon(
                painter = painterResource(id = R.drawable.ic_24_right),
                contentDescription = null,
                tint = CustomTheme.colors.text.secondary,
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .mirrorForRtl()
            )
        }
    }
}

@Preview
@Composable
fun AccountUiPreview() {
    AppTheme {
        AccountUi(
            component = FakeAccountComponent(),
            navBarHeight = 64.dp
        )
    }
}
