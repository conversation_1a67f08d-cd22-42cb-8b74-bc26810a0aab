package com.metacards.metacards.features.user.data

import android.content.Context
import android.os.Build
import co.touchlab.kermit.Logger
import com.google.firebase.Timestamp
import com.metacards.metacards.core.error_handling.UnauthorizedException
import com.metacards.metacards.core.localization.data.AppLanguageService
import com.metacards.metacards.core.localization.domain.AppLanguage
import com.metacards.metacards.core.user.data.UserDto
import com.metacards.metacards.core.user.data.toUser
import com.metacards.metacards.core.user.domain.AuthUserInfo
import com.metacards.metacards.core.user.domain.FavoriteCard
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.user.domain.UserId
import com.metacards.metacards.core.user.domain.toDto
import com.metacards.metacards.core.utils.LoadableState
import com.metacards.metacards.core.utils.e
import com.metacards.metacards.core.utils.getAppVersionName
import com.metacards.metacards.features.user.domain.UserRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import java.util.TimeZone
import kotlin.coroutines.cancellation.CancellationException

class UserRepositoryImpl(
    private val userDataSource: UserDataSource,
    private val coroutineScope: CoroutineScope,
    private val ioDispatcher: CoroutineDispatcher,
    private val languageService: AppLanguageService,
    private val context: Context
) : UserRepository {

    companion object {
        private const val OS_NAME = "Android"
    }

    private val logger = Logger.withTag("UserRepositoryImpl")
    private var job: Job? = null
    private var authUserInfo: AuthUserInfo? = null

    override val user: MutableStateFlow<User?> = MutableStateFlow(null)

    override suspend fun fetchUser(authUserInfo: AuthUserInfo?) {
        this.authUserInfo = authUserInfo

        if (authUserInfo == null) {
            user.value = null
            return
        }

        if (userDataSource.getUser(authUserInfo.userId.value) == null) {
            userDataSource.createUser(authUserInfo.userId, authUserInfo.email, getUserInfo())
        }

        updateCurrentUser()
    }

    override suspend fun toggleFavoriteCard(isFavorite: Boolean, card: FavoriteCard) {
        val user = user.value ?: throw IllegalStateException("User is null")
        val newFavoriteCards = user.favoriteCards.toMutableList()

        if (isFavorite) {
            newFavoriteCards.removeIf { it.id == card.id }
            newFavoriteCards.add(card)
        } else {
            newFavoriteCards.remove(card)
        }

        userDataSource.updateFavoriteCards(user.userId, newFavoriteCards)
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun getPurchasedDecksIdFlow(): Flow<LoadableState<List<String>>> {
        return user.flatMapLatest { user ->
            user?.userId?.let { userId ->
                userDataSource.getUserDecksFlow(userId)
            } ?: flow { emit(LoadableState(data = emptyList())) }
        }
    }

    override suspend fun getPurchasedDecksId(): List<String?> {
        return user.value?.userId?.let { userId ->
            userDataSource.getUserDecks(userId)
        } ?: emptyList<String>()
    }

    override suspend fun deleteUser(userId: UserId) {
        userDataSource.deleteUser(userId)
    }

    override suspend fun updateUser(userId: UserId, updatedUser: User) {
        userDataSource.updateUser(userId, updatedUser.toDto())
    }

    private fun updateCurrentUser() {
        job?.cancel()

        job = coroutineScope.launch(ioDispatcher) {
            try {
                val authUserInfo = authUserInfo ?: throw UnauthorizedException(
                    NullPointerException("AuthUser is null")
                )

                userDataSource.getUserFlow(authUserInfo.userId.value).map { userDto ->
                    userDto?.toUser(authUserInfo)
                }.collectLatest { newUser ->
                    user.value = newUser
                }
            } catch (e: CancellationException) {
                throw e
            } catch (e: Exception) {
                logger.e(e)
            }
        }
    }

    override suspend fun addDeckToPurchased(deckId: String) {
        val result = userDataSource.addDeckToPurchased(deckId)

        logger.d(result.toString())
    }

    override suspend fun updateYearOfBirth(userId: UserId, yearOfBirth: Int) {
        userDataSource.updateUser(userId, mapOf("yearOfBirth" to yearOfBirth))
    }

    override suspend fun updateGender(userId: UserId, gender: UserDto.Gender) {
        userDataSource.updateUser(userId, mapOf("gender" to gender))
    }

    override suspend fun updateName(userId: UserId, name: String) {
        userDataSource.updateUser(userId, mapOf("name" to name))
    }

    override suspend fun updateLanguage(userId: UserId, language: AppLanguage) {
        userDataSource.updateUser(userId, mapOf("language" to language))
    }

    override suspend fun updateUserInfoOnAppLaunch(userId: UserId) {
        userDataSource.updateUser(userId, getUserInfo())
    }

    private fun getUserInfo(): Map<String, Any> = mapOf(
        "deviceName" to "${Build.MANUFACTURER} ${Build.MODEL}",
        "osVersion" to "$OS_NAME ${Build.VERSION.SDK_INT}",
        "appVersion" to context.getAppVersionName().orEmpty(),
        "timezone" to TimeZone.getDefault().rawOffset / 1000 / 60,
        "language" to languageService.getLanguage(),
        "lastAuthDate" to Timestamp.now(),
    )
}
