package com.metacards.metacards.features.record.ui.record_details.widget

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.button.MetaTransparentButton
import com.metacards.metacards.features.R
import com.metacards.metacards.features.record.ui.record_details.RecordDetailsComponent
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun RecordBottomButtons(
    component: RecordDetailsComponent
) {
    val userSubscriptionState by component.userSubscriptionState.collectAsState()

    BoxWithFade(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 16.dp),
        listOfColors = listOf(
            CustomTheme.colors.gradient.backgroundEnd.copy(alpha = 0f),
            CustomTheme.colors.gradient.backgroundEnd
        )
    ) {
        when (userSubscriptionState) {
            is User.SubscriptionState.None, is User.SubscriptionState.Ongoing -> {
                DefaultButtons(
                    component = component,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                )
            }

            null -> NotAuthorizedButtons(
                component = component,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
            )
        }
    }
}

@Composable
private fun DefaultButtons(component: RecordDetailsComponent, modifier: Modifier = Modifier) {
    val buttonText = when (component.recordType) {
        RecordDetailsComponent.RecordType.InCreation ->
            R.string.add_record_complete_button.strResDesc().localizedByLocal()

        RecordDetailsComponent.RecordType.Common ->
            R.string.common_record_complete_button.strResDesc().localizedByLocal()

        RecordDetailsComponent.RecordType.Archived ->
            R.string.archive_record_complete_button.strResDesc().localizedByLocal()
    }

    val buttonState by component.buttonState.collectAsState()

    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        MetaAccentButton(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp),
            state = buttonState,
            text = buttonText,
            onClick = component::onCompleteButtonClick
        )
    }
}

@Composable
private fun NotAuthorizedButtons(component: RecordDetailsComponent, modifier: Modifier = Modifier) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            modifier = Modifier
                .padding(horizontal = 16.dp, vertical = 4.dp)
                .align(Alignment.CenterHorizontally),
            text = R.string.add_record_authorize_button_label.strResDesc().localizedByLocal(),
            style = CustomTheme.typography.caption.medium,
            color = CustomTheme.colors.text.secondary,
            textAlign = TextAlign.Center
        )

        MetaAccentButton(
            modifier = Modifier.fillMaxWidth(),
            text = R.string.add_record_authorize_button.strResDesc().localizedByLocal(),
            onClick = component::onAuthButtonClick
        )

        MetaTransparentButton(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp),
            text = R.string.add_record_skip_button.strResDesc().localizedByLocal(),
            onClick = component::onSkipButtonClick
        )
    }
}