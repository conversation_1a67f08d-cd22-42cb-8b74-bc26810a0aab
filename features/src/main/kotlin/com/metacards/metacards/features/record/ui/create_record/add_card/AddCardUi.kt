package com.metacards.metacards.features.record.ui.create_record.add_card

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.camera.CameraPreviewWithPhoto
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import com.metacards.metacards.features.R
import com.metacards.metacards.features.record.ui.create_record.add_card.image_tracking.ImageTrackingUi
import com.metacards.metacards.features.record.ui.create_record.add_card.widgets.CardFrame
import com.metacards.metacards.features.record.ui.create_record.add_card.widgets.PhotoSwitcher
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun AddCardUi(
    component: AddCardComponent,
    modifier: Modifier = Modifier
) {
    val currentState by component.currentState.collectAsState()

    when (currentState) {
        AddCardComponent.State.Camera -> CameraUi(component, modifier, currentState)
        AddCardComponent.State.ImageTracking -> ImageTrackingUi(modifier) {
            Content(
                component = component,
                currentState = currentState,
                action = component::onContinueClick,
                isHideFrame = it
            )
        }
    }
}

@Composable
private fun CameraUi(
    component: AddCardComponent,
    modifier: Modifier = Modifier,
    currentState: AddCardComponent.State
) {
    Box(modifier) {
        CameraPreviewWithPhoto(
            modifier = Modifier.fillMaxSize(),
            takePhoto = component::takePhoto
        ) { takePhoto ->
            Content(component, currentState, false, takePhoto)
        }
    }
}

@Composable
private fun Content(
    component: AddCardComponent,
    currentState: AddCardComponent.State,
    isHideFrame: Boolean,
    action: () -> Unit
) {
    val cardFrameText = when (currentState) {
        AddCardComponent.State.Camera -> R.string.camera_frame_hint.strResDesc()
        AddCardComponent.State.ImageTracking -> R.string.image_tracking_frame_hint.strResDesc()
    }

    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        TopNavigationBar(leadingIcon = { BackNavigationItem() })

        Spacer(modifier = Modifier.size(8.dp))

        CardFrame(cardFrameText, isHideFrame)

        Spacer(modifier = Modifier.size(16.dp))

        PhotoSwitcher(component)

        Spacer(modifier = Modifier.size(20.dp))

        if (isHideFrame || currentState == AddCardComponent.State.Camera) {
            val cardButtonText =
                if (isHideFrame) R.string.image_tracking_button_title.strResDesc() else R.string.camera_photo_button_title.strResDesc()
            MetaAccentButton(
                modifier = Modifier
                    .padding(16.dp)
                    .fillMaxWidth(),
                text = cardButtonText.localizedByLocal(),
                onClick = { action() }
            )
        }
    }
}

@Preview
@Composable
private fun CreateUiPreview() {
    AppTheme {
        AddCardUi(component = FakeAddCardComponent())
    }
}
