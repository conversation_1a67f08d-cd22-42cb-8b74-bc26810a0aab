package com.metacards.metacards.features.advbanner.domain

import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.features.advbanner.data.BannerDataSource
import com.metacards.metacards.features.advbanner.domain.entity.AccountAdvBanner
import com.metacards.metacards.features.user.domain.GetUserSubscriptionStateInteractor
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine

class GetAccountScreenAdvBannerInteractor(
    private val getUserSubscriptionStateInteractor: GetUserSubscriptionStateInteractor,
    private val bannerDataSource: BannerDataSource
) {

    fun execute(): Flow<AccountAdvBanner?> = combine(
        getUserSubscriptionStateInteractor.execute(),
        bannerDataSource.getAccountBanners()
    ) { subscriptionState, bannersDto ->
        val banners = bannersDto.map { it.toDomain() }
        val result = banners.find { it.priority == BannerDataSource.HIGHEST_PRIORITY } ?: run {
            val userHasSubscription = subscriptionState is User.SubscriptionState.Ongoing
            if (!userHasSubscription) {
                banners.minByOrNull { it.priority }
            } else {
                null
            }
        }
        result
    }
}
