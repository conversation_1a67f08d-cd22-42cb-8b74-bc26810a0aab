package com.metacards.metacards.features.account.ui.language

import android.os.Parcelable
import com.metacards.metacards.core.localization.domain.AppLanguage
import kotlinx.coroutines.flow.StateFlow
import kotlinx.parcelize.Parcelize

interface AccountLanguageComponent {
    val languageFlow: StateFlow<AppLanguage>

    fun onLanguageClick(language: AppLanguage)
    fun onSaveClick()
    fun onDismiss()

    @Parcelize
    object Config : Parcelable

    sealed interface Output {
        object DismissRequested : Output
    }
}