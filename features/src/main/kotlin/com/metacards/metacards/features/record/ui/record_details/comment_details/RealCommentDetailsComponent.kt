package com.metacards.metacards.features.record.ui.record_details.comment_details

import com.arkivanov.decompose.ComponentContext

class RealCommentDetailsComponent(
    componentContext: ComponentContext,
    override val commentText: String,
    override val commentCreationDate: String?,
    override val cardImageUrl: String?,
    override val onCardClick: (() -> Unit)?,
    private val onOutput: (CommentDetailsComponent.Output) -> Unit
) : ComponentContext by componentContext, CommentDetailsComponent {

    override fun onDismiss() {
        onOutput(CommentDetailsComponent.Output.DismissRequested)
    }
}