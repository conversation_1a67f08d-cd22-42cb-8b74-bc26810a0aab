package com.metacards.metacards.features.course.ui.passed_test_result

import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.localization.ui.LocalizableString
import com.metacards.metacards.features.course.domain.entity.CourseContentId
import com.metacards.metacards.features.course.domain.entity.CourseId
import com.metacards.metacards.features.course.domain.entity.CourseResult
import com.metacards.metacards.features.course.domain.entity.CourseThemeId
import com.metacards.metacards.features.layout.domain.CardSource
import com.metacards.metacards.features.layout.domain.LayoutId
import com.metacards.metacards.features.layout.domain.PredefinedLayoutWithAvailable
import kotlinx.coroutines.flow.StateFlow

interface CoursePassedTestResultComponent {

    val courseName: LocalizableString?
    val isShareInProgress: StateFlow<Boolean>
    val courseResult: StateFlow<CourseResult?>
    val predefinedLayouts: StateFlow<List<PredefinedLayoutWithAvailable>>
    val testId: CourseContentId
    val dialogControl: DialogControl<DefaultDialogComponent.Config, DefaultDialogComponent>
    val title: StateFlow<LocalizableString>

    fun onRepeatTestClick()
    fun onCloseClick()
    fun onShareClick()
    fun onVideoFullScreenClick(position: Long, videoUrl: String)
    fun onLayoutClick(layoutId: LayoutId)
    fun onBlockContentClick()

    sealed interface Output {
        data object CloseRequested : Output
        data class RepeatTestRequested(val contentId: CourseContentId) : Output
        data class SubscriptionBottomSheetRequested(val withAdv: Boolean) : Output
        data class LayoutDetailsRequested(
            val cardSource: CardSource,
            val courseId: CourseId?,
            val themeId: CourseThemeId?
        ) : Output
        data object AuthScreenRequested : Output
    }
}