package com.metacards.metacards.features.auth.ui.main_auth

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import com.airbnb.lottie.compose.LottieAnimatable
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionResult
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieAnimatable
import com.airbnb.lottie.compose.rememberLottieComposition
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.button.LogoButton
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.button.MetaSecondaryButton
import com.metacards.metacards.features.R
import com.metacards.metacards.features.auth.domain.SSOType
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun MainAuthUi(component: MainAuthComponent) {
    val ssoVariants by component.ssoAuthVariants.collectAsState()
    val shouldAnimate by component.shouldAnimate.collectAsState()

    BoxWithFade(
        modifier = Modifier.fillMaxSize(),
        listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        val (composition, animatable) = animatedLogo(shouldAnimate, component)

        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
        ) {
            Icon(
                modifier = Modifier
                    .padding(top = 16.dp)
                    .size(24.dp)
                    .align(Alignment.TopEnd)
                    .clickable(boundedRipple = false, onClick = component::onCloseButtonClick),
                painter = painterResource(id = R.drawable.ic_24_close),
                contentDescription = null,
                tint = CustomTheme.colors.icons.primary
            )

            Column(
                modifier = Modifier.align(Alignment.Center),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.SpaceEvenly
            ) {

                // For animate used another composable,
                // so if should animate this composable should be hidden
                LottieAnimation(
                    modifier = Modifier
                        .fillMaxWidth()
                        .alpha(0f),
                    alignment = Alignment.TopCenter,
                    composition = composition.value,
                    contentScale = ContentScale.None,
                    progress = { 1f }
                )

                Spacer(modifier = Modifier.height(40.dp))

                Content(animatable, shouldAnimate, component)
            }

            Box(modifier = Modifier.align(Alignment.BottomCenter)) {
                SSOAuthVariants(ssoVariants, component::onSSOClick)
            }
        }
    }
}

@Composable
private fun ColumnScope.Content(
    animatable: LottieAnimatable,
    shouldAnimate: Boolean,
    component: MainAuthComponent
) {
    val visibilityIntOffsetSpec = tween<IntOffset>(animatable.getNewAnimationDuration())
    val visibilityFloatSpec = tween<Float>(animatable.getNewAnimationDuration())
    val slideIn = slideInVertically(visibilityIntOffsetSpec) { it / 2 }
    val fade = fadeIn(visibilityFloatSpec)

    AnimatedVisibility(
        modifier = Modifier.weight(1f),
        visible = !shouldAnimate || animatable.progress >= 0.4f, // magic number to start button animation
        enter = slideIn + fade
    ) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            MetaAccentButton(
                modifier = Modifier.fillMaxWidth(),
                text = R.string.main_auth_sign_up_button_text.strResDesc().localizedByLocal(),
                onClick = component::onSignUpButtonClick
            )

            MetaSecondaryButton(
                modifier = Modifier.fillMaxWidth(),
                text = R.string.main_auth_sign_in_button_text.strResDesc().localizedByLocal(),
                onClick = component::onSignInButtonClick
            )
        }
    }
}

@Composable
private fun BoxScope.animatedLogo(
    shouldAnimate: Boolean,
    component: MainAuthComponent
): Pair<LottieCompositionResult, LottieAnimatable> {
    val composition =
        rememberLottieComposition(LottieCompositionSpec.RawRes(R.raw.anim_logo_step2))
    val animatable = rememberLottieAnimatable()

    LaunchedEffect(shouldAnimate) {
        if (shouldAnimate) {
            composition.await()
            animatable.animate(composition.value)
            component.onAnimationEnd()
        }
    }

    LottieAnimation(
        modifier = Modifier
            .fillMaxSize()
            .offset(y = (-120).dp) // magic number to alignment by screen center
            .align(Alignment.Center),
        alignment = Alignment.Center,
        composition = composition.value,
        contentScale = ContentScale.None,
        progress = {
            if (shouldAnimate) animatable.value else 1f
        }
    )
    return Pair(composition, animatable)
}

@Composable
private fun SSOAuthVariants(
    ssoVariants: List<SSOType>,
    onSSOClick: (SSOType) -> Unit
) {
    if (ssoVariants.isNotEmpty()) {
        Column(
            modifier = Modifier.padding(bottom = 16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceEvenly
        ) {
            Text(
                modifier = Modifier.padding(bottom = 16.dp),
                text = R.string.main_auth_sign_in_by_sso_text.strResDesc().localizedByLocal(),
                style = CustomTheme.typography.caption.medium,
                color = CustomTheme.colors.text.primary
            )

            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                ssoVariants.forEachIndexed { index, _ ->
                    if (index != 0) {
                        Spacer(Modifier.width(8.dp))
                    }

                    LogoButton(
                        modifier = Modifier.weight(1f),
                        onClick = { onSSOClick(ssoVariants[index]) },
                        logoResource = ssoVariants[index].getSSOLogoResourceByType()
                    )
                }
            }
        }
    }
}

private fun LottieAnimatable.getNewAnimationDuration() =
    (((composition?.duration ?: 0f) * progress)).toInt()

private fun SSOType.getSSOLogoResourceByType(): Int {
    return when (this) {
        is SSOType.Google -> R.drawable.ic_24_logo_gg
        is SSOType.VK -> R.drawable.ic_24_logo_vk
        is SSOType.OK -> R.drawable.ic_24_logo_ok
        is SSOType.Yandex -> R.drawable.ic_24_logo_ya
    }
}

@Preview
@Composable
fun MainAuthUiPreview() {
    AppTheme {
        MainAuthUi(component = FakeMainAuthComponent())
    }
}