import com.google.firebase.crashlytics.buildtools.gradle.CrashlyticsExtension

plugins {
    id("com.android.application")
    kotlin("android")
    kotlin("plugin.serialization")
    kotlin("plugin.parcelize")
    kotlin("plugin.compose")
    id("com.google.gms.google-services")
    id("io.gitlab.arturbosch.detekt")
    id("com.google.firebase.crashlytics")
}

// CI
apply {
    from("ci.gradle")
}

val isLocalBuild = System.getenv("GITLAB_CI") != "true"
val isMergeRequestBuild = System.getenv("CI_MERGE_REQUEST_ID") != null

android {
    val minSdkVersion: Int by rootProject.extra
    val targetSdkVersion: Int by rootProject.extra
    val jvmTargetVersion: String by rootProject.extra
    val applicationId: String by rootProject.extra

    namespace = applicationId
    compileSdk = targetSdkVersion

    bundle {
        language {
            enableSplit = false
        }
    }

    defaultConfig {
        this.applicationId = applicationId
        minSdk = minSdkVersion
        targetSdk = targetSdkVersion
        versionCode = 53
        versionName = "1.8.6"
    }

    androidResources {
        noCompress.addAll(listOf("filamat", "ktx"))
    }

    signingConfigs {
        getByName("debug") {
            storeFile = project.file("metacards-debug.jks")
            storePassword = "android"
            keyAlias = "androiddebugkey"
            keyPassword = "android"
        }

        create("release") {
            storeFile = project.file("metacards-release.jks")
            storePassword = System.getenv("KEY_STORE_PASSWORD")
            keyAlias = System.getenv("KEY_ALIAS")
            keyPassword = System.getenv("KEY_PASSWORD")
        }
    }

    buildTypes {
        debug {
            if (!isLocalBuild && !isMergeRequestBuild) {
                isMinifyEnabled = true
                isShrinkResources = true
                /**
                 Билды которые собирает CI для теста должны быть isMinifyEnabled = true для
                 своевременного обнаружения проблем, которые вызывает R8.
                 isDebuggable = false нужен для устранения проблемы:
                 BuildType 'debug' is both debuggable and has 'isMinifyEnabled' set to true.
                 Debuggable builds are no longer name minified and all code optimizations
                 and obfuscation will be disabled.
                 Это влечет за собой то, что BuildConfig.DEBUG для модуля app вернет false.
                 Вместо BuildConfig.DEBUG следует использовать const val isDebugBuild.
                 */
                isDebuggable = false
                setProguardFiles(
                    listOf(
                        getDefaultProguardFile("proguard-android-optimize.txt"),
                        "proguard-rules.pro"
                    )
                )
            }

            versionNameSuffix = "-debug"
            signingConfig = signingConfigs["debug"]
        }
        release {
            isMinifyEnabled = true
            isShrinkResources = true
            setProguardFiles(
                listOf(
                    getDefaultProguardFile("proguard-android-optimize.txt"),
                    "proguard-rules.pro"
                )
            )
            signingConfig = signingConfigs["release"]

            configure<CrashlyticsExtension> {
                mappingFileUploadEnabled = true
            }
        }
    }

    val backendDimension = "BACKEND"
    setFlavorDimensions(listOf(backendDimension))
    productFlavors {
        val devFlavor = "dev"
        val prodFlavor = "prod"

        create(devFlavor) {
            dimension = backendDimension
        }

        create(prodFlavor) {
            dimension = backendDimension
        }
    }

    compileOptions {
        isCoreLibraryDesugaringEnabled = true
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = jvmTargetVersion
    }

    buildFeatures {
        compose = true
    }

    packaging {
        // всю папку с метаданными лучше не вырезать,
        // так как они нужны для некоторых функций Android Studio, например, LayoutInspector
        // при возникновении проблем с билдом, добавляем нужные файлы в listOf
        resources.excludes += listOf()
    }
}

dependencies {
    coreLibraryDesugaring(libs.android.desugar)

    // Modules
    implementation(project(":core"))
    implementation(project(":features"))

    // UI
    implementation(libs.activity.compose)
    implementation(libs.splashscreen)

    implementation(libs.androidx.appcompat)

    implementation(platform(libs.firebase.bom))
    implementation(libs.firebase.firestore)
    implementation(libs.firebase.messaging)
    implementation(libs.firebase.crashlytics)

    implementation(libs.seismic)

    // Architecture
    implementation(libs.bundles.decompose)

    // DI
    implementation(libs.koin)

    // Debugging
    implementation(libs.logger.kermit)
}
