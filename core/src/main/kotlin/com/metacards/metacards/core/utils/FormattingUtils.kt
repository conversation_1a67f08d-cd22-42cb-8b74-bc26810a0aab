package com.metacards.metacards.core.utils

import android.content.Context
import kotlinx.datetime.DatePeriod
import kotlinx.datetime.LocalDate
import kotlinx.datetime.minus
import com.metacards.metacards.core.R
import com.metacards.metacards.core.localization.ui.toStringByLocal
import dev.icerock.moko.resources.desc.strResDesc
import java.text.SimpleDateFormat
import java.util.Locale

private const val DAY_MONTH_YEAR_PATTERN = "d MMMM yyyy"
private const val DAY_MONTH_PATTERN = "d MMMM"

fun LocalDate.prettyFormat(context: Context): String {
    val today = getTodayLocalDate()
    val yesterday = today - DatePeriod(days = 1)

    return when {
        this == today -> R.string.common_today.strResDesc().toStringByLocal(context)
        this == yesterday -> R.string.common_yesterday.strResDesc().toStringByLocal(context)
        this.year == today.year -> formatWithoutYear()
        else -> formatWithYear()
    }
}

fun LocalDate.formatWithYear(): String {
    return SimpleDateFormat(DAY_MONTH_YEAR_PATTERN, Locale.getDefault()).format(toDate())
}

fun LocalDate.formatWithoutYear(): String {
    return SimpleDateFormat(DAY_MONTH_PATTERN, Locale.getDefault()).format(toDate())
}