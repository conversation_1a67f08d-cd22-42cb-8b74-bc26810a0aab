package com.metacards.metacards.core.settings

import android.content.SharedPreferences
import androidx.core.content.edit
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext

class AndroidSettings(
    private val sharedPreferences: SharedPreferences,
    private val dispatcher: CoroutineDispatcher
) : Settings {

    override suspend fun getString(key: String): String? = withContext(dispatcher) {
        sharedPreferences.getString(key, null)
    }

    override suspend fun getLong(key: String): Long? = withContext(dispatcher) {
        val value = sharedPreferences.getLong(key, 0)
        if (sharedPreferences.contains(key)) value else null
    }

    override suspend fun getBoolean(key: String): Boolean? = withContext(dispatcher) {
        val value = sharedPreferences.getBoolean(key, false)
        if (sharedPreferences.contains(key)) value else null
    }

    override suspend fun putString(key: String, value: String) = with<PERSON>ontext(dispatcher) {
        sharedPreferences.edit {
            putString(key, value)
        }
    }

    override suspend fun putLong(key: String, value: Long) = with<PERSON>ontext(dispatcher) {
        sharedPreferences.edit {
            putLong(key, value)
        }
    }

    override suspend fun putBoolean(key: String, value: Boolean) = withContext(dispatcher) {
        sharedPreferences.edit {
            putBoolean(key, value)
        }
    }

    override suspend fun remove(key: String) = withContext(dispatcher) {
        sharedPreferences.edit {
            remove(key)
        }
    }

    override fun registerListener(listener: SharedPreferences.OnSharedPreferenceChangeListener) {
        sharedPreferences.registerOnSharedPreferenceChangeListener(listener)
    }

    override fun unregisterListener(listener: SharedPreferences.OnSharedPreferenceChangeListener) {
        sharedPreferences.unregisterOnSharedPreferenceChangeListener(listener)
    }
}