package com.metacards.metacards.core.image

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.drawable.Drawable
import android.net.Uri
import android.text.Layout
import android.text.StaticLayout
import android.text.TextPaint
import android.text.TextUtils
import androidx.compose.ui.graphics.toArgb
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.toBitmap
import com.metacards.metacards.core.R
import com.metacards.metacards.core.localization.ui.toStringByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import dev.icerock.moko.resources.desc.strResDesc

class CardImageGenerator(
    private val context: Context,
) : AsyncImageGenerator<CardImageGenerator.Arguments>(context) {

    data class Arguments(
        val cardUrl: String,
        val isDailyCard: Boolean,
        val text: String? = null
    ) : ImageGenerator.Arguments

    override suspend fun generateImage(
        arguments: Arguments
    ): Uri {
        val cardTitleRes = if (arguments.isDailyCard) {
            R.string.sharing_manager_card_of_the_day
        } else {
            R.string.sharing_manager_lu_card
        }
        val cardDrawable = getImage(arguments.cardUrl)
        val finalBitmap = getCardWithSharingOverlay(
            cardDrawable = cardDrawable,
            title = cardTitleRes.strResDesc().toStringByLocal(context),
            text = arguments.text
        )

        return saveBitmapToExternalFiles(context, finalBitmap)
    }

    private fun getCardWithSharingOverlay(
        cardDrawable: Drawable,
        settings: CardOverlaySettings = CardOverlaySettings(),
        title: String? = null,
        text: String? = null
    ): Bitmap {
        /**
         * Создаем фон, размеры которого соответствуют размерам экрана в пикселях
         */
        val resultImageBitmap = Bitmap.createBitmap(
            CardOverlaySettings.SCREEN_WIDTH_PX,
            CardOverlaySettings.SCREEN_HEIGHT_PX,
            Bitmap.Config.ARGB_8888
        )
        val resultImageCanvas = Canvas(resultImageBitmap)
        requireNotNull(
            ContextCompat.getDrawable(context, R.drawable.bg_share_card)
        ).run {
            setBounds(0, 0, resultImageBitmap.width, resultImageBitmap.height)
            draw(resultImageCanvas)
        }

        /**
         * Обрезаем и подгоняем размер карточки
         */
        val roundedCardBitmap = createRoundedAndScaledBitmap(
            cardDrawable = cardDrawable,
            settings = settings,
        )
        val roundedCardCanvas = Canvas(roundedCardBitmap)

        /**
         * Накладываем оверлей на изображение
         */
        requireNotNull(
            ContextCompat.getDrawable(context, R.drawable.overlay_share_card)
        ).run {
            setBounds(0, 0, roundedCardBitmap.width, roundedCardBitmap.height)
            draw(roundedCardCanvas)
        }

        /**
         * Накладываем изображение на фон
         */
        resultImageCanvas.drawBitmap(
            roundedCardBitmap,
            settings.calculateCardDrawableHorizontalPadding(),
            settings.calculateCardDrawableTopPadding(),
            null
        )

        /**
         * Накладываем заголовок
         */
        if (title != null) {
            val textPaint = Paint().apply {
                color = CustomTheme.colors.text.primary.toArgb()
                textSize = settings.calculateTitleSize()
            }
            val textWidth = textPaint.measureText(title)
            val textX = (resultImageCanvas.width - textWidth) / 2
            val textY = settings.calculateTitleTopPadding()
            resultImageCanvas.drawText(title, textX, textY, textPaint)
        }

        /**
         * Накладываем текст
         */
        if (text != null) {
            val textPaint = TextPaint().apply {
                color = CustomTheme.colors.text.primary.toArgb()
                textSize = settings.calculateTextSize()
            }

            val staticLayoutWidth =
                resultImageCanvas.width - (settings.calculateTextHorizontalPadding()).toInt()
            val staticLayout =
                StaticLayout.Builder.obtain(text, 0, text.length, textPaint, staticLayoutWidth)
                    .setAlignment(Layout.Alignment.ALIGN_CENTER)
                    .setMaxLines(5)
                    .setEllipsize(TextUtils.TruncateAt.END)
                    .build()
            val textBitMap = Bitmap.createBitmap(
                staticLayout.width,
                staticLayout.height,
                Bitmap.Config.ARGB_8888
            )
            val textCanvas = Canvas(textBitMap)
            staticLayout.draw(textCanvas)

            val textX = ((resultImageCanvas.width - staticLayoutWidth) / 2).toFloat()
            val textY = settings.calculateTextTopPadding(roundedCardCanvas.height)
            resultImageCanvas.drawBitmap(
                textBitMap,
                textX,
                textY,
                null
            )
        }

        return resultImageBitmap
    }

    private fun createRoundedAndScaledBitmap(
        cardDrawable: Drawable,
        settings: CardOverlaySettings,
    ): Bitmap {
        val originalBitmap = cardDrawable.toBitmap().copy(Bitmap.Config.ARGB_8888, true)
        val output = Bitmap.createBitmap(
            originalBitmap.width,
            originalBitmap.height,
            Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(output)
        val paint = Paint()
        val rect = Rect(0, 0, originalBitmap.width, originalBitmap.height)
        val rectF = RectF(rect)
        paint.isAntiAlias = true
        canvas.drawARGB(0, 0, 0, 0)
        canvas.drawRoundRect(
            rectF,
            CardOverlaySettings.CORNER_RADIUS,
            CardOverlaySettings.CORNER_RADIUS,
            paint
        )
        paint.setXfermode(PorterDuffXfermode(PorterDuff.Mode.SRC_IN))
        canvas.drawBitmap(originalBitmap, rect, rect, paint)
        return Bitmap.createScaledBitmap(
            output,
            settings.calculateImageRequiredWidth(),
            settings.calculateImageRequiredHeight(),
            true
        )
    }
}

/**
 * Для генерации изображения используем параметры экрана Pixel 8 pro,
 */
private class CardOverlaySettings {
    companion object {
        const val SCREEN_WIDTH_PX: Int = 1344
        const val SCREEN_HEIGHT_PX: Int = 2769
        const val DENSITY: Float = 3f
        const val CARD_DRAWABLE_TOP_PADDING: Int = 100
        const val CARD_DRAWABLE_HORIZONTAL_PADDING: Int = 16
        const val TITLE_TOP_PADDING: Int = 56
        const val TEXT_TOP_PADDING: Int = 20
        const val TEXT_HORIZONTAL_PADDING = 24
        const val TITLE_TEXT_SIZE: Int = 17
        const val TEXT_TEXT_SIZE: Int = 15
        const val CORNER_RADIUS: Float = 30f
        const val CARD_ASPECT_RATIO: Float = 0.666f
    }

    fun calculateTextTopPadding(imageHeight: Int): Float {
        val imageWithPaddingHeight = calculateCardDrawableTopPadding() + imageHeight
        val textPadding = (TEXT_TOP_PADDING + TEXT_TEXT_SIZE) * DENSITY
        return imageWithPaddingHeight + textPadding
    }

    fun calculateCardDrawableTopPadding(): Float = (CARD_DRAWABLE_TOP_PADDING * DENSITY)
    fun calculateCardDrawableHorizontalPadding(): Float =
        (CARD_DRAWABLE_HORIZONTAL_PADDING * DENSITY)

    fun calculateTitleTopPadding(): Float = ((TITLE_TOP_PADDING + TITLE_TEXT_SIZE) * DENSITY)
    fun calculateTextHorizontalPadding(): Float = TEXT_HORIZONTAL_PADDING * 2 * DENSITY

    fun calculateImageRequiredWidth() =
        (SCREEN_WIDTH_PX - (CARD_DRAWABLE_HORIZONTAL_PADDING * 2 * DENSITY)).toInt()

    fun calculateImageRequiredHeight() = (calculateImageRequiredWidth() / CARD_ASPECT_RATIO).toInt()

    fun calculateTitleSize() = TITLE_TEXT_SIZE * DENSITY
    fun calculateTextSize() = (TEXT_TEXT_SIZE * DENSITY)
}
