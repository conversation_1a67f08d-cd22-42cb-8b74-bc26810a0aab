package com.metacards.metacards.core.widget.button

import androidx.annotation.DrawableRes
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.ButtonElevation
import androidx.compose.material.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import com.metacards.metacards.core.R
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.theme.material.AppTheme

@Composable
fun LogoInvertButton(
    @DrawableRes logoResource: Int,
    modifier: Modifier = Modifier,
    state: ButtonState = ButtonState.Enabled,
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    elevation: ButtonElevation = MetaButtonDefaults.logoButton.buttonElevation,
    border: BorderStroke? = null,
    shape: Shape = MetaButtonDefaults.logoButton.buttonShape,
    colors: ButtonColorsWithPressed = MetaButtonDefaults.logoButton.buttonInvertColorsWithPressed,
    onClick: () -> Unit = {},
    contentPadding: PaddingValues = MetaButtonDefaults.logoButton.buttonPaddingValues,
) {
    LogoButton(
        onClick = onClick,
        modifier = modifier,
        state = state,
        interactionSource = interactionSource,
        elevation = elevation,
        border = border,
        shape = shape,
        colors = colors,
        contentPadding = contentPadding,
        content = {
            Icon(
                modifier = Modifier.alpha(it),
                painter = painterResource(id = logoResource),
                contentDescription = null
            )
        }
    )
}

@Composable
fun LogoInvertButton(
    modifier: Modifier = Modifier,
    state: ButtonState = ButtonState.Enabled,
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    elevation: ButtonElevation = MetaButtonDefaults.logoButton.buttonElevation,
    border: BorderStroke? = null,
    shape: Shape = MetaButtonDefaults.logoButton.buttonShape,
    colors: ButtonColorsWithPressed = MetaButtonDefaults.logoButton.buttonInvertColorsWithPressed,
    onClick: () -> Unit = {},
    contentPadding: PaddingValues = MetaButtonDefaults.logoButton.buttonPaddingValues,
    content: @Composable (Float) -> Unit
) {
    LogoButton(
        onClick = onClick,
        modifier = modifier,
        state = state,
        interactionSource = interactionSource,
        elevation = elevation,
        border = border,
        shape = shape,
        colors = colors,
        contentPadding = contentPadding,
        content = content
    )
}

@Preview
@Composable
fun LogoInvertButtonPreview() {
    AppTheme {
        LogoInvertButton(
            modifier = Modifier.fillMaxWidth(),
            content = {
                Icon(
                    painter = painterResource(id = R.drawable.ic_64_mail),
                    contentDescription = null
                )
            },
            state = ButtonState.Enabled,
            onClick = {}
        )
    }
}

@Preview
@Composable
fun LogoInvertButtonLoadingPreview() {
    AppTheme {
        LogoInvertButton(
            modifier = Modifier.fillMaxWidth(),
            content = {
                Icon(
                    painter = painterResource(id = R.drawable.ic_64_mail),
                    contentDescription = null
                )
            },
            state = ButtonState.Loading,
            onClick = {}
        )
    }
}

@Preview
@Composable
fun LogoInvertButtonDisabledPreview() {
    AppTheme {
        LogoInvertButton(
            modifier = Modifier.fillMaxWidth(),
            content = {
                Icon(
                    painter = painterResource(id = R.drawable.ic_64_mail),
                    contentDescription = null
                )
            },
            state = ButtonState.Disabled,
            onClick = {}
        )
    }
}