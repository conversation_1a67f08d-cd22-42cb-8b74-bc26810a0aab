package com.metacards.metacards.core.dialog

import android.os.Parcelable
import com.arkivanov.decompose.router.slot.ChildSlot
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow

/**
 * Class to configure and control dialog's behaviours
 */
interface DialogControl<C : Parcelable, T : Any> {

    val dialogOverlay: StateFlow<ChildSlot<*, T>>
    val dismissEvent: Flow<Unit>
    val canDismissed: Boolean

    fun show(config: C)
    fun dismiss()
}
