package com.metacards.metacards.core.widget

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.R
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.ErrorMessage
import com.metacards.metacards.core.widget.button.MetaAccentButton
import dev.icerock.moko.resources.compose.localized
import dev.icerock.moko.resources.desc.desc
import dev.icerock.moko.resources.desc.strResDesc

@Composable
fun ErrorPlaceholder(
    errorMessage: ErrorMessage,
    onRetryClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp, Alignment.CenterVertically),
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
            .padding(horizontal = 16.dp)
            .fillMaxSize()
    ) {
        Text(
            text = errorMessage.error.localized(),
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth(),
            color = CustomTheme.colors.text.primary,
            style = CustomTheme.typography.heading.secondary,
        )

        if (errorMessage.description != null) {
            Text(
                text = errorMessage.description.localized(),
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth(),
                color = CustomTheme.colors.text.secondary,
                style = CustomTheme.typography.body.primary,
            )
        }

        MetaAccentButton(
            text = R.string.common_update.strResDesc().localizedByLocal(),
            onClick = onRetryClick,
            modifier = Modifier.fillMaxWidth(),
        )
    }
}

@Preview
@Composable
fun ErrorPlaceholderPreview() {
    AppTheme {
        ErrorPlaceholder(
            errorMessage = ErrorMessage("Error message".desc()),
            onRetryClick = {}
        )
    }
}