package com.metacards.metacards.core.utils

import androidx.compose.ui.text.capitalize
import com.google.firebase.Timestamp
import com.kizitonwose.calendar.core.CalendarDay
import com.kizitonwose.calendar.core.atStartOfMonth
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.desc
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.atStartOfDayIn
import kotlinx.datetime.atTime
import kotlinx.datetime.toInstant
import kotlinx.datetime.toJavaInstant
import kotlinx.datetime.toJavaLocalDate
import kotlinx.datetime.toJavaLocalDateTime
import kotlinx.datetime.toKotlinInstant
import kotlinx.datetime.toKotlinLocalDate
import kotlinx.datetime.toKotlinLocalDateTime
import kotlinx.datetime.toLocalDateTime
import com.metacards.metacards.core.R
import java.time.LocalDateTime
import java.time.Month
import java.time.YearMonth
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.format.TextStyle
import java.time.temporal.WeekFields
import java.util.Date
import java.util.Locale
import androidx.compose.ui.text.intl.Locale as ComposeLocale

const val TOTAL_DAYS_IN_WEEK = 7
val APPLICATION_START_DATE = LocalDate(year = 2023, month = Month.JANUARY, dayOfMonth = 1)

private const val DAY_END_HOURS = 23
private const val DAY_END_MINUTES = 59
private const val DAY_END_SECONDS = 59
private const val DEFAULT_DISPLAY_PATTERN = "dd MMMM HH:mm"
private val DATE_TIME_FORMATTER_DAY_MONTH_YEAR_TIME: DateTimeFormatter =
    DateTimeFormatter.ofPattern("dd.MM.yyyy, HH:mm")

fun Timestamp.toKotlinInstant(): Instant {
    return toDate().toInstant().toKotlinInstant()
}

fun Instant.toTimestamp(): Timestamp {
    return Timestamp(Date.from(toJavaInstant()))
}

fun Instant.toLocalDate(): LocalDate {
    return toLocalDateTime(TimeZone.currentSystemDefault()).date
}

fun Instant.formatWithPattern(pattern: String): String {
    val formatter = DateTimeFormatter.ofPattern(pattern).withZone(ZoneId.systemDefault())
    return formatter?.format(toJavaInstant()) ?: toLocalDate().toString()
}

fun getTodayLocalDate(): LocalDate {
    return Clock.System.now().toLocalDate()
}

fun LocalDate.toDate(): Date {
    return Date.from(this.atStartOfDayIn(TimeZone.currentSystemDefault()).toJavaInstant())
}

fun CalendarDay.endToInstant(): Instant {
    return this.date
        .atTime(DAY_END_HOURS, DAY_END_MINUTES, DAY_END_SECONDS)
        .toKotlinLocalDateTime()
        .toInstant(TimeZone.currentSystemDefault())
}

fun YearMonth.startToInstant(): Instant {
    return this
        .atStartOfMonth()
        .atStartOfDay()
        .toKotlinLocalDateTime()
        .toInstant(TimeZone.currentSystemDefault())
}

fun YearMonth.endToInstant(): Instant {
    return this
        .atEndOfMonth()
        .atTime(DAY_END_HOURS, DAY_END_MINUTES, DAY_END_SECONDS)
        .toKotlinLocalDateTime()
        .toInstant(TimeZone.currentSystemDefault())
}

fun LocalDate.startToInstant(): Instant {
    return this.atStartOfDayIn(TimeZone.currentSystemDefault())
}

fun LocalDate.endToInstant(): Instant {
    return this
        .atTime(DAY_END_HOURS, DAY_END_MINUTES, DAY_END_SECONDS)
        .toInstant(TimeZone.currentSystemDefault())
}

fun YearMonth.getDisplayName(locale: Locale): StringDesc {
    val composeLocale = ComposeLocale(locale.toLanguageTag())

    return if (composeLocale.language != "ru") {
        month.getDisplayName(TextStyle.FULL, locale).capitalize(composeLocale).desc()
    } else {
        when (monthValue) { // TODO: Реализация выше дает родительный падеж (январЯ) => придумать получше
            1 -> R.string.calendar_january.strResDesc()
            2 -> R.string.calendar_february.strResDesc()
            3 -> R.string.calendar_march.strResDesc()
            4 -> R.string.calendar_april.strResDesc()
            5 -> R.string.calendar_may.strResDesc()
            6 -> R.string.calendar_june.strResDesc()
            7 -> R.string.calendar_july.strResDesc()
            8 -> R.string.calendar_august.strResDesc()
            9 -> R.string.calendar_september.strResDesc()
            10 -> R.string.calendar_october.strResDesc()
            11 -> R.string.calendar_november.strResDesc()
            12 -> R.string.calendar_december.strResDesc()
            else -> "".desc()
        }
    }
}

fun LocalDate.firstDayOfWeek(): LocalDate {
    val dayOfWeekField = WeekFields.of(Locale.getDefault()).dayOfWeek()
    return toJavaLocalDate().with(dayOfWeekField, 1).toKotlinLocalDate()
}

fun LocalDate.firstDayOfMonth(): LocalDate {
    return LocalDate(year, month, 1)
}

fun YearMonth.firstDayOfMonth(): LocalDate {
    return LocalDate(year, month, 1)
}

fun LocalDate.toYearMonth(): YearMonth {
    return YearMonth.of(year, month)
}

fun LocalDateTime.toInstant(): Instant {
    return this.toKotlinLocalDateTime().toInstant(TimeZone.currentSystemDefault())
}

fun Instant.getDisplayName(pattern: String = DEFAULT_DISPLAY_PATTERN): String {
    val dateFormatter = DateTimeFormatter.ofPattern(pattern)

    return this.toLocalDateTime(TimeZone.currentSystemDefault()).toJavaLocalDateTime()
        .format(dateFormatter)
}

fun zonedDateTimeMoscow(): String {
    return LocalDateTime
        .now(ZoneId.of("Europe/Moscow"))
        .format(DATE_TIME_FORMATTER_DAY_MONTH_YEAR_TIME)
}
