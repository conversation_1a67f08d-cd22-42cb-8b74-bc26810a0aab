package com.metacards.metacards.core.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.metacards.metacards.core.theme.custom.CustomTheme

@Composable
fun FullscreenCircularProgress(
    modifier: Modifier = Modifier,
    overlay: Boolean = false,
    overlayOpacity: Float = 0.5f
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .then(
                if (overlay) {
                    Modifier.background(CustomTheme.colors.background.primary.copy(alpha = overlayOpacity))
                } else {
                    Modifier
                }
            ),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator(
            color = CustomTheme.colors.icons.primary
        )
    }
}
