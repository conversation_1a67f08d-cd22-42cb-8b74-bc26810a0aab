package com.metacards.metacards.core.auth_suggestion_dialog

import com.metacards.metacards.core.R
import com.metacards.metacards.core.dialog.default_component.model.DialogData
import com.metacards.metacards.core.utils.Resource
import dev.icerock.moko.resources.desc.StringDesc

fun DialogData.Companion.createAuthSuggestion(cancelAction: () -> Unit, acceptAction: () -> Unit) = DialogData(
    title = StringDesc.Resource(R.string.add_record_auth_dialog_title),
    message = StringDesc.Resource(R.string.add_record_auth_dialog_message),
    buttons = listOf(
        DialogData.Button(
            title = StringDesc.Resource(R.string.add_record_auth_dialog_cancel_button),
            action = cancelAction
        ),
        DialogData.Button(
            title = StringDesc.Resource(R.string.add_record_auth_dialog_auth_button),
            action = acceptAction
        )
    )
)