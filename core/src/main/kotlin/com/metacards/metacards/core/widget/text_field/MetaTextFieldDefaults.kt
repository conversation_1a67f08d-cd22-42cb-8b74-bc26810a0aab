package com.metacards.metacards.core.widget.text_field

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.interaction.InteractionSource
import androidx.compose.foundation.interaction.collectIsFocusedAsState
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.TextFieldColors
import androidx.compose.material.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme

object MetaTextFieldDefaults {

    val Shape: Shape = RoundedCornerShape(16.dp)
    val FocusedBorderWidth = 1.dp
    val UnfocusedBorderWidth = 1.dp
    val InnerPaddings = PaddingValues(16.dp)
    private const val AnimationDuration = 150

    @Composable
    fun textFieldColors(
        textColor: Color = CustomTheme.colors.text.primary,
        disabledTextColor: Color = CustomTheme.colors.text.primary,
        errorTextColor: Color = CustomTheme.colors.text.primary,
        cursorColor: Color = textColor,
        errorCursorColor: Color = errorTextColor,
        focusedIndicatorColor: Color = CustomTheme.colors.stroke.primary,
        unfocusedIndicatorColor: Color = CustomTheme.colors.system.unspecified,
        errorIndicatorColor: Color = CustomTheme.colors.stroke.error,
        disabledIndicatorColor: Color = unfocusedIndicatorColor,
        leadingIconColor: Color = textColor,
        disabledLeadingIconColor: Color = disabledTextColor,
        errorLeadingIconColor: Color = errorTextColor,
        trailingIconColor: Color = textColor,
        disabledTrailingIconColor: Color = disabledTextColor,
        errorTrailingIconColor: Color = errorTextColor,
        backgroundColor: Color = CustomTheme.colors.background.primary,
        focusedLabelColor: Color = CustomTheme.colors.text.disabled,
        unfocusedLabelColor: Color = CustomTheme.colors.text.disabled,
        disabledLabelColor: Color = CustomTheme.colors.text.disabled,
        errorLabelColor: Color = CustomTheme.colors.text.disabled,
        placeholderColor: Color = CustomTheme.colors.text.disabled,
        disabledPlaceholderColor: Color = CustomTheme.colors.text.disabled,
    ) = TextFieldDefaults.textFieldColors(
        textColor = textColor,
        disabledTextColor = disabledTextColor,
        cursorColor = cursorColor,
        errorCursorColor = errorCursorColor,
        focusedIndicatorColor = focusedIndicatorColor,
        unfocusedIndicatorColor = unfocusedIndicatorColor,
        errorIndicatorColor = errorIndicatorColor,
        disabledIndicatorColor = disabledIndicatorColor,
        leadingIconColor = leadingIconColor,
        disabledLeadingIconColor = disabledLeadingIconColor,
        errorLeadingIconColor = errorLeadingIconColor,
        trailingIconColor = trailingIconColor,
        disabledTrailingIconColor = disabledTrailingIconColor,
        errorTrailingIconColor = errorTrailingIconColor,
        backgroundColor = backgroundColor,
        focusedLabelColor = focusedLabelColor,
        unfocusedLabelColor = unfocusedLabelColor,
        disabledLabelColor = disabledLabelColor,
        errorLabelColor = errorLabelColor,
        placeholderColor = placeholderColor,
        disabledPlaceholderColor = disabledPlaceholderColor
    )

    @Composable
    fun animateBorderStrokeAsState(
        enabled: Boolean,
        isError: Boolean,
        interactionSource: InteractionSource,
        colors: TextFieldColors,
        focusedBorderThickness: Dp,
        unfocusedBorderThickness: Dp
    ): State<BorderStroke> {
        val focused by interactionSource.collectIsFocusedAsState()
        val indicatorColor = colors.indicatorColor(enabled, isError, interactionSource)
        val targetThickness = if (focused) focusedBorderThickness else unfocusedBorderThickness
        val animatedThickness = if (enabled) {
            animateDpAsState(targetThickness, tween(durationMillis = AnimationDuration))
        } else {
            rememberUpdatedState(unfocusedBorderThickness)
        }
        return rememberUpdatedState(
            BorderStroke(animatedThickness.value, SolidColor(indicatorColor.value))
        )
    }
}