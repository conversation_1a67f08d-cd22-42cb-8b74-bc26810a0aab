package com.metacards.metacards.core.dialog.popup

import android.os.Parcelable
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import com.metacards.metacards.core.dialog.DialogControl

@Composable
fun <C : Parcelable, T : Any> MetaPopup(
    dialogControl: DialogControl<C, T>,
    content: @Composable (T) -> Unit
) {
    val childSlot by dialogControl.dialogOverlay.collectAsState()

    val component = childSlot.child?.instance

    if (component != null) {
        Popup(
            alignment = Alignment.BottomCenter,
            properties = PopupProperties(focusable = true),
            onDismissRequest = dialogControl::dismiss
        ) {
            content(component)
        }
    }
}