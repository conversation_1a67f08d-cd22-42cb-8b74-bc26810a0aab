package com.metacards.metacards.core.notification

import android.annotation.SuppressLint
import co.touchlab.kermit.Logger
import com.google.firebase.messaging.FirebaseMessagingService
import com.metacards.metacards.core.koin
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import com.google.firebase.messaging.RemoteMessage as FirebaseRemoteMessage

@SuppressLint("MissingFirebaseInstanceTokenRefresh")
class FirebaseMessagingServiceImpl : FirebaseMessagingService(), KoinComponent {

    private val pushNotifier: PushNotifier by inject()
    private val logger = Logger.withTag("FirebaseMessagingServiceImpl")

    override fun onMessageReceived(remoteMessage: FirebaseRemoteMessage) {
        logger.d("onMessageReceived ${remoteMessage.toLoggingString()}")
        val message = remoteMessage.toDomain()
        pushNotifier.showNotification(message)
    }

    override fun getKoin() = application.koin
}

private fun FirebaseRemoteMessage.toLoggingString(): String {
    return "title: ${notification?.title}, body: ${notification?.body}, data: $data"
}

private fun FirebaseRemoteMessage.toDomain(): RemoteMessage {
    return RemoteMessage(
        title = notification?.title ?: "",
        body = notification?.body ?: "",
        imageUrl = notification?.imageUrl?.toString(),
        pushData = data.ifEmpty { null },
        channelId = notification?.channelId
    )
}