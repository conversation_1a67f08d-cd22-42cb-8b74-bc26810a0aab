package com.metacards.metacards.core.localization.ui

import android.content.Context
import android.os.Parcelable
import co.touchlab.kermit.Logger
import com.metacards.metacards.core.localization.domain.AppLanguage
import com.metacards.metacards.core.utils.e
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.parcelize.Parcelize

@Parcelize
class LocalizableString(
    private val localizationMap: Map<String, String?>,
    val isNonLocalizable: Boolean = false
) : StringDesc, Parcelable {
    companion object {
        fun createNonLocalizable(value: String): LocalizableString {
            return LocalizableString(
                AppLanguage.entries.associate {
                    it.name.lowercase() to value
                },
                isNonLocalizable = true
            )
        }

        const val NOT_FOUND = "Not found"

        val empty get() = LocalizableString(
            AppLanguage.entries.associate {
                it.name.lowercase() to ""
            },
            isNonLocalizable = true
        )
    }

    override fun toString(): String {
        val appLanguage = AppLanguage.getDefaultAppLanguage()
        return getString(appLanguage) ?: localizationMap.values.firstOrNull() ?: super.toString()
    }

    override fun toString(context: Context): String {
        return toString()
    }

    fun toString(appLanguage: AppLanguage, fallback: String = NOT_FOUND): String {
        return getString(appLanguage) ?: run {
            Logger.withTag("LocalizableString")
                .e(IllegalStateException("Translate for language $appLanguage not found in $localizationMap"))
            return@run fallback
        }
    }

    fun toMap(): Map<String, String?> = localizationMap

    private fun getString(appLanguage: AppLanguage): String? = if (isNonLocalizable) {
        getNotLocalizedString()
    } else {
        getLocalizedString(appLanguage)
    }?.replaceUnicodeLineSeparatorWithNewLine()

    private fun getLocalizedString(appLanguage: AppLanguage): String? =
        localizationMap[appLanguage.toString().lowercase()]?.takeIf { it.isNotBlank() }

    private fun getNotLocalizedString(): String? =
        localizationMap.values.firstOrNull { it?.isNotBlank() == true }

    private fun String.replaceUnicodeLineSeparatorWithNewLine(): String = replace('\u2028', '\n')
}