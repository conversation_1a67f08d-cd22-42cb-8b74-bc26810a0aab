package com.metacards.metacards.core.sharing

import android.content.Context
import android.content.Intent
import androidx.core.content.ContextCompat.startActivity
import com.metacards.metacards.core.R
import com.metacards.metacards.core.image.CardImageGenerator
import com.metacards.metacards.core.image.CourseResultImageGenerator
import com.metacards.metacards.core.localization.ui.toStringByLocal
import dev.icerock.moko.resources.desc.strResDesc
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

// from xml/provider_paths
private const val EXTERNAL_FILES_PATH = "external_files"
private const val IMAGE_MEME = "image/*"

class SharingManager(
    private val context: Context,
    private val cardImageGenerator: CardImageGenerator,
    private val courseResultImageGenerator: CourseResultImageGenerator
) {

    suspend fun shareCourseTestResult(
        imageUrl: String?,
        title: String,
        text: String
    ) = withContext(Dispatchers.IO) {
        val imageUri = courseResultImageGenerator.generateImage(
            CourseResultImageGenerator.Arguments(imageUrl, title, text)
        )
        val intent = Intent(Intent.ACTION_SEND)
            .apply {
                setDataAndType(imageUri, IMAGE_MEME)
                putExtra(Intent.EXTRA_STREAM, imageUri)
                // putExtra(Intent.EXTRA_TEXT, "$extraText $googlePlayUrl") скрыто, пока нет переводов
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }
        val chooserIntent = Intent.createChooser(intent, "Share card!").apply {
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }
        startActivity(context, chooserIntent, null)
    }

    suspend fun shareCard(
        cardUrl: String,
        isDailyCard: Boolean,
        text: String? = null
    ) = withContext(Dispatchers.Default) {
        val cardTitleRes = if (isDailyCard) {
            R.string.sharing_manager_card_of_the_day
        } else {
            R.string.sharing_manager_lu_card
        }

        val finalUri = cardImageGenerator.generateImage(
            CardImageGenerator.Arguments(cardUrl, isDailyCard, text)
        )

        val extraText = R.string.sharing_manager_share_card_extra_text.strResDesc().toStringByLocal(context)
        val googlePlayUrl = R.string.sharing_manager_app_url.strResDesc().toStringByLocal(context)

        val intent = Intent(Intent.ACTION_SEND)
            .apply {
                setDataAndType(finalUri, IMAGE_MEME)
                putExtra(Intent.EXTRA_STREAM, finalUri)
               // putExtra(Intent.EXTRA_TEXT, "$extraText $googlePlayUrl") скрыто, пока нет переводов
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }
        val chooserIntent = Intent.createChooser(intent, "Share card!").apply {
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }
        startActivity(context, chooserIntent, null)
    }
}