package com.metacards.metacards.core.widget

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.Stable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

@Immutable
object FadeDefaults {
    @Stable
    enum class Direction {
        Vertical, Horizontal
    }
}

private val defaultGradient = listOf(
    Color(0x0009090F),
    Color(0x9907080C),
    Color(0xFF06060A)
)

@Composable
fun BoxWithFade(
    modifier: Modifier = Modifier,
    contentAlignment: Alignment = Alignment.TopStart,
    propagateMinConstraints: Boolean = false,
    listOfColors: List<Color> = defaultGradient,
    behindContent: @Composable BoxScope.() -> Unit = {},
    content: @Composable BoxScope.() -> Unit
) {
    Box(
        modifier = modifier,
        contentAlignment = contentAlignment,
        propagateMinConstraints = propagateMinConstraints,
    ) {
        behindContent()

        if (listOfColors.size > 1) {
            Fade(
                modifier = modifier.matchParentSize(),
                listOfColors = listOfColors
            )
        }

        content()
    }
}

@Composable
fun Fade(
    modifier: Modifier = Modifier,
    listOfColors: List<Color> = defaultGradient,
    direction: FadeDefaults.Direction = FadeDefaults.Direction.Vertical
) {
    val brush = if (direction == FadeDefaults.Direction.Vertical) {
        Brush.verticalGradient(listOfColors)
    } else {
        Brush.horizontalGradient(listOfColors)
    }

    Canvas(
        modifier = modifier,
        onDraw = {
            drawRect(brush)
        }
    )
}

@Preview
@Composable
fun BoxWithFadePreview() {
    BoxWithFade(modifier = Modifier.size(200.dp)) {}
}