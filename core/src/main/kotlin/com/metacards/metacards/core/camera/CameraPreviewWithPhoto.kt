package com.metacards.metacards.core.camera

import android.view.ViewGroup
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageCapture
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat

@Composable
fun CameraPreviewWithPhoto(
    modifier: Modifier = Modifier,
    takePhoto: (ImageCapture) -> Unit,
    content: @Composable (onClick: () -> Unit) -> Unit
) {
    if (LocalInspectionMode.current) {
        Box(modifier.background(Color.Black))
        return
    }

    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val executor = ContextCompat.getMainExecutor(LocalContext.current)
    val imageCapture = ImageCapture.Builder().build()

    var cameraProvider: ProcessCameraProvider? = remember { null }
    val cameraProviderFuture = ProcessCameraProvider.getInstance(context)

    cameraProviderFuture.addListener(
        { cameraProvider = cameraProviderFuture.get() },
        executor
    )

    DisposableEffect(key1 = Unit) {
        onDispose {
            cameraProvider!!.unbindAll()
        }
    }

    Box(modifier) {
        AndroidView(
            modifier = modifier.matchParentSize(),
            factory = { context ->
                val previewView = PreviewView(context).apply {
                    layoutParams = ViewGroup.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )
                }

                val preview = androidx.camera.core.Preview.Builder().build().also {
                    it.setSurfaceProvider(previewView.surfaceProvider)
                }

                val cameraSelector = CameraSelector.Builder()
                    .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                    .build()

                val cameraProviderFutureLocal = ProcessCameraProvider.getInstance(context)

                cameraProviderFutureLocal.addListener({
                    val cameraProviderLocal = cameraProviderFutureLocal.get()
                    cameraProviderLocal.unbindAll()
                    cameraProviderLocal.bindToLifecycle(
                        lifecycleOwner,
                        cameraSelector,
                        preview,
                        imageCapture
                    )
                }, executor)
                previewView
            }
        )

        content {
            takePhoto(imageCapture)
        }
    }
}
