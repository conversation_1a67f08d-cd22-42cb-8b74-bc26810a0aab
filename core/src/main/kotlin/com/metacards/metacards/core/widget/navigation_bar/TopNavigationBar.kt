package com.metacards.metacards.core.widget.navigation_bar

import androidx.annotation.DrawableRes
import androidx.compose.animation.AnimatedContent
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.R
import com.metacards.metacards.core.button.Debounce
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.utils.dispatchOnBackPressed
import com.metacards.metacards.core.utils.mirrorForRtl
import com.metacards.metacards.core.widget.button.MetaButtonDefaults
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.desc
import kotlinx.coroutines.launch

@Composable
fun TopNavigationBar(
    modifier: Modifier = Modifier,
    title: StringDesc = "".desc(),
    contentPadding: PaddingValues = PaddingValues(16.dp),
    textPadding: PaddingValues = PaddingValues(horizontal = 24.dp),
    textStyle: TextStyle = CustomTheme.typography.heading.medium,
    textColor: Color = CustomTheme.colors.text.primary,
    leadingIcon: @Composable (RowScope.() -> Unit)? = null,
    trailingContent: @Composable RowScope.() -> Unit = {}
) {
    Row(
        modifier = modifier.padding(contentPadding),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        leadingIcon?.invoke(this)

        Text(
            modifier = Modifier
                .weight(1f)
                .padding(
                    if (leadingIcon != null) {
                        textPadding
                    } else {
                        PaddingValues()
                    }
                ),
            text = title.localizedByLocal(),
            overflow = TextOverflow.Ellipsis,
            maxLines = 1,
            style = textStyle,
            color = textColor
        )

        trailingContent()
    }
}

@Composable
fun IconNavigationItem(
    @DrawableRes iconRes: Int,
    modifier: Modifier = Modifier,
    iconTint: Color = CustomTheme.colors.icons.primary,
    paddingValues: PaddingValues = PaddingValues(0.dp),
    onClick: () -> Unit = {}
) {
    val debounce = remember { Debounce() }
    val coroutineScope = rememberCoroutineScope()
    Icon(
        modifier = modifier
            .padding(paddingValues)
            .clickable(
                boundedRipple = false,
                onClick = {
                    coroutineScope.launch {
                        debounce.click(
                            id = "IconNavigationItem",
                            block = onClick
                        )
                    }
                }
            ),
        painter = painterResource(id = iconRes),
        contentDescription = null,
        tint = iconTint
    )
}

@Composable
fun LoadingIconNavigationItem(
    @DrawableRes iconRes: Int,
    isLoading: Boolean = false,
    iconTint: Color = CustomTheme.colors.icons.primary,
    paddingValues: PaddingValues = PaddingValues(0.dp),
    onClick: () -> Unit = {}
) {
    AnimatedContent(
        targetState = isLoading,
        label = ""
    ) { isLoadingInProgress ->
        if (isLoadingInProgress) {
            CircularProgressIndicator(
                modifier = Modifier
                    .padding(paddingValues)
                    .size(MetaButtonDefaults.bigButton.minContentSize),
                color = CustomTheme.colors.icons.primary
            )
        } else {
            Icon(
                modifier = Modifier
                    .padding(paddingValues)
                    .clickable(
                        boundedRipple = false,
                        onClick = onClick
                    ),
                painter = painterResource(id = iconRes),
                contentDescription = null,
                tint = iconTint
            )
        }
    }
}

@Composable
fun BackNavigationItem(
    @DrawableRes iconRes: Int = R.drawable.ic_24_arrowa_back,
    modifier: Modifier = Modifier,
    iconTint: Color = CustomTheme.colors.icons.primary,
    paddingValues: PaddingValues = PaddingValues(0.dp),
    onClick: () -> Unit = {}
) {
    val context = LocalContext.current

    IconNavigationItem(
        modifier = modifier.mirrorForRtl(),
        iconRes = iconRes,
        iconTint = iconTint,
        paddingValues = paddingValues
    ) {
        onClick()
        dispatchOnBackPressed(context)
    }
}

@Preview
@Composable
fun TopNavigationBarPreview() {
    AppTheme {
        TopNavigationBar(
            modifier = Modifier.fillMaxWidth(),
            title = "Title".desc(),
            leadingIcon = { BackNavigationItem() },
            trailingContent = { IconNavigationItem(R.drawable.ic_24_share) }
        )
    }
}