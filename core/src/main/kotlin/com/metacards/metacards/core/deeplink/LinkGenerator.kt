package com.metacards.metacards.core.deeplink

import com.metacards.metacards.core.localization.ui.LocalizableString

interface LinkGenerator {

    fun generateDeckLink(
        linkInfo: DeckLinkInfo,
        onGenerated: (url: String?, error: String?) -> Unit
    )

    fun generateCourseLink(
        linkInfo: CourseLinkInfo,
        onGenerated: (url: String?, error: String?) -> Unit
    )
}

data class DeckLinkInfo(
    val deckId: String,
    val title: LocalizableString,
    val coverUrl: String
)

data class CourseLinkInfo(
    val themeId: String,
    val courseId: String,
    val title: LocalizableString,
    val coverUrl: String
)