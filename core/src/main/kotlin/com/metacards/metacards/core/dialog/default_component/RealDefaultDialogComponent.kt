package com.metacards.metacards.core.dialog.default_component

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.dialog.default_component.model.DialogData

class RealDefaultDialogComponent(
    componentContext: ComponentContext,
    override val dialogData: DialogData,
    private val onDismiss: () -> Unit
) : ComponentContext by componentContext, DefaultDialogComponent {

    override fun onDismiss() {
        onDismiss.invoke()
    }
}