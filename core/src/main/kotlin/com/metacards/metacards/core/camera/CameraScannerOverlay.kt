package com.metacards.metacards.core.camera

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.ClipOp
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.clipPath
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme

@Composable
fun CameraScannerOverlay(
    text: String,
    hasProgressBar: Boolean,
    modifier: Modifier = Modifier
) {
    Box(modifier) {
        Canvas(modifier) {
            val frameRect = Rect(
                center = center,
                radius = size.minDimension / 2 - 40.dp.toPx()
            )
            val cornerRadius = CornerRadius(30.dp.toPx())

            drawRoundRect(
                color = CustomTheme.colors.button.small,
                topLeft = frameRect.topLeft,
                size = frameRect.size,
                cornerRadius = cornerRadius
            )

            val clipRectHeight = frameRect.height - 2 * 40.dp.toPx()
            val clipRect = Path().apply {
                addRect(
                    Rect(
                        left = 0.0f,
                        top = center.y - clipRectHeight / 2,
                        right = size.width,
                        bottom = center.y + clipRectHeight / 2
                    )
                )
            }

            clipPath(clipRect, clipOp = ClipOp.Difference) {
                drawRoundRect(
                    color = Color.White.copy(alpha = 0.8f),
                    style = Stroke(3.dp.toPx()),
                    topLeft = frameRect.topLeft,
                    size = frameRect.size,
                    cornerRadius = cornerRadius
                )
            }
        }

        if (hasProgressBar) {
            CircularProgressIndicator(
                modifier = Modifier.align(Alignment.Center),
                color = CustomTheme.colors.icons.primary
            )
        } else {
            Text(
                modifier = Modifier
                    .align(Alignment.Center)
                    .padding(horizontal = 56.dp),
                text = text,
                textAlign = TextAlign.Center,
                color = CustomTheme.colors.text.caption,
                style = CustomTheme.typography.heading.medium
            )
        }
    }
}