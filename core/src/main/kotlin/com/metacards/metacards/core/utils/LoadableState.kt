package com.metacards.metacards.core.utils

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map

/**
 * Represents LCE-state.
 */
data class LoadableState<T : Any>(
    val loading: Boolean = false,
    val data: T? = null,
    val error: ErrorMessage? = null
)

fun <T : Any, R : Any> Flow<LoadableState<T>>.mapLoadableSuspend(transform: suspend (T?) -> R?) =
    map { it.mapDataSuspend(transform) }

suspend fun <T : Any, R : Any> LoadableState<T>.mapDataSuspend(transform: suspend (T?) -> R?) =
    LoadableState(loading, transform(data), error)

fun <T : Any, R : Any> Flow<LoadableState<T>>.mapLoadable(transform: (T?) -> R?) =
    map { it.mapData(transform) }

fun <T : Any, R : Any> LoadableState<T>.mapData(transform: (T?) -> R?) =
    LoadableState(loading, transform(data), error)

fun <T1 : Any, T2 : Any, R : Any> combineLoadableState(
    state1: LoadableState<T1>,
    state2: LoadableState<T2>,
    transformError: (ErrorMessage?, ErrorMessage?) -> ErrorMessage? = { error1, error2 ->
        error1 ?: error2
    },
    transformData: (T1?, T2?) -> R?,
): LoadableState<R> {
    val loading = state1.loading || state2.loading
    val error = transformError(state1.error, state2.error)
    val data = transformData(state1.data, state2.data)
    return LoadableState(loading, data, error)
}

fun <T1 : Any, T2 : Any, T3 : Any, R : Any> combineLoadableState(
    state1: LoadableState<T1>,
    state2: LoadableState<T2>,
    state3: LoadableState<T3>,
    transformError: (ErrorMessage?, ErrorMessage?, ErrorMessage?) -> ErrorMessage? = { error1, error2, error3 ->
        error1 ?: error2 ?: error3
    },
    transformData: (T1?, T2?, T3?) -> R?,
): LoadableState<R> {
    val loading = state1.loading || state2.loading
    val error = transformError(state1.error, state2.error, state3.error)
    val data = transformData(state1.data, state2.data, state3.data)
    return LoadableState(loading, data, error)
}

fun <T1 : Any, T2 : Any, R : Any> combineLoadableStateFlow(
    flow1: Flow<LoadableState<T1>>,
    flow2: Flow<LoadableState<T2>>,
    transformError: (ErrorMessage?, ErrorMessage?) -> ErrorMessage? = { error1, error2 ->
        error1 ?: error2
    },
    transformData: (T1?, T2?) -> R?,
): Flow<LoadableState<R>> = combine(flow1, flow2) { state1, state2 ->
    combineLoadableState(state1, state2, transformError, transformData)
}

fun <T1 : Any, T2 : Any, T3 : Any, R : Any> combineLoadableStateFlow(
    flow1: Flow<LoadableState<T1>>,
    flow2: Flow<LoadableState<T2>>,
    flow3: Flow<LoadableState<T3>>,
    transformError: (ErrorMessage?, ErrorMessage?, ErrorMessage?) -> ErrorMessage? = { error1, error2, error3 ->
        error1 ?: error2 ?: error3
    },
    transformData: (T1?, T2?, T3?) -> R?,
): Flow<LoadableState<R>> = combine(flow1, flow2, flow3) { state1, state2, state3 ->
    combineLoadableState(state1, state2, state3, transformError, transformData)
}