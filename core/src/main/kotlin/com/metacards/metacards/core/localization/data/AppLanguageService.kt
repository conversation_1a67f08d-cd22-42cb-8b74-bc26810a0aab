package com.metacards.metacards.core.localization.data

import com.metacards.metacards.core.localization.domain.AppLanguage
import kotlinx.coroutines.flow.StateFlow

interface AppLanguageService {
    val currentAppLanguage: StateFlow<AppLanguage>

    suspend fun onAppStart()
    suspend fun setLanguage(appLanguage: AppLanguage, appStart: <PERSON>olean)
    fun getLanguage(): AppLanguage
    fun addListener(listener: AppLanguageUpdateListener)
    fun removeListener(listener: AppLanguageUpdateListener)
}