package com.metacards.metacards.core.button

enum class ButtonState {
    Enabled,
    Disabled,
    Loading, ;

    fun isEnabled(): <PERSON><PERSON>an = this == Enabled
    fun isNotDisabled(): Boolean = this != Disabled
    fun isLoading(): Boolean = this == Loading

    companion object {
        fun create(disabled: Boolean = false, loading: <PERSON>olean = false): ButtonState {
            return when {
                loading -> Loading
                disabled -> Disabled
                else -> Enabled
            }
        }
    }
}