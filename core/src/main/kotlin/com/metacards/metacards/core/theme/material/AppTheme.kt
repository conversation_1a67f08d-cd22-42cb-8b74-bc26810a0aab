package com.metacards.metacards.core.theme.material

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material.MaterialTheme
import androidx.compose.material.darkColors
import androidx.compose.material.lightColors
import androidx.compose.runtime.Composable
import com.metacards.metacards.core.theme.custom.CustomTheme

private val LightColorPalette = lightColors(
    primary = CustomTheme.colors.button.accent,
    primaryVariant = IndigoA700,
    secondary = CustomTheme.colors.button.secondary,
    background = CustomTheme.colors.background.primary,
    surface = CustomTheme.colors.background.modal,
    error = CustomTheme.colors.text.error
)

private val DarkColorPalette = darkColors(
    primary = CustomTheme.colors.button.accent,
    primaryVariant = IndigoA700,
    secondary = CustomTheme.colors.button.secondary,
    background = CustomTheme.colors.background.primary,
    surface = CustomTheme.colors.background.modal,
    error = CustomTheme.colors.text.error
)

@Composable
fun AppTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit
) {
    val colors = if (darkTheme) {
        DarkColorPalette
    } else {
        LightColorPalette
    }

    MaterialTheme(
        colors = colors,
        typography = TypographyMaterial,
        shapes = Shapes,
        content = content
    )
}