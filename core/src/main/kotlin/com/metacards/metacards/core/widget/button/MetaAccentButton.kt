package com.metacards.metacards.core.widget.button

import androidx.annotation.DrawableRes
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.R
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.theme.material.AppTheme

@Composable
fun MetaAccentButton(
    text: String,
    modifier: Modifier = Modifier,
    state: ButtonState = ButtonState.Enabled,
    @DrawableRes leadingIconResource: Int? = null,
    @DrawableRes trailingIconResource: Int? = null,
    onClick: () -> Unit
) {
    MetaButton(
        text = text,
        modifier = modifier,
        state = state,
        leadingWidget = {
            leadingIconResource?.let { resource ->
                MetaButtonIconWidget(
                    modifier = Modifier.padding(horizontal = 8.dp),
                    icon = resource
                )
            }
        },
        trailingWidget = {
            trailingIconResource?.let { resource ->
                MetaButtonIconWidget(
                    modifier = Modifier.padding(horizontal = 8.dp),
                    icon = resource
                )
            }
        },
        onClick = onClick
    )
}

@Preview(group = "AccentButton")
@Composable
fun MetaAccentButtonPreview() {
    AppTheme {
        MetaAccentButton(
            modifier = Modifier.fillMaxWidth(),
            state = ButtonState.Enabled,
            text = "Button",
            onClick = {}
        )
    }
}

@Preview(group = "AccentButton")
@Composable
fun MetaAccentButtonLoadingPreview() {
    AppTheme {
        MetaAccentButton(
            modifier = Modifier.fillMaxWidth(),
            state = ButtonState.Loading,
            text = "Button",
            onClick = {}
        )
    }
}

@Preview(group = "AccentButton")
@Composable
fun MetaAccentButtonDisabledPreview() {
    AppTheme {
        MetaAccentButton(
            modifier = Modifier.fillMaxWidth(),
            state = ButtonState.Disabled,
            text = "Button",
            onClick = {}
        )
    }
}

@Preview(group = "AccentButton")
@Composable
fun MetaAccentButtonWithIconsPreview() {
    AppTheme {
        MetaAccentButton(
            modifier = Modifier.fillMaxWidth(),
            state = ButtonState.Enabled,
            text = "Button",
            leadingIconResource = R.drawable.ic_24_archive,
            trailingIconResource = R.drawable.ic_24_card,
            onClick = {}
        )
    }
}