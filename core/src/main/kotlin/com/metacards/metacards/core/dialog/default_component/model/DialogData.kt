package com.metacards.metacards.core.dialog.default_component.model

import android.os.Parcelable
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.desc
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue

@Parcelize
data class DialogData(
    val title: @RawValue StringDesc,
    val message: @RawValue StringDesc,
    val buttons: List<Button>
) : Parcelable {
    @Parcelize
    data class Button(
        val title: @RawValue StringDesc,
        @IgnoredOnParcel val action: () -> Unit = {}
    ) : Parcelable {
        companion object {
            val mock = Button("mock".desc()) {
                println("Mock dialog action")
            }
        }
    }

    companion object {
        val mock = DialogData(
            title = "Title".desc(),
            message = "Dialog message".desc(),
            buttons = listOf(Button.mock, Button.mock)
        )
    }
}