package com.metacards.metacards.core.preferences

import android.content.SharedPreferences
import androidx.core.content.edit
import com.metacards.metacards.core.adv.domain.AdvUserConsentState
import com.metacards.metacards.core.alert.domain.ShakerPopupState
import com.metacards.metacards.core.localization.data.LocalizationStorage
import com.metacards.metacards.core.localization.domain.AppLanguage
import com.metacards.metacards.core.preferences.models.TutorialState
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class PreferencesServiceImpl(
    private val appPrefs: SharedPreferences,
    private val userPrefs: SharedPreferences,
) :
    PreferencesService,
    LocalizationStorage {

    companion object {
        private const val TUTORIAL_KEY = "TUTORIAL_KEY"
        private const val WELCOME_SCREENS_LAUNCH_KEY = "WELCOME_SCREENS_LAUNCH_KEY"
        private const val LOGIN_SCREEN_LAUNCH_KEY = "LOGIN_SCREEN_LAUNCH_KEY"
        private const val FIRST_LAUNCH_SUBSCRIPTION_KEY = "FIRST_LAUNCH_SUBSCRIPTION_KEY"
        private const val USER_SUBSCRIPTION_TO_NOTIFICATIONS_KEY =
            "USER_SUBSCRIPTION_TO_NOTIFICATIONS_KEY"
        private const val CAMERA_PERMISSION_DIALOG_KEY = "CAMERA_PERMISSION_DIALOG_KEY"
        private const val SHAKER_POPUP_STATE_KEY = "SHAKER_POPUP_STATE_KEY"
        private const val APP_LANGUAGE_KEY = "APP_LANGUAGE_STATE_KEY"
        private const val KEY_USER_CONSENT_STATE = "KEY_USER_CONSENT_STATE"
        private const val KEY_SUBSCRIBED_PUSH_TIMEZONE = "KEY_SUBSCRIBED_PUSH_TIMEZONE"
    }

    override suspend fun getTutorialState(): TutorialState = with(Dispatchers.IO) {
        return TutorialState.fromString(
            appPrefs.getString(TUTORIAL_KEY, TutorialState.START.toString())
        )
    }

    override suspend fun setTutorialState(state: TutorialState) =
        with(Dispatchers.IO) {
            appPrefs.edit { putString(TUTORIAL_KEY, state.toString()) }
        }

    override suspend fun getWelcomeScreensLaunchFlag(): Boolean =
        appPrefs.getBoolean(WELCOME_SCREENS_LAUNCH_KEY, true)

    override suspend fun disableWelcomeScreensLaunchFlag() = appPrefs.edit {
        putBoolean(WELCOME_SCREENS_LAUNCH_KEY, false)
    }

    override suspend fun getFirstAppLaunchSubscriptionFlag(): Boolean = with(Dispatchers.IO) {
        return userPrefs.getBoolean(FIRST_LAUNCH_SUBSCRIPTION_KEY, false)
    }

    override suspend fun enableFirstAppLaunchSubscriptionFlag() = with(Dispatchers.IO) {
        userPrefs.edit { putBoolean(FIRST_LAUNCH_SUBSCRIPTION_KEY, true) }
    }

    override suspend fun setUserSubscriptionToNotifications(isSubscribed: Boolean) =
        with(Dispatchers.IO) {
            userPrefs.edit { putBoolean(USER_SUBSCRIPTION_TO_NOTIFICATIONS_KEY, isSubscribed) }
        }

    override suspend fun getUserSubscriptionToNotifications(): Boolean {
        return userPrefs.getBoolean(USER_SUBSCRIPTION_TO_NOTIFICATIONS_KEY, true)
    }

    override suspend fun getShakerPopupState(): ShakerPopupState = with(Dispatchers.IO) {
        return ShakerPopupState.fromString(
            appPrefs.getString(SHAKER_POPUP_STATE_KEY, ShakerPopupState.NONE.toString())
        )
    }

    override suspend fun setShakerPopupState(state: ShakerPopupState) = with(Dispatchers.IO) {
        appPrefs.edit { putString(SHAKER_POPUP_STATE_KEY, state.toString()) }
    }

    override suspend fun getCameraPermissionDialogViewedFlag(): Boolean = with(Dispatchers.IO) {
        return appPrefs.getBoolean(CAMERA_PERMISSION_DIALOG_KEY, false)
    }

    override suspend fun setCameraPermissionDialogViewedFlag() {
        appPrefs.edit { putBoolean(CAMERA_PERMISSION_DIALOG_KEY, true) }
    }

    override fun getAppLanguage(): AppLanguage? = with(Dispatchers.IO) {
        return AppLanguage.fromString(
            userPrefs.getString(APP_LANGUAGE_KEY, null)
        )
    }

    override suspend fun setAppLanguage(language: AppLanguage) = with(Dispatchers.IO) {
        userPrefs.edit { putString(APP_LANGUAGE_KEY, language.toString()) }
    }

    override suspend fun getAuthScreenLaunchFlag(): Boolean =
        appPrefs.getBoolean(LOGIN_SCREEN_LAUNCH_KEY, true)

    override suspend fun disableAuthScreenLaunchFlag() = appPrefs.edit {
        putBoolean(LOGIN_SCREEN_LAUNCH_KEY, false)
    }

    override suspend fun getAdvUserConsentState(): AdvUserConsentState = withContext(Dispatchers.IO) {
        appPrefs
            .getString(
                KEY_USER_CONSENT_STATE,
                AdvUserConsentState.Unknown.toData()
            )
            ?.toDomain()
            ?: AdvUserConsentState.Unknown
    }

    override suspend fun setAdvUserConsentState(advUserConsentState: AdvUserConsentState) = withContext(Dispatchers.IO) {
        appPrefs.edit {
            putString(KEY_USER_CONSENT_STATE, advUserConsentState.toData())
        }
    }

    override suspend fun clear() {
        userPrefs.edit { clear() }
    }

    override suspend fun getSubscribedPushTimezone(): Int? = withContext(Dispatchers.IO) {
        userPrefs.getString(KEY_SUBSCRIBED_PUSH_TIMEZONE, null)?.toInt()
    }

    override suspend fun setSubscribedPushTimezone(timezone: Int?) = withContext(Dispatchers.IO) {
        userPrefs.edit { putString(KEY_SUBSCRIBED_PUSH_TIMEZONE, timezone?.toString()) }
    }
}

private fun AdvUserConsentState.toData(): String = when (this) {
    AdvUserConsentState.Unknown -> "unknown"
    AdvUserConsentState.Granted -> "granted"
    AdvUserConsentState.Denied -> "denied"
}

private fun String.toDomain(): AdvUserConsentState = when (this) {
    "unknown" -> AdvUserConsentState.Unknown
    "granted" -> AdvUserConsentState.Granted
    "denied" -> AdvUserConsentState.Denied
    else -> AdvUserConsentState.Unknown
}
