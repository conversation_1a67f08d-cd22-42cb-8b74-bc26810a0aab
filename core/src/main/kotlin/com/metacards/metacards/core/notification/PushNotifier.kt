package com.metacards.metacards.core.notification

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent.FLAG_IMMUTABLE
import android.app.PendingIntent.FLAG_UPDATE_CURRENT
import android.app.PendingIntent.getActivity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.core.app.NotificationCompat
import com.metacards.metacards.core.R
import com.metacards.metacards.core.activity.ActivityProvider
import com.metacards.metacards.core.localization.domain.AppLanguage

class PushNotifier(
    private val context: Context,
    private val activityProvider: ActivityProvider
) {
    companion object {
        private const val DEFAULT_CHANNEL_ID = "Metaphorical cards channel ID"
        private const val DEFAULT_CHANNEL_NAME = "Default Channel"
        private const val DEFAULT_CHANNEL_DESCRIPTION = "Push messages"
        const val PUSH_DATA_KEY = "PUSH_DATA_KEY"
    }

    private val notificationManager by lazy {
        context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
    }

    private var notificationId: Int = 1

    fun showNotification(remoteMessage: RemoteMessage) = with(context) {
        val activity = activityProvider.activity ?: return@with
        val pendingIntent = getActivity(
            this,
            notificationId,
            Intent(this, activity.javaClass).apply {
                remoteMessage.pushData?.let {
                    putExtra(PUSH_DATA_KEY, it.toBundle())
                }
                flags = Intent.FLAG_ACTIVITY_SINGLE_TOP
            },
            FLAG_IMMUTABLE or FLAG_UPDATE_CURRENT
        )

        val notification = NotificationCompat.Builder(
            this,
            remoteMessage.channelId ?: getChannelId()
        )
            .setSmallIcon(R.drawable.ic_push_small_icon)
            .setContentTitle(remoteMessage.title)
            .setContentText(remoteMessage.body)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .build()

        notificationManager.notify(notificationId, notification)
    }

    private fun Map<String, String>.toBundle(): Bundle {
        return Bundle().apply {
            forEach { putString(it.key, it.value) }
        }
    }

    private fun getChannelId(): String {
        val channel =
            NotificationChannel(
                DEFAULT_CHANNEL_ID,
                DEFAULT_CHANNEL_NAME,
                NotificationManager.IMPORTANCE_DEFAULT
            )
                .apply {
                    lockscreenVisibility = Notification.VISIBILITY_PUBLIC
                    description = DEFAULT_CHANNEL_DESCRIPTION
                }
        notificationManager.createNotificationChannel(channel)
        return DEFAULT_CHANNEL_ID
    }
}

enum class NotificationTopics(private val topicName: String) {
    MARKETING("marketing"),
    SYSTEM("system"),
    DAILY("daily");

    private var timezone: Int? = null
    private var language: AppLanguage? = null
    private var userId: String? = null

    fun withUserId(userId: String): NotificationTopics {
        this.userId = userId
        return this
    }

    fun withLanguage(language: AppLanguage): NotificationTopics {
        // пока что в пушах только русский и английский языки
        if (language == AppLanguage.RUS) {
            this.language = AppLanguage.RUS
        } else {
            this.language = AppLanguage.ENG
        }
        return this
    }

    fun withTimezone(timezone: Int?): NotificationTopics {
        this.timezone = timezone
        return this
    }

    private fun clear() {
        timezone = null
        language = null
        userId = null
    }

    val value: String
        get() = listOfNotNull(
            topicName,
            userId,
            language?.toString()?.lowercase(),
            timezone
        )
            .joinToString(separator = "_")
            .also { clear() }
}