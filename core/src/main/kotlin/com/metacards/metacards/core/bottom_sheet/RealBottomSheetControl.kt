package com.metacards.metacards.core.bottom_sheet

import android.os.Parcelable
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.router.slot.ChildSlot
import com.arkivanov.decompose.router.slot.SlotNavigation
import com.arkivanov.decompose.router.slot.activate
import com.arkivanov.decompose.router.slot.childSlot
import com.arkivanov.decompose.router.slot.dismiss
import com.metacards.metacards.core.utils.toStateFlow
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlin.reflect.KClass

/**
 * Если в одном компоненте подразумевается использоваение более одного ботомшита/диалога
 * то каждому из них должен быть присвоен уникальный строковый ключ-идентификатор.
 * Иначе приложение упадет с ошибкой (Another supplier is already registered with the key)
 * Это особенность реализации childOverlay в библиотеку decompose
 */
private const val SHEET_CHILD_OVERLAY_KEY = "sheetChildOverlay"

inline fun <reified C : Parcelable, T : Any> ComponentContext.bottomSheetControl(
    noinline bottomSheetComponentFactory: (C, ComponentContext, BottomSheetControl<C, T>) -> T,
    key: String? = null,
    halfExpandingSupported: Boolean,
    hidingSupported: Boolean,
    handleBackButton: Boolean = false,
): BottomSheetControl<C, T> {
    return bottomSheetControl(
        bottomSheetComponentFactory,
        key,
        halfExpandingSupported,
        hidingSupported,
        handleBackButton,
        C::class
    )
}

fun <C : Parcelable, T : Any> ComponentContext.bottomSheetControl(
    bottomSheetComponentFactory: (C, ComponentContext, BottomSheetControl<C, T>) -> T,
    key: String? = null,
    halfExpandingSupported: Boolean,
    hidingSupported: Boolean,
    handleBackButton: Boolean = false,
    clazz: KClass<C>,
): BottomSheetControl<C, T> = RealBottomSheetControl(
    this,
    bottomSheetComponentFactory,
    key ?: SHEET_CHILD_OVERLAY_KEY,
    halfExpandingSupported,
    hidingSupported,
    handleBackButton,
    clazz,
)

private class RealBottomSheetControl<C : Parcelable, T : Any>(
    componentContext: ComponentContext,
    private val bottomSheetComponentFactory: (C, ComponentContext, BottomSheetControl<C, T>) -> T,
    key: String,
    override val halfExpandingSupported: Boolean,
    override val hidingSupported: Boolean,
    handleBackButton: Boolean,
    clazz: KClass<C>,
) : BottomSheetControl<C, T> {

    private val sheetNavigation = SlotNavigation<C>()

    override val sheetState = MutableStateFlow(BottomSheetControl.State.Hidden)

    override val dismissEvent = MutableSharedFlow<Unit>(
        extraBufferCapacity = 1,
        onBufferOverflow = BufferOverflow.DROP_OLDEST
    )

    /**
     * child overlay это один из типов навигации в decompose, у него может быть только один instance
     * Когда надо показать bottom sheet мы добавляем в него компонент боттом шита,
     * когда он закрывается его удаляем. Можно для каждого боттом шита использовать отдельный
     * компонент, можно сделать какой-то общий компонент и передавать его
     *
     * https://arkivanov.github.io/Decompose/navigation/slot/overview/
     * В либе Decompose переименовали child overlay в child slot
     */
    override val sheetOverlay: StateFlow<ChildSlot<*, T>> =
        componentContext.childSlot(
            source = sheetNavigation,
            handleBackButton = handleBackButton,
            key = key,
            configurationClass = clazz,
            childFactory = { configuration, context ->
                bottomSheetComponentFactory(configuration, context, this)
            }
        ).toStateFlow(componentContext.lifecycle)

    override fun onStateChangedFromUI(state: BottomSheetControl.State) {
        if (sheetOverlay.value.child?.instance == null) {
            sheetState.value = BottomSheetControl.State.Hidden
            return
        }

        sheetState.value = state
    }

    override fun onStateChangeAnimationEnd(targetState: BottomSheetControl.State) {
        if (targetState == BottomSheetControl.State.Hidden) {
            sheetNavigation.dismiss()
            dismissEvent.tryEmit(Unit)
        }
    }

    override fun show(config: C) {
        sheetNavigation.activate(config)
        sheetState.value = BottomSheetControl.State.Expanded
    }

    override fun dismiss() {
        sheetState.value = BottomSheetControl.State.Hidden
        sheetNavigation.dismiss()
        dismissEvent.tryEmit(Unit)
    }

    /**
     * в shouldUpdateState мы делаем проверку, может ли bottom sheet перейти в это состояние.
     * например проверка флага hiddenSupported
     */
    override fun shouldUpdateState(newState: BottomSheetControl.State) = when (newState) {
        BottomSheetControl.State.Expanded -> true
        BottomSheetControl.State.HalfExpanded -> halfExpandingSupported
        BottomSheetControl.State.Hidden -> hidingSupported
    }
}
