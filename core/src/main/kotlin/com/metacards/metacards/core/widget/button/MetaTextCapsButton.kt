package com.metacards.metacards.core.widget.button

import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme

@Composable
fun MetaTextCapsButton(
    text: String,
    modifier: Modifier = Modifier,
    state: ButtonState = ButtonState.Enabled,
    onClick: () -> Unit = {},
) {
    MetaTextButton(
        text = text.uppercase(),
        onClick = onClick,
        modifier = modifier,
        state = state,
        interactionSource = remember { MutableInteractionSource() },
        elevation = MetaButtonDefaults.textButton.buttonElevation,
        border = null,
        shape = MetaButtonDefaults.textButton.buttonShape,
        colors = MetaButtonDefaults.textButton.buttonCapsColorsWithPressed,
        contentPadding = MetaButtonDefaults.textButton.buttonPaddingValues,
        textStyle = CustomTheme.typography.caption.medium
    )
}

@Preview
@Composable
fun MetaTextCapsButtonPreview() {
    AppTheme {
        MetaTextCapsButton(text = "Text button")
    }
}

@Preview
@Composable
fun MetaTextCapsButtonDisabledPreview() {
    AppTheme {
        MetaTextCapsButton(text = "Text button", state = ButtonState.Disabled)
    }
}