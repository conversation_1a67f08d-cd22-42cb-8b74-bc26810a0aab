package com.metacards.metacards.core.error_handling

import com.metacards.metacards.core.R
import com.metacards.metacards.core.utils.ErrorMessage
import com.metacards.metacards.core.utils.Resource
import com.metacards.metacards.core.utils.ResourceFormatted
import com.metacards.metacards.core.utils.isDebugBuild
import dev.icerock.moko.resources.desc.Raw
import dev.icerock.moko.resources.desc.StringDesc

/**
 * Returns human readable messages for exceptions.
 */

val Exception.detailedErrorMessage: ErrorMessage
    get() = when (this) {
        is ServerException -> message?.let { ErrorMessage(StringDesc.Raw(it)) }
            ?: ErrorMessage(StringDesc.Resource(R.string.error_invalid_response))

        is DeserializationException -> ErrorMessage(StringDesc.Resource(R.string.error_invalid_response))

        is NoServerResponseException -> ErrorMessage(StringDesc.Resource(R.string.error_no_server_response))

        is NoInternetException -> ErrorMessage(StringDesc.Resource(R.string.error_no_internet_connection))

        is UnauthorizedException -> ErrorMessage(StringDesc.Resource(R.string.error_unauthorized))

        is ExternalAppNotFoundException -> ErrorMessage(StringDesc.Resource(R.string.error_matching_application_not_found))

        else -> {
            val description = this.message
            ErrorMessage(
                if (description != null && isDebugBuild) {
                    StringDesc.ResourceFormatted(
                        R.string.error_unexpected_with_message,
                        description
                    )
                } else {
                    StringDesc.Resource(R.string.error_unexpected)
                }
            )
        }
    }

val Exception.errorMessage: ErrorMessage
    get() = when (this) {
        is NoInternetException, is UnauthorizedException -> ErrorMessage(
            StringDesc.Resource(R.string.error_no_internet_connection)
        )

        else -> {
            val description = this.message
            ErrorMessage(
                if (description != null && isDebugBuild) {
                    StringDesc.ResourceFormatted(
                        R.string.error_unexpected_with_message,
                        description
                    )
                } else {
                    StringDesc.Resource(R.string.error_unexpected)
                }
            )
        }
    }

val Exception.fullScreenErrorMessage: ErrorMessage
    get() = ErrorMessage(
        error = StringDesc.Resource(R.string.error_unexpected),
        description = StringDesc.Resource(R.string.error_unexpected_description)
    )