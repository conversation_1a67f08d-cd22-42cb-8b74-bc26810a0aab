package com.metacards.metacards.core.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.R
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.clickable
import com.metacards.metacards.core.utils.mirrorForRtl
import com.metacards.metacards.core.widget.button.MetaAccentButton
import com.metacards.metacards.core.widget.button.MetaButtonIconWidget
import dev.icerock.moko.resources.desc.StringDesc

@Composable
fun SuccessUi(
    header: StringDesc,
    mainText: StringDesc,
    buttonText: StringDesc,
    onCompleteButtonClick: () -> Unit,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    BoxWithFade(modifier.fillMaxSize(), listOfColors = CustomTheme.colors.gradient.backgroundList) {
        MetaButtonIconWidget(
            icon = R.drawable.ic_24_arrowa_back,
            modifier = Modifier
                .mirrorForRtl()
                .padding(16.dp)
                .clickable { onBackClick() }
        )

        Image(
            painter = painterResource(id = R.drawable.bg_success),
            contentDescription = null,
            contentScale = ContentScale.Crop,
            modifier = Modifier
                .align(Alignment.Center)
                .fillMaxWidth()
        )

        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .padding(16.dp)
                .align(Alignment.Center)
        ) {
            Text(
                text = header.localizedByLocal(),
                color = CustomTheme.colors.text.primary,
                style = CustomTheme.typography.heading.medium,
                textAlign = TextAlign.Center
            )

            Text(
                text = mainText.localizedByLocal(),
                color = CustomTheme.colors.text.primary,
                style = CustomTheme.typography.body.primary,
                textAlign = TextAlign.Center
            )
        }

        MetaAccentButton(
            text = buttonText.localizedByLocal(),
            onClick = onCompleteButtonClick,
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 16.dp, end = 16.dp, bottom = 4.dp)
                .align(Alignment.BottomCenter)
        )
    }
}