package com.metacards.metacards.core.paged_loading.internal

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.StateFlow
import me.aartikov.sesame.loop.startIn
import com.metacards.metacards.core.paged_loading.DataMerger
import com.metacards.metacards.core.paged_loading.PagedLoader
import com.metacards.metacards.core.paged_loading.PagedLoading
import com.metacards.metacards.core.paged_loading.PagedLoading.Event
import com.metacards.metacards.core.paged_loading.PagedLoading.State

internal class PagedLoadingImpl<T : Any>(
    scope: CoroutineScope,
    override val loader: PagedLoader<T>,
    initialState: State<T>,
    dataMerger: DataMerger<T>
) : PagedLoading<T> {

    private val mutableEventFlow = MutableSharedFlow<Event<T>>(
        extraBufferCapacity = 100, onBufferOverflow = BufferOverflow.DROP_OLDEST
    )

    private val loop: PagedLoadingLoop<T> = PagedLoadingLoop(
        initialState = initialState,
        reducer = PagedLoadingReducer(dataMerger),
        effectHandlers = listOf(
            PagedLoadingEffectHandler(loader),
            EventEffectHandler { event -> mutableEventFlow.tryEmit(event) }
        )
    )

    override val stateFlow: StateFlow<State<T>>
        get() = loop.stateFlow

    override val eventFlow: Flow<Event<T>>
        get() = mutableEventFlow

    init {
        loop.startIn(scope)
    }

    override fun loadFirstPage(clear: Boolean) {
        loop.dispatch(Action.LoadFirstPage(clear))
    }

    override fun reloadPage() {
        loop.dispatch(Action.ReloadPage)
    }

    override fun loadNext() {
        loop.dispatch(Action.LoadNextPage)
    }

    override fun loadPrevious() {
        loop.dispatch(Action.LoadPreviousPage)
    }

    override fun cancel(clear: Boolean) {
        loop.dispatch(Action.Cancel(clear))
    }

    override fun mutateData(transform: (List<T>) -> List<T>) {
        loop.dispatch(Action.MutateData(transform))
    }
}