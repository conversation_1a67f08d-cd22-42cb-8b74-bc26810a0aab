package com.metacards.metacards.core.widget.button

import androidx.annotation.DrawableRes
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.size
import androidx.compose.material.Button
import androidx.compose.material.ButtonColors
import androidx.compose.material.ButtonElevation
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme

@Composable
fun MetaButton(
    text: String,
    modifier: Modifier = Modifier,
    state: ButtonState = ButtonState.Enabled,
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    elevation: ButtonElevation = MetaButtonDefaults.bigButton.buttonElevation,
    border: BorderStroke? = null,
    shape: Shape = MetaButtonDefaults.bigButton.buttonShape,
    colors: ButtonColors = MetaButtonDefaults.bigButton.buttonAccentColors,
    contentPadding: PaddingValues = MetaButtonDefaults.bigButton.buttonPaddingValues,
    textStyle: TextStyle = CustomTheme.typography.button.primary,
    leadingWidget: @Composable (() -> Unit)? = null,
    trailingWidget: @Composable (() -> Unit)? = null,
    onClick: () -> Unit = {},
) {
    Button(
        onClick = onClick,
        modifier = modifier.heightIn(min = MetaButtonDefaults.bigButton.minHeight),
        enabled = state.isNotDisabled() && !state.isLoading(),
        interactionSource = interactionSource,
        elevation = elevation,
        border = border,
        shape = shape,
        colors = colors,
        contentPadding = contentPadding
    ) {

        if (state.isLoading()) {
            CircularProgressIndicator(
                modifier = Modifier.size(MetaButtonDefaults.bigButton.minContentSize),
                color = CustomTheme.colors.icons.primary
            )
        } else {
            val textColor = colors.contentColor(enabled = state.isEnabled())

            leadingWidget?.invoke()

            Text(
                text = text,
                style = textStyle,
                color = textColor.value,
            )

            trailingWidget?.invoke()
        }
    }
}

@Composable
fun MetaButtonIconWidget(
    @DrawableRes icon: Int,
    modifier: Modifier = Modifier,
    tint: Color = CustomTheme.colors.icons.primary
) {
    Icon(
        modifier = modifier,
        painter = painterResource(id = icon),
        contentDescription = null,
        tint = tint
    )
}

@Preview
@Composable
fun MetaButtonPreview() {
    AppTheme {
        MetaButton(
            modifier = Modifier.fillMaxWidth(),
            text = "Button",
            state = ButtonState.Enabled,
            onClick = {}
        )
    }
}

@Preview
@Composable
fun MetaButtonLoadingPreview() {
    AppTheme {
        MetaButton(
            modifier = Modifier.fillMaxWidth(),
            text = "Button",
            state = ButtonState.Loading,
            onClick = {}
        )
    }
}

@Preview
@Composable
fun MetaButtonDisabledPreview() {
    AppTheme {
        MetaButton(
            modifier = Modifier.fillMaxWidth(),
            text = "Button",
            state = ButtonState.Disabled,
            onClick = {}
        )
    }
}