package com.metacards.metacards.core.widget.button

import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme

@Composable
fun MetaTransparentButton(
    text: String,
    modifier: Modifier = Modifier,
    state: ButtonState = ButtonState.Enabled,
    onClick: () -> Unit
) {
    MetaButton(
        text = text,
        modifier = modifier,
        state = state,
        onClick = onClick,
        interactionSource = remember { MutableInteractionSource() },
        elevation = MetaButtonDefaults.bigButton.buttonTransparentElevation,
        shape = MetaButtonDefaults.bigButton.buttonShape,
        colors = MetaButtonDefaults.bigButton.buttonTransparentColors,
        contentPadding = MetaButtonDefaults.bigButton.buttonPaddingValues,
        textStyle = CustomTheme.typography.button.primary
    )
}

@Preview(group = "TransparentButton")
@Composable
fun MetaTransparentButtonPreview() {
    AppTheme {
        MetaTransparentButton(
            modifier = Modifier.fillMaxWidth(),
            state = ButtonState.Enabled,
            text = "Button",
            onClick = {}
        )
    }
}

@Preview(group = "TransparentButton")
@Composable
fun MetaTransparentButtonLoadingPreview() {
    AppTheme {
        MetaTransparentButton(
            modifier = Modifier.fillMaxWidth(),
            state = ButtonState.Loading,
            text = "Button",
            onClick = {}
        )
    }
}

@Preview(group = "TransparentButton")
@Composable
fun MetaTransparentButtonDisabledPreview() {
    AppTheme {
        MetaTransparentButton(
            modifier = Modifier.fillMaxWidth(),
            state = ButtonState.Disabled,
            text = "Button",
            onClick = {}
        )
    }
}