package com.metacards.metacards.core.device

import com.metacards.metacards.core.settings.SettingsFactory
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.UUID

class DeviceInfoServiceImpl(
    settingsFactory: SettingsFactory,
) : DeviceInfoService {

    companion object {
        private const val DEVICE_ID_KEY = "deviceId"
    }

    private val settings = settingsFactory.createSettings("deviceId")
    private val mutex = Mutex()

    override suspend fun getDeviceId(): DeviceId {
        val deviceId = settings.getString(DEVICE_ID_KEY)
        return if (deviceId != null) {
            DeviceId(deviceId)
        } else {
            generateDeviceId()
        }
    }

    private suspend fun generateDeviceId(): DeviceId = mutex.withLock {
        val generatedDeviceId = UUID.randomUUID().toString()
        settings.putString(DEVICE_ID_KEY, generatedDeviceId)
        return DeviceId(generatedDeviceId)
    }
}