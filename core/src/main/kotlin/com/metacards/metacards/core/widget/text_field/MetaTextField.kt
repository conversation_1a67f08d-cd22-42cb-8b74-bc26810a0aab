package com.metacards.metacards.core.widget.text_field

import android.annotation.SuppressLint
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.relocation.BringIntoViewRequester
import androidx.compose.foundation.relocation.bringIntoViewRequester
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material.TextFieldColors
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.FocusState
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.platform.SoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.R
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.clickable
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.desc
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.launch
import ru.mobileup.kmm_form_validation.control.InputControl
import ru.mobileup.kmm_form_validation.toCompose

@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun MetaTextField(
    modifier: Modifier = Modifier,
    value: String,
    heightMin: Dp = Dp.Unspecified,
    heightMax: Dp = Dp.Unspecified,
    onClick: () -> Unit = {},
    onValueChanged: (String) -> Unit = {},
    onFocusChanged: (FocusState) -> Unit = {},
    placeholder: String? = null,
    colors: TextFieldColors = MetaTextFieldDefaults.textFieldColors(),
    shape: Shape = MetaTextFieldDefaults.Shape,
    leadingContent: @Composable (BoxScope.() -> Unit)? = null,
    trailingIcon: @Composable (() -> Unit)? = null,
    maxLines: Int = Int.MAX_VALUE,
    minLines: Int = 1,
    headerMessage: (@Composable () -> Unit)? = null,
    footerMessage: (@Composable () -> Unit)? = null,
    innerFooterContent: (@Composable () -> Unit)? = null,
    footerError: (@Composable (errorMessage: StringDesc) -> Unit)? = {
        DefaultTextFieldFooterError(it)
    },
    decorationOverlay: @Composable BoxScope.(PaddingValues) -> Unit = {},
    visualTransformation: VisualTransformation = VisualTransformation.None,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    error: StringDesc? = null,
    isEnabled: Boolean = true,
    hasFocus: Boolean = false,
    singleLine: Boolean = minLines <= 1,
    readOnly: Boolean = false,
    changeCursorPositionEvent: Flow<Int> = emptyFlow(),
    shouldBeCarriedDown: Boolean = false,
) {
    val density = LocalDensity.current
    val focusRequester = remember { FocusRequester() }
    val isInFocus = remember { mutableStateOf(false) }
    val currentValue by rememberUpdatedState(value)
    var currentSelection by remember { mutableStateOf(TextRange(currentValue.length)) }
    var currentComposition by remember { mutableStateOf<TextRange?>(null) }
    val interactionSource = remember { MutableInteractionSource() }
    val keyboard = LocalSoftwareKeyboardController.current
    var carriedLines by remember { mutableIntStateOf(0) }
    val minLinesWithCarry by remember { derivedStateOf { minLines + carriedLines } }
    var trailingIconWidth by remember { mutableStateOf(0.dp) }
    val currentTextFieldValue by remember {
        derivedStateOf {
            TextFieldValue(currentValue, currentSelection, currentComposition)
        }
    }

    LaunchedEffect(key1 = changeCursorPositionEvent) {
        changeCursorPositionEvent.collectLatest {
            currentSelection = TextRange(it)
        }
    }

    focusRequester.handleFocus(hasFocus, isInFocus, keyboard)

    BasicTextField(
        value = currentTextFieldValue,
        onValueChange = {
            onValueChanged(it.text)
            currentSelection = it.selection
            currentComposition = it.composition
        },
        modifier = modifier
            .heightIn(heightMin, heightMax)
            .then(if (isEnabled) Modifier.clickable { onClick() } else Modifier)
            .focusRequester(focusRequester)
            .onFocusChanged {
                isInFocus.value = it.isFocused
                onFocusChanged(it)
            },
        readOnly = readOnly,
        enabled = isEnabled,
        textStyle = CustomTheme.typography.body.primary.merge(
            TextStyle(color = colors.textColor(enabled = isEnabled).value)
        ),
        visualTransformation = visualTransformation,
        keyboardOptions = keyboardOptions,
        keyboardActions = keyboardActions,
        singleLine = singleLine,
        maxLines = maxLines,
        minLines = minLinesWithCarry,
        cursorBrush = SolidColor(colors.cursorColor(isError = error != null).value),
        interactionSource = interactionSource,
        onTextLayout = {
            val lastLine = it.getLineForOffset(currentTextFieldValue.selection.end)

            if (shouldBeCarriedDown) {
                if (lastLine == minLinesWithCarry - 1) {
                    carriedLines++
                } else if (lastLine == minLinesWithCarry - 3 && carriedLines > 0) {
                    carriedLines--
                }
            }
        },
        decorationBox = { textField ->
            Column(modifier = Modifier) {
                val borderStroke = MetaTextFieldDefaults.animateBorderStrokeAsState(
                    isEnabled,
                    error != null,
                    interactionSource,
                    colors,
                    MetaTextFieldDefaults.FocusedBorderWidth,
                    MetaTextFieldDefaults.UnfocusedBorderWidth
                )

                headerMessage?.invoke()

                Box(modifier = Modifier.heightIn(heightMin, heightMax)) {
                    Row(
                        modifier = Modifier
                            .heightIn(heightMin, heightMax)
                            .clip(shape)
                            .background(colors.backgroundColor(enabled = isEnabled).value)
                            .border(border = borderStroke.value, shape)
                            .padding(MetaTextFieldDefaults.InnerPaddings),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.Top
                    ) {
                        leadingContent?.let {
                            Box(
                                modifier = Modifier.padding(end = 16.dp),
                                contentAlignment = Alignment.CenterEnd
                            ) {
                                it()
                            }
                        }

                        Box(modifier = Modifier.weight(1f)) {
                            if (placeholder != null) {
                                /*
                                Если textField() вызвать внутри if с условием value.isEmpty()
                                вылетит [IllegalStateException], поэтому эти два if'a нельзя объединить
                                 */
                                if (value.isEmpty()) {
                                    Text(
                                        text = placeholder,
                                        color = colors.placeholderColor(enabled = isEnabled).value,
                                        style = CustomTheme.typography.body.primary
                                    )
                                }
                            }

                            Column {
                                textField()
                                innerFooterContent?.invoke()
                            }
                        }
                        Spacer(modifier = Modifier.width(trailingIconWidth))
                    }

                    Box(
                        modifier = Modifier
                            .align(Alignment.CenterEnd)
                            .padding(horizontal = 16.dp)
                            .onSizeChanged { with(density) { trailingIconWidth = it.width.toDp() } }
                    ) {
                        trailingIcon?.invoke()
                    }

                    decorationOverlay(MetaTextFieldDefaults.InnerPaddings)
                }

                if (error == null) footerMessage?.invoke() else footerError?.invoke(error)
            }
        }
    )
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun MetaTextField(
    inputControl: InputControl,
    modifier: Modifier = Modifier,
    heightMin: Dp = Dp.Unspecified,
    heightMax: Dp = Dp.Unspecified,
    placeholder: String? = null,
    onClick: () -> Unit = {},
    colors: TextFieldColors = MetaTextFieldDefaults.textFieldColors(),
    shape: Shape = MetaTextFieldDefaults.Shape,
    leadingIcon: @Composable (BoxScope.() -> Unit)? = null,
    trailingIcon: @Composable (() -> Unit)? = null,
    headerMessage: (@Composable () -> Unit)? = null,
    footerMessage: (@Composable () -> Unit)? = null,
    innerFooterContent: (@Composable () -> Unit)? = null,
    footerError: (@Composable (errorMessage: StringDesc) -> Unit)? = {
        DefaultTextFieldFooterError(
            it
        )
    },
    decorationOverlay: @Composable BoxScope.(PaddingValues) -> Unit = {},
    maxLines: Int = Int.MAX_VALUE,
    minLines: Int = 1,
    visualTransformation: VisualTransformation = inputControl.visualTransformation.toCompose(),
    readOnly: Boolean = false,
    changeCursorPositionEvent: Flow<Int> = emptyFlow()
) {
    val bringIntoViewRequester = remember { BringIntoViewRequester() }
    val coroutineScope = rememberCoroutineScope()
    val hasFocus by inputControl.hasFocus.collectAsState()
    val error by inputControl.error.collectAsState()
    val text by inputControl.text.collectAsState()
    val isEnabled by inputControl.enabled.collectAsState()

    bringIntoViewRequester.handleScrollToEvent(inputControl)

    MetaTextField(
        modifier = modifier.bringIntoViewRequester(bringIntoViewRequester),
        value = text,
        shape = shape,
        heightMin = heightMin,
        heightMax = heightMax,
        decorationOverlay = decorationOverlay,
        onClick = onClick,
        onValueChanged = inputControl::onTextChanged,
        onFocusChanged = { focusState ->
            if (focusState.isFocused) {
                coroutineScope.launch { bringIntoViewRequester.bringIntoView() }
            }
            inputControl.onFocusChanged(focusState.isFocused)
        },
        headerMessage = headerMessage,
        footerMessage = footerMessage,
        innerFooterContent = innerFooterContent,
        footerError = footerError,
        placeholder = placeholder,
        colors = colors,
        leadingContent = leadingIcon,
        trailingIcon = trailingIcon,
        maxLines = maxLines,
        minLines = minLines,
        visualTransformation = visualTransformation,
        keyboardOptions = inputControl.keyboardOptions.toCompose(),
        keyboardActions = KeyboardActions.Default,
        error = error,
        isEnabled = isEnabled,
        hasFocus = hasFocus,
        singleLine = inputControl.singleLine,
        readOnly = readOnly,
        changeCursorPositionEvent = changeCursorPositionEvent
    )
}

@Composable
fun DefaultTextFieldFooterError(errorMessage: StringDesc) {
    Text(
        modifier = Modifier.padding(start = 16.dp, top = 8.dp),
        text = errorMessage.localizedByLocal(),
        color = CustomTheme.colors.text.error,
        style = CustomTheme.typography.caption.small,
    )
}

@Composable
fun DefaultTextFieldFooterMessage(message: StringDesc) {
    Text(
        modifier = Modifier.padding(start = 16.dp, top = 8.dp),
        text = message.localizedByLocal(),
        color = CustomTheme.colors.text.secondary,
        style = CustomTheme.typography.caption.small,
    )
}

@OptIn(ExperimentalComposeUiApi::class)
@SuppressLint("ComposableNaming")
@Composable
private fun FocusRequester.handleFocus(
    hasFocus: Boolean,
    isInFocus: MutableState<Boolean>,
    keyboard: SoftwareKeyboardController?
) {
    val focusManager = LocalFocusManager.current

    LaunchedEffect(hasFocus) {
        if (hasFocus) {
            requestFocus()
            keyboard?.show()
        } else if (isInFocus.value) {
            focusManager.clearFocus()
        }
    }
}

@SuppressLint("ComposableNaming")
@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun BringIntoViewRequester.handleScrollToEvent(inputControl: InputControl) {
    LaunchedEffect(key1 = inputControl) {
        inputControl.scrollToItEvent.collectLatest {
            bringIntoView()
        }
    }
}

@Preview
@Composable
fun MetaTextFieldPreview(
    inputControl: InputControl = InputControl(CoroutineScope(Dispatchers.Main)),
    footerMessage: @Composable (() -> Unit)? = {}
) {
    AppTheme {
        MetaTextField(
            modifier = Modifier.fillMaxWidth(),
            inputControl = inputControl,
            placeholder = "Placeholder",
            leadingIcon = {
                Text(
                    modifier = Modifier.padding(end = 8.dp),
                    text = "+7",
                    color = CustomTheme.colors.text.primary,
                    style = CustomTheme.typography.body.primary,
                )
            },
            trailingIcon = {
                Icon(
                    modifier = Modifier
                        .padding(start = 8.dp)
                        .clickable(false) { },
                    tint = CustomTheme.colors.icons.primary,
                    painter = painterResource(id = R.drawable.ic_24_visible),
                    contentDescription = null
                )
            },
            footerMessage = footerMessage
        )
    }
}

@Preview
@Composable
fun MetaTextFieldFilledPreview() {
    val coroutineScope = rememberCoroutineScope()
    val inputControl = InputControl(coroutineScope)

    inputControl.setText("999 777 55 33")

    MetaTextFieldPreview(inputControl)
}

@SuppressLint("StateFlowValueCalledInComposition")
@Preview
@Composable
fun MetaTextFieldWithErrorPreview() {
    val coroutineScope = rememberCoroutineScope()
    val inputControl = InputControl(coroutineScope)

    inputControl.setText("999 777 55 33 2")
    inputControl.error.value = "Неправильный номер телефона".desc()

    MetaTextFieldPreview(inputControl)
}

@Preview
@Composable
fun MetaTextFieldWithMessagePreview() {
    val coroutineScope = rememberCoroutineScope()
    val inputControl = InputControl(coroutineScope)

    inputControl.setText("999 777 55 33 2")

    MetaTextFieldPreview(inputControl) {
        DefaultTextFieldFooterMessage("Введите номер телефона через +7 без 8".desc())
    }
}
