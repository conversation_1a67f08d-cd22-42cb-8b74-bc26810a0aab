package com.metacards.metacards.core.paged_loading

/**
 * Allows to specify how stored data is merged with incoming page data
 */
interface DataMerger<T : Any> {
    fun mergeToStart(storedData: List<T>, incomingData: List<T>): List<T>

    fun mergeToEnd(storedData: List<T>, incomingData: List<T>): List<T>
}

class DuplicateRemovingDataMerger<T : Any>(
    private val keySelector: (T) -> Any
) : DataMerger<T> {

    override fun mergeToStart(storedData: List<T>, incomingData: List<T>): List<T> {
        val storedKeys = storedData.map(keySelector).toHashSet()
        return incomingData.filter { keySelector(it) !in storedKeys } + storedData
    }

    override fun mergeToEnd(storedData: List<T>, incomingData: List<T>): List<T> {
        val storedKeys = storedData.map(keySelector).toHashSet()
        return storedData + incomingData.filter { keySelector(it) !in storedKeys }
    }
}