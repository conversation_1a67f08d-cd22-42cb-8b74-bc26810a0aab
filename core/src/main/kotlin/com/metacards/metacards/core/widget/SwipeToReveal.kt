package com.metacards.metacards.core.widget

import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.offset
import androidx.compose.material.DismissDirection
import androidx.compose.material.DismissState
import androidx.compose.material.DismissValue
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.FixedThreshold
import androidx.compose.material.ResistanceConfig
import androidx.compose.material.SwipeableDefaults
import androidx.compose.material.ThresholdConfig
import androidx.compose.material.swipeable
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import kotlin.math.roundToInt

private val DISMISS_THRESHOLD = 56.dp

/**
 * Доработанный вариант [androidx.compose.material.SwipeToDismiss], с возможностью указать максимальный сдвиг [dismissContent] при свайпе.
 * @param dismissMaxOffset Максимальный сдвиг [dismissContent]. Если передать [Dp.Unspecified], то будет использована максимальная ширина контейнера
 */
@OptIn(ExperimentalMaterialApi::class)
@Composable
fun SwipeToReveal(
    state: DismissState,
    modifier: Modifier = Modifier,
    directions: Set<DismissDirection> = setOf(
        DismissDirection.EndToStart,
        DismissDirection.StartToEnd
    ),
    enabled: Boolean = true,
    dismissMaxOffset: Dp = Dp.Unspecified,
    dismissThresholds: (DismissDirection) -> ThresholdConfig = {
        FixedThreshold(DISMISS_THRESHOLD)
    },
    background: @Composable RowScope.() -> Unit,
    dismissContent: @Composable RowScope.() -> Unit
) {
    BoxWithConstraints(modifier = modifier) {
        val isRtl = LocalLayoutDirection.current == LayoutDirection.Rtl
        val width =
            if (dismissMaxOffset == Dp.Unspecified) {
                constraints.maxWidth.toFloat()
            } else {
                with(LocalDensity.current) { dismissMaxOffset.toPx() }
            }

        val anchors = mutableMapOf(0f to DismissValue.Default)
        if (DismissDirection.StartToEnd in directions) anchors += width to DismissValue.DismissedToEnd
        if (DismissDirection.EndToStart in directions) anchors += -width to DismissValue.DismissedToStart

        val thresholds = { from: DismissValue, to: DismissValue ->
            dismissThresholds(getDismissDirection(from, to)!!)
        }
        val minFactor =
            if (DismissDirection.EndToStart in directions) SwipeableDefaults.StandardResistanceFactor else SwipeableDefaults.StiffResistanceFactor
        val maxFactor =
            if (DismissDirection.StartToEnd in directions) SwipeableDefaults.StandardResistanceFactor else SwipeableDefaults.StiffResistanceFactor
        Box(
            Modifier.swipeable(
                state = state,
                anchors = anchors,
                thresholds = thresholds,
                orientation = Orientation.Horizontal,
                enabled = enabled,
                reverseDirection = isRtl,
                resistance = ResistanceConfig(
                    basis = width,
                    factorAtMin = minFactor,
                    factorAtMax = maxFactor
                )
            )
        ) {
            if (enabled) {
                Row(
                    content = background,
                    modifier = Modifier
                        .matchParentSize()
                        .offset {
                            IntOffset(
                                state.offset.value.roundToInt() + dismissMaxOffset.roundToPx(),
                                0
                            )
                        }
                )
            }

            Row(
                content = dismissContent,
                modifier = Modifier.offset { IntOffset(state.offset.value.roundToInt(), 0) }
            )
        }
    }
}

private fun getDismissDirection(from: DismissValue, to: DismissValue): DismissDirection? {
    return when {
        // settled at the default state
        from == to && from == DismissValue.Default -> null
        // has been dismissed to the end
        from == to && from == DismissValue.DismissedToEnd -> DismissDirection.StartToEnd
        // has been dismissed to the start
        from == to && from == DismissValue.DismissedToStart -> DismissDirection.EndToStart
        // is currently being dismissed to the end
        from == DismissValue.Default && to == DismissValue.DismissedToEnd -> DismissDirection.StartToEnd
        // is currently being dismissed to the start
        from == DismissValue.Default && to == DismissValue.DismissedToStart -> DismissDirection.EndToStart
        // has been dismissed to the end but is now animated back to default
        from == DismissValue.DismissedToEnd && to == DismissValue.Default -> DismissDirection.StartToEnd
        // has been dismissed to the start but is now animated back to default
        from == DismissValue.DismissedToStart && to == DismissValue.Default -> DismissDirection.EndToStart
        else -> null
    }
}