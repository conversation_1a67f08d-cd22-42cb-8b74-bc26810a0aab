package com.metacards.metacards.core.utils

import android.content.Context
import android.graphics.Bitmap
import android.renderscript.Allocation
import android.renderscript.Element
import android.renderscript.RenderScript
import android.renderscript.ScriptIntrinsicBlur
import coil.size.Size
import coil.transform.Transformation

@Suppress("DEPRECATION")
class BlurTransformation(private val context: Context) : Transformation {

    companion object {
        private const val BLUR_INTENSITY = 10
        private const val BLUR_RADIUS = 25f
    }

    override val cacheKey: String = "BlurTransformation"

    override suspend fun transform(input: Bitmap, size: Size): Bitmap {
        val rs = RenderScript.create(context)
        val blurredBitmap: Bitmap = input.copy(Bitmap.Config.ARGB_8888, true)
        repeat(BLUR_INTENSITY) {
            val allocation = Allocation.createFromBitmap(
                rs,
                blurredBitmap,
                Allocation.MipmapControl.MIPMAP_FULL,
                Allocation.USAGE_SCRIPT
            )
            val output = Allocation.createTyped(rs, allocation.type)
            ScriptIntrinsicBlur.create(rs, Element.U8_4(rs)).apply {
                setRadius(BLUR_RADIUS)
                setInput(allocation)
                forEach(output)
            }
            output.copyTo(blurredBitmap)
        }
        return blurredBitmap
    }
}