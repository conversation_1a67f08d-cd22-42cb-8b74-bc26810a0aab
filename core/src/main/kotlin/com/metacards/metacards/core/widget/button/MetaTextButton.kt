package com.metacards.metacards.core.widget.button

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.material.Button
import androidx.compose.material.ButtonElevation
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme

@Composable
fun MetaTextButton(
    text: String,
    modifier: Modifier = Modifier,
    state: ButtonState = ButtonState.Enabled,
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    elevation: ButtonElevation = MetaButtonDefaults.textButton.buttonElevation,
    border: BorderStroke? = null,
    shape: Shape = MetaButtonDefaults.textButton.buttonShape,
    colors: ButtonColorsWithPressed = MetaButtonDefaults.textButton.buttonColorsWithPressed,
    contentPadding: PaddingValues = MetaButtonDefaults.textButton.buttonPaddingValues,
    textStyle: TextStyle = CustomTheme.typography.caption.small,
    onClick: () -> Unit = {},
) {
    val pressed by interactionSource.collectIsPressedAsState()
    val buttonColors = colors.getButtonColors(pressed)

    Button(
        onClick = onClick,
        modifier = modifier,
        enabled = state.isNotDisabled(),
        interactionSource = interactionSource,
        elevation = elevation,
        border = border,
        shape = shape,
        colors = buttonColors,
        contentPadding = contentPadding
    ) {
        val textColor = buttonColors.contentColor(enabled = state.isEnabled())

        Text(
            text = text,
            style = textStyle,
            color = textColor.value,
        )
    }
}

@Preview
@Composable
fun MetaTextButtonPreview() {
    AppTheme {
        MetaTextButton(text = "Text button")
    }
}

@Preview
@Composable
fun MetaTextButtonDisabledPreview() {
    AppTheme {
        MetaTextButton(text = "Text button", state = ButtonState.Disabled)
    }
}