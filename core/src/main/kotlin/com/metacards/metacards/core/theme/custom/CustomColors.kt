package com.metacards.metacards.core.theme.custom

import androidx.compose.ui.graphics.Color

data class CustomColors(
    val isLight: Boolean = false,
    val background: Background = Background(),
    val text: Text = Text(),
    val icons: Icons = Icons(),
    val button: Button = Button(),
    val stroke: Stroke = Stroke(),
    val mars: Mars = Mars(),
    val moon: Moon = Moon(),
    val gradient: Gradient = Gradient(),
    val system: System = System(),
    val accent: Accent = Accent(),
    val star: Star = Star()
) {

    data class Background(
        val primary: Color = Color(0x8C2E3251),
        val modal: Color = Color(0xFF191B2B),
        val hint: Color = Color(0x66000000),
        val disabled: Color = Color(0x8C000000),
        val tabs: Color = Color(0xFF1C1C1C),
        val segmentControl: Color = Color(0xFF323553),
        val placeholder: Color = Color(0xFF1F2034),
        val message: Color = Color(0xFF161622),
        val notification: Color = Color(0xFF165336),
        val alert: Color = Color(0xFF000000),
        val disabledBackground: Color = Color(0x8C000000),
        val card: Color = Color(0x33FFFFFF)
    )

    data class Text(
        val primary: Color = Color(0xFFE6E8FA),
        val secondary: Color = Color(0xFF797EA4),
        val caption: Color = Color(0xFFFFFFFF),
        val inverted: Color = Color(0xFF2B2D40),
        val disabled: Color = Color(0x66797EA4),
        val error: Color = Color(0xFFE22929),
        val linearCustomGradientStart: Color = Color(0xFF5C36CB),
        val linearCustomGradientEnd: Color = Color(0xFF922FCF)
    ) {
        val linearCustomGradient = listOf(linearCustomGradientStart, linearCustomGradientEnd)
    }

    data class Icons(
        val primary: Color = Color(0xFFE6E8FA),
        val secondary: Color = Color(0xFFFFFFFF),
        val tertiary: Color = Color(0xFF797EA4),
        val noActive: Color = Color(0xFF818181),
        val disabled: Color = Color(0x66797EA4),
    )

    data class Button(
        val accent: Color = Color(0xFF4829A0),
        val primary: Color = Color(0xFFFFFFFF),
        val secondary: Color = Color(0x8C2E3251),
        val disabled: Color = Color(0xFF341F72),
        val small: Color = Color(0x8C1C1C1C),
        val linearCustomGradientStart: Color = Color(0xFF4829A0),
        val linearCustomGradientEnd: Color = Color(0xFF7329A0)
    ) {
        val linearCustomGradient = listOf(linearCustomGradientStart, linearCustomGradientEnd)
    }

    data class Stroke(
        val primary: Color = Color(0xFF4829A0),
        val secondary: Color = Color(0x33FFFFFF),
        val error: Color = Color(0xFFE22929),
    )

    data class Mars(
        val brightness20: Color = Color(0xFFC3684B),
        val brightness40: Color = Color(0xFFDE7655),
        val brightness60: Color = Color(0xFFE48E69),
        val brightness80: Color = Color(0xFFFAAA87),
        val brightness100: Color = Color(0xFFFFC6AD),
        val darkest: Color = Color(0xFFB45132),
        val lightest: Color = Color(0xFFFFE9DF),
    )

    data class Moon(
        val brightness20: Color = Color(0xFF6E8479),
        val brightness40: Color = Color(0xFF859F92),
        val brightness60: Color = Color(0xFF9ABFAD),
        val brightness80: Color = Color(0xFFAED0C0),
        val brightness100: Color = Color(0xFFD4E5D8),
        val darkest: Color = Color(0xFF52655C),
        val lightest: Color = Color(0xFFF0F6F3),
    )

    data class Gradient(
        val tailMoonStart: Color = Color(0x4D8CA397),
        val tailMoonEnd: Color = Color(0x008CA397),
        val tailMarsStart: Color = Color(0x4DE08761),
        val tailMarsEnd: Color = Color(0x00E08761),
        val scaleMarsStart: Color = Color(0x26D04146),
        val scaleMarsEnd: Color = Color(0x08D04146),
        val scaleMoonStart: Color = Color(0x267B9186),
        val scaleMoonEnd: Color = Color(0x087B9186),
        val backgroundStart: Color = Color(0xFF1F2133),
        val backgroundEnd: Color = Color(0xFF000000),
        val brandStart: Color = Color(0x086749BB),
        val brandEnd: Color = Color(0x456749BB),
    ) {
        val backgroundList = listOf(backgroundStart, backgroundEnd)
        val tailMoonList = listOf(tailMoonStart, tailMoonEnd)
        val tailMarsList = listOf(tailMarsStart, tailMarsEnd)
        val scaleMoonList = listOf(scaleMoonStart, scaleMoonEnd)
        val scaleMarsList = listOf(scaleMarsStart, scaleMarsEnd)
        val brandList = listOf(brandStart, brandEnd)
    }

    data class System(
        val invert: Color = Color(0xFFFFFFFF),
        val basic: Color = Color(0xFF000000),
        val transparent: Color = Color(0x00000000),
        val shimmer: Color = invert.copy(alpha = 0.6f),
        val unspecified: Color = Color.Unspecified,
        val navigationBarColor: Color = Color(0x74101117)
    )

    data class Accent(
        val primary: Color = Color(0xFFE3563B),
        val secondary: Color = Color(0xFFB63F29)
    )

    data class Star(
        val disable: Color = Color(0xFF797EA4),
        val active: Color = Color(0xFFDCE0FB),
        val activeShineGradientStart: Color = Color(0xDCE0FB26),
        val activeShineGradientEnd: Color = Color(0xDCE0FB00),
        val open: Color = Color(0xFFF2D5FF),
        val line: Color = Color(0xFF6B669D),
    ) {
        val activeShineGradient = listOf(activeShineGradientStart, activeShineGradientEnd)
    }
}