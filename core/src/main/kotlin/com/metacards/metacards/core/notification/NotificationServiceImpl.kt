package com.metacards.metacards.core.notification

import com.google.firebase.ktx.Firebase
import com.google.firebase.messaging.ktx.messaging
import com.metacards.metacards.core.localization.data.AppLanguageService
import com.metacards.metacards.core.localization.domain.AppLanguage
import com.metacards.metacards.core.preferences.PreferencesService
import com.metacards.metacards.core.user.domain.User
import com.metacards.metacards.core.user.domain.UserProvider
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import java.util.TimeZone

class NotificationServiceImpl(
    coroutineScope: CoroutineScope,
    private val preferencesService: PreferencesService,
    appLanguageService: AppLanguageService,
    userProvider: UserProvider
) : NotificationService {

    private val user = userProvider.getUser()
    private val appLanguage = appLanguageService.currentAppLanguage

    init {
        coroutineScope.launch {
            if (!preferencesService.getFirstAppLaunchSubscriptionFlag()) {
                subscribeToCommonNotifications(
                    language = appLanguage.value,
                    timezone = user.value?.timezone
                )
                preferencesService.enableFirstAppLaunchSubscriptionFlag()
            }
        }

        var previousUser: User? = user.value
        var previousLanguage: AppLanguage = appLanguage.value

        coroutineScope.launch {
            if (!preferencesService.getUserSubscriptionToNotifications()) return@launch
            val previousTimeZone = preferencesService.getSubscribedPushTimezone()
            val currentTimeZone = TimeZone.getDefault().rawOffset / 1000 / 60

            unsubscribeFromCommonNotifications(appLanguage.value, null)

            if (previousTimeZone != currentTimeZone) {
                TimeZone
                    .getAvailableIDs()
                    .map { TimeZone.getTimeZone(it) }
                    .map { it.rawOffset / 1000 / 60 }
                    .distinct()
                    .forEach {
                        unsubscribeFromCommonNotifications(
                            language = appLanguage.value,
                            timezone = it
                        )
                    }

                preferencesService.setSubscribedPushTimezone(currentTimeZone)
            }

            combine(user, appLanguage) { user, language -> user to language }
                .distinctUntilChanged()
                .filter { preferencesService.getUserSubscriptionToNotifications() }
                .onEach { (currentUser, currentLanguage) ->
                    if (currentUser?.timezone != previousUser?.timezone || currentLanguage != previousLanguage) {
                        unsubscribeFromCommonNotifications(
                            language = previousLanguage,
                            timezone = previousUser?.timezone
                        )

                        subscribeToCommonNotifications(
                            language = currentLanguage,
                            timezone = currentUser?.timezone
                        )
                    }

                    if (currentUser?.userId != previousUser?.userId) {
                        previousUser?.let {
                            unsubscribeFromUserNotifications(it.userId.value)
                        }

                        currentUser?.let {
                            subscribeToUserNotifications(it.userId.value)
                        }
                    }

                    previousUser = currentUser
                    previousLanguage = currentLanguage
                }
                .launchIn(coroutineScope)
        }
    }

    private suspend fun subscribeToCommonNotifications(
        language: AppLanguage,
        timezone: Int?
    ) {
        val subscribeMarketingTask = Firebase.messaging
            .subscribeToTopic(
                NotificationTopics
                    .MARKETING
                    .withLanguage(language)
                    .withTimezone(timezone)
                    .value
            )

        val subscribeSystemTask = Firebase.messaging
            .subscribeToTopic(
                NotificationTopics
                    .SYSTEM
                    .withLanguage(language)
                    .withTimezone(timezone)
                    .value
            )

        val subscribeDailyTask = Firebase.messaging
            .subscribeToTopic(
                NotificationTopics
                    .DAILY
                    .withLanguage(language)
                    .withTimezone(timezone)
                    .value
            )

        subscribeMarketingTask.await()
        subscribeSystemTask.await()
        subscribeDailyTask.await()
    }

    private suspend fun unsubscribeFromCommonNotifications(
        language: AppLanguage,
        timezone: Int?
    ) {
        val unsubscribeMarketingTask = Firebase.messaging
            .unsubscribeFromTopic(
                NotificationTopics
                    .MARKETING
                    .withLanguage(language)
                    .withTimezone(timezone)
                    .value
            )

        val unsubscribeSystemTask = Firebase.messaging
            .unsubscribeFromTopic(
                NotificationTopics
                    .SYSTEM
                    .withLanguage(language)
                    .withTimezone(timezone)
                    .value
            )

        val unsubscribeDailyTask = Firebase.messaging
            .unsubscribeFromTopic(
                NotificationTopics
                    .DAILY
                    .withLanguage(language)
                    .withTimezone(timezone)
                    .value
            )

        unsubscribeMarketingTask.await()
        unsubscribeSystemTask.await()
        unsubscribeDailyTask.await()
    }

    private suspend fun subscribeToUserNotifications(userId: String) {
        Firebase.messaging.subscribeToTopic(NotificationTopics.MARKETING.withUserId(userId).value)
            .await()
        Firebase.messaging.subscribeToTopic(NotificationTopics.SYSTEM.withUserId(userId).value)
            .await()
        Firebase.messaging.subscribeToTopic(NotificationTopics.DAILY.withUserId(userId).value)
            .await()
    }

    private suspend fun unsubscribeFromUserNotifications(userId: String) {
        Firebase.messaging.unsubscribeFromTopic(NotificationTopics.MARKETING.withUserId(userId).value)
            .await()
        Firebase.messaging.unsubscribeFromTopic(NotificationTopics.SYSTEM.withUserId(userId).value)
            .await()
        Firebase.messaging.unsubscribeFromTopic(NotificationTopics.DAILY.withUserId(userId).value)
            .await()
    }

    override suspend fun toggleNotificationSubscription(isSubscribed: Boolean) {
        if (isSubscribed) {
            subscribeToCommonNotifications(
                language = appLanguage.value,
                timezone = user.value?.timezone
            )
            user.value?.let {
                subscribeToUserNotifications(
                    userId = it.userId.value
                )
            }
        } else {
            unsubscribeFromCommonNotifications(
                language = appLanguage.value,
                timezone = user.value?.timezone
            )
            user.value?.let {
                unsubscribeFromUserNotifications(
                    userId = it.userId.value
                )
            }
        }
    }
}