package com.metacards.metacards.core.widget.tab

import androidx.compose.animation.core.AnimationSpec
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.relocation.BringIntoViewRequester
import androidx.compose.foundation.relocation.bringIntoViewRequester
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.Placeable
import androidx.compose.ui.layout.SubcomposeLayout
import androidx.compose.ui.platform.debugInspectorInfo
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.desc
import kotlinx.coroutines.launch

private enum class SubComposeID {
    PRE_CALCULATE_ITEM,
    ITEM,
    INDICATOR
}

@Composable
fun MetaTabs(
    selectedTabIndex: Int,
    tabItems: List<StringDesc>,
    modifier: Modifier = Modifier,
    tabPaddings: MetaTabPaddings = MetaTabsDefaults.TabPaddings,
    tabTextStyle: TextStyle = CustomTheme.typography.caption.large,
    onTabClicked: (Int) -> Unit,
) {
    MetaTabRow(
        modifier = modifier.horizontalScroll(rememberScrollState()),
        selectedTabPosition = selectedTabIndex,
        fixedSize = false,
        tabPaddings = tabPaddings
    ) {
        tabItems.forEachIndexed { index, string ->
            MetaTabTitle(
                title = string,
                position = index,
                isSelected = index == selectedTabIndex,
                tabPadding = tabPaddings.tabPadding,
                textStyle = tabTextStyle,
                onClick = onTabClicked
            )
        }
    }
}

@Composable
fun MetaTabRow(
    modifier: Modifier = Modifier,
    tabColor: MetaTabColors = MetaTabsDefaults.TabColors,
    tabShapes: MetaTabShapes = MetaTabsDefaults.TabShapes,
    tabPaddings: MetaTabPaddings = MetaTabsDefaults.TabPaddings,
    tabAnimationSpecs: MetaTabAnimationSpecs = MetaTabsDefaults.TabAnimationSpecs,
    fixedSize: Boolean = true,
    selectedTabPosition: Int = 0,
    tabItem: @Composable () -> Unit,
) {
    Surface(
        modifier = modifier,
        color = tabColor.containerColor,
        shape = tabShapes.containerShape
    ) {
        SubcomposeLayout(
            Modifier
                .padding(tabPaddings.rowPadding)
                .selectableGroup()
        ) { constraints ->
            val tabMeasurable: List<Placeable> =
                subcompose(SubComposeID.PRE_CALCULATE_ITEM, tabItem)
                    .map { it.measure(constraints) }

            val itemsCount = tabMeasurable.size
            val maxItemWidth = tabMeasurable.maxOf { it.width }
            val maxItemHeight = tabMeasurable.maxOf { it.height }

            val tabPlacables = subcompose(SubComposeID.ITEM, tabItem).map {
                val c = if (fixedSize) {
                    constraints.copy(
                        minWidth = maxItemWidth,
                        maxWidth = maxItemWidth,
                        minHeight = maxItemHeight
                    )
                } else {
                    constraints
                }
                it.measure(c)
            }

            val tabPositions = tabPlacables.mapIndexed { index, placeable ->
                val itemWidth = if (fixedSize) maxItemWidth else placeable.width
                val x = if (fixedSize) {
                    maxItemWidth * index
                } else {
                    val leftTabWith = tabPlacables.take(index).sumOf { it.width }
                    leftTabWith
                }
                TabPosition(x.toDp(), itemWidth.toDp())
            }

            val tabRowWidth = if (fixedSize) {
                maxItemWidth * itemsCount
            } else {
                tabPlacables.sumOf { it.width }
            }

            layout(tabRowWidth, maxItemHeight) {
                subcompose(SubComposeID.INDICATOR) {
                    Box(
                        Modifier
                            .tabIndicator(
                                tabPositions[selectedTabPosition],
                                tabAnimationSpecs.indicatorPositionAnimationSpec,
                                tabPaddings.tabPadding
                            )
                            .fillMaxWidth()
                            .height(maxItemHeight.toDp())
                            .background(
                                color = tabColor.indicatorColor,
                                shape = tabShapes.indicatorShape
                            )
                    )
                }.forEach {
                    it.measure(Constraints.fixed(tabRowWidth, maxItemHeight)).placeRelative(0, 0)
                }

                tabPlacables.forEachIndexed { index, placeable ->
                    val x = if (fixedSize) {
                        maxItemWidth * index
                    } else {
                        val leftTabWidth = tabPlacables.take(index).sumOf { it.width }
                        leftTabWidth
                    }
                    placeable.placeRelative(x, 0)
                }
            }
        }
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun MetaTabTitle(
    title: StringDesc,
    textStyle: TextStyle,
    position: Int,
    isSelected: Boolean,
    tabPadding: PaddingValues = MetaTabsDefaults.TabPaddings.tabPadding,
    tabColors: MetaTabColors = MetaTabsDefaults.TabColors,
    animationSpec: AnimationSpec<Color>? = null,
    onClick: (Int) -> Unit,
) {
    val colorState = if (animationSpec == null) {
        tabColors.contentColor(selected = isSelected)
    } else {
        tabColors.contentColor(selected = isSelected, animationSpec = animationSpec)
    }

    val animatedColor by colorState

    val focusRequester = remember { BringIntoViewRequester() }
    val scope = rememberCoroutineScope()

    Text(
        text = title.localizedByLocal(),
        modifier = Modifier
            .bringIntoViewRequester(focusRequester)
            .wrapContentWidth(Alignment.CenterHorizontally)
            .padding(tabPadding)
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null
            ) {
                onClick(position)
                scope.launch {
                    focusRequester.bringIntoView()
                }
            },
        color = animatedColor,
        style = textStyle
    )
}

private fun Modifier.tabIndicator(
    tabPosition: TabPosition,
    animationSpec: AnimationSpec<Dp>,
    padding: PaddingValues,
    height: Dp = 2.dp,
): Modifier = composed(
    inspectorInfo = debugInspectorInfo {
        name = "tabIndicatorOffset"
        value = tabPosition
    }
) {
    val currentTabWidth by animateDpAsState(
        targetValue = tabPosition.width,
        animationSpec = animationSpec
    )
    val indicatorOffset by animateDpAsState(
        targetValue = tabPosition.left,
        animationSpec = animationSpec
    )
    val heightModifier =
        if (height == Dp.Unspecified) Modifier.fillMaxHeight() else Modifier.height(height)

    fillMaxWidth()
        .wrapContentSize(Alignment.BottomStart)
        .offset(x = indicatorOffset)
        .width(currentTabWidth)
        .padding(padding)
        .then(heightModifier)
}

@Preview
@Composable
fun MetaTabsPreview() {
    var selectedTab by remember { mutableIntStateOf(0) }
    val tabs = listOf("Tab1".desc(), "TabTabTab2".desc(), "Tab3".desc())

    AppTheme {
        MetaTabs(selectedTab, tabs) { selectedTab = it }
    }
}