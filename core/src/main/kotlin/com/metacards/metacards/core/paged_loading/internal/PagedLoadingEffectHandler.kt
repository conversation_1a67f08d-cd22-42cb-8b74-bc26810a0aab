package com.metacards.metacards.core.paged_loading.internal

import kotlinx.coroutines.Job
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import me.aartikov.sesame.loop.EffectHandler
import com.metacards.metacards.core.paged_loading.PagedLoader
import kotlin.coroutines.cancellation.CancellationException

internal class PagedLoadingEffectHandler<T : Any>(private val loader: PagedLoader<T>) :
    EffectHandler<Effect<T>, Action<T>> {

    private var job: Job? = null

    override suspend fun handleEffect(effect: Effect<T>, actionConsumer: (Action<T>) -> Unit) {
        when (effect) {
            is Effect.LoadFirstPage -> loadFirstPage(actionConsumer)
            is Effect.ReloadPage -> reloadPage(effect.loadedData, actionConsumer)
            is Effect.LoadNextPage -> loadNextPage(effect.loadedData, actionConsumer)
            is Effect.LoadPreviousPage -> loadPreviousPage(effect.loadedData, actionConsumer)
            is Effect.CancelLoading -> cancelLoading()
            else -> Unit
        }
    }

    private suspend fun loadFirstPage(
        actionConsumer: (Action<T>) -> Unit
    ) = coroutineScope {
        job?.cancel()
        job = launch {
            try {
                val page = loader.loadFirstPage()
                if (isActive) {
                    actionConsumer(Action.FirstPageLoaded(page))
                }
            } catch (e: CancellationException) {
                throw e
            } catch (e: Exception) {
                if (isActive) {
                    actionConsumer(Action.LoadingError(e))
                }
            }
        }
    }

    private suspend fun reloadPage(loadedData: List<T>, actionConsumer: (Action<T>) -> Unit) =
        coroutineScope {
            job?.cancel()
            job = launch {
                try {
                    val page = loader.reloadPage(loadedData)
                    if (isActive) {
                        actionConsumer(Action.PageReloaded(page))
                    }
                } catch (e: CancellationException) {
                    throw e
                } catch (e: Exception) {
                    if (isActive) {
                        actionConsumer(Action.LoadingError(e))
                    }
                }
            }
        }

    private suspend fun loadNextPage(
        loadedData: List<T>,
        actionConsumer: (Action<T>) -> Unit
    ) = coroutineScope {
        job?.cancel()
        job = launch {
            try {
                val page = loader.loadNextPage(loadedData)
                if (isActive) {
                    actionConsumer(Action.NextPageLoaded(page))
                }
            } catch (e: CancellationException) {
                throw e
            } catch (e: Exception) {
                if (isActive) {
                    actionConsumer(Action.LoadingError(e))
                }
            }
        }
    }

    private suspend fun loadPreviousPage(
        loadedData: List<T>,
        actionConsumer: (Action<T>) -> Unit
    ) = coroutineScope {
        job?.cancel()
        job = launch {
            try {
                val page = loader.loadPreviousPage(loadedData)
                if (isActive) {
                    actionConsumer(Action.PreviousPageLoaded(page))
                }
            } catch (e: CancellationException) {
                throw e
            } catch (e: Exception) {
                if (isActive) {
                    actionConsumer(Action.LoadingError(e))
                }
            }
        }
    }

    private fun cancelLoading() {
        job?.cancel()
        job = null
    }
}