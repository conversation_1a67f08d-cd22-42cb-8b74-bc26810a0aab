package com.metacards.metacards.core.notification

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context

private const val NOTIFICATION_CHANNEL_ID_MARKETING = "marketing"
private const val NOTIFICATION_CHANNEL_ID_MARKETING_USER = "marketing_user"
private const val NOTIFICATION_CHANNEL_ID_SYSTEM = "system"
private const val NOTIFICATION_CHANNEL_ID_SYSTEM_USER = "system_user"
private const val NOTIFICATION_CHANNEL_ID_DAILY = "daily"
private const val NOTIFICATION_CHANNEL_ID_DAILY_USER = "daily_user"

// key - ID, value - name
private val channelNameById: Map<String, NotificationTopics> = mapOf(
    NOTIFICATION_CHANNEL_ID_MARKETING to NotificationTopics.MARKETING,
    NOTIFICATION_CHANNEL_ID_MARKETING_USER to NotificationTopics.MARKETING,
    NOTIFICATION_CHANNEL_ID_SYSTEM to NotificationTopics.SYSTEM,
    NOTIFICATION_CHANNEL_ID_SYSTEM_USER to NotificationTopics.SYSTEM,
    NOTIFICATION_CHANNEL_ID_DAILY to NotificationTopics.DAILY,
    NOTIFICATION_CHANNEL_ID_DAILY_USER to NotificationTopics.DAILY
)

fun createNotificationChannels(context: Context) {
    val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
    channelNameById.forEach {
        createNotificationChannel(
            notificationManager = notificationManager,
            channelId = it.key,
            channelName = it.value.value
        )
    }
}

private fun createNotificationChannel(
    notificationManager: NotificationManager,
    channelId: String,
    channelName: String
) {
    NotificationChannel(
        channelId,
        channelName,
        NotificationManager.IMPORTANCE_DEFAULT
    ).apply {
        lockscreenVisibility = Notification.VISIBILITY_PUBLIC
        notificationManager.createNotificationChannel(this)
    }
}