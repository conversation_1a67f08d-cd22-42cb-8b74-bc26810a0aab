package com.metacards.metacards.core.widget.tab

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.AnimationSpec
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.State
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.widget.tab.MetaTabsDefaults.AnimationDuration

object MetaTabsDefaults {
    val TabColors = MetaTabColors()
    val TabShapes = MetaTabShapes()
    val TabPaddings = MetaTabPaddings()
    val TabAnimationSpecs = MetaTabAnimationSpecs()
    const val AnimationDuration = 250
}

data class TabPosition(
    val left: Dp,
    val width: Dp
)

@Immutable
data class MetaTabColors(
    val containerColor: Color = CustomTheme.colors.system.transparent,
    val indicatorColor: Color = CustomTheme.colors.icons.primary,
    val selectedContentColor: Color = CustomTheme.colors.icons.primary,
    val unselectedContentColor: Color = CustomTheme.colors.text.secondary
) {

    @Composable
    fun contentColor(
        selected: Boolean,
        animationSpec: AnimationSpec<Color> = tween(durationMillis = AnimationDuration)
    ): State<Color> {
        val targetValue = if (selected) selectedContentColor else unselectedContentColor
        return animateColorAsState(targetValue, animationSpec)
    }
}

@Immutable
data class MetaTabShapes(
    val containerShape: Shape = RoundedCornerShape(0.dp),
    val indicatorShape: Shape = RoundedCornerShape(0.dp),
)

@Immutable
data class MetaTabPaddings(
    val rowPadding: PaddingValues = PaddingValues(0.dp),
    val tabPadding: PaddingValues = PaddingValues(horizontal = 6.dp),
)

@Immutable
data class MetaTabAnimationSpecs(
    val textColorAnimationSpec: AnimationSpec<Color> = tween(
        durationMillis = AnimationDuration,
        easing = FastOutSlowInEasing
    ),
    val indicatorPositionAnimationSpec: AnimationSpec<Dp> = tween(
        durationMillis = AnimationDuration,
        easing = FastOutSlowInEasing
    ),
)