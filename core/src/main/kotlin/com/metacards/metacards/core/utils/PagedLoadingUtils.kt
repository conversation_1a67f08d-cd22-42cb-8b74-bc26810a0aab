package com.metacards.metacards.core.utils

import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import com.metacards.metacards.core.error_handling.fullScreenErrorMessage
import com.metacards.metacards.core.paged_loading.PagedLoading

data class PagedData<T : Any>(
    val list: List<T>,
    val hasNextPage: Boolean = false,
    val hasPreviousPage: Boolean = false,
    val loadingNextPage: Boolean = false,
    val loadingPreviousPage: Boolean = false
)

fun <T : Any> PagedLoading.State<T>.toLoadableState(): LoadableState<PagedData<T>> {
    return LoadableState(
        loading = loadingStatus == PagedLoading.LoadingStatus.LoadingFirstPage,
        data = data?.let {
            PagedData(
                list = it.list,
                hasNextPage = it.hasNextPage,
                hasPreviousPage = it.hasPreviousPage,
                loadingNextPage = loadingStatus == PagedLoading.LoadingStatus.LoadingNextPage,
                loadingPreviousPage = loadingStatus == PagedLoading.LoadingStatus.LoadingPreviousPage
            )
        },
        error = error?.fullScreenErrorMessage
    )
}

@Composable
fun LazyListState.OnEndReached(
    callback: () -> Unit,
    itemCountGap: Int,
    scrollingToEndRequired: Boolean
) {

    val endReached by remember(this) {
        derivedStateOf {
            val lastVisibleItem = layoutInfo.visibleItemsInfo.lastOrNull()
            lastVisibleItem != null && lastVisibleItem.index >= layoutInfo.totalItemsCount - 1 - itemCountGap
        }
    }

    if (endReached && (!scrollingToEndRequired || isScrollingToEnd())) {
        LaunchedEffect(Unit) {
            callback()
        }
    }
}

@Composable
fun LazyListState.OnBeginReached(
    callback: () -> Unit,
    itemCountGap: Int,
    scrollingToBeginRequired: Boolean
) {
    val topReached by remember(this) {
        derivedStateOf {
            val lastVisibleItem = layoutInfo.visibleItemsInfo.firstOrNull()
            lastVisibleItem != null && lastVisibleItem.index <= itemCountGap
        }
    }

    if (topReached && (!scrollingToBeginRequired || isScrollingToBegin())) {
        LaunchedEffect(Unit) {
            callback()
        }
    }
}

@Composable
private fun LazyListState.isScrollingToBegin(): Boolean {
    var previousIndex by remember(this) { mutableIntStateOf(firstVisibleItemIndex) }
    var previousScrollOffset by remember(this) { mutableIntStateOf(firstVisibleItemScrollOffset) }
    return remember(this) {
        derivedStateOf {
            if (previousIndex != firstVisibleItemIndex) {
                previousIndex > firstVisibleItemIndex
            } else {
                previousScrollOffset > firstVisibleItemScrollOffset
            }.also {
                previousIndex = firstVisibleItemIndex
                previousScrollOffset = firstVisibleItemScrollOffset
            }
        }
    }.value && isScrollInProgress
}

@Composable
private fun LazyListState.isScrollingToEnd(): Boolean {
    var previousIndex by remember(this) { mutableIntStateOf(firstVisibleItemIndex) }
    var previousScrollOffset by remember(this) { mutableIntStateOf(firstVisibleItemScrollOffset) }
    return remember(this) {
        derivedStateOf {
            if (previousIndex != firstVisibleItemIndex) {
                previousIndex < firstVisibleItemIndex
            } else {
                previousScrollOffset < firstVisibleItemScrollOffset
            }.also {
                previousIndex = firstVisibleItemIndex
                previousScrollOffset = firstVisibleItemScrollOffset
            }
        }
    }.value && isScrollInProgress
}