package com.metacards.metacards.core.widget.button

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.clickable

@Composable
fun MetaCustomButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enable: Boolean = true,
    shape: Shape = MetaButtonDefaults.bigButton.buttonShape
) {

    val bgModifier = remember(enable) {
        if (enable) {
            Modifier
                .background(
                    Brush.horizontalGradient(CustomTheme.colors.button.linearCustomGradient),
                    shape
                )
        } else {
            Modifier
                .background(CustomTheme.colors.button.disabled, shape)
        }
    }

    Box(
        modifier = modifier
            .fillMaxWidth()
            .then(bgModifier)
            .clickable(onClick = onClick)
    ) {
        Text(
            text = text,
            style = CustomTheme.typography.button.primary,
            color = CustomTheme.colors.text.caption,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        )
    }
}

@Preview
@Composable
private fun MetaCustomButtonPreview() {
    Box(
        modifier = Modifier
            .padding(32.dp),
        contentAlignment = Alignment.Center
    ) {
        MetaCustomButton(
            text = "MetaCustomButtonPreview",
            enable = true,
            onClick = {}
        )
    }
}