package com.metacards.metacards.core.dialog

import android.os.Parcelable
import com.arkivanov.decompose.router.slot.ChildSlot
import com.metacards.metacards.core.utils.createFakeChildSlot
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class FakeDialogControl<C : Parcelable, T : Any>(dialogComponent: T) : DialogControl<C, T> {

    override val dialogOverlay: StateFlow<ChildSlot<*, T>> =
        MutableStateFlow(createFakeChildSlot(dialogComponent))

    override val dismissEvent: StateFlow<Unit> = MutableStateFlow(Unit)

    override val canDismissed: Boolean = true

    override fun show(config: C) = Unit

    override fun dismiss() = Unit
}
