package com.metacards.metacards.core.message.ui

import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material.rememberSwipeableState
import androidx.compose.material.swipeable
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.message.domain.Message
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.utils.toPx
import kotlin.math.roundToInt

private const val THIRD_OF_SCREEN = 0.33f
private const val DISMISS_CONFIRM_THRESHOLD_PX = 50

/**
 * Displays a [Message] as a popup at the bottom of screen.
 */
@Composable
fun MessageUi(
    component: MessageComponent,
    modifier: Modifier = Modifier,
    bottomPadding: Dp
) {
    val visibleMessage by component.visibleMessage.collectAsState()
    val additionalBottomPadding = with(LocalDensity.current) {
        LocalMessageOffsets.current.values.maxOrNull()?.toDp() ?: 0.dp
    }
    Box(
        modifier = modifier
            .fillMaxSize()
            .padding(bottomPadding + additionalBottomPadding)
    ) {
        visibleMessage?.let {
            val inverseIsDarkTheme = MaterialTheme.colors.isLight
            AppTheme(inverseIsDarkTheme) {
                MessagePopup(
                    message = it,
                    onAction = component::onActionClick
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun MessagePopup(
    message: Message,
    onAction: () -> Unit
) {
    val screenHeight = with(LocalConfiguration.current) { screenHeightDp }
    val screenHeightPx = with(LocalDensity.current) { screenHeight.toPx().toFloat() }

    val swipeableState = rememberSwipeableState(
        initialValue = 0,
        confirmStateChange = { it < DISMISS_CONFIRM_THRESHOLD_PX }
    )

    val anchors = mapOf(
        0f to 0,
        screenHeightPx * THIRD_OF_SCREEN to 1
    )

    LaunchedEffect(key1 = message) {
        swipeableState.snapTo(0)
    }

    Popup(
        alignment = Alignment.BottomCenter,
        properties = PopupProperties(
            dismissOnBackPress = false,
            dismissOnClickOutside = false
        )
    ) {
        Card(
            shape = RoundedCornerShape(16.dp),
            backgroundColor = if (message.isNotification) {
                CustomTheme.colors.background.notification
            } else {
                CustomTheme.colors.background.message
            },
            elevation = 0.dp,
            modifier = Modifier
                .padding(start = 16.dp, end = 16.dp)
                .wrapContentSize()
                .swipeable(
                    state = swipeableState,
                    anchors = anchors,
                    orientation = Orientation.Vertical
                )
                .offset {
                    IntOffset(0, swipeableState.offset.value.roundToInt())
                }
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                modifier = Modifier
                    .padding(all = 16.dp)
                    .fillMaxWidth()
            ) {
                message.iconRes?.let {
                    Icon(
                        painter = painterResource(it),
                        contentDescription = null,
                        tint = MaterialTheme.colors.primary
                    )
                }
                Text(
                    modifier = Modifier.weight(1f),
                    text = message.text.localizedByLocal(),
                    color = CustomTheme.colors.text.primary,
                    style = CustomTheme.typography.body.primary
                )
                message.actionTitle?.let {
                    MessageButton(text = it.localizedByLocal(), onClick = onAction)
                }
            }
        }
    }
}

@Composable
private fun MessageButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    TextButton(
        onClick = onClick,
        modifier = modifier,
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.body2
        )
    }
}

@Preview(showSystemUi = true)
@Composable
fun MessageUiPreview() {
    AppTheme {
        MessageUi(FakeMessageComponent(), Modifier, 40.dp)
    }
}