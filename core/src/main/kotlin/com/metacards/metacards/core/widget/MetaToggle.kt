package com.metacards.metacards.core.widget

import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.material.Switch
import androidx.compose.material.SwitchDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme

@Composable
fun MetaToggle(
    checked: Boolean,
    modifier: Modifier = Modifier,
    onCheckedChange: (Boolean) -> Unit
) {
    Switch(
        checked = checked,
        onCheckedChange = onCheckedChange,
        modifier = modifier,
        enabled = true,
        interactionSource = remember { MutableInteractionSource() },
        colors = MetaToggleDefaults.colors
    )
}

@Preview
@Composable
fun MetaToggleCheckedPreview() {
    AppTheme {
        MetaToggle(checked = true) {}
    }
}

@Preview
@Composable
fun MetaToggleUncheckedPreview() {
    AppTheme {
        MetaToggle(checked = false) {}
    }
}

private object MetaToggleDefaults {
    val colors
        @Composable get() = SwitchDefaults.colors(
            checkedThumbColor = CustomTheme.colors.icons.primary,
            checkedTrackColor = CustomTheme.colors.button.accent,
            uncheckedThumbColor = CustomTheme.colors.icons.primary,
            uncheckedTrackColor = CustomTheme.colors.button.secondary,
        )
}