package com.metacards.metacards.core.utils

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingCommand
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch

/**
 * [SharingStarted] strategy for manual control when flow is active
 */
fun SharingStarted.Companion.Activable(activeFlow: StateFlow<Boolean>): SharingStarted {
    return SharingStarted {
        activeFlow.map { active ->
            if (active) SharingCommand.START else SharingCommand.STOP
        }
    }
}

suspend fun withProgress(
    progressStateFlow: MutableStateFlow<Boolean>,
    block: suspend () -> Unit
) {
    try {
        progressStateFlow.value = true
        block()
    } finally {
        progressStateFlow.value = false
    }
}

fun <T> Flow<T>.mutableStateIn(
    scope: CoroutineScope,
    initialValue: T
): MutableStateFlow<T> {
    val flow = MutableStateFlow(initialValue)

    scope.launch {
        <EMAIL>(flow)
    }

    return flow
}