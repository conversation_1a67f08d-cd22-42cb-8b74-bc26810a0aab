package com.metacards.metacards.core.widget.button

import androidx.compose.animation.animateColorAsState
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ButtonColors
import androidx.compose.material.ButtonDefaults
import androidx.compose.material.ButtonElevation
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.theme.custom.CustomTheme

@Immutable
object MetaButtonDefaults {
    @Stable
    val bigButton: BigButtonDefaults = BigButtonDefaults()

    @Stable
    val textButton: TextButtonDefaults = TextButtonDefaults()

    @Stable
    val logoButton: LogoButtonDefaults = LogoButtonDefaults()
}

@Immutable
class LogoButtonDefaults internal constructor() {
    private val buttonHorizontalPadding = 24.dp
    private val buttonVerticalPadding = 16.dp

    @Stable
    val buttonShape = RoundedCornerShape(16.dp)

    @Stable
    val buttonPaddingValues = PaddingValues(
        start = buttonHorizontalPadding,
        top = buttonVerticalPadding,
        end = buttonHorizontalPadding,
        bottom = buttonVerticalPadding
    )

    @Stable
    val buttonColorsWithPressed = ButtonColorsWithPressed(
        backgroundColor = CustomTheme.colors.button.secondary,
        contentColor = CustomTheme.colors.system.unspecified,
        disabledBackgroundColor = CustomTheme.colors.button.secondary,
        disabledContentColor = CustomTheme.colors.system.unspecified,
        pressedBackgroundColor = CustomTheme.colors.button.secondary,
        pressedContentColor = CustomTheme.colors.system.unspecified,
    )

    @Stable
    val buttonInvertColorsWithPressed = ButtonColorsWithPressed(
        backgroundColor = CustomTheme.colors.system.invert,
        contentColor = CustomTheme.colors.system.unspecified,
        disabledBackgroundColor = CustomTheme.colors.system.invert,
        disabledContentColor = CustomTheme.colors.system.unspecified,
        pressedBackgroundColor = CustomTheme.colors.system.invert,
        pressedContentColor = CustomTheme.colors.system.unspecified,
    )

    @Stable
    val buttonElevation: ButtonElevation
        @Composable get() = ButtonDefaults.elevation(
            defaultElevation = 0.dp,
            pressedElevation = 0.dp,
            disabledElevation = 0.dp,
            hoveredElevation = 0.dp,
            focusedElevation = 0.dp,
        )
}

@Immutable
class TextButtonDefaults internal constructor() {
    private val buttonHorizontalPadding = 0.dp
    private val buttonVerticalPadding = 0.dp

    @Stable
    val buttonShape = RoundedCornerShape(0.dp)

    @Stable
    val buttonPaddingValues = PaddingValues(
        start = buttonHorizontalPadding,
        top = buttonVerticalPadding,
        end = buttonHorizontalPadding,
        bottom = buttonVerticalPadding
    )

    @Stable
    val buttonColorsWithPressed = ButtonColorsWithPressed(
        backgroundColor = CustomTheme.colors.system.transparent,
        contentColor = CustomTheme.colors.text.caption,
        disabledBackgroundColor = CustomTheme.colors.system.transparent,
        disabledContentColor = CustomTheme.colors.text.disabled,
        pressedBackgroundColor = CustomTheme.colors.system.transparent,
        pressedContentColor = CustomTheme.colors.text.disabled,
    )

    @Stable
    val buttonCapsColorsWithPressed = ButtonColorsWithPressed(
        backgroundColor = CustomTheme.colors.system.transparent,
        contentColor = CustomTheme.colors.text.caption,
        disabledBackgroundColor = CustomTheme.colors.system.transparent,
        disabledContentColor = CustomTheme.colors.text.disabled,
        pressedBackgroundColor = CustomTheme.colors.system.transparent,
        pressedContentColor = CustomTheme.colors.text.caption,
    )

    @Stable
    val buttonElevation: ButtonElevation
        @Composable get() = ButtonDefaults.elevation(
            defaultElevation = 0.dp,
            pressedElevation = 0.dp,
            disabledElevation = 0.dp,
            hoveredElevation = 0.dp,
            focusedElevation = 0.dp,
        )
}

@Immutable
class ButtonColorsWithPressed(
    val backgroundColor: Color,
    val contentColor: Color,
    val disabledBackgroundColor: Color,
    val disabledContentColor: Color,
    val pressedBackgroundColor: Color,
    val pressedContentColor: Color,
) {
    @Stable
    fun backgroundColor(pressed: Boolean): Color {
        return if (pressed) pressedBackgroundColor else backgroundColor
    }

    @Stable
    fun contentColor(pressed: Boolean): Color {
        return if (pressed) pressedContentColor else contentColor
    }

    @Stable
    @Composable
    fun getButtonColors(pressed: Boolean): ButtonColors {
        return toButtonColors(pressed)
    }
}

@Stable
@Composable
fun ButtonColorsWithPressed.toButtonColors(pressed: Boolean): ButtonColors {
    val backgroundColor = backgroundColor(pressed)
    val contentColor = contentColor(pressed)

    val buttonBackgroundColorAnimated by animateColorAsState(targetValue = backgroundColor)
    val buttonContentColorAnimated by animateColorAsState(targetValue = contentColor)

    return ButtonDefaults.buttonColors(
        backgroundColor = buttonBackgroundColorAnimated,
        contentColor = buttonContentColorAnimated,
        disabledBackgroundColor = disabledBackgroundColor,
        disabledContentColor = disabledContentColor,
    )
}

@Immutable
class BigButtonDefaults internal constructor() {
    private val buttonHorizontalPadding = 16.dp
    private val buttonVerticalPadding = 16.dp

    @Stable
    val buttonShape = RoundedCornerShape(48.dp)

    @Stable
    val minHeight = 52.dp

    @Stable
    val minContentSize = minHeight - buttonVerticalPadding * 2

    @Stable
    val buttonPaddingValues = PaddingValues(
        start = buttonHorizontalPadding,
        top = buttonVerticalPadding,
        end = buttonHorizontalPadding,
        bottom = buttonVerticalPadding
    )

    @Stable
    val buttonAccentColors: ButtonColors
        @Composable get() = ButtonDefaults.buttonColors(
            backgroundColor = CustomTheme.colors.button.accent,
            contentColor = CustomTheme.colors.text.caption,
            disabledBackgroundColor = CustomTheme.colors.button.disabled,
            disabledContentColor = CustomTheme.colors.text.disabled,
        )

    @Stable
    val buttonPrimaryColors: ButtonColors
        @Composable get() = ButtonDefaults.buttonColors(
            backgroundColor = CustomTheme.colors.button.primary,
            contentColor = CustomTheme.colors.text.inverted,
            disabledBackgroundColor = CustomTheme.colors.button.primary,
            disabledContentColor = CustomTheme.colors.text.disabled,
        )

    @Stable
    val buttonSecondaryColors: ButtonColors
        @Composable get() = ButtonDefaults.buttonColors(
            backgroundColor = CustomTheme.colors.button.secondary,
            contentColor = CustomTheme.colors.text.primary,
            disabledBackgroundColor = CustomTheme.colors.button.secondary,
            disabledContentColor = CustomTheme.colors.text.disabled,
        )

    @Stable
    val buttonTransparentColors: ButtonColors
        @Composable get() = ButtonDefaults.buttonColors(
            backgroundColor = CustomTheme.colors.system.transparent,
            contentColor = CustomTheme.colors.text.primary,
            disabledBackgroundColor = CustomTheme.colors.system.transparent,
            disabledContentColor = CustomTheme.colors.text.disabled,
        )

    @Stable
    val buttonElevation: ButtonElevation
        @Composable get() = ButtonDefaults.elevation()

    @Stable
    val buttonTransparentElevation: ButtonElevation
        @Composable get() = ButtonDefaults.elevation(
            defaultElevation = 0.dp,
            pressedElevation = 0.dp,
            disabledElevation = 0.dp,
            hoveredElevation = 0.dp,
            focusedElevation = 0.dp,
        )
}