package com.metacards.metacards.core.localization.ui

import android.content.Context
import com.metacards.metacards.core.localization.domain.AppLanguage
import dev.icerock.moko.resources.desc.StringDesc

class LocalizableStringWithFallback(
    private val original: LocalizableString,
    private val fallback: StringDesc
) : StringDesc {

    override fun toString(context: Context): String {
        val appLanguage = AppLanguage.getDefaultAppLanguage()
        return toString(context, appLanguage)
    }

    fun toString(context: Context, appLanguage: AppLanguage): String {
        val resolvedFallback = fallback.getLocalizedValue(context, appLanguage)
        return original.toString(appLanguage, resolvedFallback)
    }
}