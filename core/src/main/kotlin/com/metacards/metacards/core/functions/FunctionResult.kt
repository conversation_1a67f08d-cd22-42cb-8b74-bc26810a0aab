package com.metacards.metacards.core.functions

import kotlinx.serialization.Serializable

@Serializable
data class FunctionResult<T>(private val rawMap: Map<String, T>?) {
    val result: String? = rawMap?.get(RESULT_PARAM) as? String?
    val data: T? = rawMap?.get(DATA_PARAM)
    val isSuccess: Boolean = result?.lowercase() == SUCCESS_RESULT
    val isError: Boolean = result?.lowercase() == ERROR_RESULT

    companion object {
        private const val ERROR_RESULT = "error"
        private const val SUCCESS_RESULT = "success"
        private const val RESULT_PARAM = "result"
        private const val DATA_PARAM = "data"
    }
}