package com.metacards.metacards.core.preferences.models

enum class TutorialState {
    START, SELECT_CARD, JOUR<PERSON>L, PREDEFINED, FINISH, FORCE_FINISH, COMPLETED, NONE;

    companion object {
        fun fromString(state: String?): TutorialState {
            return try {
                TutorialState.valueOf(state ?: "")
            } catch (ex: Exception) {
                NONE
            }
        }
    }
}