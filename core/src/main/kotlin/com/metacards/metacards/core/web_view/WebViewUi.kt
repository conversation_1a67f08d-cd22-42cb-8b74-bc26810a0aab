package com.metacards.metacards.core.web_view

import android.annotation.SuppressLint
import android.content.Intent
import android.net.Uri
import android.view.ViewGroup
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.viewinterop.AndroidView
import com.metacards.metacards.core.R
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import com.metacards.metacards.core.widget.BoxWithFade
import com.metacards.metacards.core.widget.navigation_bar.BackNavigationItem
import com.metacards.metacards.core.widget.navigation_bar.TopNavigationBar
import dev.icerock.moko.resources.desc.strResDesc

private const val DEEPLINK_PREFIX = "metacards://"
private const val AUTH_PARAMETER = "auth?"
private const val AUTH_ERROR = "error=access_denied"

@SuppressLint("SetJavaScriptEnabled")
@Composable
fun WebViewUi(
    component: WebViewComponent,
    modifier: Modifier = Modifier
) {
    val url = component.url ?: component.urlResource?.localizedByLocal()
    BoxWithFade(
        modifier = modifier
            .fillMaxSize()
            .safeDrawingPadding(),
        listOfColors = CustomTheme.colors.gradient.backgroundList
    ) {
        Column {
            TopNavigationBar(
                title = component.title.strResDesc(),
                leadingIcon = { BackNavigationItem(iconRes = R.drawable.ic_24_close) }
            )
            AndroidView(
                factory = {
                    WebView(it).apply {
                        layoutParams = ViewGroup.LayoutParams(
                            ViewGroup.LayoutParams.MATCH_PARENT,
                            ViewGroup.LayoutParams.MATCH_PARENT
                        )
                        webViewClient = object : WebViewClient() {
                            override fun shouldOverrideUrlLoading(
                                view: WebView?,
                                request: WebResourceRequest
                            ): Boolean {
                                val authHandledUrl = manageWebRequest(request)

                                return if (!authHandledUrl.startsWith(DEEPLINK_PREFIX)) {
                                    super.shouldOverrideUrlLoading(view, request)
                                } else {
                                    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(authHandledUrl))
                                    component.activity?.startActivity(intent)
                                    true
                                }
                            }
                        }
                        settings.javaScriptEnabled = true
                        url?.let { it1 -> loadUrl(it1) }
                        clipToOutline = true
                    }
                },
                modifier = Modifier.fillMaxSize()
            )
        }
    }
}

private fun manageWebRequest(request: WebResourceRequest): String {
    var url = request.url.toString()

    if (url.contains(AUTH_ERROR)) {
        url = DEEPLINK_PREFIX + AUTH_PARAMETER + AUTH_ERROR
    }

    return url
}

@Preview
@Composable
fun WebViewUiPreview() {
    AppTheme {
        WebViewUi(component = FakeWebViewComponent())
    }
}
