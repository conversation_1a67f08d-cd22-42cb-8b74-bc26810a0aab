package com.metacards.metacards.core.utils

import kotlinx.coroutines.CoroutineScope
import ru.mobileup.kmm_form_validation.control.InputControl
import ru.mobileup.kmm_form_validation.options.ImeAction
import ru.mobileup.kmm_form_validation.options.KeyboardOptions
import ru.mobileup.kmm_form_validation.options.KeyboardType
import ru.mobileup.kmm_form_validation.options.PasswordVisualTransformation
import ru.mobileup.kmm_form_validation.options.TextTransformation

private val inputControlFilterWhitespacesTransformation = TextTransformation { it.filterNot(Char::isWhitespace) }
private const val PASSWORD_MAX_LENGTH = 30
const val PASSWORD_MIN_LENGTH = 8

fun passwordInput(
    coroutineScope: CoroutineScope,
    action: ImeAction,
) = InputControl(
    coroutineScope = coroutineScope,
    keyboardOptions = KeyboardOptions(
        keyboardType = KeyboardType.Password,
        imeAction = action
    ),
    visualTransformation = PasswordVisualTransformation(),
    maxLength = PASSWORD_MAX_LENGTH,
    textTransformation = inputControlFilterWhitespacesTransformation
)
