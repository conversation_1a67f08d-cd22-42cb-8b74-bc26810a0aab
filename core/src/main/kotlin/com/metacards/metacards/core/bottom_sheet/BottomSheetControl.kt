package com.metacards.metacards.core.bottom_sheet

import android.os.Parcelable
import com.arkivanov.decompose.router.slot.ChildSlot
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow

interface BottomSheetControl<C : Parcelable, T : Any> {

    val sheetOverlay: StateFlow<ChildSlot<*, T>>
    val sheetState: StateFlow<State>
    val halfExpandingSupported: Boolean
    val hidingSupported: Boolean
    val dismissEvent: Flow<Unit>

    fun shouldUpdateState(newState: State): Boolean
    fun onStateChangedFromUI(state: State)
    fun onStateChangeAnimationEnd(targetState: State)
    fun show(config: C)
    fun dismiss()

    enum class State {
        Expanded, HalfExpanded, Hidden
    }
}
