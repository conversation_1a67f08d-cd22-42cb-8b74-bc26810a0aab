package com.metacards.metacards.core.web_view

import androidx.activity.ComponentActivity
import androidx.annotation.StringRes
import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.activity.ActivityProvider
import dev.icerock.moko.resources.desc.StringDesc

class RealWebViewComponent(
    componentContext: ComponentContext,
    override val url: String?,
    override val urlResource: StringDesc?,
    @StringRes override val title: Int,
    activityProvider: ActivityProvider
) : ComponentContext by componentContext, WebViewComponent {

    override val activity: ComponentActivity? = activityProvider.activity
}