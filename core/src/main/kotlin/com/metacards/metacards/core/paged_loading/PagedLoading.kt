package com.metacards.metacards.core.paged_loading

import com.metacards.metacards.core.paged_loading.internal.PagedLoadingImpl
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach

/**
 * Data loader for [PagedLoading].
 */
interface PagedLoader<T : Any> {
    /**
     * Loads a first page.
     * @return page with data.
     */
    suspend fun loadFirstPage(): Page<T>

    /**
     * Reload page.
     * @param loadedData already loaded data
     * @return page with data.
     */
    suspend fun reloadPage(loadedData: List<T>): Page<T> {
        return Page(emptyList(), hasNextPage = false, hasPreviousPage = false)
    }

    /**
     * Loads the next page.
     * @param loadedData already loaded data
     * @return page with data.
     */
    suspend fun loadNextPage(loadedData: List<T>): Page<T>

    /**
     * Loads the previous page.
     * @param loadedData already loaded data
     * @return page with data.
     */
    suspend fun loadPreviousPage(loadedData: List<T>): Page<T> {
        return Page(emptyList(), hasNextPage = false, hasPreviousPage = false)
    }
}

/**
 * Helps to load paged data and manage loading state.
 */
interface PagedLoading<T : Any> {

    /**
     * Loading state.
     */
    data class State<out T>(
        val loadingStatus: LoadingStatus,
        val data: Data<T>?,
        val error: Exception?
    ) {
        companion object {
            fun <T> initial(): State<T> {
                return State(loadingStatus = LoadingStatus.None, data = null, error = null)
            }

            fun <T> mock(list: List<T>): State<T> {
                return State(
                    loadingStatus = LoadingStatus.None,
                    error = null,
                    data = Data(list = list, hasPreviousPage = false, hasNextPage = false)
                )
            }
        }
    }

    enum class LoadingStatus {
        None,
        LoadingFirstPage,
        ReloadPage,
        LoadingNextPage,
        LoadingPreviousPage
    }

    data class Data<out T>(
        val list: List<T>,
        val hasNextPage: Boolean,
        val hasPreviousPage: Boolean
    )

    /**
     * Loading event.
     */
    sealed interface Event<out T> {
        object FirstPageLoaded : Event<Nothing>
        object PageReloaded : Event<Nothing>
        object NextPageLoaded : Event<Nothing>
        object PreviousPageLoaded : Event<Nothing>

        /**
         * An error occurred. [stateDuringLoading] a state that was during the failed loading.
         */
        data class Error<T>(val exception: Exception, val stateDuringLoading: State<T>) : Event<T> {

            /**
             * Is true when there is previously loaded data. It is useful to not show an error dialog when a fullscreen error is already shown.
             */
            val hasData get() = stateDuringLoading.data != null
        }
    }

    /**
     * Flow of loading states.
     */
    val stateFlow: StateFlow<State<T>>

    /**
     * Flow of loading events.
     */
    val eventFlow: Flow<Event<T>>

    /**
     * [PagedLoader] used to create this PagedLoading
     */
    val loader: PagedLoader<T>

    /**
     * Requests to load a first page.
     * @param clear if true than previously loaded data will be instantly cleared and in progress loading will be canceled.
     * Otherwise previously loaded data will be preserved until successful outcome.
     * If another [loadFirstPage] request is in progress than new one will be ignored.
     * If [loadNext] or [loadPrevious] request is in progress than it will be canceled.
     */
    fun loadFirstPage(clear: Boolean = false)

    /**
     * Requests to reload page.
     * If another [reloadPage] request is in progress than new one will be ignored.
     * If [loadNext] or [loadPrevious] request is in progress than it will be canceled.
     */
    fun reloadPage()

    /**
     * Requests to load the next page. Loaded data will be added to the end of a previously loaded list.
     * The request will be ignored if another one [loadFirstPage] or [loadNext] is already in progress.
     * If [loadPrevious] request is in progress than it will be canceled.
     */
    fun loadNext()

    /**
     * Requests to load the previous page. Loaded data will be added to the start of a previously loaded list.
     * The request will be ignored if another one [loadFirstPage] or [loadPrevious] is already in progress.
     * If [loadNext] request is in progress than it will be canceled.
     */
    fun loadPrevious()

    /**
     * Requests to cancel in-progress loading.
     * @param clear if true than current data and error will be cleared.
     */
    fun cancel(clear: Boolean = false)

    /**
     * Mutates current data with a [transform] function.
     */
    fun mutateData(transform: (List<T>) -> List<T>)
}

/**
 * A helper method to handle [PagedLoading.Event.Error].
 */
fun <T : Any> PagedLoading<T>.handleErrors(
    scope: CoroutineScope,
    handler: (PagedLoading.Event.Error<T>) -> Unit
): Job {
    return eventFlow.filterIsInstance<PagedLoading.Event.Error<T>>()
        .onEach {
            handler(it)
        }
        .launchIn(scope)
}

/**
 * Creates an implementation of [PagedLoading].
 */
fun <T : Any> PagedLoading(
    scope: CoroutineScope,
    loader: PagedLoader<T>,
    initialState: PagedLoading.State<T> = PagedLoading.State.initial(),
    dataMerger: DataMerger<T>
): PagedLoading<T> {
    return PagedLoadingImpl(scope, loader, initialState, dataMerger)
}