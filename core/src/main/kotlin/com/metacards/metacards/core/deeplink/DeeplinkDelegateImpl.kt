package com.metacards.metacards.core.deeplink

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import co.touchlab.kermit.Logger
import com.metacards.metacards.core.activity.ActivityProvider
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeRun
import com.metacards.metacards.core.notification.PushNotifier
import io.branch.referral.Branch
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement

class DeeplinkDelegateImpl(
    private val json: <PERSON>son,
    private val activityProvider: ActivityProvider,
    private val errorHandler: ErrorHandler,
    private val deeplinkService: DeeplinkService
) : DeeplinkDelegate {
    private val branchLogger = Logger.withTag("Branch SDK")
    private val logger = Logger.withTag("DeeplinkDelegateImpl")
    private val activity
        get() = activityProvider.activity

    override fun handleDeeplinkOnCreate(intent: Intent) {
        if (intent.isBranchLink) {
            handleBranchLink(intent)
        } else {
            handleDeeplink(intent)
        }
    }

    override fun handleDeeplinkOnNewIntent(intent: Intent?) {
        if (intent.isBranchLink) {
            handleBranchLinkOnNew(intent)
        } else {
            handleDeeplink(intent)
        }
    }

    private fun handleDeeplink(intent: Intent?) {
        safeRun(errorHandler) {
            val intentLocal = intent ?: return@safeRun
            val pushData: Bundle? = intent.getBundleExtra(PushNotifier.PUSH_DATA_KEY)

            if (intentLocal.data != null) {
                val isHandled = deeplinkService.tryHandleDeeplink(intentLocal.data!!)
                if (!isHandled && pushData != null) handleDeeplinkByBundle(pushData)
            } else {
                if (pushData != null) handleDeeplinkByBundle(pushData)
            }
        }
    }

    private fun handleDeeplinkByBundle(bundle: Bundle) {
        logger.i("received data: ${bundle.keySet()}")
        val map = bundle.keySet().associateWith {
            bundle.getString(it)
        }
        val jsonData = json.encodeToJsonElement(map)

        jsonData.let {
            logger.i("received json: $it")
            deeplinkService.tryHandleDeeplink(it)
        }
    }

    private val Intent?.isBranchLink: Boolean
        get() = this?.data?.host?.contains(".app.link") == true

    private fun handleBranchLinkOnNew(intent: Intent?) {
        logger.i("Branch link detected")

        intent?.putExtra("branch_force_new_session", true)
        activity?.intent = intent
        Branch.sessionBuilder(activity)
            .withCallback(branchCallback)
            .reInit()
    }

    private fun handleBranchLink(intent: Intent?) {
        Branch.sessionBuilder(activity)
            .withCallback(branchCallback)
            .withData(intent?.data)
            .init()
    }

    private val branchCallback =
        Branch.BranchUniversalReferralInitListener { branchUniversalObject, linkProperties, error ->
            if (error != null) {
                branchLogger.e("branch init failed. Caused by -" + error.message)
            } else {
                branchLogger.i("branch init complete!")
                if (branchUniversalObject != null) {
                    branchLogger.i("title " + branchUniversalObject.title)
                    branchLogger.i(
                        "CanonicalIdentifier " + branchUniversalObject.canonicalIdentifier
                    )

                    val metaDataJson = branchUniversalObject.contentMetadata.convertToJson()
                    branchLogger.i("metadata $metaDataJson")

                    safeRun(errorHandler) {
                        val uri = Uri.parse(branchUniversalObject.canonicalUrl)
                        deeplinkService.tryHandleDeeplink(uri)
                    }
                }
                if (linkProperties != null) {
                    branchLogger.i("Channel " + linkProperties.channel)
                    branchLogger.i("control params " + linkProperties.controlParams)
                }
            }
        }
}
