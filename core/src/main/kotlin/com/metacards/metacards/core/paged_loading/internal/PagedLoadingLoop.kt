package com.metacards.metacards.core.paged_loading.internal

import me.aartikov.sesame.loop.Loop
import me.aartikov.sesame.loop.Next
import me.aartikov.sesame.loop.Reducer
import me.aartikov.sesame.loop.next
import me.aartikov.sesame.loop.nothing
import com.metacards.metacards.core.paged_loading.DataMerger
import com.metacards.metacards.core.paged_loading.Page
import com.metacards.metacards.core.paged_loading.PagedLoading
import com.metacards.metacards.core.paged_loading.PagedLoading.Event
import com.metacards.metacards.core.paged_loading.PagedLoading.LoadingStatus
import com.metacards.metacards.core.paged_loading.PagedLoading.State

internal sealed class Action<out T : Any> {
    data class LoadFirstPage(val clear: Boolean) : Action<Nothing>()
    object ReloadPage : Action<Nothing>()
    object LoadNextPage : Action<Nothing>()
    object LoadPreviousPage : Action<Nothing>()

    data class Cancel(val clear: Boolean) : Action<Nothing>()
    data class MutateData<T : Any>(val transform: (List<T>) -> List<T>) : Action<T>()

    data class FirstPageLoaded<T : Any>(val page: Page<T>) : Action<T>()
    data class PageReloaded<T : Any>(val page: Page<T>) : Action<T>()
    data class NextPageLoaded<T : Any>(val page: Page<T>) : Action<T>()
    data class PreviousPageLoaded<T : Any>(val page: Page<T>) : Action<T>()
    data class LoadingError(val exception: Exception) : Action<Nothing>()
}

internal sealed class Effect<out T : Any> {
    object LoadFirstPage : Effect<Nothing>()
    data class ReloadPage<T : Any>(val loadedData: List<T>) : Effect<T>()
    data class LoadNextPage<T : Any>(val loadedData: List<T>) : Effect<T>()
    data class LoadPreviousPage<T : Any>(val loadedData: List<T>) : Effect<T>()
    object CancelLoading : Effect<Nothing>()
    data class EmitEvent<T : Any>(val event: Event<T>) : Effect<T>()
}

internal typealias PagedLoadingLoop<T> = Loop<State<T>, Action<T>, Effect<T>>

internal class PagedLoadingReducer<T : Any>(
    private val dataMerger: DataMerger<T>
) : Reducer<State<T>, Action<T>, Effect<T>> {

    override fun reduce(state: State<T>, action: Action<T>): Next<State<T>, Effect<T>> =
        when (action) {
            is Action.LoadFirstPage -> {
                when {
                    action.clear -> next(
                        State(
                            loadingStatus = LoadingStatus.LoadingFirstPage,
                            data = null,
                            error = null
                        ),
                        Effect.LoadFirstPage
                    )

                    state.loadingStatus != LoadingStatus.LoadingFirstPage -> next(
                        state.copy(loadingStatus = LoadingStatus.LoadingFirstPage),
                        Effect.LoadFirstPage
                    )

                    else -> nothing()
                }
            }

            is Action.ReloadPage -> {
                when {
                    state.data != null && state.loadingStatus != LoadingStatus.ReloadPage -> next(
                        state.copy(loadingStatus = LoadingStatus.ReloadPage),
                        Effect.ReloadPage(state.data.list)
                    )

                    else -> nothing()
                }
            }

            is Action.LoadNextPage -> {
                when {
                    state.data != null && state.data.hasNextPage
                            && (state.loadingStatus == LoadingStatus.None || state.loadingStatus == LoadingStatus.ReloadPage || state.loadingStatus == LoadingStatus.LoadingPreviousPage) -> {
                        next(
                            state.copy(loadingStatus = LoadingStatus.LoadingNextPage),
                            Effect.LoadNextPage(state.data.list)
                        )
                    }

                    else -> nothing()
                }
            }

            is Action.LoadPreviousPage -> {
                when {
                    state.data != null && state.data.hasPreviousPage
                            && (state.loadingStatus == LoadingStatus.None || state.loadingStatus == LoadingStatus.ReloadPage || state.loadingStatus == LoadingStatus.LoadingNextPage) -> {
                        next(
                            state.copy(loadingStatus = LoadingStatus.LoadingPreviousPage),
                            Effect.LoadPreviousPage(state.data.list)
                        )
                    }

                    else -> nothing()
                }
            }

            is Action.Cancel -> {
                next(
                    State(
                        loadingStatus = LoadingStatus.None,
                        data = if (action.clear) null else state.data,
                        error = if (action.clear) null else state.error
                    ),
                    Effect.CancelLoading
                )
            }

            is Action.FirstPageLoaded -> {
                next(
                    State(
                        loadingStatus = LoadingStatus.None,
                        data = PagedLoading.Data(
                            list = action.page.data,
                            hasNextPage = action.page.hasNextPage,
                            hasPreviousPage = action.page.hasPreviousPage
                        ),
                        error = null
                    ),
                    Effect.EmitEvent(Event.FirstPageLoaded)
                )
            }

            is Action.PageReloaded -> {
                next(
                    State(
                        loadingStatus = LoadingStatus.None,
                        data = PagedLoading.Data(
                            list = action.page.data,
                            hasNextPage = action.page.hasNextPage,
                            hasPreviousPage = action.page.hasPreviousPage
                        ),
                        error = null
                    ),
                    Effect.EmitEvent(Event.PageReloaded)
                )
            }

            is Action.NextPageLoaded -> {
                next(
                    State(
                        loadingStatus = LoadingStatus.None,
                        data = state.data?.copy(
                            list = dataMerger.mergeToEnd(state.data.list, action.page.data),
                            hasNextPage = action.page.hasNextPage
                        ),
                        error = null
                    ),
                    Effect.EmitEvent(Event.NextPageLoaded)
                )
            }

            is Action.PreviousPageLoaded -> {
                next(
                    State(
                        loadingStatus = LoadingStatus.None,
                        data = state.data?.copy(
                            list = dataMerger.mergeToStart(state.data.list, action.page.data),
                            hasPreviousPage = action.page.hasPreviousPage
                        ),
                        error = null
                    ),
                    Effect.EmitEvent(Event.PreviousPageLoaded)
                )
            }

            is Action.LoadingError -> {
                next(
                    state.copy(
                        loadingStatus = LoadingStatus.None,
                        error = action.exception
                    ),
                    Effect.EmitEvent(Event.Error(action.exception, state))
                )
            }

            is Action.MutateData -> {
                next(
                    state.copy(
                        data = state.data?.copy(list = action.transform(state.data.list))
                    )
                )
            }
        }
}