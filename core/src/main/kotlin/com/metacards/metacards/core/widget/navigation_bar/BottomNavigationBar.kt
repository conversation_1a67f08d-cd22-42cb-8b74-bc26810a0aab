package com.metacards.metacards.core.widget.navigation_bar

import androidx.compose.animation.animateColorAsState
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.Surface
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInRoot
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.message.ui.noOverlapByMessage
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.widget.WidgetPosition

@Composable
fun BottomNavigationBar(
    items: List<NavigationItem>,
    onItemSelected: (NavigationPage) -> Unit,
    onFirstIconMeasured: (WidgetPosition) -> Unit,
    modifier: Modifier = Modifier
) {
    val firstPositionModifier = Modifier
        .onGloballyPositioned { layoutCoordinates ->
            val positionInRoot = layoutCoordinates.positionInRoot()
            onFirstIconMeasured(
                WidgetPosition(
                    xOffset = positionInRoot.x,
                    yOffset = positionInRoot.y,
                    layoutCoordinates.size
                )
            )
        }
    Box(
        modifier = modifier.noOverlapByMessage(),
        contentAlignment = Alignment.Center
    ) {
        Surface(
            modifier = Modifier.padding(bottom = 12.dp),
            elevation = 0.dp,
            shape = RoundedCornerShape(32.dp),
            color = CustomTheme.colors.background.tabs
        ) {
            val contentPadding = PaddingValues(horizontal = 32.dp, vertical = 16.dp)
            Row(
                modifier = Modifier.padding(contentPadding),
                horizontalArrangement = Arrangement.spacedBy(28.dp),
            ) {
                items.forEachIndexed { i, item ->
                    BottomNavigationTab(
                        item = item,
                        onItemSelected = { onItemSelected(item.page) },
                        modifier = if (i == 0) firstPositionModifier else Modifier
                    )
                }
            }
        }
    }
}

@Composable
private fun BottomNavigationTab(
    item: NavigationItem,
    onItemSelected: () -> Unit,
    modifier: Modifier = Modifier
) {
    val tint =
        if (item.isSelected) CustomTheme.colors.icons.secondary else CustomTheme.colors.icons.noActive
    val animatedTint by animateColorAsState(targetValue = tint, label = "BottomNavigationTab")

    Icon(
        modifier = modifier
            .size(24.dp)
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = rememberRipple(bounded = false),
                onClick = onItemSelected
            ),
        painter = painterResource(id = item.page.getIconResource()),
        contentDescription = null,
        tint = animatedTint
    )
}

@Preview
@Composable
fun BottomNavigationBarPreview() {
    BottomNavigationBar(
        items = listOf(
            NavigationItem(NavigationPage.Home, true),
            NavigationItem(NavigationPage.Journal, false),
            NavigationItem(NavigationPage.Showcase, false),
            NavigationItem(NavigationPage.Account, false),
        ),
        onItemSelected = { },
        onFirstIconMeasured = {}
    )
}

@Preview
@Composable
fun BottomNavigationTabSelectedPreview() {
    BottomNavigationTab(
        item = NavigationItem(NavigationPage.Home, true),
        onItemSelected = {}
    )
}

@Preview
@Composable
fun BottomNavigationTabUnselectedPreview() {
    BottomNavigationTab(
        item = NavigationItem(NavigationPage.Home, false),
        onItemSelected = {}
    )
}