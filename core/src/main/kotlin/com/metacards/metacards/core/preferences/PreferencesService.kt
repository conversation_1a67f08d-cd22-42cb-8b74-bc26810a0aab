package com.metacards.metacards.core.preferences

import com.metacards.metacards.core.adv.domain.AdvUserConsentState
import com.metacards.metacards.core.alert.domain.ShakerPopupState
import com.metacards.metacards.core.preferences.models.TutorialState

interface PreferencesService {

    suspend fun getTutorialState(): TutorialState

    suspend fun setTutorialState(state: TutorialState)

    suspend fun getWelcomeScreensLaunchFlag(): Boolean

    suspend fun disableWelcomeScreensLaunchFlag()

    suspend fun getFirstAppLaunchSubscriptionFlag(): Boolean

    suspend fun enableFirstAppLaunchSubscriptionFlag()

    suspend fun getUserSubscriptionToNotifications(): Boolean

    suspend fun setUserSubscriptionToNotifications(isSubscribed: Boolean)

    suspend fun getShakerPopupState(): ShakerPopupState

    suspend fun setShakerPopupState(state: ShakerPopupState)

    suspend fun getCameraPermissionDialogViewedFlag(): Boolean

    suspend fun setCameraPermissionDialogViewedFlag()

    suspend fun getAuthScreenLaunchFlag(): Boolean

    suspend fun disableAuthScreenLaunchFlag()

    suspend fun getAdvUserConsentState(): AdvUserConsentState

    suspend fun setAdvUserConsentState(advUserConsentState: AdvUserConsentState)

    suspend fun getSubscribedPushTimezone(): Int?

    suspend fun setSubscribedPushTimezone(timezone: Int?)

    suspend fun clear()
}
