package com.metacards.metacards.core.settings

import android.content.SharedPreferences

interface Settings {

    suspend fun getString(key: String): String?

    suspend fun getLong(key: String): Long?

    suspend fun getBoolean(key: String): Boolean?

    suspend fun putString(key: String, value: String)

    suspend fun putLong(key: String, value: Long)

    suspend fun putBoolean(key: String, value: Boolean)

    suspend fun remove(key: String)

    fun registerListener(listener: SharedPreferences.OnSharedPreferenceChangeListener)

    fun unregisterListener(listener: SharedPreferences.OnSharedPreferenceChangeListener)
}