package com.metacards.metacards.core.theme.material

import androidx.compose.material.Typography
import com.metacards.metacards.core.theme.custom.CustomTheme

val TypographyMaterial = Typography(
    body1 = CustomTheme.typography.body.primary,
    body2 = CustomTheme.typography.body.secondary,
    button = CustomTheme.typography.button.primary,
    h1 = CustomTheme.typography.heading.primary,
    h2 = CustomTheme.typography.heading.secondary,
    h3 = CustomTheme.typography.heading.medium,
    h4 = CustomTheme.typography.heading.small,
    caption = CustomTheme.typography.caption.small,
    subtitle1 = CustomTheme.typography.caption.bigSemiBold,
    subtitle2 = CustomTheme.typography.caption.medium
)