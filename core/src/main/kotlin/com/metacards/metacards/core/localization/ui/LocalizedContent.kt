package com.metacards.metacards.core.localization.ui

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.ui.platform.LocalContext
import com.metacards.metacards.core.localization.domain.AppLanguage
import dev.icerock.moko.resources.desc.StringDesc

val LocalAppLanguage = compositionLocalOf { AppLanguage.getDefaultAppLanguage() }

@Composable
fun LocalizedContent(
    appLanguage: AppLanguage,
    content: @Composable () -> Unit
) {
    CompositionLocalProvider(LocalAppLanguage provides appLanguage) {
        content()
    }
}

@Composable
fun StringDesc.localizedByLocal(): String {
    return getLocalizedValue(
        context = LocalContext.current,
        appLanguage = LocalAppLanguage.current
    )
}

@Composable
fun StringDesc.localizedByLocalNullable(): String? {
    return getLocalizedValue(
        context = LocalContext.current,
        appLanguage = LocalAppLanguage.current
    ).takeIf { it != LocalizableString.NOT_FOUND }
}

fun StringDesc.toStringByLocal(context: Context): String {
    return getLocalizedValue(
        context = context,
        appLanguage = AppLanguage.getDefaultAppLanguage()
    )
}

fun StringDesc.getLocalizedValue(context: Context, appLanguage: AppLanguage): String {
    return when (this) {
        is LocalizableString -> toString(appLanguage)
        is LocalizableStringWithFallback -> toString(context, appLanguage)
        else -> toString(context)
    }
}