package com.metacards.metacards.core.theme.custom

import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.sp

data class CustomTypography(
    val heading: Heading = Heading(),
    val button: Button = Button(),
    val body: Body = Body(),
    val caption: Caption = Caption(),
    val quote: Quote = Quote()
) {

    data class Heading(
        val primary: TextStyle = TextStyle(
            fontSize = 34.sp,
            fontWeight = FontWeight(600)
        ),
        val secondary: TextStyle = TextStyle(
            fontSize = 24.sp,
            fontWeight = FontWeight(500)
        ),
        val additional: TextStyle = TextStyle(
            fontSize = 19.sp,
            fontWeight = FontWeight(500)
        ),
        val medium: TextStyle = TextStyle(
            fontSize = 17.sp,
            fontWeight = FontWeight(500)
        ),
        val small: TextStyle = TextStyle(
            fontSize = 15.sp,
            fontWeight = FontWeight(600)
        ),
    )

    data class Button(
        val primary: TextStyle = TextStyle(
            fontSize = 17.sp,
            fontWeight = FontWeight(500)
        ),
        val small: TextStyle = TextStyle(
            fontSize = 13.sp,
            fontWeight = FontWeight(500)
        ),
    )

    data class Body(
        val primary: TextStyle = TextStyle(
            fontSize = 15.sp,
            fontWeight = FontWeight(400)
        ),
        val secondary: TextStyle = TextStyle(
            fontSize = 15.sp,
            fontWeight = FontWeight(500)
        ),
        val secondaryUnderline: TextStyle = TextStyle(
            fontSize = 15.sp,
            fontWeight = FontWeight(500),
            textDecoration = TextDecoration.Underline
        )
    )

    data class Caption(
        val large: TextStyle = TextStyle(
            fontSize = 24.sp,
            fontWeight = FontWeight(300)
        ),
        val bigSemiBold: TextStyle = TextStyle(
            fontSize = 13.sp,
            fontWeight = FontWeight(600)
        ),
        val medium: TextStyle = TextStyle(
            fontSize = 13.sp,
            fontWeight = FontWeight(500)
        ),
        val small: TextStyle = TextStyle(
            fontSize = 12.sp,
            fontWeight = FontWeight(400)
        ),
    )

    data class Quote(
        val primary: TextStyle = TextStyle(
            fontFamily = NotoFontFamily,
            fontWeight = FontWeight(200),
            fontSize = 24.sp,
            lineHeight = 29.sp
        )
    )
}