package com.metacards.metacards.core.network.firestore

import com.google.firebase.firestore.CollectionReference
import com.google.firebase.firestore.DocumentReference
import com.google.firebase.firestore.DocumentSnapshot
import com.google.firebase.firestore.Query
import com.google.firebase.firestore.QuerySnapshot
import com.metacards.metacards.core.utils.LoadableState
import kotlinx.coroutines.flow.SharedFlow

fun CollectionReference.getFlow(host: FirestoreListenerHost): SharedFlow<QuerySnapshot> {
    val listener = FirestoreFlowListener<QuerySnapshot>()
    val registration = addSnapshotListener(listener)
    host.addListener(registration)

    return listener.sharedFlow
}

fun CollectionReference.getFlowWithLoadable(host: FirestoreListenerHost): SharedFlow<LoadableState<QuerySnapshot>> {
    val listener = FireStoreLoadableFlowListener<QuerySnapshot>()
    val registration = addSnapshotListener(listener)
    host.addListener(registration)

    return listener.sharedFlow
}

fun Query.getFlowWithLoadable(host: FirestoreListenerHost): SharedFlow<LoadableState<QuerySnapshot>> {
    val listener = FireStoreLoadableFlowListener<QuerySnapshot>()
    val registration = addSnapshotListener(listener)
    host.addListener(registration)

    return listener.sharedFlow
}

fun DocumentReference.getFlow(host: FirestoreListenerHost): SharedFlow<DocumentSnapshot> {
    val listener = FirestoreFlowListener<DocumentSnapshot>()
    val registration = addSnapshotListener(listener)
    host.addListener(registration)

    return listener.sharedFlow
}

fun DocumentReference.getFlowWithLoadable(host: FirestoreListenerHost): SharedFlow<LoadableState<DocumentSnapshot>> {
    val listener = FireStoreLoadableFlowListener<DocumentSnapshot>()
    val registration = addSnapshotListener(listener)
    host.addListener(registration)

    return listener.sharedFlow
}