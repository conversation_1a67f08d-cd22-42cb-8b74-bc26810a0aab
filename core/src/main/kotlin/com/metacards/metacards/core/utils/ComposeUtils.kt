package com.metacards.metacards.core.utils

import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTransformGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.draw.scale
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.drawscope.Fill
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.lerp
import androidx.compose.ui.input.pointer.PointerEventPass
import androidx.compose.ui.input.pointer.PointerInputChange
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.platform.debugInspectorInfo
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay
import ru.mobileup.kmm_form_validation.options.TextTransformation
import kotlin.math.min

fun Modifier.clickable(
    boundedRipple: Boolean = true,
    enabled: Boolean = true,
    onClick: () -> Unit
) = composed {
    this.then(
        clickable(
            interactionSource = remember { MutableInteractionSource() },
            indication = rememberRipple(bounded = boundedRipple),
            enabled = enabled,
            onClick = onClick
        )
    )
}

fun LazyListState.isScrolledToEnd() = layoutInfo.visibleItemsInfo.lastOrNull()?.index == layoutInfo.totalItemsCount - 1

/**
 * A [Modifier] that draws a border around elements that are recomposing. The border increases in
 * size and interpolates from red to green as more recompositions occur before a timeout.
 */
@Stable
fun Modifier.recomposeHighlighter(): Modifier = this.then(recomposeModifier)

// Use a single instance + @Stable to ensure that recompositions can enable skipping optimizations
// Modifier.composed will still remember unique data per call site.
private val recomposeModifier =
    Modifier.composed(inspectorInfo = debugInspectorInfo { name = "recomposeHighlighter" }) {
        // The total number of compositions that have occurred. We're not using a State<> here be
        // able to read/write the value without invalidating (which would cause infinite
        // recomposition).
        val totalCompositions = remember { arrayOf(0L) }
        totalCompositions[0]++

        // The value of totalCompositions at the last timeout.
        val totalCompositionsAtLastTimeout = remember { mutableLongStateOf(0L) }

        // Start the timeout, and reset everytime there's a recomposition. (Using totalCompositions
        // as the key is really just to cause the timer to restart every composition).
        LaunchedEffect(totalCompositions[0]) {
            delay(3000)
            totalCompositionsAtLastTimeout.longValue = totalCompositions[0]
        }

        Modifier.drawWithCache {
            onDrawWithContent {
                // Draw actual content.
                drawContent()

                // Below is to draw the highlight, if necessary. A lot of the logic is copied from
                // Modifier.border
                val numCompositionsSinceTimeout =
                    totalCompositions[0] - totalCompositionsAtLastTimeout.longValue

                val hasValidBorderParams = size.minDimension > 0f
                if (!hasValidBorderParams || numCompositionsSinceTimeout <= 0) {
                    return@onDrawWithContent
                }

                val (color, strokeWidthPx) =
                    when (numCompositionsSinceTimeout) {
                        // We need at least one composition to draw, so draw the smallest border
                        // color in blue.
                        1L -> Color.Blue to 1f
                        // 2 compositions is _probably_ okay.
                        2L -> Color.Green to 2.dp.toPx()
                        // 3 or more compositions before timeout may indicate an issue. lerp the
                        // color from yellow to red, and continually increase the border size.
                        else -> {
                            lerp(
                                Color.Yellow.copy(alpha = 0.8f),
                                Color.Red.copy(alpha = 0.5f),
                                min(1f, (numCompositionsSinceTimeout - 1).toFloat() / 100f)
                            ) to numCompositionsSinceTimeout.toInt().dp.toPx()
                        }
                    }

                val halfStroke = strokeWidthPx / 2
                val topLeft = Offset(halfStroke, halfStroke)
                val borderSize = Size(size.width - strokeWidthPx, size.height - strokeWidthPx)

                val fillArea = (strokeWidthPx * 2) > size.minDimension
                val rectTopLeft = if (fillArea) Offset.Zero else topLeft
                val size = if (fillArea) size else borderSize
                val style = if (fillArea) Fill else Stroke(strokeWidthPx)

                drawRect(
                    brush = SolidColor(color),
                    topLeft = rectTopLeft,
                    size = size,
                    style = style
                )
            }
        }
    }

private const val ZOOM_MIN_SCALE = 1F
private const val ZOOM_MAX_SCALE = 3F

/**
 * Adds zoom and drag-when-zoomed ability to composable
 * @param pagerScrollEnabledState optional scroll state provider for view pagers
 */
fun Modifier.zoomAndDrag(
    minScale: Float = ZOOM_MIN_SCALE,
    maxScale: Float = ZOOM_MAX_SCALE,
    pagerScrollEnabledState: MutableState<Boolean>? = null
): Modifier = composed {
    var scale by remember { mutableFloatStateOf(minScale) }
    var offset by remember { mutableStateOf(Offset.Zero) }

    val modifier = pointerInput(Unit) {
        detectTransformGestures(
            panZoomLock = true,
            onGesture = { _, pan, gestureZoom, _ ->
                scale = maxOf(minScale, minOf(scale * gestureZoom, maxScale))

                val maxX = (size.width * (scale - 1)) / 2
                val minX = -maxX
                val offsetX = maxOf(minX, minOf(maxX, offset.x + pan.x))
                val maxY = (size.height * (scale - 1)) / 2
                val minY = -maxY
                val offsetY = maxOf(minY, minOf(maxY, offset.y + pan.y))

                offset = offset.copy(x = offsetX, y = offsetY)
            }
        )
    }.graphicsLayer(
        scaleX = scale,
        scaleY = scale,
        translationX = offset.x,
        translationY = offset.y,
    )
    pagerScrollEnabledState?.value = scale == minScale

    return@composed this.then(modifier)
}

object UserInputTransformation : TextTransformation {
    override fun transform(text: String): String {
        return text.replace('\n', ' ')
    }
}

fun String.applyStyleForSubstring(
    subString: String,
    style: TextStyle
) = buildAnnotatedString {
    append(this@applyStyleForSubstring)
    if (indexOf(subString) == -1) return@buildAnnotatedString
    addStyle(
        style = style.toSpanStyle(),
        start = indexOf(subString),
        end = indexOf(subString) + subString.length
    )
}

fun Modifier.mirrorForRtl() = composed {
    when (LocalLayoutDirection.current) {
        LayoutDirection.Ltr -> this
        LayoutDirection.Rtl -> {
            this.scale(scaleX = -1f, scaleY = 1f)
        }
    }
}

/**
 * Включение(или выключение) жестов для пользователя компонента и их child'ов
 */
fun Modifier.userGesturesEnabled(enabled: Boolean) =
    if (!enabled) {
        pointerInput(Unit) {
            awaitPointerEventScope {
                // ждем все эвенты
                while (true) {
                    awaitPointerEvent(pass = PointerEventPass.Initial)
                        .changes
                        .forEach(PointerInputChange::consume)
                }
            }
        }
    } else {
        this
    }
