package com.metacards.metacards.core.utils

import com.google.firebase.Timestamp
import kotlinx.datetime.toJavaLocalDateTime
import kotlinx.datetime.toLocalDateTime
import kotlinx.serialization.KSerializer
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.Date

object TimestampAsStringSerializer : KSerializer<Timestamp> {
    override val descriptor: SerialDescriptor =
        PrimitiveSerialDescriptor("Timestamp", PrimitiveKind.LONG)

    override fun serialize(encoder: Encoder, value: Timestamp) =
        encoder.encodeString(
            LocalDateTime.ofEpochSecond(
                value.seconds,
                value.nanoseconds,
                ZoneOffset.UTC
            ).toString() + "Z"
        )

    override fun deserialize(decoder: Decoder): Timestamp =
        Timestamp(
            Date(
                decoder
                    .decodeString()
                    .toLocalDateTime()
                    .toJavaLocalDateTime()
                    .toEpochSecond(ZoneOffset.UTC)
            )
        )
}
