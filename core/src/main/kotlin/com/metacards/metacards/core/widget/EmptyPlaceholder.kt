package com.metacards.metacards.core.widget

import androidx.annotation.DrawableRes
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.R
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme

@Composable
fun EmptyPlaceholder(
    title: String,
    message: String,
    modifier: Modifier = Modifier,
    @DrawableRes iconRes: Int = R.drawable.ic_64_galactic,
    buttons: (@Composable ColumnScope.() -> Unit)? = null
) {
    Box(modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .matchParentSize()
                .verticalScroll(rememberScrollState()), // required to use it with pull-to-refresh
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            Icon(
                painter = painterResource(iconRes),
                contentDescription = null,
                tint = CustomTheme.colors.icons.disabled
            )

            Text(
                text = title,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .padding(top = 24.dp)
                    .fillMaxWidth(),
                style = CustomTheme.typography.heading.medium,
                color = CustomTheme.colors.text.primary
            )

            Text(
                text = message,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .padding(top = 8.dp)
                    .fillMaxWidth(),
                style = CustomTheme.typography.body.primary,
                color = CustomTheme.colors.text.primary
            )

            if (buttons != null) {
                Column(
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                        .padding(top = 16.dp)
                        .padding(bottom = 76.dp)
                ) {
                    buttons()
                }
            }
        }
    }
}

@Preview
@Composable
fun EmptyPlaceholderPreview() {
    AppTheme {
        EmptyPlaceholder(
            title = "Title",
            message = "Message"
        )
    }
}