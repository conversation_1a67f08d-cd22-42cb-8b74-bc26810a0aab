package com.metacards.metacards.core.widget

import androidx.compose.foundation.layout.Box
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.ErrorMessage
import com.metacards.metacards.core.utils.LoadableState

/**
 * Displays ([LoadableState]) with pull-to-refresh functionality.
 *
 * Note: a value of refreshing in [content] is true only when data is refreshing and swipe gesture didn't occur.
 */
@OptIn(ExperimentalMaterialApi::class)
@Composable
fun <T : Any> PullRefreshLceWidget(
    state: LoadableState<T>,
    onRefresh: () -> Unit,
    onRetryClick: () -> Unit,
    modifier: Modifier = Modifier,
    pullRefreshEnabled: Boolean = true,
    loadingProgress: @Composable () -> Unit = { FullscreenCircularProgress(modifier) },
    errorPlaceholder: @Composable (message: ErrorMessage, onRetry: () -> Unit) -> Unit = { message, onRetry ->
        ErrorPlaceholder(message, onRetry, modifier)
    },
    content: @Composable (data: T, refreshing: Boolean) -> Unit
) {

    LceWidget(
        state = state,
        onRetryClick = onRetryClick,
        modifier = modifier,
        loadingProgress = loadingProgress,
        errorPlaceholder = errorPlaceholder
    ) { data, refreshing ->
        var pullGestureOccurred by remember { mutableStateOf(false) }

        LaunchedEffect(refreshing) {
            if (!refreshing) pullGestureOccurred = false
        }

        val pullRefreshState = rememberPullRefreshState(
            refreshing = pullGestureOccurred && refreshing,
            onRefresh = {
                pullGestureOccurred = true
                onRefresh()
            }
        )

        Box(modifier.pullRefresh(pullRefreshState, pullRefreshEnabled)) {
            content(data, refreshing && !pullGestureOccurred)

            PullRefreshIndicator(
                modifier = Modifier.align(Alignment.TopCenter),
                refreshing = refreshing,
                state = pullRefreshState,
                contentColor = CustomTheme.colors.icons.primary,
                backgroundColor = CustomTheme.colors.background.primary.copy(alpha = 1.0f)
            )
        }
    }
}
