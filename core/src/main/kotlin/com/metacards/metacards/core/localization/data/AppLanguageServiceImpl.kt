package com.metacards.metacards.core.localization.data

import androidx.appcompat.app.AppCompatDelegate
import androidx.core.os.LocaleListCompat
import com.metacards.metacards.core.localization.domain.AppLanguage
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.flow.MutableStateFlow
import java.util.Locale

class AppLanguageServiceImpl(
    private val localizationStorage: LocalizationStorage,
) : AppLanguageService {

    private val listeners: MutableSet<AppLanguageUpdateListener> = mutableSetOf()

    override val currentAppLanguage: MutableStateFlow<AppLanguage> = MutableStateFlow(getLanguage())

    override suspend fun onAppStart() {
        val language = getLanguage()
        setLanguage(language, true)
    }

    override suspend fun setLanguage(appLanguage: AppLanguage, appStart: Boolean) {
        if (currentAppLanguage.value == appLanguage && !appStart) return

        val locale = getLocaleByLanguageTag(appLanguage.tag)

        localizationStorage.setAppLanguage(appLanguage)

        currentAppLanguage.value = appLanguage
        listeners.forEach { it.onAppLanguageChanged(appLanguage) }

        Locale.setDefault(locale)
        StringDesc.localeType = StringDesc.LocaleType.Custom(locale.toLanguageTag())

        AppCompatDelegate.setApplicationLocales(LocaleListCompat.create(locale))
    }

    override fun getLanguage(): AppLanguage {
        val locale = AppCompatDelegate.getApplicationLocales().takeIf { !it.isEmpty }?.get(0)
        val language = locale?.let { getLanguageByLocale(it) }
        return language ?: localizationStorage.getAppLanguage() ?: getLanguageByLocale(Locale.getDefault())
    }

    override fun addListener(listener: AppLanguageUpdateListener) {
        listeners.add(listener)
    }

    override fun removeListener(listener: AppLanguageUpdateListener) {
        listeners.remove(listener)
    }

    private fun getLanguageByLocale(locale: Locale): AppLanguage {
        return when (locale.language) {
            Locale(LOCALE_TAG_RU).language -> AppLanguage.RUS
            Locale(LOCALE_TAG_EN).language -> AppLanguage.ENG
            Locale(LOCALE_TAG_ES).language -> AppLanguage.ESP
            Locale(LOCALE_CODE_UKR).language -> AppLanguage.UKR
            Locale(LOCALE_CODE_KAZ).language -> AppLanguage.KAZ
            Locale(LOCALE_TAG_FR).language -> AppLanguage.FRA
            Locale(LOCALE_TAG_ARAB).language -> AppLanguage.ARB
            else -> AppLanguage.getDefaultAppLanguage()
        }
    }

    private fun getLocaleByLanguageTag(languageTag: String): Locale {
        val languageCode = when (languageTag) {
            LOCALE_TAG_RU -> LOCALE_TAG_RU
            LOCALE_TAG_EN -> LOCALE_TAG_EN
            LOCALE_TAG_ES -> LOCALE_TAG_ES
            LOCALE_TAG_UA -> LOCALE_CODE_UKR
            LOCALE_TAG_KZ -> LOCALE_CODE_KAZ
            LOCALE_TAG_FR -> LOCALE_TAG_FR
            LOCALE_TAG_ARAB -> LOCALE_TAG_ARAB
            else -> LOCALE_TAG_EN
        }

        return Locale(languageCode)
    }

    companion object {

        private const val LOCALE_TAG_RU = "ru"
        private const val LOCALE_TAG_EN = "en"
        private const val LOCALE_TAG_ES = "es"
        private const val LOCALE_TAG_UA = "ukr"
        private const val LOCALE_TAG_KZ = "kaz"
        private const val LOCALE_TAG_FR = "fr"
        private const val LOCALE_TAG_ARAB = "ar"

        private const val LOCALE_CODE_UKR = "uk"
        private const val LOCALE_CODE_KAZ = "kk"
    }
}
