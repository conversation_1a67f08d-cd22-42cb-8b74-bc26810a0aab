package com.metacards.metacards.core

import android.content.Context
import android.content.SharedPreferences
import androidx.annotation.StringRes
import androidx.security.crypto.EncryptedSharedPreferences
import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.activity.ActivityProvider
import com.metacards.metacards.core.adv.data.AdvUserConsentRepository
import com.metacards.metacards.core.adv.data.AdvUserConsentRepositoryImpl
import com.metacards.metacards.core.adv.domain.YandexAdvHelper
import com.metacards.metacards.core.adv.ui.AdvUserConsentComponent
import com.metacards.metacards.core.adv.ui.RealAdvUserConsentComponent
import com.metacards.metacards.core.app.Cleaner
import com.metacards.metacards.core.app.LogoutCleaner
import com.metacards.metacards.core.deeplink.DeeplinkDelegate
import com.metacards.metacards.core.deeplink.DeeplinkDelegateImpl
import com.metacards.metacards.core.device.DeviceInfoService
import com.metacards.metacards.core.device.DeviceInfoServiceImpl
import com.metacards.metacards.core.dialog.default_component.DefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.RealDefaultDialogComponent
import com.metacards.metacards.core.dialog.default_component.model.DialogData
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.external_apps.ExternalAppService
import com.metacards.metacards.core.external_apps.ExternalAppServiceImpl
import com.metacards.metacards.core.functions.CloudFunctionsService
import com.metacards.metacards.core.image.CardImageGenerator
import com.metacards.metacards.core.image.CourseResultImageGenerator
import com.metacards.metacards.core.localization.data.AppLanguageService
import com.metacards.metacards.core.localization.data.AppLanguageServiceImpl
import com.metacards.metacards.core.localization.data.LocalizationStorage
import com.metacards.metacards.core.message.data.MessageService
import com.metacards.metacards.core.message.data.MessageServiceImpl
import com.metacards.metacards.core.message.ui.MessageComponent
import com.metacards.metacards.core.message.ui.RealMessageComponent
import com.metacards.metacards.core.network.firestore.FirestoreService
import com.metacards.metacards.core.network.firestore.FirestoreStorage
import com.metacards.metacards.core.notification.NotificationService
import com.metacards.metacards.core.notification.NotificationServiceImpl
import com.metacards.metacards.core.notification.PushNotifier
import com.metacards.metacards.core.permissions.PermissionService
import com.metacards.metacards.core.preferences.AppLaunchCounterService
import com.metacards.metacards.core.preferences.AppLaunchCounterServiceImpl
import com.metacards.metacards.core.preferences.PreferencesService
import com.metacards.metacards.core.preferences.PreferencesServiceImpl
import com.metacards.metacards.core.settings.AndroidSettingsFactory
import com.metacards.metacards.core.settings.SettingsFactory
import com.metacards.metacards.core.utils.KoinConsts
import com.metacards.metacards.core.web_view.RealWebViewComponent
import com.metacards.metacards.core.web_view.WebViewComponent
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.Dispatchers
import org.koin.core.component.get
import org.koin.core.qualifier.named
import org.koin.dsl.binds
import org.koin.dsl.module

fun coreModule() = module {
    val userPrefsKey = "user_preferences_key"
    val userPrefsName = "user_preferences_name"
    val appPrefsName = "app_preferences_name"

    single(named(userPrefsName)) {
        createEncryptedPreferences(
            get(),
            prefsName = userPrefsName,
            prefsKey = userPrefsKey
        )
        createEncryptedPreferences(
            get(),
            prefsName = userPrefsName,
            prefsKey = userPrefsKey
        )
    }
    single(named(appPrefsName)) { createPreferences(get(), appPrefsName) }
    single { ActivityProvider() }
    single<MessageService> { MessageServiceImpl() }
    single {
        PreferencesServiceImpl(
            get(named(appPrefsName)),
            get(named(userPrefsName)),
        )
    } binds arrayOf(PreferencesService::class, LocalizationStorage::class)
    single<AppLaunchCounterService> { AppLaunchCounterServiceImpl(get(named(appPrefsName))) }
    single { ErrorHandler(get()) }
    single(createdAtStart = true) { PermissionService(get(), get()) }
    single { FirestoreService() }
    single { FirestoreStorage() }
    single(named(KoinConsts.DefaultIoDispatcherName)) { Dispatchers.IO }
    single<ExternalAppService> { ExternalAppServiceImpl(get()) }
    single { PushNotifier(get(), get()) }
    single<AppLanguageService> { AppLanguageServiceImpl(get()) }
    factory<DeeplinkDelegate> {
        DeeplinkDelegateImpl(
            get(named(KoinConsts.PushJsonName)),
            get(),
            get(),
            get()
        )
    }
    single { Cleaner(get(), get()) } binds arrayOf(LogoutCleaner::class)
    single<NotificationService>(createdAtStart = true) { NotificationServiceImpl(get(), get(), get(), get()) }
    single { CloudFunctionsService() }
    single<SettingsFactory> { AndroidSettingsFactory(get(), Dispatchers.IO) }
    single<DeviceInfoService> { DeviceInfoServiceImpl(get()) }
    single(createdAtStart = true) { YandexAdvHelper(get(), get(), get(), get()) }
    single<AdvUserConsentRepository> { AdvUserConsentRepositoryImpl(get()) }
    single<CardImageGenerator> { CardImageGenerator(get()) }
    single<CourseResultImageGenerator> { CourseResultImageGenerator(get()) }
}

fun ComponentFactory.createMessageComponent(
    componentContext: ComponentContext
): MessageComponent {
    return RealMessageComponent(componentContext, get())
}

fun ComponentFactory.createDefaultDialogComponent(
    componentContext: ComponentContext,
    dialogData: DialogData,
    onDismiss: () -> Unit
): DefaultDialogComponent {
    return RealDefaultDialogComponent(componentContext, dialogData, onDismiss)
}

fun ComponentFactory.createWebViewComponent(
    componentContext: ComponentContext,
    url: String,
    @StringRes title: Int
): WebViewComponent {
    return RealWebViewComponent(componentContext, url, null, title, get())
}

fun ComponentFactory.createWebViewComponent(
    componentContext: ComponentContext,
    url: StringDesc,
    @StringRes title: Int
): WebViewComponent {
    return RealWebViewComponent(componentContext, null, url, title, get())
}

fun ComponentFactory.createAdvUserConsentComponent(
    componentContext: ComponentContext,
    onOutput: (AdvUserConsentComponent.Output) -> Unit
): AdvUserConsentComponent {
    return RealAdvUserConsentComponent(componentContext, get(), get(), get(), get(), onOutput)
}

fun createEncryptedPreferences(
    context: Context,
    prefsKey: String,
    prefsName: String
): SharedPreferences {
    return EncryptedSharedPreferences.create(
        prefsName,
        prefsKey,
        context,
        EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
        EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
    )
}

fun createPreferences(
    context: Context,
    prefsName: String
): SharedPreferences {
    return context.getSharedPreferences(prefsName, Context.MODE_PRIVATE)
}
