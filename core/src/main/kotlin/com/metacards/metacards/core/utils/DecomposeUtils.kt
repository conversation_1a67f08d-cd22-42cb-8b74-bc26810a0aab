package com.metacards.metacards.core.utils

import android.os.Parcelable
import androidx.compose.animation.core.tween
import androidx.compose.runtime.State
import com.arkivanov.decompose.Child
import com.arkivanov.decompose.ComponentContext
import com.arkivanov.decompose.FaultyDecomposeApi
import com.arkivanov.decompose.extensions.compose.jetpack.stack.animation.StackAnimation
import com.arkivanov.decompose.extensions.compose.jetpack.stack.animation.fade
import com.arkivanov.decompose.extensions.compose.jetpack.stack.animation.stackAnimation
import com.arkivanov.decompose.router.slot.ChildSlot
import com.arkivanov.decompose.router.stack.ChildStack
import com.arkivanov.decompose.router.stack.StackNavigator
import com.arkivanov.decompose.value.Value
import com.arkivanov.essenty.instancekeeper.InstanceKeeper
import com.arkivanov.essenty.lifecycle.Lifecycle
import com.arkivanov.essenty.lifecycle.doOnCreate
import com.arkivanov.essenty.lifecycle.doOnDestroy
import com.arkivanov.essenty.statekeeper.StateKeeperOwner
import com.arkivanov.essenty.statekeeper.consume
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.stateIn
import me.aartikov.replica.decompose.coroutineScope
import kotlin.reflect.KProperty

/**
 * Creates a [ChildSlot] with a single active component. Should be used to create a overlay for Jetpack Compose preview.
 */
fun <T : Any> createFakeChildSlot(instance: T?): ChildSlot<*, T> {
    val child = instance?.let {
        Child.Created(
            configuration = "configuration",
            instance = instance
        )
    }

    return ChildSlot(child = child)
}

/**
 * Creates a [ChildStack] with a single active component. Should be used to create a stack for Jetpack Compose preview.
 */
fun <T : Any> createFakeChildStack(instance: T): ChildStack<*, T> {
    return ChildStack(
        configuration = "<fake>",
        instance = instance
    )
}

fun <T : Any> createFakeChildStackStateFlow(instance: T): StateFlow<ChildStack<*, T>> {
    return MutableStateFlow(createFakeChildStack(instance))
}

/**
 * Converts [Value] from Decompose to [State] from Jetpack Compose.
 */
fun <T : Any> Value<T>.toStateFlow(lifecycle: Lifecycle): StateFlow<T> {
    val state: MutableStateFlow<T> = MutableStateFlow(this.value)

    if (lifecycle.state != Lifecycle.State.DESTROYED) {
        val observer = { value: T -> state.value = value }
        subscribe(observer)
        lifecycle.doOnDestroy {
            unsubscribe(observer)
        }
    }

    return state
}

/**
 * Creates a coroutine scope tied to Decompose lifecycle. A scope is canceled when a component is destroyed.
 */
val ComponentContext.componentScope: CoroutineScope
    get() {
        if (this.lifecycle.state == Lifecycle.State.DESTROYED) {
            return lifecycle.coroutineScope() // уже будет canceled к этому моменту
        }

        val scope = (instanceKeeper.get(ComponentScopeKey) as? CoroutineScopeWrapper)?.scope
        if (scope != null) return scope

        val newScope = lifecycle.coroutineScope()
        instanceKeeper.put(ComponentScopeKey, CoroutineScopeWrapper(newScope))
        return newScope
    }

private object ComponentScopeKey

private class CoroutineScopeWrapper(val scope: CoroutineScope) : InstanceKeeper.Instance {

    override fun onDestroy() {
        // nothing
    }
}

/**
 * A helper function to save and restore component state.
 */
inline fun <reified T : Parcelable> StateKeeperOwner.persistent(
    key: String = "PersistentState",
    noinline save: () -> T,
    restore: (T) -> Unit,
) {
    stateKeeper.consume<T>(key)?.run(restore)
    stateKeeper.register(key, save)
}

fun <T : Any> Flow<T>.stateIn(
    componentContext: ComponentContext,
    initialValue: T,
    started: SharingStarted = SharingStarted.Eagerly,
) = stateIn(componentContext.componentScope, started, initialValue)

fun <T> Flow<T>.stateInNullable(
    componentContext: ComponentContext,
    initialValue: T,
    started: SharingStarted = SharingStarted.Eagerly,
) = stateIn(componentContext.componentScope, started, initialValue)

/**
 * Similar to bringToFront but searches existing component by value rather than by type.
 */
fun <C : Any> StackNavigator<C>.bringToFrontByValue(configuration: C, onComplete: () -> Unit = {}) {
    navigate(
        transformer = { stack -> stack.filterNot { it == configuration } + configuration },
        onComplete = { _, _ -> onComplete() },
    )
}

open class SuspendDelegate<T>(
    componentContext: ComponentContext,
    errorHandler: ErrorHandler,
    private val defaultValue: T? = null,
    suspendAction: suspend () -> T,
) {

    protected var value: T? = defaultValue

    init {
        componentContext.apply {
            lifecycle.doOnCreate {
                componentScope.safeLaunch(errorHandler) {
                    value = suspendAction()
                }
            }
        }
    }

    open operator fun getValue(thisRef: Any?, property: KProperty<*>): T? {
        return value ?: defaultValue
    }
}

fun <T> ComponentContext.suspendDelegate(
    errorHandler: ErrorHandler,
    defaultValue: T? = null,
    suspendAction: suspend () -> T,
): SuspendDelegate<T> = SuspendDelegate(this, errorHandler, defaultValue, suspendAction)

class SuspendListDelegate<T : Any>(
    componentContext: ComponentContext,
    errorHandler: ErrorHandler,
    private val defaultValue: List<T> = emptyList(),
    suspendAction: suspend () -> List<T>,
) : SuspendDelegate<List<T>>(componentContext, errorHandler, defaultValue, suspendAction) {

    override operator fun getValue(thisRef: Any?, property: KProperty<*>): List<T> {
        return value ?: defaultValue
    }
}

fun <T : Any> ComponentContext.suspendListDelegate(
    errorHandler: ErrorHandler,
    defaultValue: List<T> = emptyList(),
    suspendAction: suspend () -> List<T>,
): SuspendListDelegate<T> = SuspendListDelegate(this, errorHandler, defaultValue, suspendAction)

@OptIn(FaultyDecomposeApi::class)
fun <C : Any, T : Any> defaultTransitionAnimation(): StackAnimation<C, T> =
    stackAnimation(true) { _, _, _ ->
        fade(tween())
    }
