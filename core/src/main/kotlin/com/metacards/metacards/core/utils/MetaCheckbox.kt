package com.metacards.metacards.core.utils

import androidx.compose.material.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import com.metacards.metacards.core.R
import com.metacards.metacards.core.theme.custom.CustomTheme

@Composable
fun MetaCheckbox(checkboxState: Boolean, onCheckboxStateChanged: (Boolean) -> Unit) {
    val iconResource =
        if (checkboxState) R.drawable.ic_24_checkbox_on else R.drawable.ic_24_checkbox_off

    Icon(
        modifier = Modifier
            .clickable(false) { onCheckboxStateChanged(!checkboxState) },
        painter = painterResource(id = iconResource),
        contentDescription = null,
        tint = CustomTheme.colors.icons.primary
    )
}