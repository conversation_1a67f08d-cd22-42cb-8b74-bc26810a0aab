package com.metacards.metacards.core.widget

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.Dp
import com.metacards.metacards.core.theme.custom.CustomTheme

@Composable
fun SkeletonElement(cornerRadius: Dp, modifier: Modifier = Modifier) {
    Surface(
        modifier = modifier,
        shape = RoundedCornerShape(cornerRadius),
        color = CustomTheme.colors.background.primary
    ) {}
}