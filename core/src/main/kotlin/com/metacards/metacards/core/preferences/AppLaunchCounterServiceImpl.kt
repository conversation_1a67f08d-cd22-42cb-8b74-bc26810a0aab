package com.metacards.metacards.core.preferences

import android.content.SharedPreferences
import androidx.core.content.edit
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class AppLaunchCounterServiceImpl(
    private val prefs: SharedPreferences
) : AppLaunchCounterService {

    companion object {
        private const val MAIN_SCREEN_LAUNCH_COUNTER_KEY = "MAIN_SCREEN_LAUNCH_COUNTER_KEY"
    }

    override suspend fun getMainScreenLaunchCounter(): Int = withContext(Dispatchers.IO) {
        prefs.getInt(MAIN_SCREEN_LAUNCH_COUNTER_KEY, 0)
    }

    override suspend fun incrementHomeScreenLaunchCounter() = withContext(Dispatchers.IO) {
        val counter = prefs.getInt(MAIN_SCREEN_LAUNCH_COUNTER_KEY, 0)
        prefs.edit(commit = true) { putInt(MAIN_SCREEN_LAUNCH_COUNTER_KEY, counter + 1) }
    }
}
