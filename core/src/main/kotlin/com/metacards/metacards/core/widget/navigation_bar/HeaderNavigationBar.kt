package com.metacards.metacards.core.widget.navigation_bar

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.R
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme
import dev.icerock.moko.resources.desc.StringDesc
import dev.icerock.moko.resources.desc.desc

@Composable
fun HeaderNavigationBar(
    title: StringDesc,
    modifier: Modifier = Modifier,
    leadingIcon: @Composable RowScope.() -> Unit = {},
    trailingIcon: @Composable RowScope.() -> Unit = {}
) {
    TopNavigationBar(
        title = title,
        modifier = modifier,
        textPadding = PaddingValues(top = 8.dp, bottom = 8.dp, end = 8.dp),
        textStyle = CustomTheme.typography.heading.primary,
        leadingIcon = leadingIcon,
        trailingContent = trailingIcon
    )
}

@Preview
@Composable
fun HeaderNavigationBarPreview() {
    AppTheme {
        HeaderNavigationBar(
            modifier = Modifier.fillMaxWidth(),
            title = "Title".desc(),
            leadingIcon = { },
            trailingIcon = {
                IconNavigationItem(R.drawable.ic_24_share)
                Spacer(modifier = Modifier.width(8.dp))
                BackNavigationItem(R.drawable.ic_24_close)
            }
        )
    }
}