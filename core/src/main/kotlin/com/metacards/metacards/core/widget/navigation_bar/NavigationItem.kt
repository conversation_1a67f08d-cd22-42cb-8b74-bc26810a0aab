package com.metacards.metacards.core.widget.navigation_bar

import androidx.annotation.DrawableRes
import com.metacards.metacards.core.R

data class NavigationItem(
    val page: NavigationPage,
    val isSelected: Boolean = false
) {
    companion object {
        val ALL = listOf(
            NavigationItem(NavigationPage.Home),
            NavigationItem(NavigationPage.Journal),
            NavigationItem(NavigationPage.Showcase),
            NavigationItem(NavigationPage.Account),
        )

        val SELECTED_JOURNAL = listOf(
            NavigationItem(NavigationPage.Home),
            NavigationItem(NavigationPage.Journal, true),
            NavigationItem(NavigationPage.Showcase),
            NavigationItem(NavigationPage.Account),
        )

        val SELECTED_ACCOUNT = listOf(
            NavigationItem(NavigationPage.Home),
            NavigationItem(NavigationPage.Journal),
            NavigationItem(NavigationPage.Showcase),
            NavigationItem(NavigationPage.Account, true),
        )
    }
}

enum class NavigationPage {
    Home,
    Journal,
    Showcase,
    Account;

    @DrawableRes
    fun getIconResource(): Int = when (this) {
        Home -> R.drawable.ic_24_home
        Journal -> R.drawable.ic_24_journal
        Showcase -> R.drawable.ic_24_showcase
        Account -> R.drawable.ic_24_account
    }
}