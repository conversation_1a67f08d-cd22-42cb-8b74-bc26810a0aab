package com.metacards.metacards.core.external_apps

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import androidx.core.content.ContextCompat
import com.metacards.metacards.core.error_handling.ExternalAppNotFoundException

class ExternalAppServiceImpl(
    private val context: Context
) : ExternalAppService {

    override fun openBrowser(uri: Uri) {
        safeActivityLaunch(
            Intent(Intent.ACTION_VIEW, uri).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
        )
    }

    override fun openAppSettings() {
        safeActivityLaunch(
            Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.fromParts("package", context.packageName, null)
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }
        )
    }

    override fun openSubscriptionSettings(subscriptionId: String) {
        val packageName = context.packageName
        val uri =
            Uri.parse("https://play.google.com/store/account/subscriptions?sku=${subscriptionId}&package=${packageName}")
        val intent = Intent(Intent.ACTION_VIEW, uri)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK

        safeActivityLaunch(intent)
    }

    override fun rateApp() {
        val uri = Uri.parse("market://details?id=${context.packageName}")
        val goToMarket = Intent(Intent.ACTION_VIEW, uri)
        goToMarket.flags = Intent.FLAG_ACTIVITY_NEW_TASK

        if (goToMarket.resolveActivity(context.packageManager) != null) {
            safeActivityLaunch(goToMarket)
        } else {
            val intent = Intent(
                Intent.ACTION_VIEW,
                Uri.parse("https://play.google.com/store/apps/details?id=${context.packageName}")
            )
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK

            safeActivityLaunch(intent)
        }
    }

    override fun openShareSheetForText(text: String) {
        val sendIntent: Intent = Intent().apply {
            action = Intent.ACTION_SEND
            putExtra(Intent.EXTRA_TEXT, text)
            type = "text/plain"
        }

        val shareIntent = Intent.createChooser(sendIntent, null)
        shareIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK

        safeActivityLaunch(shareIntent)
    }

    private fun safeActivityLaunch(intent: Intent) {
        try {
            ContextCompat.startActivity(context, intent, null)
        } catch (e: ActivityNotFoundException) {
            throw ExternalAppNotFoundException(e)
        }
    }
}