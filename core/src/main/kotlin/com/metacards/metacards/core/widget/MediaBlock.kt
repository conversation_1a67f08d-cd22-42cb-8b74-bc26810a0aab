package com.metacards.metacards.core.widget

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.metacards.metacards.core.R
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.clickable

@Composable
fun MediaBlock(
    onInstagramClick: () -> Unit,
    onFacebookClick: () -> Unit,
    onInternetClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(24.dp, Alignment.CenterHorizontally)
    ) {
        Icon(
            painter = painterResource(id = R.drawable.ic_24_instagram),
            contentDescription = null,
            tint = CustomTheme.colors.icons.primary,
            modifier = Modifier.clickable { onInstagramClick() }
        )

        Icon(
            painter = painterResource(id = R.drawable.ic_24_facebook),
            contentDescription = null,
            tint = CustomTheme.colors.icons.primary,
            modifier = Modifier.clickable { onFacebookClick() }
        )

        Icon(
            painter = painterResource(id = R.drawable.ic_24_internet),
            contentDescription = null,
            tint = CustomTheme.colors.icons.primary,
            modifier = Modifier.clickable { onInternetClick() }
        )
    }
}