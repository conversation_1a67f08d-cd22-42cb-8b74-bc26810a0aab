package com.metacards.metacards.core.network.firestore

import com.google.firebase.firestore.ListenerRegistration
import com.google.firebase.firestore.ktx.firestore
import com.google.firebase.ktx.Firebase

class FirestoreService : FirestoreListenerHost {
    val db = Firebase.firestore

    private val _activeListeners: MutableSet<ListenerRegistration> = mutableSetOf()

    override val activeListeners: Set<ListenerRegistration>
        get() = _activeListeners

    override fun addListener(registration: ListenerRegistration) {
        _activeListeners.add(registration)
    }

    override fun removeListener(registration: ListenerRegistration) {
        _activeListeners.remove(registration)
    }

    fun clear() {
        activeListeners.forEach {
            it.remove()
        }
    }
}