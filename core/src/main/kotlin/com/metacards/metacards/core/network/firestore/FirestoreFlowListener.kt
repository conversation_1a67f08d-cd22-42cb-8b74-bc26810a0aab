package com.metacards.metacards.core.network.firestore

import co.touchlab.kermit.Logger
import com.google.firebase.firestore.EventListener
import com.google.firebase.firestore.FirebaseFirestoreException
import com.metacards.metacards.core.utils.LoadableState
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow

class FirestoreFlowListener<T : Any> : EventListener<T> {

    private val logger = Logger.withTag("FirestoreFlowListener")

    val sharedFlow =
        MutableSharedFlow<T>(replay = 1, onBufferOverflow = BufferOverflow.DROP_OLDEST)

    override fun onEvent(value: T?, error: FirebaseFirestoreException?) {
        if (error != null) {
            logger.e("OnEvent error", error)
            return
        }
        value?.let(sharedFlow::tryEmit)
    }
}

class FireStoreLoadableFlowListener<T : Any> : EventListener<T> {
    val sharedFlow =
        MutableSharedFlow<LoadableState<T>>(
            replay = 1,
            onBufferOverflow = BufferOverflow.DROP_OLDEST
        )

    init {
        sharedFlow.tryEmit(LoadableState(true))
    }

    override fun onEvent(value: T?, error: FirebaseFirestoreException?) {
        if (error != null) {
            sharedFlow.tryEmit(LoadableState(data = value))
        }
        sharedFlow.tryEmit(LoadableState(data = value))
    }
}
