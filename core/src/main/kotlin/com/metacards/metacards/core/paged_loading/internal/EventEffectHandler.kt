package com.metacards.metacards.core.paged_loading.internal

import me.aartikov.sesame.loop.EffectHandler
import com.metacards.metacards.core.paged_loading.PagedLoading

internal class EventEffectHandler<T : Any>(private val emitEvent: (PagedLoading.Event<T>) -> Unit) :
    EffectHandler<Effect<T>, Action<T>> {

    override suspend fun handleEffect(effect: Effect<T>, actionConsumer: (Action<T>) -> Unit) {
        if (effect is Effect.EmitEvent<T>) {
            emitEvent(effect.event)
        }
    }
}