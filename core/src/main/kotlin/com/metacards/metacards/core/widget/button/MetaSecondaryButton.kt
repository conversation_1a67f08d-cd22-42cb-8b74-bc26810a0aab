package com.metacards.metacards.core.widget.button

import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material.ButtonDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import com.metacards.metacards.core.button.ButtonState
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.theme.material.AppTheme

@Composable
fun MetaSecondaryButton(
    text: String,
    modifier: Modifier = Modifier,
    leadingWidget: @Composable (() -> Unit)? = null,
    state: ButtonState = ButtonState.Enabled,
    onClick: () -> Unit
) {
    MetaButton(
        text = text,
        modifier = modifier,
        state = state,
        onClick = onClick,
        interactionSource = remember { MutableInteractionSource() },
        elevation = ButtonDefaults.elevation(),
        shape = MetaButtonDefaults.bigButton.buttonShape,
        colors = MetaButtonDefaults.bigButton.buttonSecondaryColors,
        contentPadding = MetaButtonDefaults.bigButton.buttonPaddingValues,
        textStyle = CustomTheme.typography.button.primary,
        leadingWidget = leadingWidget
    )
}

@Preview(group = "SecondaryButton")
@Composable
fun MetaSecondaryButtonPreview() {
    AppTheme {
        MetaSecondaryButton(
            modifier = Modifier.fillMaxWidth(),
            state = ButtonState.Enabled,
            text = "Button",
            onClick = {}
        )
    }
}

@Preview(group = "SecondaryButton")
@Composable
fun MetaSecondaryButtonLoadingPreview() {
    AppTheme {
        MetaSecondaryButton(
            modifier = Modifier.fillMaxWidth(),
            state = ButtonState.Loading,
            text = "Button",
            onClick = {}
        )
    }
}

@Preview(group = "SecondaryButton")
@Composable
fun MetaSecondaryButtonDisabledPreview() {
    AppTheme {
        MetaSecondaryButton(
            modifier = Modifier.fillMaxWidth(),
            state = ButtonState.Disabled,
            text = "Button",
            onClick = {}
        )
    }
}