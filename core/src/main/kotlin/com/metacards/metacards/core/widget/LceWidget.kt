package com.metacards.metacards.core.widget

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.metacards.metacards.core.utils.ErrorMessage
import com.metacards.metacards.core.utils.LoadableState

/**
 * Displays [LoadableState].
 */
@Composable
fun <T : Any> LceWidget(
    state: LoadableState<T>,
    onRetryClick: () -> Unit,
    modifier: Modifier = Modifier,
    loadingProgress: @Composable () -> Unit = { FullscreenCircularProgress(modifier) },
    errorPlaceholder: @Composable (message: ErrorMessage, onRetry: () -> Unit) -> Unit = { message, onRetry ->
        ErrorPlaceholder(message, onRetry, modifier)
    },
    content: @Composable (data: T, refreshing: Boolean) -> Unit,
) {
    val (loading, data, error) = state
    when {
        data != null -> content(data, loading)

        loading -> loadingProgress()

        error != null -> errorPlaceholder(error, onRetryClick)
    }
}
