package com.metacards.metacards.core.message.ui

import kotlinx.coroutines.flow.StateFlow
import com.metacards.metacards.core.message.domain.Message

/**
 * A component for centralized message showing. There should be only one instance
 * of this component in the app connected to the root component.
 */
interface MessageComponent {

    val visibleMessage: StateFlow<Message?>

    fun onActionClick()
}
