package com.metacards.metacards.core.clipboard

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import com.metacards.metacards.core.message.data.MessageService
import com.metacards.metacards.core.message.domain.Message

class ClipboardManager(
    context: Context,
    private val messageService: MessageService
) {

    private val manager = context.getSystemService(ClipboardManager::class.java)

    fun copyToClipboard(textToCopy: String, message: Message?) {
        val clipData = ClipData.newPlainText("", textToCopy)
        manager.setPrimaryClip(clipData)
        message?.let(messageService::showMessage)
    }
}