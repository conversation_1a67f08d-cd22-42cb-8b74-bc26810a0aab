package com.metacards.metacards.core.dialog.default_component

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.AlertDialog
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties
import com.metacards.metacards.core.dialog.DialogControl
import com.metacards.metacards.core.dialog.default_component.model.DialogData
import com.metacards.metacards.core.localization.ui.localizedByLocal
import com.metacards.metacards.core.theme.custom.CustomTheme
import com.metacards.metacards.core.utils.clickable

@Composable
fun DefaultDialog(dialogControl: DialogControl<*, DefaultDialogComponent>) {
    val dialogOverlay by dialogControl.dialogOverlay.collectAsState()

    dialogOverlay.child?.instance?.let {
        DefaultDialog(component = it, dialogControl.canDismissed)
    }
}

@Composable
fun DefaultDialog(component: DefaultDialogComponent, canDismissed: Boolean) {
    val data = component.dialogData
    DefaultDialog(
        title = data.title.localizedByLocal(),
        message = data.message.localizedByLocal(),
        buttons = data.buttons,
        canDismissed = canDismissed,
        onDismiss = { component.onDismiss() }
    )
}

@Composable
fun DefaultDialog(
    title: String,
    message: String,
    buttons: List<DialogData.Button>,
    canDismissed: Boolean,
    onDismiss: () -> Unit,
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        buttons = {
            DialogButtons(buttons)
        },
        title = {
            Text(
                modifier = Modifier.padding(top = 24.dp),
                text = title,
                color = CustomTheme.colors.text.primary,
                style = CustomTheme.typography.heading.medium
            )
        },
        text = {
            Text(
                modifier = Modifier.padding(top = 16.dp),
                text = message,
                color = CustomTheme.colors.text.primary,
                style = CustomTheme.typography.body.primary
            )
        },
        backgroundColor = CustomTheme.colors.background.modal,
        properties = DialogProperties(
            dismissOnBackPress = canDismissed,
            dismissOnClickOutside = canDismissed
        )
    )
}

@Composable
fun DialogButtons(buttons: List<DialogData.Button>) {
    Box(
        modifier = Modifier
            .padding(8.dp)
            .fillMaxWidth(),
        contentAlignment = Alignment.CenterEnd
    ) {
        Row(
            modifier = Modifier,
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            buttons.forEach { DialogButton(it) }
        }
    }
}

@Composable
fun DialogButton(button: DialogData.Button) {
    Text(
        modifier = Modifier
            .padding(vertical = 10.dp, horizontal = 8.dp)
            .clickable(boundedRipple = false, onClick = button.action),
        text = button.title.localizedByLocal().uppercase(),
        color = CustomTheme.colors.text.primary
    )
}
