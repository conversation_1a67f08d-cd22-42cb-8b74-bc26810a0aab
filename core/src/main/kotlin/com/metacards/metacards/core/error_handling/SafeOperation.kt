package com.metacards.metacards.core.error_handling

import com.metacards.metacards.core.R
import com.metacards.metacards.core.utils.Resource
import dev.icerock.moko.resources.desc.StringDesc
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

/**
 * Allows to run a function safely (with error handing).
 */
fun <T> safeRun(
    errorHandler: <PERSON><PERSON>r<PERSON><PERSON><PERSON>,
    showError: Boolean = true,
    onErrorHandled: ((e: Exception) -> Unit)? = null,
    block: () -> T
): T? {
    return try {
        block()
    } catch (e: Exception) {
        errorHandler.handleError(e, showError)
        onErrorHandled?.invoke(e)
        return null
    }
}

/**
 * Allows to run a suspend function safely (with error handing).
 */
fun CoroutineScope.safeLaunch(
    errorHandler: <PERSON>rror<PERSON>and<PERSON>,
    showError: Boolean = true,
    onErrorHandled: ((e: Exception) -> Unit)? = null,
    block: suspend () -> Unit
): Job {
    val originalExceptionStackTrace = Thread.currentThread().stackTrace
    return launch {
        try {
            block()
        } catch (e: CancellationException) {
            // do nothing
        } catch (e: Exception) {
            val fullEx = if (e is UnauthorizedException) {
                UnauthorizedException(e)
            } else {
                Exception(e.message, e)
            }
            fullEx.apply {
                stackTrace = originalExceptionStackTrace
            }
            errorHandler.handleError(fullEx, showError)
            onErrorHandled?.invoke(e)
        }
    }
}

/**
 * Allows to run a suspend function safely (with error handing) and allows to retry a failed action.
 */
fun CoroutineScope.safeLaunchRetryable(
    errorHandler: ErrorHandler,
    onErrorHandled: ((e: Exception) -> Unit)? = null,
    retryActionTitle: StringDesc = StringDesc.Resource(R.string.common_retry),
    retryAction: () -> Unit,
    block: suspend () -> Unit
): Job {
    return launch {
        try {
            block()
        } catch (e: CancellationException) {
            // do nothing
        } catch (e: Exception) {
            errorHandler.handleErrorRetryable(
                exception = e,
                retryActionTitle = retryActionTitle,
                retryAction = retryAction
            )
            onErrorHandled?.invoke(e)
        }
    }
}
