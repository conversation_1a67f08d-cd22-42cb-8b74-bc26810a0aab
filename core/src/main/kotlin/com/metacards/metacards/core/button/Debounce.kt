package com.metacards.metacards.core.button

import com.arkivanov.decompose.ComponentContext
import com.metacards.metacards.core.error_handling.ErrorHandler
import com.metacards.metacards.core.error_handling.safeLaunch
import com.metacards.metacards.core.utils.componentScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * A Class to debounce clicks on various UI elements such as Button.
 *
 * Call the [click] function to perform a click
 */
class Debounce {
    val set: MutableSet<String> = mutableSetOf()

    /**
     * Run [block] if no other [click] function with this [id] is running
     */
    suspend inline fun click(
        id: String,
        progressStateFlow: MutableStateFlow<Boolean>? = null,
        crossinline block: suspend () -> Unit
    ) {
        if (set.contains(id)) return

        try {
            progressStateFlow?.value = true
            set.add(id)
            block()
        } finally {
            set.remove(id)
            progressStateFlow?.value = false
        }
    }
}

/**
 * Extension to perform [debounce] click with [id] and [onClick] in [ComponentContext]
 */
fun ComponentContext.DebounceClick(
    debounce: Debounce,
    id: String,
    errorHandler: ErrorHandler? = null,
    progressStateFlow: MutableStateFlow<Boolean>? = null,
    onErrorHandled: ((e: Exception) -> Unit)? = null,
    onClick: suspend () -> Unit
) {
    if (errorHandler == null) {
        componentScope.launch {
            debounce.click(id, progressStateFlow, onClick)
        }
    } else {
        componentScope.safeLaunch(
            errorHandler = errorHandler,
            onErrorHandled = onErrorHandled
        ) {
            debounce.click(id, progressStateFlow, onClick)
        }
    }
}