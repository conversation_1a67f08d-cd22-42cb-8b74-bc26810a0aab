package com.metacards.metacards.core.bottom_sheet

import android.os.Parcelable
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import com.metacards.metacards.core.utils.createFakeChildSlot

class FakeBottomSheetControl<C : Parcelable, T : Any>(bottomSheetComponent: T) :
    BottomSheetControl<C, T> {
    override val sheetOverlay = MutableStateFlow(createFakeChildSlot(bottomSheetComponent))

    override val halfExpandingSupported: Boolean = true
    override val hidingSupported: Boolean = true
    override val sheetState: StateFlow<BottomSheetControl.State> = MutableStateFlow(
        BottomSheetControl.State.Hidden
    )
    override val dismissEvent: StateFlow<Unit> = MutableStateFlow(Unit)

    override fun shouldUpdateState(newState: BottomSheetControl.State): Boolean = true
    override fun onStateChangedFromUI(state: BottomSheetControl.State) = Unit
    override fun onStateChangeAnimationEnd(targetState: BottomSheetControl.State) = Unit
    override fun show(config: C) = Unit
    override fun dismiss() = Unit
}
