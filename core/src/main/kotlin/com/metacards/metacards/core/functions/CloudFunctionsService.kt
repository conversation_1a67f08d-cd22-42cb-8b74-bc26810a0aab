package com.metacards.metacards.core.functions

import com.google.firebase.functions.ktx.functions
import com.google.firebase.ktx.Firebase
import kotlinx.coroutines.tasks.await
import org.json.JSONObject

@Suppress("UNCHECKED_CAST")
class CloudFunctionsService {
    private val functions = Firebase.functions

    suspend fun <T> call(functionName: String, dataJson: String): FunctionResult<T> {
        val data = JSONObject(dataJson)
        val result = functions.getHttpsCallable(functionName).call(data).await()
        return FunctionResult(result.data as? Map<String, T>)
    }

    suspend fun <T> call(functionName: String, data: Map<String, String>): FunctionResult<T> {
        val result = functions.getHttpsCallable(functionName).call(data).await()
        return FunctionResult(result.data as? Map<String, T>)
    }
}