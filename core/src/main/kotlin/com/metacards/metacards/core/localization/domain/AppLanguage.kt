package com.metacards.metacards.core.localization.domain

enum class AppLanguage(val tag: String) {
    RUS("ru"),
    ENG("en"),
    ESP("es"),
    UKR("ukr"),
    KAZ("kaz"),
    FRA("fr"),
    ARB("ar");

    companion object {
        fun fromString(language: String?): AppLanguage? {
            return try {
                AppLanguage.valueOf(language ?: "")
            } catch (ex: Exception) {
                null
            }
        }

        fun getDefaultAppLanguage(): AppLanguage {
            return ENG
        }
    }
}
